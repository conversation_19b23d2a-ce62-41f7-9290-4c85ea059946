


-- Correct Liens table


ALTER TABLE integration.dev.liens
    DROP COLUMN SourceCreateDateTime;

ALTER TABLE integration.dev.liens
    DROP COLUMN SourceModifiedDateTime;

ALTER TABLE integration.dev.liens
    DROP COLUMN CreateDateTime;

ALTER TABLE integration.dev.liens
    DROP COLUMN ModifiedDateTime;


ALTER TABLE integration.staging.liens
    DROP COLUMN SourceCreateDateTime;

ALTER TABLE integration.staging.liens
    DROP COLUMN SourceModifiedDateTime;

ALTER TABLE integration.staging.liens
    DROP COLUMN CreateDateTime;

ALTER TABLE integration.staging.liens
    DROP COLUMN ModifiedDateTime;


ALTER TABLE integration.prod.liens
    DROP COLUMN SourceCreateDateTime;

ALTER TABLE integration.prod.liens
    DROP COLUMN SourceModifiedDateTime;

ALTER TABLE integration.prod.liens
    DROP COLUMN CreateDateTime;

ALTER TABLE integration.prod.liens
    DROP COLUMN ModifiedDateTime;


ALTER TABLE integration.main.liens
    DROP COLUMN SourceCreateDateTime;

ALTER TABLE integration.main.liens
    DROP COLUMN SourceModifiedDateTime;

ALTER TABLE integration.main.liens
    DROP COLUMN CreateDateTime;

ALTER TABLE integration.main.liens
    DROP COLUMN ModifiedDateTime;



-- Correct Cases table


ALTER TABLE integration.dev.Cases
    ADD COLUMN DateSettled TIMESTAMP;


ALTER TABLE integration.staging.Cases
    ADD COLUMN DateSettled TIMESTAMP;



-- Correct Insurances table

ALTER TABLE integration.dev.insurances
    DROP COLUMN BillsPaid;

ALTER TABLE integration.dev.insurances
    ADD COLUMN BillsPaid BOOLEAN;


ALTER TABLE integration.staging.insurances
    DROP COLUMN BillsPaid;

ALTER TABLE integration.staging.insurances
    ADD COLUMN BillsPaid BOOLEAN;


