import datetime
import functools
import glob
import hashlib
import json
import pathlib
import re
import time
import typing

import cachetools
import pandas as pd
import requests.adapters
import sqlparse
import typing_extensions

from . import aws_operations, logger_config

logger = logger_config.get_logger(__name__)

with (
    open(
        'integration/integration_code/canonical_model.json', 'rb'
    ) as canonical_map_file,
    open(
        'integration/integration_code/canonical_data_model.json', 'rb'
    ) as canonical_data_map_file,
    open(
        'integration/integration_code/variable_balance_providers.json', 'rb'
    ) as variable_balance_providers_file,
    open(
        'integration/integration_code/automated_sf_updates.json', 'rb'
    ) as automated_sf_updates_file,
    open(
        'integration/integration_code/reversal_transaction_configuration.json',
        'rb',
    ) as reversal_transaction_configuration_file,
    open(
        'integration/integration_code/state_name_mapping.json',
        'rb',
    ) as state_name_mapping_file,
    open(
        'integration/integration_code/claim_settlement_configuration.json',
        'rb',
    ) as claim_settlement_configuration_file,
):
    canonical = json.load(canonical_map_file)
    canonical_objects = set(canonical.keys())
    canonical_data = json.load(canonical_data_map_file)
    variable_balance_providers = json.load(variable_balance_providers_file)[
        "name"
    ]
    automated_sf_updates = json.load(automated_sf_updates_file)
    reversal_transaction_configuration = json.load(
        reversal_transaction_configuration_file
    )
    state_name_mapping = json.load(state_name_mapping_file)
    state_code_to_name_map = state_name_mapping["state_code_to_name_map"]
    state_name_to_code_map = state_name_mapping["state_name_to_code_map"]
    claim_settlement_configuration = json.load(
        claim_settlement_configuration_file
    )


# region Canonical


def assign_gainids_to_dataframe_column(
    df: pd.DataFrame,
    column_name: str,
    canonical_object: str,
    get_gainids_func: typing.Callable[
        [list[str], str], list[str] | list[None]
    ],
) -> None:
    ids = [source_id for source_id in df[column_name]]
    retrieved_gainids = get_gainids_func(ids, canonical_object)
    df[column_name] = pd.Series(retrieved_gainids, index=df.index)


def update_charges_from_transactions(
    transactions_id_subset: typing.Optional[list[str]] = None,
) -> typing.Optional[list[str]]:
    timestamp = datetime.datetime.now()
    df_charges_to_update = (
        aws_operations.get_postgres_transactions_to_update_charges(
            transactions_id_subset
        )
    )
    if df_charges_to_update is None or df_charges_to_update.empty:
        logger.warning('No data fetched for transaction update.')
        return
    chargegainids = df_charges_to_update['chargegainid'].unique().tolist()
    source_names = set(
        aws_operations.get_sourcenames_by_gainids(chargegainids)
    )
    source_names = [name for name in source_names if name is not None]
    df_reverse_transaction_type_charge_ids = df_charges_to_update[
        df_charges_to_update["transactiontype"] == "Reversal"
    ]["chargegainid"]
    df_billings_for_transactions = (
        aws_operations.get_billings_for_transactions(
            df_charges_to_update['transactiongainid'].tolist(),
        )
    )
    servicing_start_date_map = df_billings_for_transactions.set_index(
        'transactiongainid'
    )['billingservicingstartdatetime'].to_dict()
    servicing_end_date_map = df_billings_for_transactions.set_index(
        'transactiongainid'
    )['billingservicingenddatetime'].to_dict()
    df_charges_to_update['billingservicingstartdatetime'] = (
        df_charges_to_update['transactiongainid'].map(servicing_start_date_map)
    )
    df_charges_to_update['billingservicingenddatetime'] = df_charges_to_update[
        'transactiongainid'
    ].map(servicing_end_date_map)
    df_gain_serviced_transactions = df_charges_to_update[
        (
            df_charges_to_update['transactionpaymentdatetime']
            > df_charges_to_update['billingservicingstartdatetime']
        )
        & (
            (
                df_charges_to_update['transactionpaymentdatetime']
                < df_charges_to_update['billingservicingenddatetime']
            )
            | (df_charges_to_update['billingservicingenddatetime'].isna())
        )
    ]
    df_gain_serviced_invoiceable_transactions = pd.DataFrame(
        columns=df_gain_serviced_transactions.columns
    )
    for source_name in source_names:
        if source_name in claim_settlement_configuration.keys():
            invoiceable_transaction_descriptions = (
                claim_settlement_configuration[source_name]['Transactions'][
                    'FromDescription'
                ]['Invoiceable']
            )
            invoiceable_transaction_carrier_insurance_types = (
                claim_settlement_configuration[source_name]['Transactions'][
                    'FromCarrierInsuranceType'
                ]['Invoiceable']
            )
            df_gain_serviced_transactions_with_invoiceable_descriptions = (
                df_gain_serviced_transactions[
                    df_gain_serviced_transactions[
                        'transactiondescription'
                    ].isin(invoiceable_transaction_descriptions)
                ]
            )
            df_gain_serviced_transactions_with_invoiceable_carrier_insurance_types = df_gain_serviced_transactions[
                df_gain_serviced_transactions[
                    'transactioncarrierinsurancetype'
                ].isin(invoiceable_transaction_carrier_insurance_types)
            ]
            df_gain_serviced_invoiceable_transactions = pd.concat(
                [
                    df_gain_serviced_invoiceable_transactions,
                    df_gain_serviced_transactions_with_invoiceable_carrier_insurance_types,
                    df_gain_serviced_transactions_with_invoiceable_descriptions,
                ]
            ).drop_duplicates()
    df_charges_to_update['prenegotiationgaintransaction'] = False
    df_charges_to_update['prenegotiationgaintransaction'] = (
        df_charges_to_update['transactiongainid'].isin(
            df_gain_serviced_invoiceable_transactions['transactiongainid']
        )
    )
    df_charges_to_update_denials = (
        pd.pivot_table(
            df_charges_to_update,
            values='transactionamount',
            index=['chargegainid'],
            columns=['transactioncarccode'],
            aggfunc='sum',
        )
        .fillna(0.0)
        .reset_index()
    )
    if not df_charges_to_update_denials.empty:
        carc_codes = [
            c
            for c in df_charges_to_update['transactioncarccode']
            .dropna()
            .unique()
            .tolist()
            if c
        ]
        df_charges_to_update_denials = df_charges_to_update_denials[
            ['chargegainid'] + carc_codes
        ]
    df_charges_to_update_non_denials = pd.pivot_table(
        df_charges_to_update,
        values='transactionamount',
        index=['chargegainid', 'chargeamount'],
        columns=['transactiontype', 'prenegotiationgaintransaction'],
        aggfunc='sum',
    ).fillna(0.0)
    df_charges_to_update_non_denials.columns = (
        df_charges_to_update_non_denials.columns.to_flat_index()
    )
    df_charges_to_update_non_denials.reset_index(inplace=True)
    df_charges_to_update = df_charges_to_update_non_denials.merge(
        df_charges_to_update_denials,
        on=['chargegainid'],
        how='left',
    )
    # If the type of transaction does not exist in the table, add it with 0.0
    if ('Payment', False) not in df_charges_to_update.columns:
        df_charges_to_update[('Payment', False)] = 0.0
    if ('Discount', False) not in df_charges_to_update.columns:
        df_charges_to_update[('Discount', False)] = 0.0
    if ('Adjustment', False) not in df_charges_to_update.columns:
        df_charges_to_update[('Adjustment', False)] = 0.0
    if ('Payment', True) not in df_charges_to_update.columns:
        df_charges_to_update[('Payment', True)] = 0.0
    if ('Discount', True) not in df_charges_to_update.columns:
        df_charges_to_update[('Discount', True)] = 0.0
    if ('Adjustment', True) not in df_charges_to_update.columns:
        df_charges_to_update[('Adjustment', True)] = 0.0
    if 'Deductible' not in df_charges_to_update.columns:
        df_charges_to_update['Deductible'] = 0.0
    if 'Coinsurance' not in df_charges_to_update.columns:
        df_charges_to_update['Coinsurance'] = 0.0
    if 'Copayment' not in df_charges_to_update.columns:
        df_charges_to_update['Copayment'] = 0.0
    # Need to perform float conversion before mathematical operations,
    # as they might be string values and "summing" will concatenate instead
    # Also, fill NaN values with 0.0 to avoid NaN results
    df_charges_to_update['chargeamount'] = (
        df_charges_to_update['chargeamount'].astype(float).fillna(0.0)
    )
    df_charges_to_update['nongainamountpaidtoprovider'] = (
        df_charges_to_update[('Payment', False)].astype(float).fillna(0.0)
    )
    df_charges_to_update['gainprenegotiationamountpaidtoprovider'] = (
        df_charges_to_update[('Payment', True)].astype(float).fillna(0.0)
    )
    df_charges_to_update[('Discount', False)] = (
        df_charges_to_update[('Discount', False)].astype(float).fillna(0.0)
    )
    df_charges_to_update[('Adjustment', False)] = (
        df_charges_to_update[('Adjustment', False)].astype(float).fillna(0.0)
    )
    df_charges_to_update[('Discount', True)] = (
        df_charges_to_update[('Discount', True)].astype(float).fillna(0.0)
    )
    df_charges_to_update[('Adjustment', True)] = (
        df_charges_to_update[('Adjustment', True)].astype(float).fillna(0.0)
    )
    df_charges_to_update['Deductible'] = (
        df_charges_to_update['Deductible'].astype(float).fillna(0.0)
    )
    df_charges_to_update['Coinsurance'] = (
        df_charges_to_update['Coinsurance'].astype(float).fillna(0.0)
    )
    df_charges_to_update['Copayment'] = (
        df_charges_to_update['Copayment'].astype(float).fillna(0.0)
    )
    df_charges_to_update.columns = [
        x.lower() if isinstance(x, str) else x
        for x in df_charges_to_update.columns
    ]
    df_charges_to_update['nongainadjustment'] = (
        df_charges_to_update[('Discount', False)]
        + df_charges_to_update[('Adjustment', False)]
    )
    df_charges_to_update['gainprenegotiationadjustment'] = (
        df_charges_to_update[('Discount', True)]
        + df_charges_to_update[('Adjustment', True)]
    )
    # Do not need to drop columns, they are filtered out before Redshift update
    # df_charges_to_update = df_charges_to_update.drop(
    #     columns=['Discount', 'Adjustment']
    # )
    df_charges_to_update['balance'] = (
        # Leave fillna(0.0) as additional safety measure
        df_charges_to_update['chargeamount'].fillna(0.0)
        - df_charges_to_update['nongainadjustment'].fillna(0.0)
        - df_charges_to_update['nongainamountpaidtoprovider'].fillna(0.0)
        - df_charges_to_update['gainprenegotiationadjustment'].fillna(0.0)
        - df_charges_to_update[
            'gainprenegotiationamountpaidtoprovider'
        ].fillna(0.0)
    )
    # Round to 4 decimals to match Redshift schema
    df_charges_to_update = df_charges_to_update.round(4)
    # Filter and reorder columns to match expected format for Redshift update
    df_charges_to_update = df_charges_to_update[
        [
            'chargegainid',
            'nongainamountpaidtoprovider',
            'nongainadjustment',
            'gainprenegotiationamountpaidtoprovider',
            'gainprenegotiationadjustment',
            'deductible',
            'coinsurance',
            'copayment',
            'balance',
        ]
    ]
    aws_operations.update_postgres_charges_from_non_reversal_transactions(
        df_charges_to_update, timestamp
    )
    for source_name in source_names:
        if source_name in reversal_transaction_configuration['Complete']:
            aws_operations.update_postgres_charges_from_complete_reversal_transactions(
                df_reverse_transaction_type_charge_ids,
                timestamp,
            )
        else:
            pass  # Partial Reversal TBD
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data=True)
    updated_charge_keys = [
        r for r in df_charges_to_update['chargegainid'].to_numpy()
    ]
    return updated_charge_keys


def update_billings_from_charges(
    charges_id_subset: typing.Optional[list[str]] = None,
) -> typing.Optional[list[str]]:
    timestamp = datetime.datetime.now()
    df_billings_to_update = (
        aws_operations.get_postgres_charges_to_update_billings(
            charges_id_subset
        )
    )
    if df_billings_to_update is None or df_billings_to_update.empty:
        logger.error('No data fetched for billing update')
        return
    df_billings_to_update['totalbalance'] = (
        df_billings_to_update['totalamount'].fillna(0)
        - df_billings_to_update['totalnongainadjustment'].fillna(0)
        - df_billings_to_update['totalnongainamountpaidtoprovider'].fillna(0)
        - df_billings_to_update['totalgainprenegotiationadjustment'].fillna(0)
        - df_billings_to_update[
            'totalgainprenegotiationamountpaidtoprovider'
        ].fillna(0)
    )
    # round to 4 decimals
    df_billings_to_update = df_billings_to_update.round(4)
    # rearrange columns
    df_billings_to_update = df_billings_to_update[
        [
            'gainid',
            'totalamount',
            'totalnongainadjustment',
            'totalnongainamountpaidtoprovider',
            'totalgainprenegotiationadjustment',
            'totalgainprenegotiationamountpaidtoprovider',
            'totaldeductible',
            'totalcoinsurance',
            'totalcopayment',
            'totalbalance',
        ]
    ]
    aws_operations.update_postgres_billings_from_charges(
        df_billings_to_update, timestamp
    )
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data=True)
    updated_billing_keys = [
        r for r in df_billings_to_update['gainid'].to_numpy()
    ]
    return updated_billing_keys


def update_cases_from_billings(
    billings_id_subset: typing.Optional[list[str]] = None,
) -> typing.Optional[list[str]]:
    timestamp = datetime.datetime.now()
    df_cases_to_update = aws_operations.get_postgres_billings_to_update_cases(
        billings_id_subset
    )
    if df_cases_to_update is None or df_cases_to_update.empty:
        logger.error('No data fetched for cases update')
        return
    df_cases_to_update['grandtotalbalance'] = (
        df_cases_to_update['grandtotalamount'].fillna(0)
        - df_cases_to_update['grandtotalnongainadjustment'].fillna(0)
        - df_cases_to_update['grandtotalnongainamountpaidtoprovider'].fillna(0)
        - df_cases_to_update['grandtotalgainprenegotiationadjustment'].fillna(
            0
        )
        - df_cases_to_update[
            'grandtotalgainprenegotiationamountpaidtoprovider'
        ].fillna(0)
    )
    # round to 4 decimals
    df_cases_to_update = df_cases_to_update.round(4)
    # rearrange columns
    df_cases_to_update = df_cases_to_update[
        [
            'gainid',
            'grandtotalamount',
            'grandtotalnongainadjustment',
            'grandtotalnongainamountpaidtoprovider',
            'grandtotalgainprenegotiationadjustment',
            'grandtotalgainprenegotiationamountpaidtoprovider',
            'grandtotaldeductible',
            'grandtotalcoinsurance',
            'grandtotalcopayment',
            'grandtotalbalance',
        ]
    ]
    aws_operations.update_postgres_cases_from_billings(
        df_cases_to_update, timestamp
    )
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data=True)
    updated_cases_keys = [r for r in df_cases_to_update['gainid'].to_numpy()]
    return updated_cases_keys


def update_cases_from_insurances(
    insurances_id_subset: set[str] | None = None,
) -> set[str] | None:
    timestamp = datetime.datetime.now()
    df_cases_to_update = (
        aws_operations.get_postgres_insurances_to_update_cases(
            insurances_id_subset=insurances_id_subset
        )
    )
    if df_cases_to_update is None or df_cases_to_update.empty:
        logger.error('No data fetched for cases update')
        return
    aws_operations.update_postgres_cases_from_insurances(
        df_cases_to_update=df_cases_to_update,
        timestamp=timestamp,
    )
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data=True)
    updated_cases_keys = set(df_cases_to_update['gainid'])
    return updated_cases_keys


def get_redshift_to_salesforce_update_settled_case_data(
    redshift_cases_data: dict[str, list[typing.Any]],
) -> pd.DataFrame:
    df_redshift_cases_data = pd.DataFrame(
        redshift_cases_data, columns=canonical['Cases'].keys()
    )
    # Pending payment cases are the cases which have non-null GrandTotalSettlementAmount and DateSettled
    df_redshift_pending_payment_from_hcp_cases_data = (
        df_redshift_cases_data.dropna(
            subset=['GrandTotalSettlementAmount', 'DateSettled']
        )
    )
    df_redshift_pending_payment_from_hcp_cases_data = df_redshift_cases_data[
        ['GainId', 'GrandTotalSettlementAmount', 'DateSettled']
    ]
    df_redshift_pending_payment_from_hcp_cases_data[
        'GrandTotalSettlementAmount'
    ] = df_redshift_pending_payment_from_hcp_cases_data[
        'GrandTotalSettlementAmount'
    ].astype(
        str
    )
    df_redshift_pending_payment_from_hcp_cases_data['DateSettled'] = (
        df_redshift_pending_payment_from_hcp_cases_data['DateSettled'].apply(
            lambda x: datetime.datetime.strftime(x, '%Y-%m-%d')
        )
    )
    return df_redshift_pending_payment_from_hcp_cases_data


def get_redshift_to_salesforce_update_settled_billing_data(
    df_redshift_billings_data: pd.DataFrame,
    update_settled_status: str,
) -> pd.DataFrame:
    if update_settled_status == 'Pending Payment from HCP':
        df_redshift_update_settled_billings_data = df_redshift_billings_data[
            (df_redshift_billings_data['PaidTo'] == 'HCP')
            & (df_redshift_billings_data['PaidBy'] == 'Attorney')
        ].copy()
        df_redshift_update_settled_billings_data['PayoffStatus'] = (
            'Pending Payment from HCP'
        )
    elif update_settled_status == 'Liability Insurance Payment to Provider':
        df_redshift_update_settled_billings_data = df_redshift_billings_data[
            (df_redshift_billings_data['PaidTo'] == 'HCP')
            & (
                df_redshift_billings_data['PaidBy']
                == 'AutoCommercialInsurance'
            )
        ].copy()
        df_redshift_update_settled_billings_data['PayoffStatus'] = (
            'Liability Insurance Payment to Provider'
        )
    elif update_settled_status == 'WriteOff':
        df_redshift_update_settled_billings_data = df_redshift_billings_data[
            df_redshift_billings_data['Status'] == 'WriteOff'
        ].copy()
        df_redshift_update_settled_billings_data['PayoffStatus'] = (
            'Write Off by Provider'
        )
    elif update_settled_status == 'PaidByHealthInsurance':
        return df_redshift_billings_data[
            df_redshift_billings_data['status'] == 'PaidByHealthInsurance'
        ][['GainId']]
    else:
        raise ValueError(
            f'Invalid update settled status: {update_settled_status}'
        )

    return df_redshift_update_settled_billings_data[['GainId', 'PayoffStatus']]


# endregion


# region Others


def get_aws_name(file_name: str) -> str:
    aws_file_name = file_name
    if '/' in file_name:
        aws_file_name = file_name.split('/')[-1]
    return aws_file_name


def write_json(data: typing.Any, name: str) -> str:
    with open(f'{name}.json', 'w') as out:
        json.dump(data, out, separators=(',', ':'), sort_keys=False)
    return f'{name}.json'


def clean_up_file(file_name: str) -> None:
    files = glob.glob(f'*{file_name[:-4]}*')
    for file in files:
        pathlib.Path(file).unlink(missing_ok=True)


def clean_up_temp_downloads() -> None:
    files = glob.glob('*integration/integration_code/temp_downloads/*')
    for file in files:
        pathlib.Path(file).unlink(missing_ok=True)


def get_client_canonical_name(source: str, name: str) -> str:
    return canonical_data[source]['ToCanonical']['Clients'].get(name, name)


def get_client_salesforce_name(name: str | None) -> str | None:
    return canonical_data['Salesforce']['FromCanonical']['Clients'].get(
        name, name
    )


def get_canonical_ids_from_df(values: pd.DataFrame) -> list[str]:
    canonical_ids = set()  # use set to prevent duplicate plaintiff ids
    for val in values.itertuples():
        canonical_ids.add(val.GainId)
    return list(
        canonical_ids
    )  # convert to list because list-compatible redshift query insertion is defined already


def get_canonical_ids(
    canonical_object: str, values: list[dict[str, typing.Any]]
) -> list[str]:
    canonical_ids = set()  # use set to prevent duplicate plaintiff ids
    for val in values:
        canonical_ids.add(val[canonical[canonical_object]['GainId']])
    return list(
        canonical_ids
    )  # convert to list because list-compatible redshift query insertion is defined already


def get_related_plaintiff_ids(
    canonical_object: str, values: pd.DataFrame
) -> list[str]:
    plaintiff_ids = set()  # use set to prevent duplicate plaintiff ids
    for val in values.itertuples():
        plaintiff_ids.add(str(val.PlaintiffId))
        # related plaintiff would have the same source as case
    return list(
        plaintiff_ids
    )  # convert to list because list-compatible redshift query insertion is defined already


def get_related_case_ids(
    canonical_object: str, values: pd.DataFrame
) -> list[str]:
    case_ids = set()  # use set to prevent duplicate plaintiff ids
    for val in values.itertuples():
        case_ids.add(val.CaseId)
        # related case would have the same source as billing
    return list(
        case_ids
    )  # convert to list because list-compatible redshift query insertion is defined already


def get_related_billing_ids(
    canonical_object: str, values: pd.DataFrame
) -> list[str]:
    billing_ids = set()  # use set to prevent duplicate plaintiff ids
    for val in values.itertuples():
        billing_ids.add(val.BillingId)
        # related billing would have the same source as charge
    return list(
        billing_ids
    )  # convert to list because list-compatible redshift query insertion is defined already


def get_canonical_object_column_list(canonical_object: str) -> list[str]:
    global canonical, canonical_objects
    parsed_canonical_object = canonical_object.lower()

    matching_object = next(
        (
            obj
            for obj in canonical_objects
            if obj.lower() == parsed_canonical_object
        ),
        None,
    )

    if matching_object is None:
        raise ValueError(
            f'Object {parsed_canonical_object} not found in canonical model.'
        )
    return list(canonical[matching_object].keys())


def get_utc_timestamp_string(timestamp: datetime.datetime) -> str:
    # Convert the datetime object to UTC (if it's not already in UTC)
    timestamp = (
        timestamp.replace(tzinfo=datetime.timezone.utc)
        if timestamp.tzinfo is None
        else timestamp.astimezone(datetime.timezone.utc)
    )
    # Format the UTC datetime object to the required format
    # 'Z' at the end to indicate UTC timezone, and remove the microseconds
    timestamp_string = timestamp.isoformat().replace('+00:00', 'Z')[:-4] + 'Z'
    return timestamp_string


def get_md5_hash(string: str) -> str:
    # NOTE: DO NOT CHANGE THIS FUNCTION!!!
    # It is used to generate unique source keys in many cases
    # Changing this function will result in data duplication
    return hashlib.md5(string.encode('utf-8')).hexdigest()


def extract_address_number(address: str):
    # Regular expression to find the number at the beginning of the address
    match = re.match(r'(\d+)', address)
    if match:
        return match.group(1)
    return address


def extract_zip_code(zipcode: str):
    # Regular expression to find the first 5 digits of the ZIP code
    match = re.search(r'(\d{5})', zipcode)
    if match:
        return match.group(1)
    return zipcode


def clean_address_zip_code(
    zip_code: typing.Optional[str],
) -> typing.Optional[str]:
    if not zip_code:  # Safety check
        return zip_code
    zip_code = zip_code.strip()
    if len(zip_code) > 5:  # If zipcode is in 12345-6789 format
        zip_code = zip_code[:5]  # Only keep the first 5 digits
    return zip_code


def clean_date(date: typing.Optional[str]) -> typing.Optional[datetime.date]:
    if not date:  # Safety check
        return None
    return datetime.datetime.date(
        datetime.datetime.strptime(date, '%Y-%m-%dT%H:%M:%SZ')
    )


def convert_boolean_val(string: str | bool | None) -> bool | None:
    if not string:  # Safety check
        return None
    if isinstance(string, bool):  # Safety check #2
        return string
    string_lower = string.lower()
    if string_lower in ['true', '1', 't', 'y', 'yes']:
        return True
    if string_lower in ['false', '0', 'f', 'n', 'no']:
        return False
    return None


def remove_nonalphanumeric_characters(
    string: str,
) -> str:
    if not string:
        return string
    return re.sub(r'\(|-|\)|\+|\.|\,| ', '', string)


def remove_nonnumeric_characters(
    string: typing.Optional[str],
) -> typing.Optional[str]:
    if not string:  # Safety check
        return string
    return re.sub(r'[^0-9]', '', string)


def get_state_code_from_state_name(
    state_name: typing.Optional[str],
) -> typing.Optional[str]:
    if not state_name:  # Safety check
        return state_name
    state_name = state_name.strip().title()
    state_code = state_name_to_code_map.get(state_name, None)
    if state_code is None:
        raise ValueError(f'State name {state_name} not found in the map.')
    return state_code


def get_state_name_from_state_code(
    state_code: typing.Optional[str],
) -> typing.Optional[str]:
    if not state_code:  # Safety check
        return state_code
    state_code = state_code.strip().upper()
    state_name = state_code_to_name_map.get(state_code, None)
    if state_name is None:
        raise ValueError(f'State code {state_code} not found in the map.')
    return state_name


def batch_list(
    input_list: typing.Optional[list[str]], batch_size: int = 500
) -> typing.Optional[list[list[str]]]:
    if not input_list:  # Safety check
        return
    return [
        input_list[i : i + batch_size]
        for i in range(0, len(input_list), batch_size)
    ]


def download_pdf(url: str, file_name: str) -> None:
    response = requests.get(url)
    if response.status_code == 200:
        with open(file_name, "wb") as f:
            f.write(response.content)
    else:
        print("Error:", response.status_code)
        print(response.text)


def is_valid_json(s: str | None):
    if not isinstance(s, str):
        return False
    s = s.strip()
    if (s.startswith('{') and s.endswith('}')) or (
        s.startswith('[') and s.endswith(']')
    ):
        try:
            json.loads(s)
            return True
        except ValueError:
            return False
    return False


def clean_cpt_code(cpt: str):
    return (
        '' if not cpt else cpt.strip()[:5] if (len(cpt) > 5) else cpt
    )  # keep first 5 characters only


# endregion


# region HTTP management


# Boilerplate code for setting default timeout across entire HTTP session
class TimeoutHTTPAdapter(requests.adapters.HTTPAdapter):
    def __init__(self, *args: typing.Any, **kwargs: dict[str, typing.Any]):
        # self.timeout = timeout
        if "timeout" in kwargs:
            self.timeout = kwargs["timeout"]
            del kwargs["timeout"]
        super().__init__(*args, **kwargs)

    def send(
        self,
        request: requests.PreparedRequest,
        *args: tuple[typing.Any],
        **kwargs: typing.Any,
    ):
        timeout = kwargs.get("timeout")
        if timeout is None:
            kwargs["timeout"] = self.timeout
        return super().send(request, **kwargs)


def setup_session(
    session: requests.Session | None = None,
    default_timeout: int = 2,
    retries: int = 3,
    backoff_factor: int = 2,
    auth_token: str | None = None,
    auth_type: str = 'Bearer',
) -> requests.Session:
    '''
    Sets up a session with default timeout and retry settings
    The auth_token parameter is used to set the Authorization header for the session
    The auth_type parameter is used to set the type of authorization, e.g. Bearer, Basic, etc.
    Also sets up a hook to raise an HTTPError if the HTTP request returns an unsuccessful status code
    Finally, mounts the session to the HTTP and HTTPS adapters
    '''
    if not session:  # if no session passed in, set up session
        session = requests.Session()
    assert_status_hook = (
        lambda response, *args, **kwargs: response.raise_for_status()
    )  # will raise an HTTPError if the HTTP request returns an unsuccessful status code
    session.hooks['response'] = [assert_status_hook]
    retry_strategy = requests.adapters.Retry(
        total=retries,
        backoff_factor=backoff_factor,  # exponential backoff factor
        status_forcelist=[429, 500, 502, 503, 504],
        method_whitelist=["HEAD", "GET", "OPTIONS"],
    )  # retry configuration settings
    kwargs = {
        'timeout': default_timeout,
        'max_retries': retry_strategy,
    }
    session.mount(
        'https://',
        TimeoutHTTPAdapter(**kwargs),
    )  # Default timeout set, must manually override in further instances
    # session.mount(
    #     'http://',
    #     TimeoutHTTPAdapter(
    #         timeout=default_timeout, max_retries=retry_strategy
    #     ),
    # )  # Can add http mount if necessary, currently only using https
    if auth_token:  # If auth_token passed in, set Authorization header
        if auth_type == 'Bearer':
            session.headers.update({'Authorization': f'Bearer {auth_token}'})
        # Other auth types can be added here
    return session


def rate_limited(
    max_calls_per_minute: int = 60,
) -> typing.Callable[[typing.Any], typing.Any]:
    interval = 60 / max_calls_per_minute
    last_called = [0.0]

    def decorator(
        func: typing.Callable[[typing.Any], typing.Any],
    ) -> typing.Callable[[typing.Any], typing.Any]:
        @functools.wraps(func)
        def wrapped(
            *args: tuple[typing.Any], **kwargs: dict[str, typing.Any]
        ) -> typing.Any:
            elapsed = time.time() - last_called[0]
            wait_time = interval - elapsed
            if wait_time > 0:
                # print(
                #     f'Rate limit reached. Waiting for {round(wait_time, 2)} seconds.'
                # )
                time.sleep(wait_time)
            result = func(*args, **kwargs)
            last_called[0] = time.time()
            return result

        return wrapped

    return decorator


def check_call_cache(
    cache: cachetools.Cache[str, requests.Response],
    url: str,
    params: dict[str, int],
) -> requests.Response | None:
    cache_key = f'{url}-{params}'
    cached_response = cache.get(cache_key, None)
    return cached_response


def cache_response(
    cache: cachetools.Cache[str, requests.Response],
    url: str,
    params: dict[str, int],
    response: requests.Response,
) -> None:
    cache_key = f'{url}-{params}'
    cache[cache_key] = response


def run_query(query: str):
    parsed = sqlparse.parse(query)

    if not parsed:
        raise ValueError("Empty query is not allowed.")

    for statement in parsed:
        if not isinstance(statement, sqlparse.sql.Statement):
            raise ValueError("Invalid SQL statement.")

        for token in statement.tokens:
            if token.ttype is sqlparse.tokens.DML:
                if token.value.upper() != "SELECT":
                    raise ValueError("Only SELECT statements are allowed.")
                break
        else:
            raise ValueError("No valid DML statement found.")

    return aws_operations.run_query(
        typing.cast(typing_extensions.LiteralString, query)
    )


# endregion
