

INSERT INTO lawfirms (
    name,
    phone,
    fax,
    email,
    followupemail,
    billingaddressline1,
    billingaddressline2,
    billingaddresscity,
    billingaddressstate,
    billingaddresszip,
    physicaladdressline1,
    physicaladdressline2,
    physicaladdresscity,
    physicaladdressstate,
    physicaladdresszip,
    website,
    typeoflaw,
    description,
    employeecountrange,
    donotfund,
    donotfundtype,
    automaticcaseupdaterequest,
    nonresponsive,
    nonresponsivenote,
    portalaccount,
    portalrewardsparticipant,
    parentid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    createdatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    notes,
    gainid
) VALUES (
    'Krasno Krasno & Onwudinjo',
    '**********',
    '**********',
    '',
    '',
    '239 4Th Ave 15Th Fl',
    '',
    'Pittsburgh',
    'Pennsylvania',
    '15222',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    NULL,
    '',
    NULL,
    NULL,
    '',
    NULL,
    NULL,
    '',
    true,
    NULL,
    NULL,
    '2024-09-09 12:48:18.75431',
    '2024-09-13 10:32:34',
    false,
    '',
    false,
    '',
    '',
    '8cf52fc7e0504b1f'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    salesforce_external_container_id,
    salesforce_external_container_createddatetime,
    salesforce_external_container_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    jopari_id,
    jopari_createddatetime,
    jopari_modifieddatetime,
    filevine_id,
    filevine_createddatetime,
    filevine_modifieddatetime,
    canonical_object
) VALUES (
    '8cf52fc7e0504b1f',
    '2024-09-09 08:45:54.928985',
    '2024-09-13 10:32:46.862344',
    NULL,
    NULL,
    NULL,
    NULL,  -- salesforce_external_container_id is NULL
    NULL,  -- salesforce_external_container_createddatetime is NULL
    NULL,  -- salesforce_external_container_modifieddatetime is NULL
    '49de9d118fc255a6663e',
    NULL,  -- ati_createddatetime is NULL
    NULL,  -- ati_modifieddatetime is NULL
    NULL,  -- jopari_id is NULL
    NULL,  -- jopari_createddatetime is NULL
    NULL,  -- jopari_modifieddatetime is NULL
    NULL,  -- filevine_id is NULL
    NULL,  -- filevine_createddatetime is NULL
    NULL,  -- filevine_modifieddatetime is NULL
    'lawfirms'
);

INSERT INTO lawfirms (
    name,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    'Lawfirms manual review',
    false,
    true,
    '8cf52fc7e0504b1g'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '8cf52fc7e0504b1g',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '8cf52fc7e0504b1g',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_lawfirms_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'lawfirms'
);

INSERT INTO LawFirms (
    GainID,
    Name,
    Phone,
    Fax,
    BillingAddressLine1,
    BillingAddressCity,
    BillingAddressState,
    BillingAddressZip,
    PhysicalAddressLine1,
    PhysicalAddressCity,
    PhysicalAddressState,
    PhysicalAddressZip,
    Website,
    TypeOfLaw,
    Description,
    PortalAccount,
    RelevantToGain,
    ModifiedDateTime
)
VALUES (
    '366e1db3dc1da840',
    'Mike Hostilo Law Firm',
    '(*************',
    '(*************',
    '33 Park of Commerce Blvd',
    'Savannah',
    'Georgia',
    '31405',
    '33 Park of Commerce Blvd',
    'Savannah',
    'Georgia',
    '31405',
    'https://www.mikehostilolawfirm.com',
    'Personal Injury',
    'Personal Injury and car accident lawyers licensed in Georgia, South Carolina, Alabama, and Florida.',
    TRUE,
    TRUE,
    CURRENT_TIMESTAMP
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
)
VALUES (
    '366e1db3dc1da840',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    '001G000001XqF10IAF',  -- Change this to the correct Salesforce ID
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    'lawfirms' -- NOTE: This should be lowercase
);


