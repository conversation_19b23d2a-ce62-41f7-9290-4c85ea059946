import os
import typing
from pathlib import Path
from unittest.mock import patch

import psycopg
import pytest
from pytest_postgresql import factories
from pytest_postgresql.janitor import DatabaseJanitor

current_file_dir = Path(__file__).resolve().parent


# Define base schema and seed files to load
schema_files = [
    Path(current_file_dir, 'schemas', 'gain_id_map.sql'),
    Path(current_file_dir, 'schemas', 'integration_timestamps.sql'),
    Path(current_file_dir, 'schemas', 'medical_facilities.sql'),
    Path(current_file_dir, 'schemas', 'law_firms.sql'),
    Path(current_file_dir, 'schemas', 'legal_personnel.sql'),
    Path(current_file_dir, 'schemas', 'plaintiffs.sql'),
    Path(current_file_dir, 'schemas', 'cases.sql'),
    Path(current_file_dir, 'schemas', 'billings.sql'),
    Path(current_file_dir, 'schemas', 'charges.sql'),
    Path(current_file_dir, 'schemas', 'files.sql'),
    Path(current_file_dir, 'schemas', 'transactions.sql'),
    Path(current_file_dir, 'schemas', 'disbursals.sql'),
    Path(current_file_dir, 'schemas', 'insurances.sql'),
    Path(current_file_dir, 'schemas', 'intakes.sql'),
    Path(current_file_dir, 'schemas', 'liens.sql'),
    Path(current_file_dir, 'schemas', 'surgery.sql'),
    Path(current_file_dir, 'schemas', 'notes.sql'),
    Path(current_file_dir, 'schemas', 'disable_foreign_keys.sql'),
    Path(current_file_dir, 'seeds', 'integrations.sql'),
    Path(current_file_dir, 'seeds', 'plaintiffs_seeds.sql'),
    Path(current_file_dir, 'seeds', 'medical_facilities.sql'),
    Path(current_file_dir, 'seeds', 'law_firms.sql'),
    Path(current_file_dir, 'seeds', 'legal_personnel.sql'),
    Path(current_file_dir, 'seeds', 'cases.sql'),
    Path(current_file_dir, 'seeds', 'billings.sql'),
    Path(current_file_dir, 'seeds', 'insurances.sql'),
    Path(current_file_dir, 'seeds', 'charges.sql'),
    Path(current_file_dir, 'seeds', 'files.sql'),
    Path(current_file_dir, 'seeds', 'notes_seeds.sql'),
    Path(current_file_dir, 'seeds', 'transactions.sql'),
]

# Get all SQL files from a directory
patches_dir = Path(
    current_file_dir,
    '..',
    'integration',
    'integration_code',
    'postgres_patches',
)
patch_files = list(patches_dir.glob('*.sql'))

# Combine all files to load
all_files_to_load = schema_files + patch_files

postgres_host = 'localhost'
if os.getenv('CI_JOB_ID'):
    postgres_host = os.getenv('POSTGRES_HOST')

postgresql_in_docker = factories.postgresql_noproc(
    host=postgres_host,
    dbname="integration",
    password='postgres',
    load=all_files_to_load,  # pyright: ignore[reportArgumentType]
)


@pytest.fixture
def database(postgresql_in_docker: typing.Any):
    with DatabaseJanitor(
        user=postgresql_in_docker.user,
        host=postgresql_in_docker.host,
        port=postgresql_in_docker.port,
        dbname=postgresql_in_docker.dbname,
        version=postgresql_in_docker.version,
        password=postgresql_in_docker.password,
        template_dbname=postgresql_in_docker.template_dbname,
    ):
        os.environ['POSTGRES_HOST'] = postgresql_in_docker.host
        os.environ['POSTGRES_PORT'] = str(postgresql_in_docker.port)
        os.environ['POSTGRES_DBNAME'] = postgresql_in_docker.dbname
        os.environ['POSTGRES_USER'] = postgresql_in_docker.user
        os.environ['POSTGRES_PASSWORD'] = postgresql_in_docker.password
        with psycopg.connect(
            dbname=postgresql_in_docker.dbname,
            user=postgresql_in_docker.user,
            password=postgresql_in_docker.password,
            host=postgresql_in_docker.host,
            port=postgresql_in_docker.port,
        ) as database:
            yield database


@pytest.fixture()
def verify_errors():
    with patch('integration.integration_code.logger_config.audit_log') as mock:

        def log_audit_log(*args: typing.Any, **kwargs: typing.Any):
            print(*args, str(kwargs))

        mock.side_effect = log_audit_log

        yield mock


@pytest.fixture
def mock_s3_to_postgres():
    import integration.integration_code.aws_operations as aws_operations

    with patch(
        "integration.integration_code.local_operations.aws_operations.s3_to_postgres",
        side_effect=aws_operations.s3_to_postgres,
    ) as mock_function:
        yield mock_function
