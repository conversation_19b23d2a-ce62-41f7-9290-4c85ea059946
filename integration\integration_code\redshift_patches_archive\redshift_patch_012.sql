/*

Modify existing tables in the integration database for the canonical model

*/

/*
-- Remove columns from Billings
-- Implement AFTER medical facilities are added
-- Commented out for now

ALTER TABLE IF EXISTS integration.dev.Billings
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine1,
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine2,
    DROP COLUMN IF EXISTS MedicalFacilityAddressCity,
    DROP COLUMN IF EXISTS MedicalFacilityAddressState,
    DROP COLUMN IF EXISTS MedicalFacilityAddressZip
;

ALTER TABLE IF EXISTS integration.staging.Billings
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine1,
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine2,
    DROP COLUMN IF EXISTS MedicalFacilityAddressCity,
    DROP COLUMN IF EXISTS MedicalFacilityAddressState,
    DROP COLUMN IF EXISTS MedicalFacilityAddressZip
;

ALTER TABLE IF EXISTS integration.prod.Billings
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine1,
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine2,
    DROP COLUMN IF EXISTS MedicalFacilityAddressCity,
    DROP COLUMN IF EXISTS MedicalFacilityAddressState,
    DROP COLUMN IF EXISTS MedicalFacilityAddressZip
;

ALTER TABLE IF EXISTS integration.main.Billings
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine1,
    DROP COLUMN IF EXISTS MedicalFacilityAddressLine2,
    DROP COLUMN IF EXISTS MedicalFacilityAddressCity,
    DROP COLUMN IF EXISTS MedicalFacilityAddressState,
    DROP COLUMN IF EXISTS MedicalFacilityAddressZip
;
