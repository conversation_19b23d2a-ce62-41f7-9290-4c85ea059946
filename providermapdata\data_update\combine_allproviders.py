# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning,reportCallIssue=warning,reportArgumentType=warning
import json
import pathlib

import boto3
import pandas as pd
from django.http import JsonResponse

from . import fixed_variables as fixvar
from . import mdsave_providers as mdsave
from . import salesforce_providers as sfproviders


def combine_all_providers(request):
    with open('./providermapdata/data_update/credentials.json', 'rb') as f:
        credentials = json.load(f)

    # Access to s3 buckets
    access_key = credentials['aws-s3']['access_key']
    secret_access_key = credentials['aws-s3']['secret_access_key']
    s3_client = boto3.client(
        's3',
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_access_key,
    )

    df_DefinitiveProviders = sfproviders.return_salesforce_providers()[1]
    df_MDSaveProviders = mdsave.return_mdsave_providers()
    df_SalesforceProviders = sfproviders.return_salesforce_providers()[0]
    df_Providers = pd.concat(
        [df_DefinitiveProviders, df_MDSaveProviders, df_SalesforceProviders],
        ignore_index=True,
        sort=False,
    )
    df_Providers = df_Providers.fillna('')
    df_Providers = df_Providers.drop(
        df_Providers[df_Providers['Latitude'] == ''].index
    )
    df_Providers = df_Providers.drop(
        df_Providers[df_Providers['ProviderSpecialty'] == ''].index
    )

    df_Providers.loc[:, 'specialty'] = df_Providers[
        'ProviderSpecialty'
    ].str.lower()
    for i in range(len(fixvar.final_speciality_yes)):
        fixvar.final_speciality_yes[i] = fixvar.final_speciality_yes[i].lower()

    df_Providers_filtered = df_Providers[
        df_Providers['specialty'].isin(fixvar.final_speciality_yes)
    ]

    df_Providers_filtered.insert(
        len(df_Providers_filtered.columns),
        'closed',
        df_Providers_filtered['ProviderName'].str.lower(),
    )
    df_Providers_filtered = df_Providers_filtered.drop(
        index=df_Providers_filtered[
            df_Providers_filtered['closed'].str.contains("closed")
        ].index.tolist()
    )

    df_Providers_filtered = df_Providers_filtered.drop('specialty', axis=1)
    df_Providers_filtered = df_Providers_filtered.drop('closed', axis=1)
    df_Providers_filtered.insert(
        3, 'GroupedSpecialty', df_Providers_filtered['ProviderSpecialty']
    )

    specialty_renaming = {"GroupedSpecialty": fixvar.grouped_specialty_rename}
    df_Providers_filtered = df_Providers_filtered.replace(specialty_renaming)
    df_Providers_filtered.insert(
        0, 'Id', range(1, len(df_Providers_filtered) + 1)
    )
    df_Providers_filtered['ZipCode'] = df_Providers_filtered['ZipCode'].astype(
        str
    )

    response = s3_client.get_object(
        Bucket="gain-servicing", Key="providers-map/providersparencleanGA.csv"
    )
    status = response.get("ResponseMetadata", {}).get("HTTPStatusCode")

    if status == 200:
        print(f"Successful S3 get_object response. Status - {status}")
        df_ParenCleanProviders = pd.read_csv(
            response.get("Body"), sep=',', index_col=False
        )
    else:
        print(f"Unsuccessful S3 get_object response. Status - {status}")
    df_Providers_filtered['ProviderNameOriginal'] = df_Providers_filtered[
        'ProviderName'
    ]
    df_Providers_filtered['ProviderNameOriginal'] = df_Providers_filtered[
        'ProviderName'
    ]

    name_replace = pd.Series(
        df_ParenCleanProviders.newName.values,
        index=df_ParenCleanProviders.name,
    ).to_dict()
    name_renaming = {"ProviderName": name_replace}
    df_Providers_filtered = df_Providers_filtered.replace(name_renaming)

    df_Providers_filtered['ProviderName'] = df_Providers_filtered[
        'ProviderName'
    ].str.replace(r'\bspeciality\b', 'specialty', regex=True)
    df_Providers_filtered['ProviderName'] = df_Providers_filtered[
        'ProviderName'
    ].str.replace(r'\bSpeciality\b', 'Specialty', regex=True)
    df_Providers_filtered['ProviderName'] = df_Providers_filtered[
        'ProviderName'
    ].str.replace(r'\bMultispeciality\b', 'Multispecialty ', regex=True)
    df_Providers_filtered['ProviderName'][
        df_Providers_filtered['ProviderName']
        .str.lower()
        .str.contains('speciality')
    ]

    df_Providers_filtered.loc[
        df_Providers_filtered['Website'][
            df_Providers_filtered['Website'].str.contains('http')
        ].index.values.tolist(),
        'Website',
    ] = (
        df_Providers_filtered['Website'][
            df_Providers_filtered['Website'].str.contains('http')
        ]
        .str.partition('//')[2]
        .tolist()
    )

    print(
        "The Definitive data's shape is {} rows, {} cols".format(
            df_DefinitiveProviders.shape[0], df_DefinitiveProviders.shape[1]
        )
    )
    print(
        "The MDSave data's shape is {} rows, {} cols".format(
            df_MDSaveProviders.shape[0], df_MDSaveProviders.shape[1]
        )
    )
    print(
        "The Salesforce data's shape is {} rows, {} cols".format(
            df_SalesforceProviders.shape[0], df_SalesforceProviders.shape[1]
        )
    )
    print(
        "All the providers data's shape is {} rows, {} cols".format(
            df_Providers_filtered.shape[0], df_Providers_filtered.shape[1]
        )
    )
    # df_Providers_filtered.to_csv('s3://gain-servicing/providers-map/providersmap.csv',encoding='utf-8',index=False, header=True)
    df_Providers_filtered.to_csv(
        './providermapdata/data_update/providersmap.csv',
        encoding='utf-8',
        index=False,
        header=True,
    )
    s3_client.upload_file(
        Filename='./providermapdata/data_update/providersmap.csv',
        Bucket='gain-servicing',
        Key='providers-map/providersmap.csv',
    )
    response.get("ResponseMetadata", {}).get("HTTPStatusCode")
    pathlib.Path('./providermapdata/data_update/providersmap.csv').unlink(
        missing_ok=True
    )
    return JsonResponse(
        {
            'DataShape': (
                df_Providers_filtered.shape[0],
                df_Providers_filtered.shape[1],
            ),
            'password': 'test',
        }
    )
