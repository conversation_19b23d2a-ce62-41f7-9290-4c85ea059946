CREATE TABLE disbursals (
    gainid VARCHAR(16) NOT NULL,
    status VARCHAR(100),
    amountdue NUMERIC(10, 4),
    checknumber VARCHAR(20),
    checkdate TIMESTAMP WITHOUT TIME ZONE,
    amountpaid NUMERIC(10, 4),
    type VARCHAR(100),
    caseid VARCHAR(20) NOT NULL,
    relevanttogain BOOLEAN,
    todelete BOOLEAN DEFAULT false,
    todeletesystem VARCHAR(100),
    deletepreventoverride BOOLEAN DEFAULT false,
    deletepreventoverridereason VARCHAR(1000),
    PRIMARY KEY (gainid)
);