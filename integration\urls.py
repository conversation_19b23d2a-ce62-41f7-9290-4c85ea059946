'''
GainInternalWebApp URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
'''

import django.urls

from . import views

app_name = 'integration'
urlpatterns = [
    django.urls.path(
        'filevine/upsert/', views.filevine_upsert, name='filevine_upsert'
    ),
    django.urls.path(
        'athenahealth/upsert/',
        views.athenahealth_upsert,
        name='athenahealth_upsert',
    ),
    django.urls.path(
        'jopari/upsert/',
        views.jopari_upsert,
        name='jopari_upsert',
    ),
    django.urls.path(
        'get_route_last_run_time',
        views.get_route_last_run_time,
        name='get_route_last_run_time',
    ),
    django.urls.path(
        'salesforce/upsert/', views.salesforce_upsert, name='salesforce_upsert'
    ),
    django.urls.path(
        'salesforce/duplicates/',
        views.duplicates_from_salesforce,
        name='duplicates_from_salesforce',
    ),
    django.urls.path(
        'salesforce/delete/',
        views.remove_salesforce_records,
        name='remove_salesforce_records',
    ),
    django.urls.path(
        'salesforce/withdraw/',
        views.salesforce_delete,
        name='salesforce_delete',
    ),
    django.urls.path(
        'salesforce/update_settled/',
        views.salesforce_update_settled,
        name='salesforce_update_settled',
    ),
    django.urls.path(
        'salesforce/postupsertupdate/',
        views.salesforce_post_upsert_update,
        name='post_upsert_update',
    ),
    django.urls.path('ati/upsert/', views.ati_upsert, name='ati_upsert'),
    django.urls.path('ati/withdraw/', views.ati_delete, name='ati_delete'),
    django.urls.path(
        'ati/update_settled/',
        views.ati_update_settled,
        name='ati_update_settled',
    ),
    django.urls.path(
        'review/counts/',
        views.reviewed_counts,
        name='review_counts',
    ),
    django.urls.path(
        'review/salesforce/fields/',
        views.get_object_fields,
        name='get_object_fields',
    ),
    django.urls.path(
        'review/opportunity/update/',
        views.reviewed_data_update,
        name='review_opportunity_update',
    ),
    django.urls.path(
        'review/opportunity/remove_salesforce_notfound/',
        views.remove_salesforce_notfound,
        name='remove_salesforce_notfound',
    ),
    django.urls.path(
        'review/funding/update/',
        views.reviewed_data_update,
        name='review_funding_update',
    ),
    django.urls.path(
        'charges/update_charges_from_transactions/',
        views.update_charges_from_transactions,
        name='update_charges_from_transactions',
    ),
    django.urls.path(
        'billings/update_billings_from_charges/',
        views.update_billings_from_charges,
        name='update_billings_from_charges',
    ),
    django.urls.path(
        'cases/update_cases_from_billings/',
        views.update_cases_from_billings,
        name='update_cases_from_billings',
    ),
    django.urls.path(
        'cases/update_cases_from_insurances/',
        views.update_cases_from_insurances,
        name='update_cases_from_insurances',
    ),
    django.urls.path(
        'run_select_query',
        views.run_select_query,
        name='run_select_query',
    ),
]
