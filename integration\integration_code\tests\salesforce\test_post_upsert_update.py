import typing
from unittest.mock import MagicMock

import psycopg
import pytest


class TestPostUpsertUpdate:
    @pytest.mark.django_db
    def test_should_post_upsert_update(
        self,
        database: psycopg.Connection[typing.Any],
        mock_get_sf_action: MagicMock,
        mock_get_sf_bulk: MagicMock,
    ):
        from integration.integration_code import salesforce

        mock_get_sf_bulk.return_value.query.return_value = [
            [
                {
                    'Id': 'sf_id_cases_manual_review',
                    'Treatment_Complete__c': False,
                    'Insurance_Vendor__c': 'Different name',
                    'Tail_Claim__c': '10/10/2010',
                    'OpportunityId__c': '1',
                    'Account_Name__r': {
                        'Root_Parent_Account__c': '',
                        'attributes': {'url': 'url', 'type': 'cases'},
                    },
                    'attributes': {'url': 'url', 'type': 'cases'},
                },
                {
                    'Id': 'failed',
                    'Treatment_Complete__c': False,
                    'Insurance_Vendor__c': 'Different name',
                    'Tail_Claim__c': '10/10/2010',
                    'OpportunityId__c': '1',
                    'Account_Name__r': {
                        'Root_Parent_Account__c': '',
                        'attributes': {'url': 'url', 'type': 'cases'},
                    },
                    'attributes': {'url': 'url', 'type': 'cases'},
                },
            ]
        ]

        update_response = [
            {
                'success': True,
                'created': True,
                'id': '001Ec00000emK49IAE',
                'errors': [],
            },
            {
                'success': False,
                'created': True,
                'id': '001Ec00000emK49IAE',
                'errors': [],
            },
        ]

        mock_update = MagicMock()
        mock_update.return_value = update_response

        mock_get_sf_action.side_effect = [
            mock_update,
        ]

        salesforce.post_upsert_update(test=True, all_data=True)

        data, *_ = mock_update.call_args[0]

        assert data[0] == {
            'Id': 'sf_id_cases_manual_review',
            'Insurance_Vendor__c': 'Different name',
            'Tail_Claim__c': '10/10/2010',
            'Treatment_Complete__c': False,
        }
