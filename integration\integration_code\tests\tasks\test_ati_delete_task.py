from unittest.mock import MagicMock


class TestAtiDeleteTask:
    def test_ati_delete_task_with_default_args(
        self,
        mock_get_main_delete: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI delete task with default arguments."""
        from integration.integration_code.tasks import ati_delete_task

        args_dict = {}
        result = ati_delete_task(args_dict)
        mock_get_main_delete.assert_called_once_with(True, False, False)
        assert result == 'ATI Delete Complete'

    def test_ati_delete_task_with_custom_args(
        self,
        mock_get_main_delete: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI delete task with custom arguments."""
        from integration.integration_code.tasks import ati_delete_task

        args_dict = {
            'move_flag': False,
            'test': True,
            'all_data': True,
        }
        result = ati_delete_task(args_dict)
        mock_get_main_delete.assert_called_once_with(False, True, True)
        assert result == 'ATI Delete Complete'

    def test_ati_delete_task_with_partial_args(
        self,
        mock_get_main_delete: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI delete task with partial arguments."""
        from integration.integration_code.tasks import ati_delete_task

        args_dict = {'test': True, 'all_data': True}
        result = ati_delete_task(args_dict)
        mock_get_main_delete.assert_called_once_with(True, True, True)
        assert result == 'ATI Delete Complete'

    def test_ati_delete_task_lock_not_acquired(
        self,
        mock_advisory_lock_failure: MagicMock,
    ):
        """
        Test that ati_delete_task returns the correct message when the advisory lock is not acquired.
        """
        from integration.integration_code.tasks import ati_delete_task

        args_dict = {}

        result = ati_delete_task(args_dict)
        assert result == "ATI Delete Failed: Lock not acquired"
