"""GainInternalWebApp URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

import django.contrib.admin
import django.urls

from . import views

urlpatterns = [
    django.urls.path('', views.home_page, name='apphome'),
    django.urls.path('home', views.home_page),
    django.urls.path('healthcheck', views.health_check, name='healthcheck'),
    django.urls.path('admin/', django.contrib.admin.site.urls),
    django.urls.path(
        'providersmap/', django.urls.include('providermapdata.urls')
    ),
    django.urls.path('parsing/', django.urls.include('parsing.urls')),
    django.urls.path('sfdata/', django.urls.include('salesforcedata.urls')),
    django.urls.path('integration/', django.urls.include('integration.urls')),
    django.urls.path(
        'predictive_analytics/',
        django.urls.include('predictive_analytics.urls'),
    ),
    django.urls.path('chatgpt/', django.urls.include('chatgpt.urls')),
    django.urls.path('test_ip/', views.test_ip, name='test_ip'),
    django.urls.path('test_logging/', views.test_logging, name='test_logging'),
    django.urls.path(
        'check_app_config/', views.check_app_config, name='check_app_config'
    ),
]
