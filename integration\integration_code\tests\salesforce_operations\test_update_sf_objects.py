import datetime
import typing
from unittest.mock import MagicMock

import pandas as pd
import psycopg
import pytest

import integration.integration_code.salesforce_operations as salesforce_operations


class TestUpdateSfObjects:

    @pytest.mark.django_db
    def test_should_update_generic_object_in_bulk(
        self,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_method: MagicMock,
    ):
        """Test that generic Salesforce objects can be updated in bulk."""
        mock_rollback_data = MagicMock()
        mock_rollback_data.return_value = pd.DataFrame()

        new_object: dict[typing.Hashable, typing.Any] = {
            "Date_of_Birth__c": "1977-10-19",
            "Id": "006Ec00000D8RUzIAN",
        }

        salesforce_operations.update_sf_objects(
            'gain-servicing',
            'integration/ati/2024-10-23',
            [new_object],
            ['12336'],
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
            'Opportunity',
            'cases',
            'opportunities',
            mock_rollback_data,
            True,
            ['12336'],
        )

        data, *_ = mock_sf_action_method.call_args[0]

        assert data[0] == new_object

    @pytest.mark.django_db
    def test_update_sf_objects_raises_keyerror_when_missing_required_fields(
        self,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_method: MagicMock,
    ):
        """Test Salesforce update fails when an object is missing required fields."""
        mock_rollback_data = MagicMock()
        mock_rollback_data.return_value = pd.DataFrame()

        new_object: dict[typing.Hashable, typing.Any] = {
            "Date_of_Birth__c": "1977-10-19",
            # Missing "Id" field
        }

        with pytest.raises(KeyError):
            salesforce_operations.update_sf_objects(
                'gain-servicing',
                'integration/ati/2024-10-23',
                [new_object],
                ['12336'],
                datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
                'Opportunity',
                'cases',
                'opportunities',
                mock_rollback_data,
                True,
                ['12336'],
            )

    @pytest.mark.django_db
    def test_should_log_errors_on_s3_in_bulk(
        self,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_with_error_method: MagicMock,
    ):
        """Test that errors are properly logged when updating Salesforce objects."""
        mock_rollback_data = MagicMock()
        mock_rollback_data.return_value = pd.DataFrame()

        new_object: dict[typing.Hashable, typing.Any] = {
            "Date_of_Birth__c": "1977-10-19",
            "Id": "006Ec00000D8RUzIAN",
        }

        failure_object: dict[typing.Hashable, typing.Any] = {
            "Date_of_Birth__c": "1977-10-20",
            "Id": "006Ec00000D8RV0IAN",
        }

        salesforce_operations.update_sf_objects(
            'gain-servicing',
            'integration/ati/2024-10-23',
            [new_object, failure_object],
            ['12336'],
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
            'Opportunity',
            'cases',
            'opportunities',
            mock_rollback_data,
            True,
            ['12336'],
        )

        data, *_ = mock_sf_action_with_error_method.call_args[0]

        assert data[0] == new_object
        assert data[1] == failure_object
