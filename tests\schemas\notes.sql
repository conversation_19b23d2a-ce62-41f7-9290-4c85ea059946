CREATE TABLE notes (
    gainid VARCHAR(16) NOT NULL DEFAULT '',
    note VA<PERSON>HA<PERSON>(10000),
    notecreatorname VA<PERSON>HAR(100),
    plaintiffid VARCHAR(20),
    medicalfacilityid VARCHAR(20),
    lawfirmid VARCHAR(20),
    legalpersonnelid VARCHAR(20),
    caseid VARCHAR(20),
    intakeid VARCHAR(20),
    insuranceid VARCHAR(20),
    lienid VARCHAR(20),
    disbursalid VARCHAR(20),
    billingid VARCHAR(20),
    chargeid VARCHAR(20),
    transactionid VARCHAR(20),
    fileid VARCHAR(20),
    surgeryid VARCHAR(20),
    relevanttogain BOOLEAN,
    sourcecreatedatetime TIMESTAMP WITHOUT TIME ZONE,
    sourcemodifieddatetime TIMESTAMP WITHOUT TIME ZONE,
    modifieddatetime TIMESTAMP WITHOUT TIME ZONE,
    todelete BOOLEAN DEFAULT false,
    todeletesystem VARCHAR(100),
    deletepreventoverride BOOLEAN DEFAULT false,
    deletepreventoverridereason VARCHAR(1000),
    createdatetime TIMESTAMP WITHOUT TIME ZONE,
    PRIMARY KEY (gainid),
    FOREI<PERSON><PERSON> KEY (plaintiffid) REFERENCES plaintiffs(gainid),
    FOREIGN KEY (medicalfacilityid) REFERENCES medicalfacilities(gainid),
    FOREIGN KEY (lawfirmid) REFERENCES lawfirms(gainid),
    FOREIGN KEY (legalpersonnelid) REFERENCES legalpersonnel(gainid),
    FOREIGN KEY (caseid) REFERENCES cases(gainid),
    FOREIGN KEY (intakeid) REFERENCES intakes(gainid),
    FOREIGN KEY (insuranceid) REFERENCES insurances(gainid),
    FOREIGN KEY (lienid) REFERENCES liens(gainid),
    FOREIGN KEY (disbursalid) REFERENCES disbursals(gainid),
    FOREIGN KEY (billingid) REFERENCES billings(gainid),
    FOREIGN KEY (chargeid) REFERENCES charges(gainid),
    FOREIGN KEY (transactionid) REFERENCES transactions(gainid),
    FOREIGN KEY (fileid) REFERENCES files(gainid),
    FOREIGN KEY (surgeryid) REFERENCES surgery(gainid)
);