import csv
import glob
import io
import json
import pathlib
import typing


def csv_bytes_to_json(
    csv_bytes: bytes,
) -> list[dict[str | typing.Any, str | typing.Any]]:
    # Step 1: Decode the bytes into a string (assuming UTF-8 encoding)
    csv_string = csv_bytes.decode('utf-8')

    # Step 2: Use `io.StringIO` to treat the string like a file
    csv_file = io.StringIO(csv_string)

    # Step 3: Parse CSV with Dict<PERSON>eader (creates a list of dicts)
    reader = csv.DictReader(csv_file)
    data = list(reader)

    return data


def clean_up_file(file_name: str) -> None:
    files = glob.glob(f'*{file_name[:-4]}*')
    for file in files:
        pathlib.Path(file).unlink(missing_ok=True)


def write_json(data: typing.Any, name: str) -> str:
    with open(f'{name}.json', 'w') as out:
        json.dump(data, out, separators=(',', ':'), sort_keys=False)
    return f'{name}.json'
