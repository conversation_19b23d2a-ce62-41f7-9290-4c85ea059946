import datetime
import json

import numpy as np
import pandas as pd

with (
    open(
        'integration/integration_code/canonical_model.json', 'rb'
    ) as canonical_map_file,
    open(
        'integration/integration_code/canonical_data_model.json', 'rb'
    ) as canonical_data_map_file,
    open(
        'integration/integration_code/state_name_mapping.json',
        'rb',
    ) as state_name_mapping_file,
):
    canonical = json.load(canonical_map_file)
    canonical_objects = set(canonical.keys())
    canonical_data = json.load(canonical_data_map_file)
    state_name_mapping = json.load(state_name_mapping_file)
    state_code_to_name_map = state_name_mapping["state_code_to_name_map"]
    state_name_to_code_map = state_name_mapping["state_name_to_code_map"]


def athenahealth_generate_cases_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    timestamp: str,
):
    global canonical_data
    df_input.columns, df_output.columns = [
        s.lower() for s in df_input.columns
    ], [s.lower() for s in (df_output.columns)]
    df_input.rename(
        columns={
            'finalaccidentdate': 'accidentdate',
            'finalautoaccidentstate': 'accidentstate',
        },
        inplace=True,
    )
    (
        df_input['plaintiffdateofbirth'],
        df_input['accidentdate'],
        df_input['accidentdescription'],
    ) = (
        pd.to_datetime(df_input['plaintiffdateofbirth']),
        pd.to_datetime(df_input['accidentdate']),
        df_input['accidentdescription'].str.replace('|', ''),
    )
    df_in_cols, df_out_cols = set(df_input.columns), set(df_output.columns)
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output['plaintiffdateofbirth'], df_output['accidentdate'] = (
        df_output['plaintiffdateofbirth'].dt.date,
        df_output['accidentdate'].dt.date,
    )
    return df_output


def athenahealth_generate_plaintiffs_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    timestamp: str,
):
    global canonical_data
    df_input.columns, df_output.columns = [
        s.lower() for s in df_input.columns
    ], [s.lower() for s in (df_output.columns)]
    df_input['dateofbirth'] = pd.to_datetime(df_input['dateofbirth'])
    df_in_cols, df_out_cols = set(df_input.columns), set(df_output.columns)
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output['dateofbirth'] = df_output['dateofbirth'].dt.date
    return df_output


def athenahealth_generate_billings_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    timestamp: str,
):
    global canonical_data
    df_input.columns, df_output.columns = [
        s.lower() for s in df_input.columns
    ], [s.lower() for s in (df_output.columns)]
    df_input.rename(
        columns={'billedamount': 'billedcharges1', 'cptcode': 'cpt1'},
        inplace=True,
    )
    df_input['dateofservice'] = pd.to_datetime(df_input['dateofservice'])
    df_in_cols, df_out_cols = set(df_input.columns), set(df_output.columns)
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    existing, row_indexes_to_drop = {}, []
    for row in df_output.itertuples(
        index=True
    ):  # Include the index in the named tuple
        dummy_index = row.Index  # Extract index from the named tuple
        if (
            row[1],
            row[2],
            row[17],
            row[18],
        ) in existing:  # fetch using ClaimId and DateOfService
            row_temp = df_output.iloc[
                existing[(row[1], row[2], row[17], row[18])]
            ]  # fetch existing row
            if (
                'null' in row_temp[27] and 'null' not in row[27]
            ):  # if CaseId in original row is missing accident date, and new row has the accident date
                row_temp[27] = row[
                    27
                ]  # update CaseId value in original row to use CaseId with the accident date
            if (
                row[1] == row_temp[1]
                and row[2] == row_temp[2]
                and (  # if Source, Plaintiff, Billed amount and CPT code are the same
                    (row[4] == row_temp[4] and row[19] == row_temp[19])
                    or (  # check new row against all columns of existing row
                        row[4] == row_temp[5] and row[19] == row_temp[20]
                    )
                    or (
                        row[4] == row_temp[6] and row[19] == row_temp[21]
                    )  # for Billed Amount and CPT Code
                    or (row[4] == row_temp[7] and row[19] == row_temp[22])
                    or (row[4] == row_temp[8] and row[19] == row_temp[23])
                    or (row[4] == row_temp[9] and row[19] == row_temp[24])
                )
            ):
                pass  # it's the same row => skip
            else:  # else, not the same row, combine billed charges and cpt codes into original row
                f = 0
                for i in range(5, 10):  # find next empty column
                    if np.isnan(row_temp[i]) or row_temp[i] == '':
                        f = 1
                        break
                if (
                    f == 1
                ):  # i.e. if there are columns to insert, if f = 0 means no more columns are available
                    row_temp[i] = row[4]  # set billed charges
                    row_temp[i + 15] = row[
                        19
                    ]  # set cpt; '15' is index difference between billedcharges2 and cptcode2 in canonical model
            df_output.iloc[existing[(row[1], row[2], row[17], row[18])]] = (
                row_temp
            )
            row_indexes_to_drop.append(dummy_index)
        else:
            if dummy_index in range(873, 876):
                pass
            existing[(row[1], row[2], row[17], row[18])] = dummy_index
    for i in row_indexes_to_drop[
        ::-1
    ]:  # go in reverse order, going in order causes later indexes to fail because the number of values is less
        df_output.drop(
            df_output.index[i], inplace=True
        )  # also index/rows to be removed become inaccurate
    df_output.reset_index(drop=True, inplace=True)
    # Compute Total Amount
    df_output['totalamount'] = (
        df_output.billedcharges1.fillna(0)
        + df_output.billedcharges2.fillna(0)
        + df_output.billedcharges3.fillna(0)
        + df_output.billedcharges4.fillna(0)
        + df_output.billedcharges5.fillna(0)
        + df_output.billedcharges6.fillna(0)
    )
    df_output['totalamount'] = df_output['totalamount'].round(decimals=2)
    df_output['dateofservice'] = df_output['dateofservice'].dt.date
    return df_output


def athenahealth_dataview_generate_s3_data(
    data: dict[str, pd.DataFrame | None], timestamp: str
):
    global canonical
    # return Canonical-compliant data, to be written to CSV and moved to S3, then copied to Redshift; returns None if incoming data is empty or null
    data = {
        'cases': (
            athenahealth_generate_cases_data(
                data['cases'],
                pd.DataFrame(columns=canonical['Cases']),
                timestamp,
            )
            if (data['cases'] is not None and not data['cases'].empty)
            else None
        ),
        'plaintiffs': (
            athenahealth_generate_plaintiffs_data(
                data['plaintiffs'],
                pd.DataFrame(columns=canonical['Plaintiffs']),
                timestamp,
            )
            if (
                data['plaintiffs'] is not None and not data['plaintiffs'].empty
            )
            else None
        ),
        'billings': (
            athenahealth_generate_billings_data(
                data['billings'],
                pd.DataFrame(columns=canonical['Billings']),
                timestamp,
            )
            if (data['billings'] is not None and not data['billings'].empty)
            else None
        ),
    }
    return data


def athenahealth_dataview_generate_csv(
    all_s3_data: dict[str, pd.DataFrame | None], timestamp: str
):
    files = {}
    timestamp_string = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    for data in all_s3_data:
        current_data = all_s3_data[data]
        if (
            current_data is None or current_data.empty
        ):  # if data is empty or missing for a canonical object
            continue
        current_data.to_csv(
            f'integration/integration_code/athenahealth_{data}_{timestamp_string}.csv',
            index=False,
        )
        files[data] = (
            f'integration/integration_code/athenahealth_{data}_{timestamp_string}.csv'
        )
    return files


def athenahealth_api_generate_csv(
    files_data: list[dict[str, str]],
    client_canonical_name: str,
    timestamp: str,
):
    global canonical, canonical_data
    timestamp_string = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    file_name, redshift_data = (
        f'integration/integration_code/athenahealth_files_{timestamp_string}.csv',
        [],
    )
    for doc in files_data:
        redshift_record = {
            'GainId': doc['gainId'],
            'PlaintiffId': doc['patientId'],
            'Url': doc['s3URI'],
            'Type': canonical_data['AthenaHealth']['ToCanonical'][
                'DocumentTypes'
            ].get(doc['documentType'], ''),
            'CaseId': None,
            'RelevantToGain': (
                True
                if canonical_data['Salesforce']['FromCanonical'][
                    'DocumentTypes'
                ].get(
                    canonical_data['AthenaHealth']['ToCanonical'][
                        'DocumentTypes'
                    ].get(doc['documentType'], ''),
                    '',
                )
                != ''
                else False
            ),
            # ^ try to get salesforce name for documentType by converting to canonical and canonical to salesforce;
            # if salesforce documentType is found, mark RelevantToGain as True, else False
            'SalesforceId': None,
            'SourceCreateDateTime': (
                datetime.datetime.fromisoformat(
                    doc['sourcecreatedatetime']
                ).strftime('%Y-%m-%d %H:%M:%S')
                if doc['sourcecreatedatetime'] != ''
                else None
            ),
            'SourceModifiedDateTime': (
                datetime.datetime.fromisoformat(
                    doc['sourcelastmodifieddatetime']
                ).strftime('%Y-%m-%d %H:%M:%S')
                if doc['sourcelastmodifieddatetime'] != ''
                else None
            ),
            'CreateDateTime': timestamp,
            'ModifiedDateTime': timestamp,
        }
        redshift_data.append(redshift_record)
    df_input, df_output = pd.DataFrame(redshift_data), pd.DataFrame(
        columns=canonical['Files']
    )
    df_input.columns, df_output.columns = [
        s.lower() for s in df_input.columns
    ], [s.lower() for s in (df_output.columns)]
    df_in_cols, df_out_cols = set(df_input.columns), set(df_output.columns)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col]
            if col in df_in_cols
            else (
                timestamp
                if ('source' not in col and 'datetime' in col)
                else np.nan
            )
        )
    df_output.to_csv(file_name, index=False)
    return file_name
