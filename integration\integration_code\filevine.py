import datetime
import pathlib

import django.conf

from . import aws_operations, filevine_operations, local_operations, models

app_config = django.conf.settings
credentials, settings = app_config.CREDENTIALS, app_config.SETTINGS


def get_main_upsert(
    test: bool,
    filevine_name: str,
    all_data: bool = False,
    skip_files: bool = False,
    cases_update_flag: bool = True,
    params: models.views.FileVineParams | None = None,
) -> None:
    '''
    Get from Filevine, post to Canonical in Redshift
    '''
    timestamp = datetime.datetime.now()
    filevine_environments = settings['Integration']['Filevine']['Environments']
    filevine_environment = filevine_operations.get_environment(
        filevine_name, filevine_environments
    )
    bucket = settings['Common']['AWS']['S3']['bucket']
    directory = '/'.join(
        [
            f"{settings['Integration']['AWS']['S3']['directory']}",
            f"{settings['Integration']['Filevine']['S3']['sub_directory']}",
            f"{filevine_name}",
            f"{timestamp.date().strftime('%Y-%m-%d')}",
        ]
    )
    api_url = settings['Integration']['Filevine']['URLs'][filevine_environment]
    filevine_operations.initialize(api_url)
    filevine_operations.authenticate(
        timestamp=timestamp,
        credentials_key=credentials['integration']['filevine'][
            filevine_environment
        ]['api_key'],
        credentials_secret=credentials['integration']['filevine'][
            filevine_environment
        ]['api_secret'],
    )
    filevine_operations.setup_session()
    projects_data = filevine_operations.get_all(test, skip_files, params)
    files, insurances_gainid_set = filevine_operations.process_all(
        bucket, directory, timestamp, filevine_name, projects_data
    )
    for file in files.values():
        pathlib.Path(file).unlink(missing_ok=True)
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    if (
        cases_update_flag and insurances_gainid_set
    ):  # Update cases from insurances if there are any insurances
        local_operations.update_cases_from_insurances(insurances_gainid_set)
