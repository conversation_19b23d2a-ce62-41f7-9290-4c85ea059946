# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning
import pathlib
from datetime import datetime

from django.http import HttpResponse
from django.shortcuts import render
from rest_framework.decorators import api_view
from rest_framework.response import Response

from salesforcedata.attyack_get_merged_templates.AttyAck_Get_Merged_Templates import (
    AttyAckGetMergedTemplates,
)

from .reset_caseupdate_date.ResetCaseupdateDate import ResetCaseupdateDate


@api_view(['GET', 'POST'])
def salesforce_data_home(request):
    return render(request, 'salesforce/salesforce.html')
    content = "This is the home page for Salesforce data processing!"
    return Response(content)
    return HttpResponse(status=200)


@api_view(['GET', 'POST'])
def salesforce_resetCaseDate(request):
    rcd = ResetCaseupdateDate()
    rcd.reset_caseupdate_date(
        [
            '0061M00001BH96rQAD',
            '0068Y00001HLqfqQAD',
            '0068Y00001HLqj4QAD',
            '0068Y00001F0whSQAR',
            '0061M000012iIvLQAU',
            '0061M00001BHlq9QAD',
        ]
    )
    content = "The case update data has been updated at {} !".format(
        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    return Response(content)
    return HttpResponse(status=200)


@api_view(['GET'])
def salesforce_attyack_get_mergedtemplate(request):
    try:
        getTemplates = AttyAckGetMergedTemplates()
        getTemplates.AttyAckGetMergedTemplates(request.data['data'])

        content = getTemplates.getOutputData()

        return Response(content, status=200)

    except Exception as e:
        return Response(f"Error occurred: + {e}", status=500)
    finally:
        files = [
            "salesforcedata/attyack_get_merged_templates/pg1.pdf",
            "salesforcedata/attyack_get_merged_templates/pg2.pdf",
            "salesforcedata/attyack_get_merged_templates/pg3.pdf",
        ]
        # remove temp pdf files
        for file in files:
            if pathlib.Path(file).exists():
                pathlib.Path(file).unlink(missing_ok=True)
