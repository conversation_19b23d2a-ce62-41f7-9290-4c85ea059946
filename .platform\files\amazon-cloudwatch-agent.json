{"agent": {"metrics_collection_interval": 60, "logfile": "/opt/aws/amazon-cloudwatch-agent/logs/amazon-cloudwatch-agent.log"}, "metrics": {"append_dimensions": {"AutoScalingGroupName": "${aws:AutoScalingGroupName}", "InstanceId": "${aws:InstanceId}"}, "aggregation_dimensions": [["AutoScalingGroupName"], ["InstanceId"]], "metrics_collected": {"mem": {"measurement": [{"name": "mem_used_percent", "rename": "MemoryUsedPercent", "unit": "Percent"}, {"name": "mem_available", "rename": "MemoryAvailable", "unit": "Bytes"}, {"name": "mem_used", "rename": "MemoryUsed", "unit": "Bytes"}], "metrics_collection_interval": 60}, "swap": {"measurement": [{"name": "swap_used_percent", "rename": "SwapUsedPercent", "unit": "Percent"}], "metrics_collection_interval": 60}, "disk": {"measurement": [{"name": "used_percent", "rename": "DiskUsedPercent", "unit": "Percent"}, {"name": "used", "rename": "DiskUsed", "unit": "Bytes"}, {"name": "free", "rename": "DiskFree", "unit": "Bytes"}], "resources": ["*"], "drop_device": true, "metrics_collection_interval": 60, "ignore_file_system_types": ["tmpfs", "devtmpfs", "overlay"]}}, "force_flush_interval": 60, "namespace": "EC2/Custom"}}