from unittest.mock import MagicMock, patch

import pytest


class TestGetPostgresServicingDatetime:
    @pytest.fixture
    def mock_psycopg_connect(self):
        with patch(
            'integration.integration_code.aws_operations.psycopg.connect'
        ) as mock_connect:
            mock_connection = MagicMock()
            mock_cursor = MagicMock()
            mock_connect.return_value.__enter__.return_value = mock_connection
            mock_connection.cursor.return_value.__enter__.return_value = (
                mock_cursor
            )

            yield {
                'mock_connection': mock_connection,
                'mock_cursor': mock_cursor,
                'mock_connect': mock_connect,
            }

    @pytest.mark.django_db
    def test_get_postgres_servicingdatetime(
        self, mock_psycopg_connect: dict[str, MagicMock]
    ):
        """Test that postgres servicing datetime is correctly retrieved."""
        from integration.integration_code import aws_operations

        gain_ids = [str(i) for i in range(1000)]

        mock_psycopg_connect['mock_cursor'].fetchall.side_effect = [
            gain_ids[:500],
            gain_ids[500:],
        ]
        mock_psycopg_connect['mock_cursor'].description = [['id']]

        result = aws_operations.get_postgres_servicingdatetime(
            'test', set(gain_ids)
        )

        mock_psycopg_connect['mock_connect'].assert_called()
        assert mock_psycopg_connect['mock_cursor'].execute.call_count == 2
        assert result is not None
        assert result.size == 1000
