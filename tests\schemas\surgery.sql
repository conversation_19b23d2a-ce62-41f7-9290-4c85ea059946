CREATE TABLE surgery (
    surgeon <PERSON><PERSON><PERSON><PERSON>(100),
    date DATE,
    charges VARCHAR(9),
    comments VA<PERSON><PERSON><PERSON>(255),
    type VA<PERSON><PERSON><PERSON>(50),
    caseid VARCHAR(20),
    relevanttogain BOOLEAN,
    sourcecreatedatetime TIMESTAMP WITHOUT TIME ZONE,
    sourcemodifieddatetime TIMESTAMP WITHOUT TIME ZONE,
    modifieddatetime TIMESTAMP WITHOUT TIME ZONE,
    todelete B<PERSON><PERSON>EAN DEFAULT false,
    todeletesystem VARCHAR(100),
    deletepreventoverride BOOLEAN DEFAULT false,
    deletepreventoverridereason VARCHAR(1000),
    createdatetime TIMESTAMP WITHOUT TIME ZONE,
    gainid VARCHAR(16) NOT NULL DEFAULT '',
    PRIMARY KEY (gainid),
    FOREIGN KEY (caseid) REFERENCES cases(gainid)
);