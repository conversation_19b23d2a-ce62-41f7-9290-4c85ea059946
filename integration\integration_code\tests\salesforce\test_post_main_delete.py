import typing
from unittest.mock import MagicMock

import psycopg
import pytest


class TestPostMainDelete:
    @pytest.mark.django_db
    def test_should_post_main_delete(
        self,
        database: psycopg.Connection[typing.Any],
        mock_get_sf_action: MagicMock,
        mock_get_sf_bulk: MagicMock,
    ):
        from integration.integration_code import salesforce

        salesforce_id = 'salesforce id'

        sf_bulk_mock = mock_get_sf_bulk.return_value

        sf_bulk_mock.query.return_value = [
            {'Id': salesforce_id, 'attributes': []}
        ]

        sf_bulk_mock.update.return_value = [
            {'success': True, 'id': salesforce_id, 'errors': []}
        ]

        salesforce.post_main_delete(
            test=True,
            all_data=True,
            partial_config={
                'plaintiffs': True,
                'legalpersonnel': True,
                'cases': True,
                'billings': True,
                'charges': True,
            },
        )

        assert sf_bulk_mock.update.call_count == 3
