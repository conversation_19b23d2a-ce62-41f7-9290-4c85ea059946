from .. import models


def audit_log(
    salesforce_id: str,
    model: str,
    input_parameters: str,
    predicted_value: str,
    is_error: bool = False,
    exception_message: str | None = None,
) -> None:
    '''
    Log the model, input parameters, predicted value, and
    whether the prediction was successful or not.
    '''
    audit_log_entry = models.audit_log(
        salesforce_id=salesforce_id,
        model=model,
        input_parameters=input_parameters,
        predicted_value=predicted_value,
        is_error=is_error,
        exception_message=exception_message,
    )
    audit_log_entry.save()
