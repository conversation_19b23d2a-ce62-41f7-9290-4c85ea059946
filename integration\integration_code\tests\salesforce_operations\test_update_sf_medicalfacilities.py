import datetime
import typing
from unittest.mock import MagicMock, patch

import pandas as pd
import psycopg
import pytest

import integration.integration_code.salesforce_operations as salesforce_operations


class TestUpdateSfMedicalFacilities:

    @pytest.mark.django_db
    @patch(
        'integration.integration_code.salesforce_operations.get_sf_medicalfacilities_update_rollback_data'
    )
    def test_should_update_medicalfacilities_in_bulk(
        self,
        mock_rollback_data: MagicMock,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_method: MagicMock,
    ):
        """Test that medical facilities can be updated in bulk."""
        mock_rollback_data.return_value = pd.DataFrame()

        new_object: dict[typing.Hashable, typing.Any] = {
            'Website': 'https://www.test.com',
            'Id': '0018Y000033LEbtQAG',
        }

        salesforce_operations.update_sf_medicalfacilities(
            'gain-servicing',
            'integration/ati/2024-10-23',
            [new_object],
            ['12336'],
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
            True,
            ['12336'],
        )

        data, *_ = mock_sf_action_method.call_args[0]

        assert data[0] == new_object
