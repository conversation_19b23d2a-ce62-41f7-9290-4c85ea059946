import collections
import datetime
import json
import typing

import pandas as pd

from . import aws_operations, filevine_operations, id_record, local_operations

filevine_gainid_map_data = collections.defaultdict(dict)
with (
    open(
        'integration/integration_code/canonical_model.json', 'rb'
    ) as canonical_map_file,
    open(
        'integration/integration_code/canonical_data_model.json', 'rb'
    ) as canonical_data_map_file,
):
    canonical = json.load(canonical_map_file)
    canonical = {k.lower(): v for k, v in canonical.items()}  # Lowercase keys
    canonical_objects = set(canonical.keys())
    canonical_data = json.load(canonical_data_map_file)


def clean_filevine_timestamp(
    timestamp: None | str,
) -> None | datetime.date:
    if timestamp is None:
        return None
    # Remove any redundant 'Z's and additional text after the first 'Z'
    if 'Z' in timestamp:
        timestamp = timestamp.split('Z')[0] + 'Z'
    # Handle microseconds with fewer than 6 digits
    if '.' in timestamp:
        date_part, time_part = timestamp.split('T')
        time_part, fractional_seconds = time_part.split('.')
        fractional_seconds = local_operations.remove_nonnumeric_characters(
            fractional_seconds
        )
        fractional_seconds = (
            (fractional_seconds + '000000')[:6]
            if fractional_seconds is not None
            else '000000'
        )  # Pad or truncate to 6 digits
        timestamp = f'{date_part}T{time_part}.{fractional_seconds}Z'
    try:  # Try parsing with microseconds first
        dt = datetime.datetime.strptime(timestamp, '%Y-%m-%dT%H:%M:%S.%fZ')
    except ValueError:  # Fallback to parsing without microseconds
        dt = datetime.datetime.strptime(timestamp, '%Y-%m-%dT%H:%M:%S%z')
    return dt.date()


def hostilo_filevine_upsert_generate_gainid_map_data(
    df: pd.DataFrame, canonical_object: str
) -> pd.DataFrame:
    if df.empty:
        return df
    if 'sourceid' not in df.columns:
        raise ValueError(
            f'sourceid not in df.columns for gainid_map of {canonical_object}'
        )
    if 'sourcecreatedatetime' not in df.columns:
        df['sourcecreatedatetime'] = pd.NaT
    if 'sourcemodifieddatetime' not in df.columns:
        df['sourcemodifieddatetime'] = pd.NaT
    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df['sourceid'],
            df['sourcecreatedatetime'],
            df['sourcemodifieddatetime'],
        )
    ]
    gainids = aws_operations.upsert_gainid_records_by_ids(
        filevine_records=source_ids, canonical_object=canonical_object
    )
    df['gainid'] = gainids
    filevine_gainid_map_data[canonical_object] = {
        source_id: gainid for source_id, gainid in zip(df['sourceid'], gainids)
    }
    df.drop(
        columns=['sourceid', 'sourcecreatedatetime', 'sourcemodifieddatetime'],
        inplace=True,
    )
    return df


def hostilo_filevine_upsert_update_phone_data(
    entity_type: str, entity: dict[str, typing.Any], phones: list[typing.Any]
) -> None:
    if not phones:
        return
    known_entity_types = ['person', 'company']
    if entity_type not in known_entity_types:
        raise ValueError('Invalid entity type')
    if entity_type == 'person':
        for phone in phones:
            if 'label' not in phone:
                entity['otherphone'] = phone['rawNumber']
                continue
            if phone['label'] == 'Mobile' or phone['label'] == 'Cell':
                entity['cellphone'] = phone['rawNumber']
            elif phone['label'] == 'Work' or phone['label'] == 'Business':
                entity['businessphone'] = phone['rawNumber']
            elif phone['label'] == 'Home' or phone['label'] == 'Main':
                entity['homephone'] = phone['rawNumber']
            elif phone['label'] == 'Fax':
                entity['fax'] = phone['rawNumber']
            else:
                entity['otherphone'] = phone['rawNumber']
    elif entity_type == 'company':  # Only one phone number for company
        entity['phone'] = phones[0]['rawNumber']


def hostilo_filevine_upsert_update_email_data(
    entity_type: str,
    entity: dict[str, typing.Any],
    emails: list[typing.Any],
) -> None:
    if not emails:
        return
    known_entity_types = ['person', 'company']
    if entity_type not in known_entity_types:
        raise ValueError('Invalid entity type')
    if entity_type == 'person':
        for email in emails:
            if entity['primaryemail'] is not None:
                if email['address'] == entity['primaryemail']:
                    # Email already captured, check others
                    continue
                entity['secondaryemail'] = email['address']
                continue
            if email['label'] == 'Work' or email['label'] == 'Business':
                entity['primaryemail'] = email['address']
            else:
                entity['secondaryemail'] = email['address']
    elif entity_type == 'company':
        pass


def hostilo_filevine_upsert_update_address_data(
    entity_type: str,
    entity: dict[str, typing.Any],
    addresses: list[typing.Any],
) -> None:
    if not addresses:
        return
    known_entity_types = ['person', 'company']
    if entity_type not in known_entity_types:
        raise ValueError('Invalid entity type')
    if entity_type == 'person':
        for address in addresses:
            if 'label' in address and (
                address['label'] == 'Primary'
                or address['label'] == 'Main'
                or address['label'] == 'Home'
                or address['label'] == 'Business'
                or address['label'] == 'Work'
            ):
                entity['primaryaddressline1'] = address.get('line1', None)
                entity['primaryaddressline2'] = address.get('line2', None)
                entity['primaryaddresscity'] = address.get('city', None)
                entity['primaryaddressstate'] = (
                    local_operations.get_state_name_from_state_code(
                        address.get('state', None)
                    )
                )
                entity['primaryaddresszip'] = address.get('postalCode', None)
                entity['primaryaddresszip'] = (
                    local_operations.clean_address_zip_code(
                        entity['primaryaddresszip']
                    )
                )
            else:
                entity['otheraddressline1'] = address.get('line1', None)
                entity['otheraddressline2'] = address.get('line2', None)
                entity['otheraddresscity'] = address.get('city', None)
                entity['otheraddressstate'] = (
                    local_operations.get_state_name_from_state_code(
                        address.get('state', None)
                    )
                )
                entity['otheraddresszip'] = address.get('postalCode', None)
                entity['otheraddresszip'] = (
                    local_operations.clean_address_zip_code(
                        entity['otheraddresszip']
                    )
                )
    elif entity_type == 'company':
        for address in addresses:
            if 'label' in address and (
                address['label'] == 'Primary'
                or address['label'] == 'Main'
                or address['label'] == 'Home'
                or address['label'] == 'Business'
                or address['label'] == 'Work'
            ):
                entity['billingaddressline1'] = address.get('line1', None)
                entity['billingaddressline2'] = address.get('line2', None)
                entity['billingaddresscity'] = address.get('city', None)
                entity['billingaddressstate'] = (
                    local_operations.get_state_name_from_state_code(
                        address.get('state', None)
                    )
                )
                entity['billingaddresszip'] = address.get('zip', None)
                entity['billingaddresszip'] = (
                    local_operations.clean_address_zip_code(
                        entity['billingaddresszip']
                    )
                )
            else:
                entity['physicaladdressline1'] = address.get('line1', None)
                entity['physicaladdressline2'] = address.get('line2', None)
                entity['physicaladdresscity'] = address.get('city', None)
                entity['physicaladdressstate'] = (
                    local_operations.get_state_name_from_state_code(
                        address.get('state', None)
                    )
                )
                entity['physicaladdresszip'] = address.get('zip', None)
                entity['physicaladdresszip'] = (
                    local_operations.clean_address_zip_code(
                        entity['physicaladdresszip']
                    )
                )


def hostilo_filevine_upsert_generate_plaintiffs_data(
    timestamp: datetime.datetime, data: dict[str, typing.Any]
) -> dict[str, typing.Any]:
    global canonical
    canonical_keys = canonical['plaintiffs']
    plaintiff: dict[str, typing.Any] = {
        k.lower(): None for k in canonical_keys
    }  # Default values for record keys is None
    plaintiff['sourceid'] = data['personId']['native']
    plaintiff['name'] = data['fullName']
    plaintiff['dateofbirth'] = data.get('birthDate', None)
    plaintiff['dateofbirth'] = local_operations.clean_date(
        plaintiff['dateofbirth']
    )
    plaintiff['ssn'] = data.get('ssn', None)
    plaintiff['ssn'] = local_operations.remove_nonnumeric_characters(
        plaintiff['ssn']
    )
    if (
        plaintiff['ssn'] is not None and len(plaintiff['ssn']) > 9
    ):  # Invalid SSN
        plaintiff['ssn'] = None  # Set to None to avoid errors
    plaintiff['taxid'] = data.get('taxId', None)
    plaintiff['driverlicense'] = data.get('driverLicenseNumber', None)
    plaintiff['gender'] = data.get('gender', None)
    plaintiff['maritalstatus'] = data.get('maritalStatus', None)
    phones = data['phones']
    hostilo_filevine_upsert_update_phone_data('person', plaintiff, phones)
    if 'fax' in plaintiff:  # Fax is not compatible with Plaintiffs object
        del plaintiff['fax']
    if 'primaryEmail' in data:
        plaintiff['primaryemail'] = data['primaryEmail']
        # For Plaintiffs, email is stored without nesting unlike LegalPersonnel
    emails = data['emails']
    hostilo_filevine_upsert_update_email_data('person', plaintiff, emails)
    addresses = data['addresses']
    hostilo_filevine_upsert_update_address_data('person', plaintiff, addresses)
    plaintiff['relevanttogain'] = False
    plaintiff['todelete'] = False
    plaintiff['deletepreventoverride'] = False
    plaintiff['sourcecreatedatetime'] = data.get('createdDate', None)
    plaintiff['sourcecreatedatetime'] = clean_filevine_timestamp(
        plaintiff['sourcecreatedatetime']
    )
    plaintiff['sourcemodifieddatetime'] = data.get('modifiedDate', None)
    plaintiff['sourcemodifieddatetime'] = clean_filevine_timestamp(
        plaintiff['sourcemodifieddatetime']
    )
    # When RS -> SF starts using modifieddatetime from gain_id_map, remove this
    plaintiff['modifieddatetime'] = timestamp  # Temporary
    return plaintiff


def hostilo_filevine_upsert_generate_legalpersonnel_data(
    timestamp: datetime.datetime, data: list[dict[str, typing.Any]]
) -> list[dict[str, typing.Any]]:
    global canonical
    canonical_keys = canonical['legalpersonnel']
    legalpersonnel = []
    legalpersonnel_roles = {
        'Case Summary: Primary Attorney': 'Attorney',
        'Case Summary: Case Manager': 'Case Manager',
        'Case Summary: Claims Paralegal': 'Paralegal',
        'Case Summary: Co-Counsel': 'Co-Attorney',  # TBD: How to capture Co-Counsel Law Firm?
    }
    for contact in data:
        role = contact.get('role', None)
        if role is None or role not in legalpersonnel_roles:
            continue
        contact_details = contact['orgContact']
        legalperson: dict[str, typing.Any] = {
            k.lower(): None for k in canonical_keys
        }  # Default values for record keys is None
        legalperson['sourceid'] = contact_details['personId']['native']
        legalperson['name'] = contact_details['fullName']
        legalperson['title'] = legalpersonnel_roles.get(role, 'Unknown Role')
        phones = contact_details['phones']
        hostilo_filevine_upsert_update_phone_data(
            'person', legalperson, phones
        )
        if 'primaryEmail' in contact_details:
            legalperson['primaryemail'] = contact_details['primaryEmail']
        emails = contact_details['emails']
        hostilo_filevine_upsert_update_email_data(
            'person', legalperson, emails
        )
        addresses = contact_details['addresses']
        hostilo_filevine_upsert_update_address_data(
            'person', legalperson, addresses
        )
        legalperson['lawfirmid'] = (
            '366e1db3dc1da840'  # Hardcoded for Hostilo  # Reference redshift_patch_017.sql for harcoded lawfirmid
        )
        legalperson['lawfirmname'] = 'Mike Hostilo Law Firm'
        legalperson['relevanttogain'] = False
        legalperson['todelete'] = False
        legalperson['deletepreventoverride'] = False
        legalperson['sourcecreatedatetime'] = contact_details.get(
            'createdDate', None
        )
        legalperson['sourcecreatedatetime'] = clean_filevine_timestamp(
            legalperson['sourcecreatedatetime']
        )
        legalperson['sourcemodifieddatetime'] = contact_details.get(
            'modifiedDate', None
        )
        legalperson['sourcemodifieddatetime'] = clean_filevine_timestamp(
            legalperson['sourcemodifieddatetime']
        )
        # When RS -> SF starts using modifieddatetime from gain_id_map, remove this
        legalperson['modifieddatetime'] = timestamp  # Temporary
        legalpersonnel.append(legalperson)
    return legalpersonnel


def hostilo_filevine_upsert_generate_cases_data_get_legalpersonnel_ids(
    timestamp: datetime.datetime, data: list[dict[str, typing.Any]]
) -> tuple[
    typing.Optional[str],
    typing.Optional[str],
    typing.Optional[str],
    typing.Optional[str],
]:
    attorney_id, paralegal_id, case_manager_id, cocounsel_id = (
        None,
        None,
        None,
        None,
    )
    for legalperson in data:
        if legalperson['title'] == 'Attorney':
            attorney_id = legalperson['sourceid']
        elif legalperson['title'] == 'Paralegal':
            paralegal_id = legalperson['sourceid']
        elif legalperson['title'] == 'Case Manager':
            case_manager_id = legalperson['sourceid']
        elif legalperson['title'] == 'Co-Attorney':
            cocounsel_id = legalperson['sourceid']
    return (attorney_id, paralegal_id, case_manager_id, cocounsel_id)


def hostilo_filevine_upsert_generate_cases_data(
    timestamp: datetime.datetime,
    data: dict[str, typing.Any],
) -> dict[str, typing.Any]:
    global canonical, canonical_data
    canonical_keys = canonical['cases']
    case: dict[str, typing.Any] = {
        k.lower(): None for k in canonical_keys
    }  # Default values for record keys is None
    plaintiff_data = data['plaintiff_data']
    case_data = data['case_data']
    legalpersonnel_data = data['legalpersonnel_data']
    case['sourceid'] = case_data['projectId']['native']
    case['plaintiffname'] = plaintiff_data['name']
    case['plaintiffdateofbirth'] = plaintiff_data['dateofbirth']
    case['status'] = case_data.get('phaseName', None)
    if case['status'] is not None:
        case['status'] = canonical_data['Filevine']['ToCanonical'][
            'StatusTypes'
        ].get(case['status'], case['status'])
    case['accidentdate'] = case_data.get('incidentDate', None)
    case['accidentdate'] = local_operations.clean_date(case['accidentdate'])
    case['type'] = None  # TBD: Determine how to get type
    if case['type'] is not None:
        case['type'] = canonical_data['Filevine']['ToCanonical'][
            'CaseTypes'
        ].get(case['type'], case['type'])
    case['plaintiffid'] = plaintiff_data['sourceid']
    (
        case['attorneyid'],
        case['paralegalid'],
        case['casemanagerid'],
        case['cocounselid'],
    ) = hostilo_filevine_upsert_generate_cases_data_get_legalpersonnel_ids(
        timestamp, legalpersonnel_data
    )
    case['relevanttogain'] = False
    case['todelete'] = False
    case['deletepreventoverride'] = False
    case['sourcecreatedatetime'] = case_data.get('createdDate', None)
    case['sourcecreatedatetime'] = clean_filevine_timestamp(
        case['sourcecreatedatetime']
    )
    case['sourcemodifieddatetime'] = case_data.get('lastActivity', None)
    case['sourcemodifieddatetime'] = clean_filevine_timestamp(
        case['sourcemodifieddatetime']
    )
    # When RS -> SF starts using modifieddatetime from gain_id_map, remove this
    case['modifieddatetime'] = timestamp  # Temporary
    return case


def hostilo_filevine_upsert_generate_insurances_data(
    timestamp: datetime.datetime, data: dict[str, typing.Any]
) -> list[dict[str, typing.Any]]:
    global canonical, canonical_data
    canonical_keys = canonical['insurances']
    insurances = []
    insurances_data = data['insurance_data']
    case_data = data['case_data']
    for insurance_data in insurances_data:
        insurance: dict[str, typing.Any] = {
            k.lower(): None for k in canonical_keys
        }  # Default values for record keys is None
        # Collections in Filevine have dataObject with the actual data
        insurance_data = insurance_data['dataObject']
        insurer_data = insurance_data.get('insurer', None)
        if insurer_data is None:
            continue
        insurance['sourceid'] = insurer_data['id']
        insurance['companyname'] = insurer_data[
            'fullname'
        ]  # For some reason, the key is lower case, not camel case
        insurance['policyid'] = insurance_data.get('policynumber', None)
        insurance['limits'] = insurance_data.get('policylimits', None)
        phones = insurer_data['phones']
        hostilo_filevine_upsert_update_phone_data('company', insurance, phones)
        emails = insurer_data['emails']
        hostilo_filevine_upsert_update_email_data('company', insurance, emails)
        addresses = insurer_data['addresses']
        hostilo_filevine_upsert_update_address_data(
            'company', insurance, addresses
        )
        insurance['probate'] = insurance_data.get('probate', None)
        insurance['bankruptcy'] = insurance_data.get('bankruptcy', None)
        insurance['subrogationlien'] = insurance_data.get(
            'subrogationLien', None
        )
        other_lien = insurance_data.get('otherLiens', None)
        if other_lien is not None:
            insurance['otherlien'] = True
            insurance['otherlienname'] = other_lien
        driver = insurance_data.get('driver', None)
        if driver is not None:
            insurance['drivername'] = driver.get('fullname', None)
        insurance['datesettled'] = insurance_data.get('dateSettled', None)
        insurance['datesettled'] = local_operations.clean_date(
            insurance['datesettled']
        )
        insurance['howcasewassettled'] = insurance_data.get(
            'howCaseWasSettled', None
        )
        insurance['totalsettlementamount'] = insurance_data.get(
            'settledAmount', None
        )
        insurance['billspaid'] = insurance_data.get('billsPaid', None)
        if insurance['billspaid'] is not None:
            insurance['billspaid'] = local_operations.convert_boolean_val(
                insurance['billspaid']
            )
        insurance['attorneyfee'] = insurance_data.get('attorneyFee', None)
        insurance['attorneyfeeflexible'] = insurance_data.get(
            'attorneyFeeFlexible', None
        )
        insurance['amounttoclient'] = insurance_data.get(
            'amountToClient', None
        )
        insurance['settlementnotes'] = insurance_data.get(
            'settlementNotes', None
        )
        insurance['notes'] = insurer_data.get('notes', None)
        insurance['type'] = insurance_data.get('insurancetype', None)
        if insurance['type'] is not None:
            insurance['type'] = canonical_data['Filevine']['ToCanonical'][
                'InsuranceTypes'
            ].get(insurance['type'], insurance['type'])
        insurance['caseid'] = case_data['sourceid']
        insurance['relevanttogain'] = False
        insurance['todelete'] = False
        insurance['deletepreventoverride'] = False
        insurance['sourcecreatedatetime'] = insurer_data.get(
            'createdDate', None
        )
        insurance['sourcecreatedatetime'] = clean_filevine_timestamp(
            insurance['sourcecreatedatetime']
        )
        insurance['sourcemodifieddatetime'] = insurer_data.get(
            'modifiedDate', None
        )
        insurance['sourcemodifieddatetime'] = clean_filevine_timestamp(
            insurance['sourcemodifieddatetime']
        )
        insurances.append(insurance)  # Add to result
    return insurances


def hostilo_filevine_upsert_generate_liens_data(
    timestamp: datetime.datetime, data: dict[str, typing.Any]
) -> list[dict[str, typing.Any]]:
    global canonical, canonical_data
    canonical_keys = canonical['liens']
    liens = []
    liens_data = data['lien_data']
    case_data = data['case_data']
    for lien_data in liens_data:
        lien: dict[str, typing.Any] = {
            k.lower(): None for k in canonical_keys
        }  # Default values for record keys is None
        # Collections in Filevine have dataObject with the actual data
        lien_data = lien_data['dataObject']
        lienholder_data = lien_data.get('lienholder', None)
        if lienholder_data is None:
            continue
        lienholder_id = lienholder_data['id']
        case_id = case_data['sourceid']
        lien_amount_due = lien_data.get('amountdue', None)
        composite_key = f'{lienholder_id}_{case_id}_{lien_amount_due if lien_amount_due is not None else ""}'
        composite_key = local_operations.get_md5_hash(composite_key)[:16]
        lien['sourceid'] = composite_key
        lien['amountdue'] = lien_amount_due
        lien['lienholdername'] = lienholder_data.get('fullname', None)
        lien['lienfilenumber'] = lien_data.get('lienFileNo', None)
        lien['originalpaidamount'] = lien_data.get(
            'originalPaidLienAmount', None
        )
        lien['isthereexcess'] = lien_data.get('isThereAnExcess', None)
        if lien['isthereexcess'] is not None:
            lien['isthereexcess'] = local_operations.convert_boolean_val(
                lien['isthereexcess']
            )
        lien['excessamount'] = lien_data.get('excessAmount', None)
        lien['excludefromsettlement'] = lien_data.get(
            'excludefromsettlement', None
        )
        if lien['excludefromsettlement'] is not None:
            lien['excludefromsettlement'] = (
                local_operations.convert_boolean_val(
                    lien['excludefromsettlement']
                )
            )
        lien['type'] = lien_data.get('lienType', None)
        if lien['type'] is not None:
            lien['type'] = canonical_data['Filevine']['ToCanonical'][
                'LienTypes'
            ].get(lien['type'], lien['type'])
        lien['caseid'] = case_id
        lien['relevanttogain'] = False
        lien['todelete'] = False
        lien['deletepreventoverride'] = False
        lien['sourcecreatedatetime'] = lienholder_data.get('createdDate', None)
        lien['sourcecreatedatetime'] = clean_filevine_timestamp(
            lien['sourcecreatedatetime']
        )
        lien['sourcemodifieddatetime'] = lienholder_data.get(
            'modifiedDate', None
        )
        lien['sourcemodifieddatetime'] = clean_filevine_timestamp(
            lien['sourcemodifieddatetime']
        )
        liens.append(lien)  # Add to result
    return liens


def hostilo_filevine_upsert_generate_files_s3_data(
    bucket: str, directory: str, file_name: str, file_url: str
) -> str:
    # Download file from URL
    name = f'integration/integration_code/temp_downloads/{file_name}'
    directory = directory + '/files'  # Add files sub-directory in S3
    filevine_operations.download_file(file_url, name)
    # Upload file to S3
    aws_operations.upload_s3(bucket, directory, name)
    # Delete file from local
    local_operations.clean_up_file(name)
    # Return S3 URL
    s3_uri = (
        f's3://{bucket}/{directory}/{local_operations.get_aws_name(file_name)}'
    )
    return s3_uri


def hostilo_filevine_upsert_generate_files_data(
    timestamp: datetime.datetime, data: dict[str, typing.Any]
) -> list[dict[str, typing.Any]]:
    global canonical, canonical_data
    canonical_keys = canonical['files']
    bucket = data['bucket']
    directory = data['directory']
    plaintiff_data = data['plaintiff_data']
    case_data = data['case_data']
    files_data = data['files_data']
    files = []
    for file_data in files_data:
        file: dict[str, typing.Any] = {
            k.lower(): None for k in canonical_keys
        }  # Default values for record keys is None
        file['sourceid'] = file_data['documentId']['native']
        # File name is not unique, so prefix with sourceid i.e. documentId
        file_name = f'{file["sourceid"]}_{file_data["filename"]}'
        file['url'] = hostilo_filevine_upsert_generate_files_s3_data(
            bucket,
            directory,
            file_name,
            file_data['documentURL'],
        )
        file['type'] = file_data.get('folderName', 'Other')
        # TBD: Determine canonical data mapping for file types
        # if file['type'] is not None:
        #     file['type'] = canonical_data['Filevine']['ToCanonical'][
        #         'FileTypes'
        #     ].get(file['type'], file['type'])
        file['plaintiffid'] = plaintiff_data['sourceid']
        file['caseid'] = case_data['sourceid']
        file['relevanttogain'] = False
        file['todelete'] = False
        file['deletepreventoverride'] = False
        file['sourcecreatedatetime'] = file_data.get('uploadDate', None)
        file['sourcecreatedatetime'] = clean_filevine_timestamp(
            file['sourcecreatedatetime']
        )
        file['sourcemodifieddatetime'] = file_data.get('modifiedDate', None)
        file['sourcemodifieddatetime'] = clean_filevine_timestamp(
            file['sourcemodifieddatetime']
        )
        # When RS -> SF starts using modifieddatetime from gain_id_map, remove this
        file['modifieddatetime'] = timestamp  # Temporary
        files.append(file)
    return files


def hostilo_filevine_upsert_generate_case_gainid_map_data(
    cases_data: pd.DataFrame,
) -> pd.DataFrame:
    global filevine_gainid_map_data
    if cases_data.empty:
        return cases_data
    cases_data['plaintiffid'] = cases_data['plaintiffid'].map(
        filevine_gainid_map_data['plaintiffs']
    )
    cases_data['attorneyid'] = cases_data['attorneyid'].map(
        filevine_gainid_map_data['legalpersonnel']
    )
    cases_data['paralegalid'] = cases_data['paralegalid'].map(
        filevine_gainid_map_data['legalpersonnel']
    )
    cases_data['casemanagerid'] = cases_data['casemanagerid'].map(
        filevine_gainid_map_data['legalpersonnel']
    )
    cases_data['cocounselid'] = cases_data['cocounselid'].map(
        filevine_gainid_map_data['legalpersonnel']
    )
    return cases_data


def hostilo_filevine_upsert_generate_insurances_gainid_map_data(
    insurances_data: pd.DataFrame,
) -> pd.DataFrame:
    global filevine_gainid_map_data
    if insurances_data.empty:
        return insurances_data
    insurances_data['caseid'] = insurances_data['caseid'].map(
        filevine_gainid_map_data['cases']
    )
    return insurances_data


def hostilo_filevine_upsert_generate_liens_gainid_map_data(
    liens_data: pd.DataFrame,
) -> pd.DataFrame:
    global filevine_gainid_map_data
    if liens_data.empty:
        return liens_data
    liens_data['caseid'] = liens_data['caseid'].map(
        filevine_gainid_map_data['cases']
    )
    return liens_data


def hostilo_filevine_upsert_generate_files_gainid_map_data(
    files_data: pd.DataFrame,
) -> pd.DataFrame:
    global filevine_gainid_map_data
    if files_data.empty:
        return files_data
    files_data['plaintiffid'] = files_data['plaintiffid'].map(
        filevine_gainid_map_data['plaintiffs']
    )
    files_data['caseid'] = files_data['caseid'].map(
        filevine_gainid_map_data['cases']
    )
    return files_data


def hostilo_filevine_upsert_generate_s3_data(
    bucket: str,
    directory: str,
    timestamp: datetime.datetime,
    projects_data: list[dict[str, typing.Any]],
) -> tuple[dict[str, pd.DataFrame], set[str]]:
    legalpersonnel_data = []
    plaintiffs_data = []
    cases_data = []
    insurances_data = []
    liens_data = []
    documents_data = []
    insurances_gainid_set = set()
    for project_data in projects_data:
        plaintiff = hostilo_filevine_upsert_generate_plaintiffs_data(
            timestamp, project_data['client_details']
        )
        plaintiffs_data.append(plaintiff)
        legalpersonnel = hostilo_filevine_upsert_generate_legalpersonnel_data(
            timestamp, project_data['contacts']
        )
        legalpersonnel_data.extend(legalpersonnel)
        data_for_cases = {
            'plaintiff_data': plaintiff,
            'case_data': project_data['project'],
            'legalpersonnel_data': legalpersonnel_data,
        }
        case = hostilo_filevine_upsert_generate_cases_data(
            timestamp, data_for_cases
        )
        cases_data.append(case)
        data_for_insurances = {
            'case_data': case,
            'insurance_data': project_data['insurances'],
        }
        insurances = hostilo_filevine_upsert_generate_insurances_data(
            timestamp, data_for_insurances
        )
        insurances_data.extend(insurances)
        data_for_liens = {
            'case_data': case,
            'lien_data': project_data['liens'],
        }
        liens = hostilo_filevine_upsert_generate_liens_data(
            timestamp, data_for_liens
        )
        liens_data.extend(liens)
        data_for_files = {
            'bucket': bucket,
            'directory': directory,
            'plaintiff_data': plaintiff,
            'case_data': case,
            'files_data': project_data['documents'],
        }
        documents = hostilo_filevine_upsert_generate_files_data(
            timestamp, data_for_files
        )
        documents_data.extend(documents)
    s3_data = {
        'plaintiffs': pd.DataFrame(plaintiffs_data),
        'legalpersonnel': pd.DataFrame(legalpersonnel_data),
        'cases': pd.DataFrame(cases_data),
        'insurances': pd.DataFrame(insurances_data),
        'liens': pd.DataFrame(liens_data),
        'files': pd.DataFrame(documents_data),
    }
    for key, df in s3_data.items():  # Final cleanup of dataframes
        if df.empty:
            continue
        s3_data[key] = df.dropna(subset=['sourceid'])
        s3_data[key] = s3_data[key].drop_duplicates(subset=['sourceid'])
    for key, df in s3_data.items():  # Generate gainid map for each dataframe
        if df.empty:
            continue
        s3_data[key] = hostilo_filevine_upsert_generate_gainid_map_data(
            df, key
        )
    # Generate linking gainids for cases, insurances, liens and files
    # Plaintiffs have no linking gainids
    # Legalpersonnel have linking to lawfirms, and are already linked
    s3_data['cases'] = hostilo_filevine_upsert_generate_case_gainid_map_data(
        s3_data['cases']
    )
    s3_data['insurances'] = (
        hostilo_filevine_upsert_generate_insurances_gainid_map_data(
            s3_data['insurances']
        )
    )
    s3_data['liens'] = hostilo_filevine_upsert_generate_liens_gainid_map_data(
        s3_data['liens']
    )
    s3_data['files'] = hostilo_filevine_upsert_generate_files_gainid_map_data(
        s3_data['files']
    )
    if not s3_data['insurances'].empty:
        insurances_gainid_set = set(s3_data['insurances']['gainid'])
    return (s3_data, insurances_gainid_set)


def hostilo_filevine_upsert_generate_csv(
    timestamp: datetime.datetime, s3_data: dict[str, pd.DataFrame]
) -> dict[str, str]:
    files = {}
    timestamp_string = timestamp.strftime('%Y-%m-%d-%H-%M-%S')
    if not s3_data:
        return files
    for data in s3_data:
        s3_data[data].to_csv(
            f'integration/integration_code/{data}_{timestamp_string}.csv',
            index=False,
        )
        files[data] = (
            f'integration/integration_code/{data}_{timestamp_string}.csv'
        )
    return files
