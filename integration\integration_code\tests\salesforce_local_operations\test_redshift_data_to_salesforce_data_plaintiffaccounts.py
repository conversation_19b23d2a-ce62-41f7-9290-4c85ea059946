import datetime
import typing
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

import integration.integration_code.salesforce_local_operations as salesforce_local_operations
import tests.test_helper as test_helper


def verify_logger_warning_called_with_message(
    mock_logger: MagicMock, expected_message: str, expected_count: int = 1
) -> bool:
    """Helper function to verify logger.warning was called with a specific message.

    Args:
        mock_logger: The mocked logger instance
        expected_message: The message to search for in warning calls
        expected_count: Expected number of calls with this message (default: 1)

    Returns:
        bool: True if the expected number of calls were found
    """
    warning_calls = [
        call
        for call in mock_logger.warning.call_args_list
        if call.args and expected_message in call.args[0]
    ]
    return len(warning_calls) == expected_count


class TestRedshiftDataToSalesforceDataPlaintiffaccounts:
    """Test cases for redshift_data_to_salesforce_data_plaintiffaccounts function.

    Focuses on missing fields detection and logging functionality.
    """

    @pytest.fixture
    def mock_dependencies(self):
        """Mock external dependencies for isolated testing."""
        with (
            patch(
                'integration.integration_code.salesforce_local_operations.salesforce_operations.get_sf_record_type_id'
            ) as mock_record_type,
            patch(
                'integration.integration_code.salesforce_local_operations.aws_operations.get_gainid_to_sourcename_map'
            ) as mock_source_map,
        ):
            mock_record_type.return_value = 'test_record_type_id'
            mock_source_map.return_value = {
                '12345': 'ATI',
                '67890': 'Jopari',
                '11111': 'TestSource',
                '22222': 'NonAutomatedSource',
            }
            yield {
                'mock_record_type': mock_record_type,
                'mock_source_map': mock_source_map,
            }

    @pytest.fixture
    def mock_logger(self):
        """Mock the logger for isolated testing of logging calls."""
        with patch(
            'integration.integration_code.salesforce_local_operations.logger'
        ) as mock_logger:
            yield mock_logger

    @pytest.fixture
    def sample_sf_plaintiffaccounts_data(self):
        """Sample Salesforce plaintiff accounts data for testing."""
        return {
            'plaintiff_accounts_nameDOB': {
                ('john doe', '1990-01-01'): ['sf_id_123'],
                ('jane smith', '1985-05-15'): [
                    'sf_id_456',
                    'sf_id_789',
                ],  # Multiple matches
                ('bob johnson', '1975-12-25'): ['sf_id_999'],
            }
        }

    def test_missing_name_field_logs_warning(
        self,
        mock_dependencies: dict[str, MagicMock],
        mock_logger: MagicMock,
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
    ):
        """Test that missing Name field is properly detected and logged."""
        # Arrange
        redshift_data = pd.DataFrame(
            [
                {
                    'GainId': 12345,
                    'Name': None,  # Missing name
                    'DateOfBirth': datetime.date(1990, 1, 1),
                }
            ]
        )
        redshift_ids = {}

        # Act
        result = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
            redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
        )

        # Assert
        assert len(result['update']) == 0
        assert len(result['insert']) == 0
        assert len(result['gainid_update']) == 0
        assert len(result['gainid_insert']) == 0

        # Verify logging was called with correct parameters
        assert verify_logger_warning_called_with_message(
            mock_logger, "Plaintiff missing name or DOB manual review"
        )

    def test_missing_date_of_birth_field_logs_warning(
        self,
        mock_dependencies: dict[str, MagicMock],
        mock_logger: MagicMock,
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
    ):
        """Test that missing DateOfBirth field is properly detected and logged."""
        # Arrange
        redshift_data = pd.DataFrame(
            [
                {
                    'GainId': 12345,
                    'Name': 'John Doe',
                    'DateOfBirth': None,  # Missing date of birth
                }
            ]
        )
        redshift_ids = {}

        # Act
        result = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
            redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
        )

        # Assert
        assert len(result['update']) == 0
        assert len(result['insert']) == 0
        assert len(result['gainid_update']) == 0
        assert len(result['gainid_insert']) == 0

        # Verify logging was called
        assert verify_logger_warning_called_with_message(
            mock_logger, "Plaintiff missing name or DOB manual review"
        )

    def test_empty_string_name_field_logs_warning(
        self,
        mock_dependencies: dict[str, MagicMock],
        mock_logger: MagicMock,
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
    ):
        """Test that empty string Name field is treated as missing and logged."""
        # Arrange
        redshift_data = pd.DataFrame(
            [
                {
                    'GainId': 12345,
                    'Name': '',  # Empty string name
                    'DateOfBirth': datetime.date(1990, 1, 1),
                }
            ]
        )
        redshift_ids = {}

        # Act
        result = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
            redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
        )

        # Assert
        assert len(result['update']) == 0
        assert len(result['insert']) == 0

        # Verify logging was called
        assert verify_logger_warning_called_with_message(
            mock_logger, "Plaintiff missing name or DOB manual review"
        )

    def test_both_fields_missing_logs_warning(
        self,
        mock_dependencies: dict[str, MagicMock],
        mock_logger: MagicMock,
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
    ):
        """Test that missing both Name and DateOfBirth fields logs warning."""
        # Arrange
        redshift_data = pd.DataFrame(
            [
                {
                    'GainId': 12345,
                    'Name': None,  # Missing name
                    'DateOfBirth': None,  # Missing date of birth
                }
            ]
        )
        redshift_ids = {}

        # Act
        result = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
            redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
        )

        # Assert
        assert len(result['update']) == 0
        assert len(result['insert']) == 0

        # Verify logging was called
        assert verify_logger_warning_called_with_message(
            mock_logger, "Plaintiff missing name or DOB manual review"
        )

    def test_multiple_matching_accounts_logs_warning(
        self,
        mock_dependencies: dict[str, MagicMock],
        mock_logger: MagicMock,
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
    ):
        """Test that multiple matching accounts scenario logs warning."""
        # Arrange
        redshift_data = pd.DataFrame(
            [
                {
                    'GainId': 11111,  # Non-ATI/Jopari source
                    'Name': 'Jane Smith',  # This name has multiple matches in sample data
                    'DateOfBirth': datetime.date(1985, 5, 15),
                }
            ]
        )
        redshift_ids = {}

        # Act
        result = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
            redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
        )

        # Assert
        assert len(result['update']) == 0
        assert len(result['insert']) == 0

        # Verify logging was called for multiple matches
        assert verify_logger_warning_called_with_message(
            mock_logger, "Multiple matching plaintiff accounts manual review"
        )

    def test_flush_logging_called_at_end(
        self,
        mock_dependencies: dict[str, MagicMock],
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
    ):
        """Test that flush logging is called at the end of processing."""
        # Arrange
        redshift_data = pd.DataFrame(
            [
                {
                    'GainId': 11111,
                    'Name': 'John Doe',
                    'DateOfBirth': datetime.date(1990, 1, 1),
                }
            ]
        )
        redshift_ids = {}

        with patch(
            'integration.integration_code.salesforce_local_operations.logger'
        ) as mock_logger:
            # Act
            salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
                redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
            )

            # Assert - Verify flush logging was called
            flush_calls = [
                call
                for call in mock_logger.warning.call_args_list
                if call.args and call.args[0] == 'flush'
            ]
            assert len(flush_calls) == 1

            # Verify flush call has correct extra data
            flush_call = flush_calls[0]
            extra_data = flush_call.kwargs.get('extra', {})
            assert extra_data.get('flush') is True

    def test_empty_dataframe_processes_without_error(
        self,
        mock_dependencies: dict[str, MagicMock],
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
        verify_errors: MagicMock,
    ):
        """Test that empty DataFrame processes without error."""
        # Arrange
        redshift_data = pd.DataFrame(columns=['GainId', 'Name', 'DateOfBirth'])
        redshift_ids = {}

        # Act
        result = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
            redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
        )

        # Assert
        assert len(result['update']) == 0
        assert len(result['insert']) == 0
        assert len(result['gainid_update']) == 0
        assert len(result['gainid_insert']) == 0
        assert len(result['update_manual_review']) == 0

        # Verify no error logging
        test_helper.verify_audit_errors(verify_errors)

    def test_ati_jopari_sources_skip_name_dob_matching(
        self,
        mock_dependencies: dict[str, MagicMock],
        sample_sf_plaintiffaccounts_data: dict[str, typing.Any],
        verify_errors: MagicMock,
    ):
        """Test that ATI and Jopari sources skip name/DOB matching logic."""
        # Arrange
        redshift_data = pd.DataFrame(
            [
                {
                    'GainId': 12345,  # ATI source
                    'Name': 'Jane Smith',  # This would normally cause multiple matches
                    'DateOfBirth': datetime.date(1985, 5, 15),
                },
                {
                    'GainId': 67890,  # Jopari source
                    'Name': 'Jane Smith',  # This would normally cause multiple matches
                    'DateOfBirth': datetime.date(1985, 5, 15),
                },
            ]
        )
        redshift_ids = {}

        # Act
        result = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
            redshift_data, redshift_ids, sample_sf_plaintiffaccounts_data
        )

        # Assert - Should process as inserts without multiple match warnings
        assert len(result['insert']) == 2

        # Verify no multiple matching warnings for ATI/Jopari sources
        assert not test_helper.verify_if_log_exists(
            verify_errors, "Multiple matching plaintiff accounts manual review"
        )
