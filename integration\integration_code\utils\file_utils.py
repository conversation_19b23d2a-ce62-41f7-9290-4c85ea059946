import csv
import io
import typing


def csv_bytes_to_json(
    csv_bytes: bytes,
) -> list[dict[str | typing.Any, str | typing.Any]]:
    # Step 1: Decode the bytes into a string (assuming UTF-8 encoding)
    csv_string = csv_bytes.decode('utf-8')

    # Step 2: Use `io.StringIO` to treat the string like a file
    csv_file = io.StringIO(csv_string)

    # Step 3: Parse CSV with Dict<PERSON>eader (creates a list of dicts)
    reader = csv.DictReader(csv_file)
    data = list(reader)

    return data
