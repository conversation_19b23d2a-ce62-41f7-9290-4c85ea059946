from unittest.mock import MagicMock

import pytest


class TestLogging:
    @pytest.mark.django_db
    def test_logging(self, mock_context: MagicMock):
        import integration.integration_code.logger_config as logger_config

        logger = logger_config.get_logger(__name__)

        logger.info("Test info message", extra={'additional_details': 'Test'})
        logger.warning("Test warn message")
        logger.error("Test error message")
