# pyright: reportArgumentType=warning
import json

import boto3
import pandas as pd

with open('./providermapdata/data_update/credentials.json', 'rb') as f:
    credentials = json.load(f)

# Access to s3 buckets
access_key = credentials['aws-s3']['access_key']
secret_access_key = credentials['aws-s3']['secret_access_key']
s3_client = boto3.client(
    's3', aws_access_key_id=access_key, aws_secret_access_key=secret_access_key
)


def return_definitive_providers():
    response = s3_client.get_object(
        Bucket="gain-servicing", Key="providers-map/allFromDefinitive.csv"
    )
    status = response.get("ResponseMetadata", {}).get("HTTPStatusCode")

    if status == 200:
        print("Successful S3 get_object response. Status - {}".format(status))
        dtype_dic = {'ZipCode': str}
        df_DefinitiveProviders = pd.read_csv(
            response.get("Body"),
            sep=',',
            index_col=False,
            dtype=dtype_dic,
            low_memory=False,
        )
        # AcceptPI = -1 means do not accept PI, 0 means don't know; 1 means accepts PI
        # For now definitive Accept PI = 0 i.e. unknown
        df_DefinitiveProviders.insert(2, 'AcceptPI', 0)
        # Update the provider type value based on whether it accept PI or not
        df_DefinitiveProviders.loc[
            df_DefinitiveProviders.AcceptPI == 1, 'ProviderType'
        ] = 1
        df_DefinitiveProviders.loc[
            df_DefinitiveProviders.AcceptPI == 0, 'ProviderType'
        ] = 2
        df_DefinitiveProviders.loc[:, 'DiscountSelfPay'] = 0
        df_DefinitiveProviders.loc[:, 'PreferredProvider'] = 0
        return df_DefinitiveProviders
    else:
        print(
            "Unsuccessful S3 get_object response. Status - {}".format(status)
        )
