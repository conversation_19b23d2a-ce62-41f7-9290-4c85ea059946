from salesforcedata.attyack_get_merged_templates.AttyAck_Get_Merged_Templates import (
    AttyAckGetMergedTemplates,
)


class TestAttyAckGetMergedTemplates:

    def test_should_load_templates_successfully(self):

        merged_templates = AttyAckGetMergedTemplates()

        listOfSFData = [
            {
                "uniqueID": "1234",
                "providerName": "Test Provider",
                "providerAutoSign": "yes",
                "lawFirmName": "Test Law Firm",
                "tableRowData": "OpportunityRowData",
                "contractOption": "Attorney and Provider",
                "relatedObjectData": [
                    {
                        "uniqueID": "1234",
                        "providerName": "Test Provider",
                        "providerAutoSign": "yes",
                        "lawFirmName": "Test Law Firm",
                        "tableRowData": "FundingRowData",
                        "contractOption": "Attorney and Gain",
                    }
                ],
            }
        ]

        merged_templates.AttyAckGetMergedTemplates(listOfSFData)
        output = merged_templates.getOutputData()
        assert len(output['pdfContents']) != 0
