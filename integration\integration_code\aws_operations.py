import collections
import csv
import datetime
import io
import json
import os
import typing
import uuid
from contextlib import contextmanager
from functools import wraps

import boto3
import botocore.exceptions
import django.conf
import numpy
import pandas as pd
import psycopg
import psycopg._encodings
import psycopg.sql
import typing_extensions

from . import context

P = typing_extensions.ParamSpec('P')
T = typing.TypeVar('T')

from . import id_record, local_operations, logger_config

logger = logger_config.get_logger(__name__)

canonical_sources = [
    'ATI',
    'Salesforce',
    'SalesforceExternalContainer',
    'Jo<PERSON><PERSON>',
    'Filevine Hostilo',
]

with open(
    'integration/integration_code/settleable_canonical_objects.json', 'rb'
) as settleable_canonical_objects_file:
    settleable_canonical_objects_data = json.load(
        settleable_canonical_objects_file
    )
    settleable_canonical_objects = settleable_canonical_objects_data["objects"]

with open(
    'integration/integration_code/canonical_update_columns.json', 'rb'
) as canonical_update_columns_file:
    canonical_update_columns = json.load(canonical_update_columns_file)

app_config = django.conf.settings
credentials, settings = app_config.CREDENTIALS, app_config.SETTINGS

aws_session = boto3.Session(
    aws_access_key_id=credentials['integration']['aws']['aws_access_key_id'],
    aws_secret_access_key=credentials['integration']['aws'][
        'aws_secret_access_key'
    ],
    region_name=settings['Common']['AWS']['S3']['region_name'],
)
s3 = aws_session.resource('s3')
psycopg._encodings._py_codecs["UNICODE"] = (
    "utf-8"  # Set encoding to utf-8 for psycopg
)
psycopg._encodings.py_codecs.update(
    (k.encode(), v) for k, v in psycopg._encodings._py_codecs.items()
)

dbname = settings['Integration']['AWS']['Postgres']['dbname']


def get_postgres_conn_string():
    return f'''
    host={os.getenv('POSTGRES_HOST', settings['Integration']['AWS']['Postgres']['host'])}
    dbname={os.getenv('POSTGRES_DBNAME', settings['Integration']['AWS']['Postgres']['dbname'])}
    port={os.getenv('POSTGRES_PORT', settings['Integration']['AWS']['Postgres']['port'])}
    user={os.getenv('POSTGRES_USER', settings['Integration']['AWS']['Postgres']['user'])}
    password={os.getenv('POSTGRES_PASSWORD', settings['Integration']['AWS']['Postgres']['password'])}
    '''


fallback_timestamp = datetime.datetime(2000, 1, 1)


# region S3


def handle_s3_exception(
    func_name: str,
    bucket: str,
    key: str,
    local_location: str,
    message: str | Exception,
) -> None:
    logger.error(
        message,
        extra={
            'additional_details': f'bucket = {bucket}, key = {key}, local_location = {local_location}',
            'route_section': func_name,
        },
    )


def s3_safe_execution(
    func: typing.Callable[P, T],
) -> typing.Callable[P, T]:
    @wraps(func)
    def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        func_name = func.__name__
        try:
            return func(*args, **kwargs)
        except botocore.exceptions.ClientError as ce:
            handle_s3_exception(
                func_name,
                str(kwargs.get('bucket', '')),
                str(kwargs.get('key', '')),
                str(kwargs.get('local_location', '')),
                'An AWS service error has occured: ' + str(ce),
            )
            return typing.cast(T, None)
        except Exception as e:
            handle_s3_exception(
                func_name,
                str(kwargs.get('bucket', '')),
                str(kwargs.get('key', '')),
                str(kwargs.get('local_location', '')),
                e,
            )
            return typing.cast(T, None)

    return wrapper


@s3_safe_execution
def download_s3(bucket: str, key: str, local_location: str) -> None:
    s3.meta.client.download_file(bucket, key, local_location)


@s3_safe_execution
def download_s3_inmemory(
    bucket: str, key: str, local_location: str | None = None
) -> bytes:
    key_name = key
    if local_location is not None:
        key_name = f'{key}/{local_operations.get_aws_name(local_location)}'

    buffer = io.BytesIO()
    s3.meta.client.download_fileobj(bucket, key_name, buffer)

    # Seek to the beginning of the BytesIO object
    buffer.seek(0)

    return buffer.getvalue()


@s3_safe_execution
def upload_s3(bucket: str, directory: str, file: str) -> None:
    key_name = f'{directory}/{local_operations.get_aws_name(file)}'
    s3.meta.client.upload_file(Filename=file, Bucket=f'{bucket}', Key=key_name)


@s3_safe_execution
def upload_s3_inmemory(
    bucket: str, directory: str, file: str, file_content: bytes
) -> None:
    key_name = f'{directory}/{local_operations.get_aws_name(file)}'

    buffer = io.BytesIO(file_content)
    buffer.seek(0)  # Ensure we're at the start of the buffer

    # Upload the file content to S3
    s3.meta.client.upload_fileobj(buffer, bucket, key_name)


@s3_safe_execution
def clean_s3(bucket: str, directory: str, files: str) -> None:
    for file in files:
        key_name = f'{directory}/{local_operations.get_aws_name(file)}'
        s3.Object(bucket, key_name).delete()


@s3_safe_execution
def get_s3(bucket: str, prefix: typing.Optional[str] = None) -> list[str]:
    s3_files_list = []
    s3_bucket_resource = s3.Bucket(bucket)
    s3_objects_list = (
        s3_bucket_resource.objects.filter(Prefix=prefix)
        if prefix
        else s3_bucket_resource.objects.all()
    )
    for s3_object in s3_objects_list:
        if not s3_object.key.endswith(
            '/'
        ):  # prevent empty directories from being considered as files
            s3_files_list.append(s3_object.key)
    return s3_files_list


@s3_safe_execution
def copy_s3(bucket: str, key: str, new_bucket: str, new_key: str) -> None:
    if not (
        bucket == new_bucket and key == new_key
    ):  # if both bucket and key are the same, then it's the same file => copying the file to the same location causes an error
        s3.meta.client.copy(
            {'Bucket': bucket, 'Key': key}, new_bucket, new_key
        )  # copy only if either bucket or key is different


@s3_safe_execution
def move_s3(
    bucket: str, key: str, new_bucket: str, new_key: str
) -> None:  # Cannot move file using boto3, so copy then delete
    if not (
        bucket == new_bucket and key == new_key
    ):  # if both bucket and key are the same, then it's the same file => copying the file to the same location causes an error
        s3.meta.client.copy(
            {'Bucket': bucket, 'Key': key}, new_bucket, new_key
        )  # copy only if either bucket or key is different
        s3.Object(bucket, key).delete()  # if copied, delete original


# endregion

# region Redshift


"""
# Psycopg Tips
# Utilize mogrify() as it assembles the final sql statement that will be attempted to be excecuted
#        Good for debugging and logging
# Server-side (default) cursor does not allow for parameters to be inserted into non SELECT and UPDATE statements
# Lists should be handled as = ANY(%(list)s) instead of IN %(list)s
"""


def is_nan(value: typing.Any):
    if isinstance(value, float):
        return numpy.isnan(value)
    elif isinstance(value, str):
        return value.lower() == 'nan'
    return False


def handle_postgres_exception(
    func_name: str,
    executable_string: psycopg.sql.SQL,
    message: str,
    inserted_params: typing.Any = None,
) -> None:
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        with psycopg.ClientCursor(conn) as ccur:
            sql_string = (
                ccur.mogrify(executable_string, inserted_params)
                if inserted_params
                else ccur.mogrify(executable_string)
            )
            logger.error(
                message,
                extra={
                    'additional_details': sql_string,
                    'route_section': func_name,
                },
            )


def postgres_safe_execution(
    func: typing.Callable[P, T],
) -> typing.Callable[P, T]:
    @wraps(func)
    def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        func_name = func.__name__
        # TBD: Get function variables from calling function
        # "executable_string" and "inserted_params"
        # to log the SQL statement and parameters
        executable_string = psycopg.sql.SQL('''''')  # Placeholder
        inserted_params = None  # Placeholder
        try:
            return func(*args, **kwargs)
        except psycopg.errors.ConnectionException as e:
            handle_postgres_exception(
                func_name,
                executable_string,
                f'Unable to connect to Postgres: {e}',
                inserted_params,
            )
            return typing.cast(T, None)
        except psycopg.errors.InFailedSqlTransaction as e:
            handle_postgres_exception(
                func_name,
                executable_string,
                f'Failed to execute SQL transaction due to an error: {e}',
                inserted_params,
            )
            return typing.cast(T, None)
        except psycopg.errors.InternalError as e:
            handle_postgres_exception(
                func_name,
                executable_string,
                f'The database encountered an internal error: {e}',
                inserted_params,
            )
            return typing.cast(T, None)
        except psycopg.errors.DataError as e:
            handle_postgres_exception(
                func_name,
                executable_string,
                f'There was a problem with the data: {e}',
                inserted_params,
            )
            return typing.cast(T, None)
        except psycopg.errors.UndefinedTable as e:
            handle_postgres_exception(
                func_name,
                executable_string,
                f'The table you are trying to query does not exist: {e}',
                inserted_params,
            )
            return typing.cast(T, None)
        except psycopg.errors.UndefinedColumn as e:
            handle_postgres_exception(
                func_name,
                executable_string,
                f'The column you are trying to query does not exist: {e}',
                inserted_params,
            )
            return typing.cast(T, None)
        except Exception as e:
            handle_postgres_exception(
                func_name,
                executable_string,
                f'An unexpected error occurred: {e}',
                inserted_params,
            )
            return typing.cast(T, None)

    return wrapper


@postgres_safe_execution
def get_postgres_by_gain_id(
    sql_string: typing_extensions.LiteralString,
    table: str,
    gainids: set[str | None],
) -> pd.DataFrame:
    global dbname
    result = pd.DataFrame()
    gainids = set(gainids)
    gainids_batches, gainids_batch, batch_size = ([], set(), 500)
    for gainid in gainids:
        if len(gainids_batch) < batch_size:
            gainids_batch.add(gainid)
        else:
            gainids_batches.append(gainids_batch)
            gainids_batch = set()
            gainids_batch.add(gainid)
    gainids_batches.append(gainids_batch)  # to get the last batch
    for _, gainids_batch in enumerate(gainids_batches):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates = []
            for gainid in gainids_batch:
                intermediate_string = ''' (GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(psycopg.sql.Literal(gainid))
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            if type(gainids_batch) is dict:
                gainids_batch = list(gainids_batch.values())
            if len(gainids_batch) == 0:
                logger.error(
                    f'The length of redshift_ids_batch is 0 for {table}'
                )
                continue
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(table.lower()),
                formatted_string,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                data = cur.fetchall()
                if cur.description:
                    columns = [desc[0] for desc in cur.description]
                    result = pd.concat(
                        [result, pd.DataFrame(data, columns=columns)]
                    )
    return result


def get_column_types(
    connection: psycopg.Connection[typing.Any], table_name: str
):
    query = '''
    SELECT column_name, data_type
    FROM information_schema.columns
    WHERE table_name = %s
    ORDER BY ordinal_position;
    '''
    with connection.cursor() as cursor:
        cursor.execute(query, (table_name,))
        # Return a dictionary of column names mapped to their types
        return {row[0]: row[1] for row in cursor.fetchall()}


def preprocess_row(
    row_dict: dict[str, typing.Any], column_types: dict[str, str]
):
    processed_row = []
    empty_string_to_none_keys = [
        'plaintiffid',
        'medicalfacilityid',
        'lawfirmid',
        'legalpersonnelid',
        'intakeid',
        'insuranceid',
        'lienid',
        'disbursalid',
        'billingid',
        'chargeid',
        'transactionid',
        'fileid',
        'surgeryid',
        'attorneyid',
        'paralegalid',
        'casemanagerid',
        'cocounselid',
        'coparalegalid',
        'cocasemanagerid',
        'parentid',
    ]

    for column_name, value in row_dict.items():
        value = (
            value.strip() if value else value
        )  # Ensure value is stripped if not empty

        if column_name in empty_string_to_none_keys and value == "":
            processed_row.append(None)
        elif (
            column_name in column_types
            and not value
            and column_types[column_name] == "boolean"
        ):
            processed_row.append(False)
        elif (
            column_name in column_types
            and not value
            and column_types[column_name]
            in ("date", "timestamp without time zone", "numeric", "integer")
        ):
            processed_row.append(None)
        else:
            # Keep the value as-is (stripped or unchanged)
            processed_row.append(value)

    return processed_row


@postgres_safe_execution
def get_version(
    conn: psycopg.Connection[typing.Any],
) -> typing.Literal["Redshift", "PostgreSQL"] | None:
    cursor = conn.cursor()

    cursor.execute("SELECT version();")
    result = cursor.fetchone()
    if result:
        version_info = result[0]
        if "Redshift" in version_info:
            return 'Redshift'
        elif "PostgreSQL" in version_info:
            return 'PostgreSQL'


def verify_is_postgres(conn: psycopg.Connection[typing.Any]) -> bool:
    version_info = get_version(conn)
    return version_info == 'PostgreSQL'


def s3_to_postgres(
    bucket: str, directory: str, file: str, canonical_object: str
) -> None:
    global dbname
    request_context = context.request_context.get()
    route = request_context.get('route')
    assert route is not None, 'Route not passed'
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        inserted_params = {}
        update_route_canonical_columns = []
        """
        # Use temporary staging table
        # staging table gets populated with newer data
        # previously existing rows are deleted from original table (found using unique gainid)
        # then staging table rows get inserted, "updating" the table with newer rows (and also inserting newer rows)
        # handles both delta changes and multiple source systems
        """
        sql_string_copy_s3 = '''
            COPY {} ({columns})
            FROM STDIN;
        '''
        sql_string_update_table = '''
            UPDATE {}
            SET {}
            FROM
                {}
            WHERE
                {}.gainid = {}.gainid;
        '''
        sql_string_insert_table = '''
            INSERT INTO {} ({columns})
            SELECT {}
            FROM {}
                LEFT JOIN {} ON {}.gainid = {}.gainid
            WHERE {}.gainid IS NULL;
        '''

        filename = local_operations.get_aws_name(file)
        inserted_params['s3_string'] = f's3://{bucket}/{directory}/{filename}'
        column_list = local_operations.get_canonical_object_column_list(
            canonical_object
        )
        canonical_columns = psycopg.sql.SQL(''' , ''').join(
            psycopg.sql.Identifier(column.lower()) for column in column_list
        )
        try:
            update_route_canonical_columns = (
                canonical_update_columns[route][canonical_object]
                if route in canonical_update_columns
                else [column.lower() for column in column_list]
            )
        except Exception:
            logger.error(
                f'Canonical object {canonical_object} not found in canonical_update_columns.',
                extra={
                    'additional_details': canonical_object,
                },
            )
        with conn.cursor() as cur:  # uses server-side cursor by default
            with psycopg.ClientCursor(
                conn
            ) as ccur:  # opens client cursor to allow parameters to be added into COPY statement
                # saves value of assembled sql statement since client cursor is already open
                # Drop any previous dummy canonical_staging tables
                executable_string = psycopg.sql.SQL(
                    '''DROP TABLE IF EXISTS {};'''
                ).format(
                    psycopg.sql.Identifier(dbname),
                    psycopg.sql.Identifier(f'{canonical_object}_staging'),
                )
                cur.execute(executable_string)
                # Create a new dummy canonical_staging table
                executable_string = psycopg.sql.SQL(
                    "DROP TABLE IF EXISTS {}; CREATE TABLE {}(LIKE {});"
                ).format(
                    psycopg.sql.Identifier(f'{canonical_object}_staging'),
                    psycopg.sql.Identifier(f'{canonical_object}_staging'),
                    psycopg.sql.Identifier(canonical_object),
                )

                cur.execute(executable_string)

                # Copy data from S3 to the dummy canonical_staging table
                filename = os.path.basename(inserted_params["s3_string"])

                executable_string = psycopg.sql.SQL(sql_string_copy_s3).format(
                    psycopg.sql.Identifier(f'{canonical_object}_staging'),
                    columns=canonical_columns,
                )

                column_types = get_column_types(
                    conn, f'{canonical_object}_staging'
                )

                with ccur.copy(executable_string) as copy:
                    with open(
                        f'integration/integration_code/{filename}', "r"
                    ) as file_to_load:
                        reader = csv.reader(
                            file_to_load
                        )  # This handles commas inside quotes automatically

                        # Read the header line to get the file's column order
                        header = next(reader)  # Read the header row

                        for _, row in enumerate(reader):
                            # Map the row values to column names
                            row_dict = dict(
                                zip(header, row)
                            )  # Map values to headers

                            # Preprocess the row while maintaining original order
                            processed_row = preprocess_row(
                                row_dict, column_types
                            )

                            if row_dict.get('modifieddatetime') == 'True':
                                raise

                            copy.write_row(processed_row)

                # Want seperate transactions for update and insert for better data integrity and easier rollback
                with conn.transaction():  # opens transaction
                    set_clauses = typing.cast(
                        typing_extensions.LiteralString,
                        ''', '''.join(
                            [
                                f'''{col} = {canonical_object}_staging.{col}'''
                                for col in update_route_canonical_columns
                                if col != 'gainid'
                            ]
                        ),
                    )
                    # Update the original table with the new data from the dummy canonical_staging table
                    executable_string = psycopg.sql.SQL(
                        sql_string_update_table
                    ).format(
                        psycopg.sql.Identifier(canonical_object),
                        psycopg.sql.SQL(set_clauses),
                        psycopg.sql.Identifier(f'{canonical_object}_staging'),
                        psycopg.sql.Identifier(canonical_object),
                        psycopg.sql.Identifier(f'{canonical_object}_staging'),
                    )
                    cur.execute(executable_string)
                with conn.transaction():  # opens transaction
                    staging_columns = ''', '''.join(
                        [
                            f'''{canonical_object}_staging.{col}'''
                            for col in column_list
                        ]
                    )
                    # Insert new data from the dummy canonical_staging table into the original table
                    executable_string = psycopg.sql.SQL(
                        sql_string_insert_table
                    ).format(
                        psycopg.sql.Identifier(canonical_object),
                        psycopg.sql.SQL(
                            typing.cast(
                                typing_extensions.LiteralString,
                                staging_columns,
                            )
                        ),
                        psycopg.sql.Identifier(f'{canonical_object}_staging'),
                        psycopg.sql.Identifier(canonical_object),
                        psycopg.sql.Identifier(canonical_object),
                        psycopg.sql.Identifier(f'{canonical_object}_staging'),
                        psycopg.sql.Identifier(canonical_object),
                        columns=canonical_columns,
                    )
                    cur.execute(executable_string)
                # Drop the dummy canonical_staging table as a final clean up
                executable_string = psycopg.sql.SQL('DROP TABLE {}').format(
                    psycopg.sql.Identifier(f'{canonical_object}_staging'),
                )
                cur.execute(executable_string)


@postgres_safe_execution
def query_postgres(
    mappings: typing.Optional[list[str] | set[str]] = None,
    relevant_only: bool = True,
    timestamp: typing.Optional[datetime.datetime] = None,
    id_subset: list[str] | list[None] | None = None,
    for_delete: bool = False,
    for_delete_prevent_override: bool = False,
    for_treatment_complete: bool = False,
    update_settled_flag: typing.Optional[str] = None,
) -> dict[str, pd.DataFrame]:
    global dbname

    if not mappings:
        # Use this for initialization of mutable sequences instead of parameter default value
        mappings = local_operations.canonical_objects
        # See more here: https://stackoverflow.com/questions/366422/how-can-i-avoid-issues-caused-by-pythons-early-bound-default-parameters-e-g-m

    redshift_data = {}

    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        if id_subset is not None and len(id_subset) == 0:
            # assumption: if non-null empty list is passed in, list was deliberately passed in empty meaning wants to pull from blank subset
            return redshift_data

        for mapping in mappings:
            inserted_params = {}
            sql_string = '''
                SELECT {columns}
                FROM {}
                WHERE
            '''

            if for_delete_prevent_override:
                sql_string += (
                    ''' ToDelete = TRUE AND DeletePreventOverride = TRUE '''
                )
            elif for_delete:
                sql_string += (
                    ''' ToDelete = TRUE AND DeletePreventOverride = FALSE '''
                )
            elif update_settled_flag is not None:
                sql_string += ''' ToDelete = FALSE and Status = %(update_settled_status)s '''
            else:
                if mapping in settleable_canonical_objects:
                    sql_string += (
                        ''' ToDelete = FALSE and Status != 'Settled' '''
                    )
                else:
                    sql_string += ''' ToDelete = FALSE '''

            if relevant_only:
                sql_string += '''
                    AND RelevantToGain = TRUE
                '''
            if for_treatment_complete:
                sql_string += '''
                    AND TreatmentCompleted = TRUE
                '''
            if timestamp:
                sql_string += '''
                    AND ModifiedDateTime > %(time)s
                '''
                inserted_params['time'] = timestamp
            if update_settled_flag is not None:
                inserted_params['update_settled_status'] = update_settled_flag

            column_list = local_operations.get_canonical_object_column_list(
                mapping
            )
            columns = psycopg.sql.SQL(' , ').join(
                psycopg.sql.SQL(
                    typing.cast(typing_extensions.LiteralString, column)
                )
                for column in column_list
            )

            if id_subset:  # tuple key unpacked
                intermediates = []
                for gainid in id_subset:
                    intermediate_string = ''' (GainId = {}) '''
                    intermediate_string = psycopg.sql.SQL(
                        intermediate_string
                    ).format(psycopg.sql.Literal(gainid))
                    intermediates.append(intermediate_string)
                formatted_string = psycopg.sql.SQL(''' OR ''').join(
                    intermediates
                )
                sql_string += ''' AND ({}) '''
                sql_string += ''';'''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier(mapping),
                    formatted_string,
                    columns=columns,
                )
            else:
                sql_string += ''';'''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier(mapping),
                    columns=columns,
                )

            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string, inserted_params)
                temp = cur.fetchall()

            # Convert result to Pandas DataFrame
            redshift_data[mapping] = pd.DataFrame(temp, columns=column_list)

    return redshift_data


@postgres_safe_execution
def query_postgres_sf_map(
    mappings: typing.Optional[list[str]] = None,
    id_subset: typing.Optional[list[str]] = None,
    get_external_id: bool = False,
) -> tuple[dict[str, dict[str, str]], dict[str, dict[str, str]]]:
    global dbname
    if (
        not mappings
    ):  # Use this for initialization of mutable sequences instead of parameter default value
        mappings = (
            local_operations.canonical_objects
        )  # See more here: https://stackoverflow.com/questions/366422/how-can-i-avoid-issues-caused-by-pythons-early-bound-default-parameters-e-g-m
    redshift_data = {}
    redshift_external_id_data = {}
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        if (
            id_subset is not None and len(id_subset) == 0
        ):  # assumption: if non-null empty list is passed in, list was deliberately passed in empty meaning wants to pull from blank subset
            return redshift_data, {}
        for mapping in mappings:
            inserted_params = {}
            if get_external_id:
                sql_string = '''
                    SELECT gainid as GainId, salesforce_id as SalesforceId, salesforce_external_container_id as SalesforceExternalIdContainer
                    FROM {} WHERE canonical_object = {}
                '''
            else:
                sql_string = '''
                    SELECT gainid as GainId, salesforce_id as SalesforceId
                    FROM {} WHERE canonical_object = {}
                '''
            if id_subset:
                intermediates = []
                for gainid in id_subset:  # tuple key unpacked
                    intermediate_string = ''' (gainid = {}) '''
                    intermediate_string = psycopg.sql.SQL(
                        intermediate_string
                    ).format(psycopg.sql.Literal(gainid))
                    intermediates.append(intermediate_string)
                formatted_string = psycopg.sql.SQL(''' OR ''').join(
                    intermediates
                )
                sql_string += ''' AND ({}) '''
                sql_string += ''';'''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier('gain_id_map'),
                    psycopg.sql.Literal(mapping),
                    formatted_string,
                )
            else:
                sql_string += ''';'''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier('gain_id_map'),
                    psycopg.sql.Literal(mapping),
                )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string, inserted_params)
                temp = cur.fetchall()
            if not get_external_id:
                redshift_data[mapping] = {
                    gainid: salesforce_id
                    for gainid, salesforce_id in temp
                    if salesforce_id is not None
                }
            else:
                redshift_data[mapping] = {
                    gainid: salesforce_id
                    for gainid, salesforce_id, _ in temp
                    if salesforce_id is not None
                }
                redshift_external_id_data[mapping] = {
                    gainid: external_id
                    for gainid, _, external_id in temp
                    if external_id is not None
                }
    if get_external_id:
        return redshift_data, redshift_external_id_data
    return redshift_data, {}


@postgres_safe_execution
def insert_postgres_sf_map(
    table: str,
    redshift_sf_map: list[tuple[str, str, datetime.datetime]],
) -> None:
    global dbname
    (
        redshift_sf_insert_map_batches,
        redshift_sf_insert_map_batch,
        batch_size,
    ) = (
        [],
        [],
        500,
    )  # query in batches of 100 ids to prevent timout issues
    for i in redshift_sf_map:
        if len(redshift_sf_insert_map_batch) < batch_size:
            redshift_sf_insert_map_batch.append(i)
        else:
            redshift_sf_insert_map_batches.append(redshift_sf_insert_map_batch)
            redshift_sf_insert_map_batch = [i]
    redshift_sf_insert_map_batches.append(
        redshift_sf_insert_map_batch
    )  # to get the last batch
    for _, redshift_sf_insert_map_batch in enumerate(
        redshift_sf_insert_map_batches
    ):
        with psycopg.connect(get_postgres_conn_string(), autocommit=True) as _:
            gainid_records = []
            salesforce_id_records = []
            for (
                gainid,
                salesforce_id,
                create_date_time,
            ) in redshift_sf_insert_map_batch:
                gainid_records.append(
                    id_record.IdRecord(
                        gainid,
                        datetime.datetime.now(),
                        datetime.datetime.now(),
                    )
                )
                salesforce_id_records.append(
                    id_record.IdRecord(salesforce_id, create_date_time)
                )
            update_gainid_records_by_sf_ids(
                gainid_records, salesforce_id_records
            )
    return


@postgres_safe_execution
def delete_postgres_sf_map(
    table: str, redshift_sf_insert_map: list[str]
) -> None:
    global dbname
    redshift_sf_insert_map_batches = []
    redshift_sf_insert_map_batch = []
    batch_size = 500  # query in batches of 100 ids to prevent timout issues
    for i in redshift_sf_insert_map:
        if len(redshift_sf_insert_map_batch) < batch_size:
            redshift_sf_insert_map_batch.append(i)
        else:
            redshift_sf_insert_map_batches.append(redshift_sf_insert_map_batch)
            redshift_sf_insert_map_batch = [i]
    redshift_sf_insert_map_batches.append(
        redshift_sf_insert_map_batch
    )  # to get the last batch
    for batch_id, redshift_sf_insert_map_batch in enumerate(
        redshift_sf_insert_map_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates = []
            for gainid in redshift_sf_insert_map_batches:
                intermediate_string = ''' (GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(psycopg.sql.Literal(gainid))
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            if len(redshift_sf_insert_map_batch) > 0:
                sql_string = '''
                    UPDATE {}
                    SET salesforce_id = NULL,
                        salesforce_modifieddatetime = NULL,
                        salesforce_createddatetime = NULL,
                        salesforce_external_container_id = NULL,
                        salesforce_external_container_createddatetime = NULL,
                        salesforce_external_container_modifieddatetime = NULL
                    WHERE
                        {}
                    ;
                '''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier('gain_id_map'),
                    formatted_string,
                )
            else:
                logger.error(
                    f'The length of redshift_sf_ids_update_batch is 0 for {table}.'
                )
                continue
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated ids for batch {batch_id+1} of {len(redshift_sf_insert_map_batches)}.'
                )
    return


@postgres_safe_execution
def update_postgres_sf_modifieddatetime(
    table: str,
    redshift_sf_ids_update: dict[str, datetime.datetime],
    curr_timestamp: datetime.datetime,
) -> None:
    global dbname
    redshift_sf_ids_update_batches = []
    redshift_sf_ids_update_batch = {}
    batch_size = 500  # update in batches of 500 ids to prevent timout issues
    for k, v in redshift_sf_ids_update.items():
        if len(redshift_sf_ids_update_batch) < batch_size:
            redshift_sf_ids_update_batch[k] = v
        else:
            redshift_sf_ids_update_batches.append(redshift_sf_ids_update_batch)
            redshift_sf_ids_update_batch = {k: v}
    redshift_sf_ids_update_batches.append(
        redshift_sf_ids_update_batch
    )  # to get the last batch
    curr_timestamp = curr_timestamp.strftime('%Y-%m-%d %H:%M:%S')
    for batch_id, redshift_sf_ids_update_batch in enumerate(
        redshift_sf_ids_update_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates, inserted_params = [], {}
            for gainid in redshift_sf_ids_update_batch:
                intermediate_string = ''' (GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(psycopg.sql.Literal(gainid))
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            if type(redshift_sf_ids_update_batch) is dict:
                redshift_sf_ids_update_batch = list(
                    redshift_sf_ids_update_batch.values()
                )
            if len(redshift_sf_ids_update_batch) == 0:
                logger.info(
                    f'The length of redshift_sf_ids_update_batch is 0 for {table}',
                )
                continue
            sql_string = '''
                UPDATE {}
                SET ModifiedDateTime = %(time)s
                WHERE
                    RelevantToGain = TRUE
                    AND (
                        {}
                    )
                ;
            '''
            inserted_params['time'] = (
                curr_timestamp  # for modifieddatetime, update value not used; curr_timestamp used as value to set instead
            )
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(table.lower()),
                formatted_string,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string, inserted_params)
                logger.info(
                    f'Updated modifieddatetime for batch {batch_id+1} of {len(redshift_sf_ids_update_batches)}.',
                )
    return


def get_postgres_createdatetime(
    table: str,
    gainids: set[str | None],
) -> pd.DataFrame:
    return get_postgres_by_gain_id(
        '''
                SELECT
                    GainId,
                    CreateDateTime
                FROM
                    {}
                WHERE
                    RelevantToGain = TRUE
                    AND (
                        {}
                    )
                ;
            ''',
        table,
        gainids,
    )


def get_postgres_sourcecreatedatetime(
    gainids: set[str | None],
    sourcename: str,
) -> pd.DataFrame:
    if sourcename == 'ATI':
        sql_string = '''
            SELECT
                GainId,
                ATI_CreatedDateTime AS SourceCreateDateTime
            FROM
                {}
            WHERE
            (
                {}
            )
            ;
        '''
    elif sourcename == 'Jopari':
        sql_string = '''
            SELECT
                GainId,
                Jopari_CreatedDateTime AS SourceCreateDateTime
            FROM
                {}
            WHERE
                RelevantToGain = TRUE
                AND (
                    {}
                )
            ;
        '''
    elif sourcename == 'FileVine':
        sql_string = '''
            SELECT
                GainId,
                FileVine_CreatedDateTime AS SourceCreateDateTime
            FROM
                {}
            WHERE
                RelevantToGain = TRUE
                AND (
                    {}
                )
            ;
        '''
    else:
        return pd.DataFrame()

    return get_postgres_by_gain_id(
        sql_string,
        'gain_id_map',
        gainids,
    )


@postgres_safe_execution
def update_postgres_charges_from_non_reversal_transactions(
    df_charges_to_update: pd.DataFrame,
    timestamp: datetime.datetime,
) -> None:
    global dbname
    batch_size = 500  # batches of 500 ids to prevent timout issues
    df_charges_to_update_batches = [
        df_charges_to_update[i : i + batch_size]
        for i in range(0, df_charges_to_update.shape[0], batch_size)
    ]
    when_intermediate_string = ''' WHEN GainId = {} THEN {} '''
    where_intermediate_string = ''' (GainId = {}) '''
    for batch_id, df_charges_to_update_batch in enumerate(
        df_charges_to_update_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates_non_gain_payments = []
            intermediates_non_gain_adjustments = []
            intermediates_gain_pre_negotiation_payments = []
            intermediates_gain_pre_negotiation_adjustments = []
            intermediates_deductibles = []
            intermediates_coinsurances = []
            intermediates_copayments = []
            intermediates_balances = []
            intermediates_where_conditions = []
            # itertuples is faster than iterrows since itertuples does not box the data into a Series whereas iterrows does
            # https://stackoverflow.com/questions/24870953/does-pandas-iterrows-have-performance-issues
            for (
                _,
                gainid,
                nongainamountpaidtoprovider,
                nongainadjustment,
                gainprenegotiationamountpaidtoprovider,
                gainprenegotiationadjustment,
                deductible,
                coinsurance,
                copayment,
                balance,
            ) in df_charges_to_update_batch.itertuples():
                intermediates_non_gain_payments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if nongainamountpaidtoprovider is None
                            else psycopg.sql.Literal(
                                nongainamountpaidtoprovider
                            )
                        ),
                    )
                )
                intermediates_non_gain_adjustments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if nongainadjustment is None
                            else psycopg.sql.Literal(nongainadjustment)
                        ),
                    )
                )
                intermediates_gain_pre_negotiation_payments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if gainprenegotiationamountpaidtoprovider is None
                            else psycopg.sql.Literal(
                                gainprenegotiationamountpaidtoprovider
                            )
                        ),
                    )
                )
                intermediates_gain_pre_negotiation_adjustments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if gainprenegotiationadjustment is None
                            else psycopg.sql.Literal(
                                gainprenegotiationadjustment
                            )
                        ),
                    )
                )
                intermediates_deductibles.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if deductible is None
                            else psycopg.sql.Literal(deductible)
                        ),
                    )
                )
                intermediates_coinsurances.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if coinsurance is None
                            else psycopg.sql.Literal(coinsurance)
                        ),
                    )
                )
                intermediates_copayments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if copayment is None
                            else psycopg.sql.Literal(copayment)
                        ),
                    )
                )
                intermediates_balances.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if balance is None
                            else psycopg.sql.Literal(balance)
                        ),
                    )
                )
                intermediates_where_conditions.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )
            i_formatted_string_payments = psycopg.sql.SQL(''' ''').join(
                intermediates_non_gain_payments
            )
            i_formatted_string_adjustments = psycopg.sql.SQL(''' ''').join(
                intermediates_non_gain_adjustments
            )
            i_formatted_string_gain_pre_negotiation_payments = psycopg.sql.SQL(
                ''' '''
            ).join(intermediates_gain_pre_negotiation_payments)
            i_formatted_string_gain_pre_negotiation_adjustments = (
                psycopg.sql.SQL(''' ''').join(
                    intermediates_gain_pre_negotiation_adjustments
                )
            )
            i_formatted_string_deductibles = psycopg.sql.SQL(''' ''').join(
                intermediates_deductibles
            )
            i_formatted_string_coinsurances = psycopg.sql.SQL(''' ''').join(
                intermediates_coinsurances
            )
            i_formatted_string_copayments = psycopg.sql.SQL(''' ''').join(
                intermediates_copayments
            )
            i_formatted_string_balances = psycopg.sql.SQL(''' ''').join(
                intermediates_balances
            )
            i_formatted_string_where = psycopg.sql.SQL(''' OR ''').join(
                intermediates_where_conditions
            )
            sql_string = '''
                UPDATE Charges
                SET
                    NonGainAmountPaidToProvider = CASE {} END,
                    NonGainAdjustment = CASE {} END,
                    GainPreNegotiationAmountPaidToProvider = CASE {} END,
                    GainPreNegotiationAdjustment = CASE {} END,
                    Deductible = CASE {} END,
                    Coinsurance = CASE {} END,
                    Copayment = CASE {} END,
                    Balance = CASE {} END,
                    ModifiedDateTime = {}
                WHERE
                    RelevantToGain = TRUE
                    AND ({});
            '''
            if len(df_charges_to_update_batch) == 0:
                logger.error(
                    'The length of df_charges_type_batch is 0.',
                )
                continue
            executable_string = psycopg.sql.SQL(sql_string).format(
                i_formatted_string_payments,
                i_formatted_string_adjustments,
                i_formatted_string_gain_pre_negotiation_payments,
                i_formatted_string_gain_pre_negotiation_adjustments,
                i_formatted_string_deductibles,
                i_formatted_string_coinsurances,
                i_formatted_string_copayments,
                i_formatted_string_balances,
                timestamp,
                i_formatted_string_where,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated charges from transactions for batch {batch_id+1} of {len(df_charges_to_update_batches)} ({len(df_charges_to_update_batch)}).'
                )


@postgres_safe_execution
def update_postgres_charges_from_complete_reversal_transactions(
    df_charges_to_update: pd.Series,  # pyright: ignore[reportMissingTypeArgument]
    timestamp: datetime.datetime,
) -> None:
    global dbname
    batch_size = 500  # batches of 500 ids to prevent timout issues
    df_charges_to_update_batches = [
        df_charges_to_update[i : i + batch_size]
        for i in range(0, df_charges_to_update.shape[0], batch_size)
    ]
    where_intermediate_string = ''' (GainId = {}) '''
    for batch_id, df_charges_to_update_batch in enumerate(
        df_charges_to_update_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates_where_conditions = []
            # iteritems used for single column Series
            for (
                _,
                gainid,
            ) in df_charges_to_update_batch.items():
                intermediates_where_conditions.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )
            i_formatted_string_where = psycopg.sql.SQL(''' OR ''').join(
                intermediates_where_conditions
            )
            sql_string = '''
                    UPDATE Charges
                    SET
                        Amount = 0,
                        NonGainAmountPaidToProvider = 0,
                        NonGainAdjustment = 0,
                        GainPreNegotiationAmountPaidToProvider = 0,
                        GainPreNegotiationAdjustment = 0,
                        Balance = 0,
                        ModifiedDateTime = {}
                    WHERE
                        RelevantToGain = TRUE
                        AND ({});
                '''
            if len(df_charges_to_update_batch) == 0:
                logger.error(
                    'The length of df_charges_type_batch is 0.',
                )
                continue
            executable_string = psycopg.sql.SQL(sql_string).format(
                timestamp,
                i_formatted_string_where,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated charges from transactions for batch {batch_id+1} of {len(df_charges_to_update_batches)} ({len(df_charges_to_update_batch)}).'
                )


@postgres_safe_execution
def update_postgres_billings_from_charges(
    df_billings_to_update: pd.DataFrame,
    timestamp: datetime.datetime,
) -> None:
    global dbname
    batch_size = 500  # batches of 500 ids to prevent timout issues
    df_billings_to_update_batches = [
        df_billings_to_update[i : i + batch_size]
        for i in range(0, df_billings_to_update.shape[0], batch_size)
    ]
    when_intermediate_string = ''' WHEN GainId = {} THEN {} '''
    where_intermediate_string = ''' (GainId = {}) '''
    for batch_id, df_billings_to_update_batch in enumerate(
        df_billings_to_update_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates_total_amounts = []
            intermediates_total_non_gain_payments = []
            intermediates_total_non_gain_adjustments = []
            intermediates_total_gain_pre_negotiation_payments = []
            intermediates_total_gain_pre_negotiation_adjustments = []
            intermediates_total_deductibles = []
            intermediates_total_coinsurances = []
            intermediates_total_copayments = []
            intermediates_total_balances = []
            intermediates_where_conditions = []
            # itertuples is faster than iterrows since itertuples does not box the data into a Series whereas iterrows does
            # https://stackoverflow.com/questions/24870953/does-pandas-iterrows-have-performance-issues
            for (
                _,
                gainid,
                totalamount,
                totalnongainadjustment,
                totalnongainamountpaidtoprovider,
                totalgainprenegotiationadjustment,
                totalgainprenegotiationamountpaidtoprovider,
                totaldeductible,
                totalcoinsurance,
                totalcopayment,
                totalbalance,
            ) in df_billings_to_update_batch.itertuples():
                intermediates_total_amounts.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalamount is None
                            else psycopg.sql.Literal(totalamount)
                        ),
                    )
                )
                intermediates_total_non_gain_adjustments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalnongainadjustment is None
                            else psycopg.sql.Literal(totalnongainadjustment)
                        ),
                    )
                )
                intermediates_total_non_gain_payments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalnongainamountpaidtoprovider is None
                            else psycopg.sql.Literal(
                                totalnongainamountpaidtoprovider
                            )
                        ),
                    )
                )
                intermediates_total_gain_pre_negotiation_adjustments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalgainprenegotiationadjustment is None
                            else psycopg.sql.Literal(
                                totalgainprenegotiationadjustment
                            )
                        ),
                    )
                )
                intermediates_total_gain_pre_negotiation_payments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalgainprenegotiationamountpaidtoprovider
                            is None
                            else psycopg.sql.Literal(
                                totalgainprenegotiationamountpaidtoprovider
                            )
                        ),
                    )
                )
                intermediates_total_deductibles.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totaldeductible is None
                            else psycopg.sql.Literal(totaldeductible)
                        ),
                    )
                )
                intermediates_total_coinsurances.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalcoinsurance is None
                            else psycopg.sql.Literal(totalcoinsurance)
                        ),
                    )
                )
                intermediates_total_copayments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalcopayment is None
                            else psycopg.sql.Literal(totalcopayment)
                        ),
                    )
                )
                intermediates_total_balances.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if totalbalance is None
                            else psycopg.sql.Literal(totalbalance)
                        ),
                    )
                )
                intermediates_where_conditions.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )
            i_formatted_string_amounts = psycopg.sql.SQL(''' ''').join(
                intermediates_total_amounts
            )
            i_formatted_string_payments = psycopg.sql.SQL(''' ''').join(
                intermediates_total_non_gain_payments
            )
            i_formatted_string_adjustments = psycopg.sql.SQL(''' ''').join(
                intermediates_total_non_gain_adjustments
            )
            i_formatted_string_gain_pre_negotiation_payments = psycopg.sql.SQL(
                ''' '''
            ).join(intermediates_total_gain_pre_negotiation_payments)
            i_formatted_string_gain_pre_negotiation_adjustments = (
                psycopg.sql.SQL(''' ''').join(
                    intermediates_total_gain_pre_negotiation_adjustments
                )
            )
            i_formatted_string_deductibles = psycopg.sql.SQL(''' ''').join(
                intermediates_total_deductibles
            )
            i_formatted_string_coinsurances = psycopg.sql.SQL(''' ''').join(
                intermediates_total_coinsurances
            )
            i_formatted_string_copayments = psycopg.sql.SQL(''' ''').join(
                intermediates_total_copayments
            )
            i_formatted_string_balances = psycopg.sql.SQL(''' ''').join(
                intermediates_total_balances
            )
            i_formatted_string_where = psycopg.sql.SQL(''' OR ''').join(
                intermediates_where_conditions
            )
            sql_string = '''
                UPDATE Billings
                SET
                    TotalAmount = CASE {} END,
                    TotalNonGainAdjustment = CASE {} END,
                    TotalNonGainAmountPaidToProvider = CASE {} END,
                    TotalGainPreNegotiationAdjustment = CASE {} END,
                    TotalGainPreNegotiationAmountPaidToProvider = CASE {} END,
                    TotalDeductible = CASE {} END,
                    TotalCoinsurance = CASE {} END,
                    TotalCopayment = CASE {} END,
                    TotalBalance = CASE {} END,
                    ModifiedDateTime = {}
                WHERE
                    RelevantToGain = TRUE
                    AND ({});
            '''
            if len(df_billings_to_update_batch) == 0:
                logger.error(
                    'The length of df_billings_type_batch is 0.',
                )
                continue
            executable_string = psycopg.sql.SQL(sql_string).format(
                i_formatted_string_amounts,
                i_formatted_string_adjustments,
                i_formatted_string_payments,
                i_formatted_string_gain_pre_negotiation_adjustments,
                i_formatted_string_gain_pre_negotiation_payments,
                i_formatted_string_deductibles,
                i_formatted_string_coinsurances,
                i_formatted_string_copayments,
                i_formatted_string_balances,
                timestamp,
                i_formatted_string_where,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated billings from charges for batch {batch_id+1} of {len(df_billings_to_update_batches)} ({len(df_billings_to_update_batch)}).'
                )


@postgres_safe_execution
def update_postgres_cases_from_billings(
    df_cases_to_update: pd.DataFrame,
    timestamp: datetime.datetime,
) -> None:
    global dbname
    batch_size = 500  # batches of 500 ids to prevent timout issues
    df_cases_to_update_batches = [
        df_cases_to_update[i : i + batch_size]
        for i in range(0, df_cases_to_update.shape[0], batch_size)
    ]
    when_intermediate_string = ''' WHEN GainId = {} THEN {} '''
    where_intermediate_string = ''' (GainId = {}) '''
    for batch_id, df_cases_to_update_batch in enumerate(
        df_cases_to_update_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates_grand_total_amounts = []
            intermediates_grand_total_non_gain_payments = []
            intermediates_grand_total_non_gain_adjustments = []
            intermediates_grand_total_gain_pre_negotiation_adjustments = []
            intermediates_grand_total_gain_pre_negotiation_payments = []
            intermediates_grand_total_deductibles = []
            intermediates_grand_total_coinsurances = []
            intermediates_grand_total_copayments = []
            intermediates_grand_total_balances = []
            intermediates_where_conditions = []
            # itertuples is faster than iterrows since itertuples does not box the data into a Series whereas iterrows does
            # https://stackoverflow.com/questions/24870953/does-pandas-iterrows-have-performance-issues
            for (
                _,
                gainid,
                grandtotalamount,
                grandtotalnongainadjustment,
                grandtotalnongainamountpaidtoprovider,
                grandtotalgainprenegotiationadjustment,
                grandtotalgainprenegotiationamountpaidtoprovider,
                grandtotaldeductible,
                grandtotalcoinsurance,
                grandtotalcopayment,
                grandtotalbalance,
            ) in df_cases_to_update_batch.itertuples():
                intermediates_grand_total_amounts.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(grandtotalamount)
                            if grandtotalamount is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_non_gain_adjustments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(grandtotalnongainadjustment)
                            if grandtotalnongainadjustment is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_non_gain_payments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(
                                grandtotalnongainamountpaidtoprovider
                            )
                            if grandtotalnongainamountpaidtoprovider
                            is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_gain_pre_negotiation_adjustments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(
                                grandtotalgainprenegotiationadjustment
                            )
                            if grandtotalgainprenegotiationadjustment
                            is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_gain_pre_negotiation_payments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(
                                grandtotalgainprenegotiationamountpaidtoprovider
                            )
                            if grandtotalgainprenegotiationamountpaidtoprovider
                            is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_deductibles.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(grandtotaldeductible)
                            if grandtotaldeductible is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_coinsurances.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(grandtotalcoinsurance)
                            if grandtotalcoinsurance is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_copayments.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(grandtotalcopayment)
                            if grandtotalcopayment is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_grand_total_balances.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.Literal(grandtotalbalance)
                            if grandtotalbalance is not None
                            else psycopg.sql.SQL("NULL::NUMERIC")
                        ),
                    )
                )
                intermediates_where_conditions.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )

            i_formatted_string_amounts = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_amounts
            )
            i_formatted_string_payments = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_non_gain_payments
            )
            i_formatted_string_adjustments = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_non_gain_adjustments
            )
            i_formatted_string_gain_pre_negotiation_adjustments = (
                psycopg.sql.SQL(''' ''').join(
                    intermediates_grand_total_gain_pre_negotiation_adjustments
                )
            )
            i_formatted_string_gain_pre_negotiation_payments = psycopg.sql.SQL(
                ''' '''
            ).join(intermediates_grand_total_gain_pre_negotiation_payments)
            i_formatted_string_deductibles = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_deductibles
            )
            i_formatted_string_coinsurances = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_coinsurances
            )
            i_formatted_string_copayments = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_copayments
            )
            i_formatted_string_balances = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_balances
            )
            i_formatted_string_where = psycopg.sql.SQL(''' OR ''').join(
                intermediates_where_conditions
            )
            sql_string = '''
                UPDATE Cases
                SET
                    GrandTotalAmount = CASE {} END,
                    GrandTotalNonGainAdjustment = CASE {} END,
                    GrandTotalNonGainAmountPaidToProvider = CASE {} END,
                    GrandTotalGainPreNegotiationAdjustment = CASE {} END,
                    GrandTotalGainPreNegotiationAmountPaidToProvider = CASE {} END,
                    GrandTotalDeductible = CASE {} END,
                    GrandTotalCoinsurance = CASE {} END,
                    GrandTotalCopayment = CASE {} END,
                    GrandTotalBalance = CASE {} END,
                    ModifiedDateTime = {}
                WHERE
                    RelevantToGain = TRUE
                    AND ({});
            '''
            if len(df_cases_to_update_batch) == 0:
                logger.error(
                    'The length of df_cases_type_batch is 0.',
                )
                continue
            executable_string = psycopg.sql.SQL(sql_string).format(
                i_formatted_string_amounts,
                i_formatted_string_adjustments,
                i_formatted_string_payments,
                i_formatted_string_gain_pre_negotiation_adjustments,
                i_formatted_string_gain_pre_negotiation_payments,
                i_formatted_string_deductibles,
                i_formatted_string_coinsurances,
                i_formatted_string_copayments,
                i_formatted_string_balances,
                timestamp,
                i_formatted_string_where,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated cases from billings for batch {batch_id+1} of {len(df_cases_to_update_batches)} ({len(df_cases_to_update_batch)}).'
                )


@postgres_safe_execution
def update_postgres_cases_from_insurances(
    df_cases_to_update: pd.DataFrame,
    timestamp: datetime.datetime,
) -> None:
    global dbname
    batch_size = 500  # batches of 500 ids to prevent timout issues
    df_cases_to_update_batches = [
        df_cases_to_update[i : i + batch_size]
        for i in range(0, df_cases_to_update.shape[0], batch_size)
    ]
    when_intermediate_string = ''' WHEN GainId = {} THEN {} '''
    where_intermediate_string = ''' (GainId = {}) '''
    for batch_id, df_cases_to_update_batch in enumerate(
        df_cases_to_update_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates_date_settled = []
            intermediates_grand_total_settlements = []
            intermediates_where_conditions = []
            # itertuples is faster than iterrows since itertuples does not box the data into a Series whereas iterrows does
            # https://stackoverflow.com/questions/24870953/does-pandas-iterrows-have-performance-issues
            for (
                _,
                gainid,
                datesettled,
                grandtotalsettlement,
            ) in df_cases_to_update_batch.itertuples():
                intermediates_date_settled.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        psycopg.sql.Literal(datesettled),
                    )
                )
                intermediates_grand_total_settlements.append(
                    psycopg.sql.SQL(when_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        (
                            psycopg.sql.SQL("NULL::NUMERIC")
                            if grandtotalsettlement is None
                            else psycopg.sql.Literal(grandtotalsettlement)
                        ),
                    )
                )
                intermediates_where_conditions.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )
            formatted_string_datesettled = psycopg.sql.SQL(''' ''').join(
                intermediates_date_settled
            )
            formatted_string_settlementamounts = psycopg.sql.SQL(''' ''').join(
                intermediates_grand_total_settlements
            )
            formatted_string_where = psycopg.sql.SQL(''' OR ''').join(
                intermediates_where_conditions
            )
            sql_string = '''
                UPDATE Cases
                SET
                    DateSettled = CASE {} END,
                    GrandTotalSettlementAmount = CASE {} END,
                    ModifiedDateTime = {}
                WHERE
                    ({});
            '''
            if len(df_cases_to_update_batch) == 0:
                logger.error(
                    'The length of df_cases_type_batch is 0.',
                )
                continue
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string_datesettled,
                formatted_string_settlementamounts,
                timestamp,
                formatted_string_where,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated cases from billings for batch {batch_id+1} of {len(df_cases_to_update_batches)} ({len(df_cases_to_update_batch)}).'
                )


@postgres_safe_execution
def get_postgres_transactions_to_update_charges(
    transactions_id_subset: typing.Optional[list[str]] = None,
) -> pd.DataFrame | None:
    global dbname
    df_data = None
    if transactions_id_subset is None:
        transactions_id_subset = []
    elif (
        transactions_id_subset is not None and len(transactions_id_subset) == 0
    ):  # if id_subset is empty list, we assume we do not want any data
        return df_data
    df_data = pd.DataFrame()
    transaction_id_batches = local_operations.batch_list(
        transactions_id_subset, 5000
    )
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        for transaction_id_batch in transaction_id_batches:
            sql = '''
                SELECT
                    Transaction.GainId TransactionGainId,
                    Transaction.ChargeId ChargeGainId,
                    Charge.Amount ChargeAmount,
                    Transaction.PaymentDateTime TransactionPaymentDateTime,
                    Transaction.Type TransactionType,
                    Transaction.CarrierInsuranceType TransactionCarrierInsuranceType,
                    Transaction.Description TransactionDescription,
                    Transaction.Amount TransactionAmount,
                    Transaction.CARCCode TransactionCARCCode
                FROM
                    Transactions Transaction
                    JOIN Charges Charge ON Charge.GainId = Transaction.ChargeId
                WHERE
                    Transaction.RelevantToGain = TRUE
            '''
            formatted_string = psycopg.sql.SQL('')
            if len(transaction_id_batch) > 0:
                id_filters = []
                for gainid in transaction_id_batch:
                    id_filter = ''' (T.GainId = {}) '''
                    id_filter = psycopg.sql.SQL(id_filter).format(
                        psycopg.sql.Literal(gainid)
                    )
                    id_filters.append(id_filter)
                formatted_string = psycopg.sql.SQL(''' OR ''').join(id_filters)
                sql += '''
                    AND (
                        ChargeId IN (
                            SELECT
                                DISTINCT ChargeId
                            FROM
                                Transactions T
                            WHERE
                                T.RelevantToGain = TRUE
                                AND T.Amount IS NOT NULL
                                AND ({})
                        )
                    )
                '''
            executable_string = psycopg.sql.SQL(sql).format(formatted_string)

            with conn.cursor() as cur:
                cur.execute(executable_string)
                data = cur.fetchall()
            df_batch_data = pd.DataFrame(
                data,
                columns=[
                    'transactiongainid',
                    'chargegainid',
                    'chargeamount',
                    'transactionpaymentdatetime',
                    'transactiontype',
                    'transactioncarrierinsurancetype',
                    'transactiondescription',
                    'transactionamount',
                    'transactioncarccode',
                ],
            )
            df_data = pd.concat([df_data, df_batch_data], ignore_index=True)
    df_data = df_data.drop_duplicates()
    return df_data


@postgres_safe_execution
def get_redshift_transactions_from_update_charges(
    charges_id_subset: typing.Optional[list[str]] = None,
) -> pd.DataFrame | None:
    global dbname
    df_data = None
    if charges_id_subset is None:
        charges_id_subset = []
    elif (
        charges_id_subset is not None and len(charges_id_subset) == 0
    ):  # if id_subset is empty list, we assume we do not want any data
        return df_data
    df_data = pd.DataFrame()
    charge_id_batches = local_operations.batch_list(charges_id_subset, 500)
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        for charge_id_batch in charge_id_batches:
            sql = '''
                SELECT
                    Transaction.ChargeId ChargeGainId,
                    Transaction.GainId TransactionGainId
                FROM
                    Transactions Transaction
                WHERE
                    Transaction.RelevantToGain = TRUE
            '''
            formatted_string = psycopg.sql.SQL('')
            id_filters = []
            for gainid in charge_id_batch:
                id_filter = ''' (Transaction.ChargeId = {}) '''
                id_filter = psycopg.sql.SQL(id_filter).format(
                    psycopg.sql.Literal(gainid)
                )
                id_filters.append(id_filter)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(id_filters)
            sql += '''
                AND (
                    {}
                )
            '''
            executable_string = psycopg.sql.SQL(sql).format(formatted_string)
            with conn.cursor() as cur:
                cur.execute(executable_string)
                data = cur.fetchall()
            df_batch_data = pd.DataFrame(
                data,
                columns=['chargegainid', 'transactiongainid'],
            )
            df_data = pd.concat([df_data, df_batch_data], ignore_index=True)
    df_data = df_data.drop_duplicates()
    return df_data


@postgres_safe_execution
def get_postgres_charges_to_update_billings(
    charges_id_subset: typing.Optional[list[str]] = None,
) -> pd.DataFrame | None:
    global dbname
    df_data = None
    if charges_id_subset is None:
        charges_id_subset = []
    elif (
        charges_id_subset is not None and len(charges_id_subset) == 0
    ):  # if id_subset is empty list, we assume we do not want any data
        return df_data
    df_data = pd.DataFrame()
    charge_id_batches = local_operations.batch_list(charges_id_subset, 5000)
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        for charge_id_batch in charge_id_batches:
            sql = '''
                SELECT
                    Charges.BillingId,
                    SUM(Charges.Amount) TotalAmount,
                    SUM(Charges.NonGainAdjustment) TotalNonGainAdjustment,
                    SUM(Charges.NonGainAmountPaidToProvider) TotalNonGainAmountPaidToProvider,
                    SUM(Charges.GainPreNegotiationAdjustment) TotalGainPreNegotiationAdjustment,
                    SUM(Charges.GainPreNegotiationAmountPaidToProvider) TotalGainPreNegotiationAmountPaidToProvider,
                    SUM(Charges.Deductible) TotalDeductible,
                    SUM(Charges.Coinsurance) TotalCoinsurance,
                    SUM(Charges.Copayment) TotalCopayment
                FROM
                    Charges Charges
                    JOIN Billings Billings ON Billings.GainId = Charges.BillingId
            '''
            formatted_string = psycopg.sql.SQL('')
            if len(charge_id_batch) > 0:
                id_filters = []
                for gainid in charge_id_batch:
                    id_filter = ''' (Charges.GainId = {}) '''
                    id_filter = psycopg.sql.SQL(id_filter).format(
                        psycopg.sql.Literal(gainid)
                    )
                    id_filters.append(id_filter)
                formatted_string = psycopg.sql.SQL(''' OR ''').join(id_filters)
                sql += '''
                    WHERE
                        (
                            BillingId IN (
                                SELECT
                                    DISTINCT BillingId
                                FROM
                                    Charges
                                WHERE
                                    Charges.Amount IS NOT NULL AND
                                    ({})
                            )
                        )
                '''
            sql += '''
                GROUP BY Charges.BillingId;
            '''
            executable_string = psycopg.sql.SQL(sql).format(formatted_string)

            with conn.cursor() as cur:
                cur.execute(executable_string)
                data = cur.fetchall()
            df_batch_data = pd.DataFrame(
                data,
                columns=[
                    'gainid',
                    'totalamount',
                    'totalnongainadjustment',
                    'totalnongainamountpaidtoprovider',
                    'totalgainprenegotiationadjustment',
                    'totalgainprenegotiationamountpaidtoprovider',
                    'totaldeductible',
                    'totalcoinsurance',
                    'totalcopayment',
                ],
            )
            df_data = pd.concat([df_data, df_batch_data], ignore_index=True)
    df_data = df_data.drop_duplicates()
    return df_data


@postgres_safe_execution
def get_postgres_billings_to_update_cases(
    billings_id_subset: typing.Optional[list[str]] = None,
) -> pd.DataFrame | None:
    global dbname
    df_data = None
    if billings_id_subset is None:
        billings_id_subset = []
    elif (
        billings_id_subset is not None and len(billings_id_subset) == 0
    ):  # if id_subset is empty list, we assume we do not want any data
        return df_data
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql = '''
            SELECT
                Billings.CaseId,
                SUM(Billings.TotalAmount) GrandTotalAmount,
                SUM(Billings.TotalNonGainAdjustment) GrandTotalNonGainAdjustment,
                SUM(Billings.TotalNonGainAmountPaidToProvider) GrandTotalNonGainAmountPaidToProvider,
                SUM(Billings.TotalGainPreNegotiationAdjustment) GrandTotalGainPreNegotiationAdjustment,
                SUM(Billings.TotalGainPreNegotiationAmountPaidToProvider) GrandTotalGainPreNegotiationAmountPaidToProvider,
                SUM(Billings.TotalDeductible) GrandTotalDeductible,
                SUM(Billings.TotalCoinsurance) GrandTotalCoinsurance,
                SUM(Billings.TotalCopayment) GrandTotalCopayment
            FROM
                Billings Billings
        '''
        formatted_string = psycopg.sql.SQL('')
        if len(billings_id_subset) > 0:
            id_filters = []
            for gainid in billings_id_subset:
                id_filter = ''' (Billings.GainId = {}) '''
                id_filter = psycopg.sql.SQL(id_filter).format(
                    psycopg.sql.Literal(gainid)
                )
                id_filters.append(id_filter)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(id_filters)
            sql += '''
                WHERE
                    ToDelete = FALSE
                    AND
                    (
                        CaseId IN (
                            SELECT
                                DISTINCT CaseId
                            FROM
                                Billings
                            WHERE
                                Billings.TotalAmount IS NOT NULL AND
                                ({})
                        )
                    )
            '''
        sql += '''
            GROUP BY
                Billings.CaseId;
        '''
        executable_string = psycopg.sql.SQL(sql).format(formatted_string)

        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
        df_data = pd.DataFrame(
            data,
            columns=[
                'gainid',
                'grandtotalamount',
                'grandtotalnongainadjustment',
                'grandtotalnongainamountpaidtoprovider',
                'grandtotalgainprenegotiationadjustment',
                'grandtotalgainprenegotiationamountpaidtoprovider',
                'grandtotaldeductible',
                'grandtotalcoinsurance',
                'grandtotalcopayment',
            ],
        )
    return df_data


@postgres_safe_execution
def get_postgres_insurances_to_update_cases(
    insurances_id_subset: set[str] | None = None,
) -> pd.DataFrame | None:
    global dbname
    df_data = None
    if not insurances_id_subset:  # If id_subset is false-y (empty or None),
        return df_data  # assume we do not want any data
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql = '''
            SELECT
                Insurances.CaseId,
                MAX(Insurances.DateSettled) DateSettled,
                SUM(Insurances.TotalSettlementAmount) GrandTotalSettlementAmount
            FROM
                Insurances Insurances
            WHERE
                Insurances.TotalSettlementAmount IS NOT NULL
        '''
        formatted_string = psycopg.sql.SQL(''' ''')
        if len(insurances_id_subset) > 0:
            id_filters = []
            for gainid in insurances_id_subset:
                id_filter = ''' (Insurances.GainId = {}) '''
                id_filter = psycopg.sql.SQL(id_filter).format(
                    psycopg.sql.Literal(gainid)
                )
                id_filters.append(id_filter)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(id_filters)
            sql += '''
                AND
                    (
                        CaseId IN (
                            SELECT
                                DISTINCT CaseId
                            FROM
                                Insurances
                            WHERE
                                ({})
                        )
                    )
            '''
        sql += '''
            GROUP BY
                Insurances.CaseId
            ;
        '''
        executable_string = psycopg.sql.SQL(sql).format(formatted_string)

        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
        df_data = pd.DataFrame(
            data,
            columns=[
                'gainid',
                'datesettled',
                'grandtotalsettlementamount',
            ],
        )
    return df_data


@postgres_safe_execution
def get_billing_gainids_from_case_gainids(
    cases_id_subset: list[str],
) -> list[str]:
    global dbname
    billing_gainids_list = []
    if cases_id_subset is None or len(cases_id_subset) == 0:
        return []

    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql = '''
            SELECT
                Billings.GainId
            FROM
                 Billings
            WHERE {}
        '''
        formatted_string = psycopg.sql.SQL('')
        if len(cases_id_subset) > 0:
            id_filters = []
            for gainid in cases_id_subset:
                id_filter = ''' (Billings.CaseId = {}) '''
                id_filter = psycopg.sql.SQL(id_filter).format(
                    psycopg.sql.Literal(gainid)
                )
                id_filters.append(id_filter)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(id_filters)

        executable_string = psycopg.sql.SQL(sql).format(formatted_string)
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            billing_gainids_list = [record[0] for record in data]
    return billing_gainids_list


@postgres_safe_execution
def update_postgres_delete_flag(
    table: str,
    redshift_sf_ids_update: dict[str, str],
    timestamp: str,
    update_servicing_end_datetime: bool = False,
) -> None:
    global dbname
    (
        redshift_sf_ids_update_batches,
        redshift_sf_ids_update_batch,
        batch_size,
    ) = (
        [],
        {},
        500,
    )  # update in batches of 500 ids to prevent timout issues
    for k, v in redshift_sf_ids_update.items():
        if len(redshift_sf_ids_update_batch) < batch_size:
            redshift_sf_ids_update_batch[k] = v
        else:
            redshift_sf_ids_update_batches.append(redshift_sf_ids_update_batch)
            redshift_sf_ids_update_batch = {k: v}
    redshift_sf_ids_update_batches.append(
        redshift_sf_ids_update_batch
    )  # to get the last batch
    for batch_id, redshift_gainids_batch in enumerate(
        redshift_sf_ids_update_batches
    ):
        if len(redshift_gainids_batch) == 0:
            logger.error(
                f'The length of redshift_gainids_batch is 0 for {table}'
            )
            continue
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            when_intermediates, where_intermediates = [], []
            for (
                gainid,
                deletesourcename,
            ) in redshift_gainids_batch.items():  # tuple key unpacked
                when_intermediate_string = ''' WHEN GainId = {} THEN {} '''
                when_intermediate_string = psycopg.sql.SQL(
                    when_intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                    psycopg.sql.Literal(deletesourcename),
                )
                when_intermediates.append(when_intermediate_string)
                where_intermediate_string = ''' (GainId = {}) '''
                where_intermediate_string = psycopg.sql.SQL(
                    where_intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                where_intermediates.append(where_intermediate_string)
            when_formatted_string = psycopg.sql.SQL(''' ''').join(
                when_intermediates
            )
            where_formatted_string = psycopg.sql.SQL(''' OR ''').join(
                where_intermediates
            )
            if update_servicing_end_datetime:
                # Add logic for updating LastWithdrawalDateTime when update_latest_withdrawal_timestamp is true
                update_update_servicing_end_datetime_string = psycopg.sql.SQL(
                    '''
                    ServicingEndDateTime = (
                        CASE
                            WHEN {} THEN {}
                            ELSE ServicingEndDateTime
                        END
                    )
                '''
                ).format(
                    psycopg.sql.Literal(update_servicing_end_datetime),
                    psycopg.sql.Literal(timestamp),
                )
                # Modify the SQL string to include the LastWithdrawalDateTime update
                sql_string = '''
                    UPDATE {}
                    SET
                        ToDelete = TRUE,
                        ToDeleteSystem = CASE {} END,
                        ModifiedDateTime = {timestamp},
                        {update_update_servicing_end_datetime_string}
                    WHERE
                        RelevantToGain = TRUE
                        AND (
                            {where_formatted_string}
                        )
                    ;
                '''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier(table.lower()),
                    when_formatted_string,
                    timestamp=psycopg.sql.Literal(timestamp),
                    update_update_servicing_end_datetime_string=update_update_servicing_end_datetime_string,
                    where_formatted_string=where_formatted_string,
                )
            else:
                sql_string = '''
                    UPDATE {}
                    SET
                        ToDelete = TRUE,
                        ToDeleteSystem = CASE {} END,
                        ModifiedDateTime = {timestamp}
                    WHERE
                        RelevantToGain = TRUE
                        AND (
                            {where_formatted_string}
                        )
                    ;
                '''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier(table.lower()),
                    when_formatted_string,
                    timestamp=psycopg.sql.Literal(timestamp),
                    where_formatted_string=where_formatted_string,
                )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated todelete for batch {batch_id+1} of {len(redshift_sf_ids_update_batches)}.',
                )


@postgres_safe_execution
def update_postgres_update_settled_flag(
    table: str,
    update_flag: str,
    redshift_sf_ids_update: dict[str, str],
    timestamp: str,
    update_servicing_end_datetime: bool = False,
) -> None:
    # keep update_servicing_end_datetime False since we currently don't consider servicing end date time
    global dbname
    (
        redshift_sf_ids_update_batches,
        redshift_sf_ids_update_batch,
        batch_size,
    ) = (
        [],
        {},
        500,
    )  # update in batches of 500 ids to prevent timout issues

    for k, v in redshift_sf_ids_update.items():
        if len(redshift_sf_ids_update_batch) < batch_size:
            redshift_sf_ids_update_batch[k] = v
        else:
            redshift_sf_ids_update_batches.append(redshift_sf_ids_update_batch)
            redshift_sf_ids_update_batch = {k: v}
    redshift_sf_ids_update_batches.append(
        redshift_sf_ids_update_batch
    )  # to get the last batch
    for batch_id, redshift_gainids_batch in enumerate(
        redshift_sf_ids_update_batches
    ):
        where_intermediate_string = ''' (GainId = {}) '''
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            where_intermediates = []
            for (
                gainid,
                _,
            ) in redshift_gainids_batch.items():  # tuple key unpacked
                where_intermediates.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )
            where_formatted_string = psycopg.sql.SQL(''' OR ''').join(
                where_intermediates
            )
            update_update_servicing_end_datetime_string = psycopg.sql.SQL(
                '''
                ServicingEndDateTime = (
                    CASE
                        WHEN {} THEN {}
                        ELSE ServicingEndDateTime
                    END
                )
            '''
            ).format(
                psycopg.sql.Literal(update_servicing_end_datetime),
                psycopg.sql.Literal(timestamp),
            )
            if len(redshift_gainids_batch) >= 1:
                sql_string = '''
                    UPDATE {}
                    SET
                        Status = {},
                        ModifiedDateTime = {timestamp}
                    WHERE
                        RelevantToGain = TRUE
                        AND (
                            {where_formatted_string}
                        )
                    ;
                '''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    psycopg.sql.Identifier(table.lower()),
                    psycopg.sql.Literal(update_flag),
                    timestamp=psycopg.sql.Literal(timestamp),
                    update_update_servicing_end_datetime_string=update_update_servicing_end_datetime_string,
                    where_formatted_string=where_formatted_string,
                )
            else:
                logger.error(
                    f'The length of redshift_gainids_batch is 0 for {table}',
                )
                continue
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated settled flag for {table} for batch {batch_id+1} of {len(redshift_sf_ids_update_batches)}.',
                )


@postgres_safe_execution
def update_postgres_paid_to_flag(
    table: str,
    update_flag: str,
    redshift_gain_ids: list[str],
    timestamp: str,
) -> None:
    global dbname
    (redshift_gain_ids_batches, batch_size) = ([], 500)
    # Create batches of batch_size to prevent timeout issues
    for i in range(0, len(redshift_gain_ids), batch_size):
        redshift_gain_ids_batches.append(redshift_gain_ids[i : i + batch_size])
    for batch_id, redshift_gainids_batch in enumerate(
        redshift_gain_ids_batches
    ):
        where_intermediate_string = ''' (GainId = {}) '''
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            where_intermediates = []
            for gainid in redshift_gainids_batch:  # tuple key unpacked
                where_intermediates.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )
            where_formatted_string = psycopg.sql.SQL(''' OR ''').join(
                where_intermediates
            )
            if len(redshift_gainids_batch) == 0:
                logger.error(
                    f'The length of redshift_gainids_batch is 0 for {table}',
                )
                continue
            sql_string = '''
                UPDATE {}
                SET
                    PaidTo = {},
                    ModifiedDateTime = {timestamp}
                WHERE
                    RelevantToGain = TRUE
                    AND (
                        {}
                    )
                ;
            '''
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(table.lower()),
                psycopg.sql.Literal(update_flag),
                where_formatted_string,
                timestamp=psycopg.sql.Literal(timestamp),
            )
            with conn.cursor() as cur:
                cur.execute(executable_string)
                logger.info(
                    f'Updated PaidTo flag for {table} for batch {batch_id+1} of {len(redshift_gain_ids_batches)} (batch size: {len(redshift_gainids_batch)}).'
                )


@postgres_safe_execution
def update_postgres_paid_by_flag(
    table: str,
    update_flag: str,
    redshift_gain_ids: list[str],
    timestamp: str,
) -> None:
    global dbname
    (redshift_gain_ids_batches, batch_size) = ([], 500)
    # Create batches of batch_size to prevent timeout issues
    for i in range(0, len(redshift_gain_ids), batch_size):
        redshift_gain_ids_batches.append(redshift_gain_ids[i : i + batch_size])
    for batch_id, redshift_gainids_batch in enumerate(
        redshift_gain_ids_batches
    ):
        where_intermediate_string = ''' (GainId = {}) '''
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            where_intermediates = []
            for gainid in redshift_gainids_batch:  # tuple key unpacked
                where_intermediates.append(
                    psycopg.sql.SQL(where_intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                    )
                )
            where_formatted_string = psycopg.sql.SQL(''' OR ''').join(
                where_intermediates
            )
            if len(redshift_gainids_batch) == 0:
                logger.error(
                    f'The length of redshift_gainids_batch is 0 for {table}',
                )
                continue
            sql_string = '''
                UPDATE {}
                SET
                    PaidBy = {},
                    ModifiedDateTime = {timestamp}
                WHERE
                    RelevantToGain = TRUE
                    AND (
                        {}
                    )
                ;
            '''
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(table.lower()),
                psycopg.sql.Literal(update_flag),
                where_formatted_string,
                timestamp=psycopg.sql.Literal(timestamp),
            )
            with conn.cursor() as cur:
                cur.execute(executable_string)
                logger.info(
                    f'Updated PaidBy flag for {table} for batch {batch_id+1} of {len(redshift_gain_ids_batches)} (batch size: {len(redshift_gainids_batch)}).'
                )


@postgres_safe_execution
def get_billing_claim_id_medical_facility_id(
    billing_gainids: set[str | None], source: str
) -> pd.DataFrame:
    medical_facility_sql_string = '''
        SELECT
            MedicalClaimNumber,
            MedicalFacilityId
        FROM
            {}
        WHERE
        (
            {}
        )
        ;
    '''
    df_claimid_medicalfacilityid = get_postgres_by_gain_id(
        medical_facility_sql_string,
        'billings',
        billing_gainids,
    )
    return df_claimid_medicalfacilityid


@postgres_safe_execution
def get_billing_claim_id_date_of_service(
    billing_gainids: set[str | None], source: str
) -> pd.DataFrame:
    sql_string = '''
        SELECT
            MedicalClaimNumber,
            DateOfService
        FROM
            {}
        WHERE
        (
            {}
        )
        ;
    '''

    return get_postgres_by_gain_id(
        sql_string,
        'billings',
        billing_gainids,
    )


@postgres_safe_execution
def get_billings_for_transactions(
    redshift_gain_ids: list[str],
) -> pd.DataFrame:
    global dbname
    redshift_transaction_ids_batches = []
    redshift_transaction_ids_batch = []
    batch_size = 500
    billing_transaction_data = pd.DataFrame()
    for gainid in redshift_gain_ids:
        if len(redshift_transaction_ids_batch) < batch_size:
            redshift_transaction_ids_batch.append(gainid)
        else:
            redshift_transaction_ids_batches.append(
                redshift_transaction_ids_batch
            )
            redshift_transaction_ids_batch = [gainid]
    redshift_transaction_ids_batches.append(
        redshift_transaction_ids_batch
    )  # to get the last batch
    for _, redshift_transaction_ids_batch in enumerate(
        redshift_transaction_ids_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            where_intermediates = []
            for gainid in redshift_transaction_ids_batch:
                where_intermediate_string = ''' (Transactions.GainId = {}) '''
                where_intermediate_string = psycopg.sql.SQL(
                    where_intermediate_string
                ).format(psycopg.sql.Literal(gainid))
                where_intermediates.append(where_intermediate_string)
            where_formatted_string = psycopg.sql.SQL(''' OR ''').join(
                where_intermediates
            )
            if len(redshift_transaction_ids_batch) >= 1:
                sql_string = '''
                    SELECT
                        Transactions.GainId AS TransactionGainId,
                        Billings.GainId AS BillingGainId,
                        Billings.ServicingStartDateTime AS BillingServicingStartDateTime,
                        Billings.ServicingEndDateTime AS BillingServicingEndDateTime
                    FROM
                        Transactions Transactions
                    JOIN Charges Charges
                    ON
                        Charges.GainId = Transactions.ChargeId
                    JOIN Billings Billings
                    ON
                        Billings.GainId = Charges.BillingId
                    WHERE
                        Billings.ToDelete = FALSE
                    AND (
                        {}
                    )
                    ;
                '''
                executable_string = psycopg.sql.SQL(sql_string).format(
                    where_formatted_string,
                )
            else:
                logger.error(
                    'The length of redshift_transaction_ids_batch is 0',
                )
                continue
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                data = cur.fetchall()
                batch_billing_data = pd.DataFrame(
                    data, columns=[desc[0] for desc in cur.description]
                )
                if billing_transaction_data.empty:
                    billing_transaction_data = batch_billing_data
                else:
                    billing_transaction_data = pd.concat(
                        [billing_transaction_data, batch_billing_data],
                        ignore_index=True,
                    )
    return billing_transaction_data


@postgres_safe_execution
def get_transactions_for_billings(
    redshift_gain_ids: list[str],
) -> pd.DataFrame:
    global dbname
    (redshit_billing_ids_batches, redshift_billing_ids_batch, batch_size) = (
        [],
        [],
        500,
    )
    billing_transaction_data = pd.DataFrame()
    for gainid in redshift_gain_ids:
        if len(redshift_billing_ids_batch) < batch_size:
            redshift_billing_ids_batch.append(gainid)
        else:
            redshit_billing_ids_batches.append(redshift_billing_ids_batch)
            redshift_billing_ids_batch = [gainid]
    redshit_billing_ids_batches.append(
        redshift_billing_ids_batch
    )  # to get the last batch
    for _, redshift_billing_ids_batch in enumerate(
        redshit_billing_ids_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            where_intermediates = []
            for gainid in redshift_billing_ids_batch:  # tuple key unpacked
                where_intermediate_string = ''' (Billings.GainId = {}) '''
                where_intermediate_string = psycopg.sql.SQL(
                    where_intermediate_string
                ).format(psycopg.sql.Literal(gainid))
                where_intermediates.append(where_intermediate_string)
            where_formatted_string = psycopg.sql.SQL(''' OR ''').join(
                where_intermediates
            )
            if len(redshift_billing_ids_batch) == 0:
                logger.error(
                    'The length of redshift_transaction_ids_batch is 0',
                )
                continue
            sql_string = '''
                SELECT
                    Billings.CaseId AS BillingCaseId,
                    Billings.GainId AS BillingGainId,
                    Billings.TotalAmount AS BillingTotalAmount,
                    Transactions.GainId AS TransactionGainId,
                    Transactions.Amount AS TransactionAmount,
                    Transactions.Description AS TransactionDescription,
                    Transactions.PostDateTime AS TransactionPostDateTime,
                    Transactions.PaymentDateTime AS TransactionPaymentDateTime,
                    Transactions.CarrierInsuranceType AS TransactionCarrierInsuranceType
                FROM
                    Transactions Transactions
                JOIN Charges Charges
                ON
                    Charges.GainId = Transactions.ChargeId
                JOIN Billings Billings
                ON
                    Billings.GainId = Charges.BillingId
                WHERE
                        Billings.ToDelete = FALSE
                AND (
                    {}
                )
                ;
            '''
            executable_string = psycopg.sql.SQL(sql_string).format(
                where_formatted_string,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                data = cur.fetchall()
                batch_transaction_data = pd.DataFrame(
                    data, columns=[desc[0] for desc in cur.description]
                )
                if billing_transaction_data.empty:
                    billing_transaction_data = batch_transaction_data
                else:
                    billing_transaction_data = pd.concat(
                        [billing_transaction_data, batch_transaction_data],
                        ignore_index=True,
                    )
    return billing_transaction_data


@postgres_safe_execution
def update_postgres_sf_deletepreventoverride(
    table: str, redshift_sf_ids_update: dict[str, str]
) -> None:
    global dbname
    (
        redshift_sf_ids_update_batches,
        redshift_sf_ids_update_batch,
        batch_size,
    ) = (
        [],
        {},
        500,
    )  # update in batches of 500 ids to prevent timout issues
    for k, v in redshift_sf_ids_update.items():
        if len(redshift_sf_ids_update_batch) < batch_size:
            redshift_sf_ids_update_batch[k] = v
        else:
            redshift_sf_ids_update_batches.append(redshift_sf_ids_update_batch)
            redshift_sf_ids_update_batch = {k: v}
    redshift_sf_ids_update_batches.append(
        redshift_sf_ids_update_batch
    )  # to get the last batch
    for batch_id, redshift_sf_ids_update_batch in enumerate(
        redshift_sf_ids_update_batches
    ):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            when_intermediates, where_intermediates = [], []
            for (
                gainid,
                reason,
            ) in redshift_sf_ids_update_batch.items():  # tuple key unpacked
                when_intermediate_string = ''' WHEN GainId = {} THEN {} '''
                when_intermediate_string = psycopg.sql.SQL(
                    when_intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                    psycopg.sql.Literal(reason),
                )
                when_intermediates.append(when_intermediate_string)
                where_intermediate_string = ''' (GainId = {}) '''
                where_intermediate_string = psycopg.sql.SQL(
                    where_intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                where_intermediates.append(where_intermediate_string)
            when_formatted_string = psycopg.sql.SQL(''' ''').join(
                when_intermediates
            )
            where_formatted_string = psycopg.sql.SQL(''' OR ''').join(
                where_intermediates
            )
            if len(redshift_sf_ids_update_batch) == 0:
                logger.error(
                    f'The length of redshift_sf_ids_update_batch is 0 for {table}',
                )
                continue
            sql_string = '''
                UPDATE {}
                SET
                    DeletePreventOverride = TRUE,
                    DeletePreventOverrideReason = CASE {} END
                WHERE
                    RelevantToGain = TRUE
                    AND (
                        {}
                    )
                ;
            '''
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(table.lower()),
                when_formatted_string,
                where_formatted_string,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated deletepreventoverride for batch {batch_id+1} of {len(redshift_sf_ids_update_batches)}.',
                )


@postgres_safe_execution
def get_legalpersonnel_lawfirm_sourceid_map(
    source: typing.Literal['ATI', 'Jopari'] = 'ATI',
) -> pd.DataFrame:
    global dbname
    result = pd.DataFrame()

    if source == 'ATI':
        sql = '''
            SELECT 
                g1.ati_id AS sourceid,
                {} as sourcename,
                g2.ati_id AS LawFirmSourceId
            FROM 
                LegalPersonnel AS LegalPersonnel
            INNER JOIN 
               gain_id_map AS g1 ON LegalPersonnel.GainId = g1.gainid
            INNER JOIN 
                gain_id_map AS g2 ON LegalPersonnel.LawFirmId = g2.gainid
            WHERE 
                LegalPersonnel.ToDelete = FALSE
                AND LegalPersonnel.RelevantToGain = TRUE;
        '''
    else:
        sql = '''
            SELECT 
                g1.jopari_id AS sourceid,
                {} as sourcename,
                g2.jopari_id AS LawFirmSourceId
            FROM 
                LegalPersonnel AS LegalPersonnel
            INNER JOIN 
                gain_id_map AS g1 ON LegalPersonnel.GainId = g1.gainid
            INNER JOIN 
                gain_id_map AS g2 ON LegalPersonnel.LawFirmId = g2.gainid
            WHERE 
                LegalPersonnel.ToDelete = FALSE
                AND LegalPersonnel.RelevantToGain = TRUE;
        '''
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        executable_string = psycopg.sql.SQL(sql).format(source)
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            col_names = [desc[0] for desc in cur.description]
            result = pd.DataFrame(data, columns=col_names)
    return result


@postgres_safe_execution
def get_rs_lawfirm_data_sourceid_map(
    source: typing.Literal['ATI', 'Jopari'] = 'ATI',
) -> pd.DataFrame:
    global dbname
    result = pd.DataFrame()
    if source == 'ATI':
        sql = '''
                SELECT 
                    g1.ati_id AS sourceid,
                    {} as sourcename,
                    LawFirms.Name, 
                    LawFirms.BillingAddressCity, 
                    LawFirms.BillingAddressState, 
                    LawFirms.BillingAddressZip, 
                    LawFirms.Phone, 
                    LawFirms.Email
                FROM
                    LawFirms LawFirms
                LEFT OUTER JOIN 
                    gain_id_map AS g1 ON LawFirms.GainId = g1.gainid
                WHERE
                    LawFirms.RelevantToGain = TRUE
        '''
    else:
        sql = '''
                SELECT 
                    g1.jopari_id AS sourceid,
                    {} as sourcename,
                    LawFirms.Name, 
                    LawFirms.BillingAddressCity, 
                    LawFirms.BillingAddressState, 
                    LawFirms.BillingAddressZip, 
                    LawFirms.Phone, 
                    LawFirms.Email
                FROM
                    LawFirms LawFirms
                LEFT OUTER JOIN 
                    gain_id_map AS g1 ON LawFirms.GainId = g1.gainid
                WHERE
                    LawFirms.RelevantToGain = TRUE
        '''
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        executable_string = psycopg.sql.SQL(sql).format(source)
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            col_names = [desc[0] for desc in cur.description]
            result = pd.DataFrame(data, columns=col_names)
    return result


@postgres_safe_execution
def get_legalpersonnel_lawfirm_sourceid_sfid_map() -> pd.DataFrame:
    global dbname
    case_map = pd.DataFrame()
    combined_data = []

    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        # Query for ATI
        sql_ati = '''
            SELECT
                LegalPersonnel.gainid as gainid,
                g2.ati_id as sourceid,
                'ATI' as sourcename,
                g2.salesforce_external_container_id as LegalPersonnelMapSFExternalIdContainer,
                g2.Salesforce_Id as LegalPersonnelSFId,
                g1.gainid as LawFirmGainId,
                g1.ati_id AS LawFirmSourceId,
                'ATI' as lawfirmsourcename,
                g1.salesforce_external_container_id AS LawFirmsMapSFExternalIdContainer,
                g1.Salesforce_Id as LawfirmSFId
            FROM
                LegalPersonnel LegalPersonnel
            LEFT OUTER JOIN
                gain_id_map g1
                ON
                    LegalPersonnel.LawFirmId = g1.gainid
            LEFT OUTER JOIN
                gain_id_map g2
                ON
                    LegalPersonnel.GainId = g2.gainid
            WHERE
                LegalPersonnel.ToDelete = FALSE
                AND
                    LegalPersonnel.RelevantToGain = TRUE
                AND g1.ati_id IS NOT NULL
                AND g2.ati_id IS NOT NULL
        '''

        with conn.cursor() as cur:
            cur.execute(sql_ati)
            data = cur.fetchall()
            col_names = [desc[0] for desc in cur.description]
            combined_data.extend(data)

        # Query for Jopari
        sql_jopari = '''
            SELECT
                LegalPersonnel.gainid as gainid,
                g2.jopari_id as sourceid,
                'Jopari' as sourcename,
                g2.salesforce_external_container_id as LegalPersonnelMapSFExternalIdContainer,
                g2.Salesforce_Id as LegalPersonnelSFId,
                g1.gainid as LawFirmGainId,
                g1.jopari_id AS LawFirmSourceId,
                'Jopari' as lawfirmsourcename,
                g1.salesforce_external_container_id AS LawFirmsMapSFExternalIdContainer,
                g1.Salesforce_Id as LawfirmSFId
            FROM
                LegalPersonnel LegalPersonnel
            LEFT OUTER JOIN
                gain_id_map g1
                ON
                    LegalPersonnel.LawFirmId = g1.gainid
            LEFT OUTER JOIN
                gain_id_map g2
                ON
                    LegalPersonnel.GainId = g2.gainid
            WHERE
                LegalPersonnel.ToDelete = FALSE
                AND
                    LegalPersonnel.RelevantToGain = TRUE
                AND g1.jopari_id IS NOT NULL
                AND g2.jopari_id IS NOT NULL
        '''

        with conn.cursor() as cur:
            cur.execute(sql_jopari)
            data = cur.fetchall()
            combined_data.extend(data)

    if combined_data:
        case_map = pd.DataFrame(combined_data, columns=col_names)

    return case_map


@postgres_safe_execution
def get_case_map_legalpersonnel_gainid_map() -> pd.DataFrame:
    global dbname
    case_map = pd.DataFrame()
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql = '''
            SELECT GainId,
            AttorneyId,
            ParalegalId,
            CaseManagerId,
            CoCounselId,
            CoParalegalId,
            CoCaseManagerId
            FROM Cases Cases
            WHERE Cases.ToDelete = FALSE
            AND Cases.RelevantToGain = TRUE
        '''
        executable_string = psycopg.sql.SQL(sql).format()
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            col_names = [desc[0] for desc in cur.description]
            case_map = pd.DataFrame(data, columns=col_names)
    return case_map


@postgres_safe_execution
def get_case_map_legalpersonnel_sourceid_map(
    source: typing.Literal['ATI', 'Jopari'] = 'ATI',
) -> pd.DataFrame:
    global dbname
    case_map = pd.DataFrame()
    if source == 'ATI':
        sql = '''
            SELECT
                g1.ati_id as sourceid,
                {} as sourcename,
                gim.ati_id AS AttorneyId,
                gim2.ati_id AS ParalegalId,
                gim3.ati_id AS CaseManagerId,
                gim4.ati_id AS CoCounselId,
                gim5.ati_id AS CoParalegalId,
                gim6.ati_id AS CoCaseManagerId
            FROM
                Cases Cases
            INNER JOIN
                gain_id_map g1 ON Cases.GainId = g1.GainId
            LEFT JOIN
                gain_id_map gim ON Cases.AttorneyId = gim.GainId
            LEFT JOIN
                gain_id_map gim2 ON Cases.ParalegalId = gim2.GainId
            LEFT JOIN
                gain_id_map gim3 ON Cases.CaseManagerId = gim3.GainId
            LEFT JOIN
                gain_id_map gim4 ON Cases.CoCounselId = gim4.GainId
            LEFT JOIN
                gain_id_map gim5 ON Cases.CoParalegalId = gim5.GainId
            LEFT JOIN
                gain_id_map gim6 ON Cases.CoCaseManagerId = gim6.GainId
            WHERE
                Cases.ToDelete = FALSE
                AND Cases.RelevantToGain = TRUE;
        '''
    else:
        sql = '''
            SELECT
                g1.jopari_id as sourceid,
                {} as sourcename,
                gim.jopari_id AS AttorneyId,
                gim2.jopari_id AS ParalegalId,
                gim3.jopari_id AS CaseManagerId,
                gim4.jopari_id AS CoCounselId,
                gim5.jopari_id AS CoParalegalId,
                gim6.jopari_id AS CoCaseManagerId
            FROM
                Cases Cases
            INNER JOIN
                gain_id_map g1 ON Cases.GainId = g1.GainId
            LEFT JOIN
                gain_id_map gim ON Cases.AttorneyId = gim.GainId
            LEFT JOIN
                gain_id_map gim2 ON Cases.ParalegalId = gim2.GainId
            LEFT JOIN
                gain_id_map gim3 ON Cases.CaseManagerId = gim3.GainId
            LEFT JOIN
                gain_id_map gim4 ON Cases.CoCounselId = gim4.GainId
            LEFT JOIN
                gain_id_map gim5 ON Cases.CoParalegalId = gim5.GainId
            LEFT JOIN
                gain_id_map gim6 ON Cases.CoCaseManagerId = gim6.GainId
            WHERE
                Cases.ToDelete = FALSE
                AND Cases.RelevantToGain = TRUE;
        '''
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        executable_string = psycopg.sql.SQL(sql).format(source)
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            col_names = [desc[0] for desc in cur.description]
            case_map = pd.DataFrame(data, columns=col_names)
    return case_map


@postgres_safe_execution
def get_postgres_delete_charges_using_billings(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    if id_subset is None or len(id_subset) == 0:
        logger.error('The id_subset for delete billings is empty, returning.')
        return []
    charge_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                Charges.GainId
            FROM
                Charges Charges
                JOIN Billings Billings
                    ON
                        Billings.GainId = Charges.BillingId
            WHERE
                Billings.ToDelete = TRUE
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = ''' (Billings.GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(psycopg.sql.Literal(gainid))
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' AND ({})'''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string,
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            charge_gainids_list = [record[0] for record in data]
    return charge_gainids_list


@postgres_safe_execution
def get_postgres_update_settled_charges_using_billings(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    if id_subset is None or len(id_subset) == 0:
        logger.error('The id_subset for settled billings is empty, returning.')
        return []
    charge_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                Charges.GainId
            FROM
                Charges Charges
                JOIN Billings Billings
                    ON
                        Billings.GainId = Charges.BillingId
            WHERE
                Billings.Status = 'Settled'
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = ''' (Billings.GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' AND ({})'''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string,
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(dbname), psycopg.sql.Identifier(dbname)
            )
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            charge_gainids_list = [record[0] for record in data]
    return charge_gainids_list


@postgres_safe_execution
def get_postgres_delete_transactions_using_charges(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    if id_subset is None or len(id_subset) == 0:
        logger.error('The id_subset for delete charges is empty, returning.')
        return []
    transaction_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                Transactions.GainId
            FROM
                Transactions Transactions
                JOIN Charges Charges
                    ON
                        Charges.GainId = Transactions.ChargeId
            WHERE
                Charges.ToDelete = TRUE
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = ''' (Charges.GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' AND ({}) '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string,
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            transaction_gainids_list = [record[0] for record in data]
    return transaction_gainids_list


@postgres_safe_execution
def get_postgres_update_settled_transactions_using_charges(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    if id_subset is None or len(id_subset) == 0:
        logger.error('The id_subset for settled charges is empty, returning.')
        return []
    transaction_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                Transactions.GainId
            FROM
                Transactions Transactions
                JOIN Charges Charges
                    ON
                        Charges.GainId = Transactions.ChargeId
            WHERE
                Charges.Status = 'Settled'
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = ''' (Charges.GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' AND ({}) '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string,
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(dbname)
            )
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            transaction_gainids_list = [record[0] for record in data]
    return transaction_gainids_list


@postgres_safe_execution
def get_postgres_delete_cases_using_billings(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    if id_subset is None or len(id_subset) == 0:
        logger.error('The id_subset for delete cases is empty, returning.')
        return []
    case_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                Case_ToDelete_Billings.GainId
            FROM
                (
                    SELECT GainId
                    FROM Cases_with_ToDelete_Billings_Status Case_ToDelete_Billings
                    GROUP BY GainId
                    HAVING COUNT(*) = 1 /* Count of 1 means either all False or all True */
                ) T
                JOIN Cases_with_ToDelete_Billings_Status Case_ToDelete_Billings ON Case_ToDelete_Billings.GainId = T.GainId
            WHERE Case_ToDelete_Billings.ToDelete = TRUE /* only all TRUE remain */
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = (
                    ''' (Case_ToDelete_Billings.GainId = {}) '''
                )
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' AND ({}) '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string,
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            case_gainids_list = [record[0] for record in data]
    return case_gainids_list


@postgres_safe_execution
def get_postgres_update_settled_cases_using_billings(
    id_subset: list[str] | None = None,
) -> pd.DataFrame:
    global dbname
    if id_subset is None or len(id_subset) == 0:
        logger.error('The id_subset for settled cases is empty, returning.')
        return pd.DataFrame()
    case_gainids_status_list = pd.DataFrame()
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                Case_Settled_Billings.GainId, Case_Settled_Billings.Status
            FROM
                Cases_with_Settled_Billings_Status Case_Settled_Billings
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = (
                    ''' (Case_Settled_Billings.GainId = {}) '''
                )
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' WHERE ({}) '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string,
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            case_gainids_status_list = [record for record in data]
            case_gainids_status_list = pd.DataFrame(
                case_gainids_status_list, columns=['GainId', 'Status']
            )
    return case_gainids_status_list


@postgres_safe_execution
def get_postgres_cases_using_billings(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    case_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        if (
            id_subset is not None and len(id_subset) == 0
        ):  # assumption: if non-null empty list is passed in, list was deliberately passed in empty meaning wants to pull from blank subset
            return case_gainids_list
        sql_string = '''
            SELECT DISTINCT
                CaseId
            FROM Billings
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:
                intermediate_string = ''' (GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' WHERE {} '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:  # uses server-side cursor by default
            cur.execute(executable_string)
            data = cur.fetchall()
            case_gainids_list = [record[0] for record in data]
    return case_gainids_list


@postgres_safe_execution
def get_postgres_case_map_using_notes(
    id_subset: list[str] | None = None,
) -> pd.DataFrame:
    global dbname
    notes_caseids = pd.DataFrame()
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        if (
            id_subset is not None and len(id_subset) == 0
        ):  # assumption: if non-null empty list is passed in, list was deliberately passed in empty meaning wants to pull from blank subset
            return notes_caseids
        sql_string = '''
            SELECT DISTINCT
                GainId, 
                CaseId
            FROM Notes
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:
                intermediate_string = ''' (GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' WHERE {} '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:  # uses server-side cursor by default
            cur.execute(executable_string)
            data = cur.fetchall()
            notes_caseids = pd.DataFrame(data, columns=['GainId', 'CaseId'])
    return notes_caseids


@postgres_safe_execution
def get_postgres_delete_plaintiffs_using_cases(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    if id_subset is None or len(id_subset) == 0:
        logger.error(
            'The id_subset for delete plaintiffs is empty, returning.'
        )
        return []
    plaintiff_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                Plaintiff_ToDelete_Cases.GainId
            FROM
                (
                    SELECT GainId
                    FROM Plaintiffs_with_ToDelete_Cases_Status Plaintiff_ToDelete_Cases
                    GROUP BY GainId
                    HAVING COUNT(*) = 1 /* Count of 1 means either all False or all True */
                ) T
                JOIN Plaintiffs_with_ToDelete_Cases_Status Plaintiff_ToDelete_Cases ON Plaintiff_ToDelete_Cases.GainId = T.GainId
            WHERE Plaintiff_ToDelete_Cases.ToDelete = TRUE /* only all TRUE remain */
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = (
                    ''' (Plaintiff_ToDelete_Cases.GainId = {}) '''
                )
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' AND ({}) '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string,
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:
            cur.execute(executable_string)
            data = cur.fetchall()
            plaintiff_gainids_list = [record[0] for record in data]
    return plaintiff_gainids_list


@postgres_safe_execution
def get_postgres_plaintiffs_using_cases(
    id_subset: list[str] | None = None,
) -> list[str]:
    global dbname
    plaintiff_gainids_list = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        if (
            id_subset is not None and len(id_subset) == 0
        ):  # assumption: if non-null empty list is passed in, list was deliberately passed in empty meaning wants to pull from blank subset
            return plaintiff_gainids_list
        sql_string = '''
            SELECT DISTINCT
                PlaintiffId
            FROM Cases
        '''
        if id_subset:
            intermediates = []
            for gainid in id_subset:  # tuple key unpacked
                intermediate_string = ''' (GainId = {}) '''
                intermediate_string = psycopg.sql.SQL(
                    intermediate_string
                ).format(
                    psycopg.sql.Literal(gainid),
                )
                intermediates.append(intermediate_string)
            formatted_string = psycopg.sql.SQL(''' OR ''').join(intermediates)
            sql_string += ''' WHERE {} '''
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format(
                formatted_string
            )
        else:
            sql_string += ''';'''
            executable_string = psycopg.sql.SQL(sql_string).format()
        with conn.cursor() as cur:  # uses server-side cursor by default
            cur.execute(executable_string)
            data = cur.fetchall()
            plaintiff_gainids_list = [record[0] for record in data]
    return plaintiff_gainids_list


def get_postgres_sources():
    global canonical_sources
    return canonical_sources


@postgres_safe_execution
def get_postgres_latest_modifieddatetime(
    mappings: typing.Optional[list[str] | set[str]] = None,
) -> dict[str, str]:
    global dbname
    if (
        not mappings
    ):  # Use this for initialization of mutable sequences instead of parameter default value
        mappings = (
            local_operations.canonical_objects
        )  # See more here: https://stackoverflow.com/questions/366422/how-can-i-avoid-issues-caused-by-pythons-early-bound-default-parameters-e-g-m
    redshift_data = {}
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        for mapping in mappings:
            sql_string = '''
                SELECT MAX(ModifiedDateTime)
                FROM {}
                WHERE IsDeleted = FALSE
                ;
            '''
            executable_string = psycopg.sql.SQL(sql_string).format(
                psycopg.sql.Identifier(mapping)
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                temp = cur.fetchall()
                redshift_data[mapping] = temp[0][0].strftime(
                    '%Y-%m-%d %H:%M:%S'
                )
    return redshift_data


def get_postgres_servicingdatetime(
    table: str,
    gainids: set[str | None],
) -> pd.DataFrame:
    return get_postgres_by_gain_id(
        '''
        SELECT
            GainId,
            ServicingStartDateTime, 
            ServicingEndDateTime
        FROM
            {}
        WHERE
            {}
        ;''',
        table,
        gainids,
    )


@contextmanager
def postgres_advisory_lock(lock_key: int):
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        with conn.cursor() as cur:
            # Try to acquire the lock (non-blocking)
            cur.execute("SELECT pg_try_advisory_lock(%s);", (lock_key,))
            result = cur.fetchone()
            if not result or result[0] == False:
                raise Exception("LOCK_NOT_ACQUIRED")

            try:
                yield
            finally:
                cur.execute("SELECT pg_advisory_unlock(%s);", (lock_key,))


def with_advisory_lock(lock_key: int, conflict_message: str | None = None):
    """
    Decorator to wrap functions with PostgreSQL advisory lock functionality.

    Args:
        lock_key: The advisory lock key to use
        conflict_message: Custom message for lock conflicts (optional)

    Returns:
        Decorated function that acquires the lock before execution and releases it after
    """

    def decorator(func: typing.Callable[P, T]) -> typing.Callable[P, T]:
        @wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            try:
                with postgres_advisory_lock(lock_key):
                    return func(*args, **kwargs)
            except Exception as e:
                raise e

        return wrapper

    return decorator


@postgres_safe_execution
def get_last_run_time(route: str | None = None) -> int | datetime.datetime:
    global dbname
    request_context = context.request_context.get()
    route = request_context.get('route', route)
    assert route is not None, 'Route not passed'
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql = '''
            SELECT LastRun
            FROM IntegrationTimestamps
            WHERE IntegrationRoute = %s 
            ;
        '''
        executable_string = psycopg.sql.SQL(sql).format()
        with conn.cursor() as cur:  # uses server-side cursor by default
            cur.execute(executable_string, (route,))
            data = cur.fetchall()
            try:
                time = data[0][0]  # .strftime('%Y-%m-%d %H:%M:%S')
                return time
            except Exception as e:
                logger.error(
                    f'Could not retrieve last run timestamp for {route}, error: {e}'
                )
    return -1


@postgres_safe_execution
def upsert_integration_timestamp(
    timestamp: str | datetime.datetime, all_data: bool
) -> None:
    global dbname
    request_context = context.request_context.get()
    route = request_context.get('route')
    assert route is not None, 'Route not passed'
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        inserted_params = {}
        if all_data:
            time = get_last_run_time(
                route
            )  # double check to make sure no previous entry exists
            if time == -1:  # if it doesn't i.e. -1, insert
                sql_string = '''
                    INSERT INTO IntegrationTimestamps
                    VALUES (
                        %(route)s,
                        %(timestamp)s
                    );
                '''
                inserted_params['route'] = route
                inserted_params['timestamp'] = timestamp
            else:
                print(
                    f'"all_data" flag is set to true, but previous entry for {route} exists; updating previous entry instead of inserting new entry'
                )
                sql_string = '''
                    UPDATE IntegrationTimestamps
                    SET LastRun = %(timestamp)s
                    WHERE IntegrationRoute = %(route)s ;
                '''
                inserted_params['route'] = route
                inserted_params['timestamp'] = timestamp
        else:
            sql_string = '''
                UPDATE IntegrationTimestamps
                SET LastRun = %(timestamp)s
                WHERE IntegrationRoute = %(route)s ;
            '''
            inserted_params['route'] = route
            inserted_params['timestamp'] = timestamp
        executable_string = psycopg.sql.SQL(sql_string).format()
        with psycopg.ClientCursor(
            conn
        ) as ccur:  # uses client cursor to allow parameters in UPDATE and INSERT INTO statements
            ccur.execute(executable_string, inserted_params)


@postgres_safe_execution
def update_postgres_sf_id_and_external_id_container_map(
    records_to_update: list[tuple[str, str]],
) -> None:
    global dbname
    batch_size = 500
    records_batches_to_update = []
    for i in range(0, len(records_to_update), batch_size):
        records_batches_to_update.append(records_to_update[i : i + batch_size])
    for batch_id, records_batch in enumerate(records_batches_to_update):
        with psycopg.connect(
            get_postgres_conn_string(), autocommit=True
        ) as conn:
            intermediates_external_id_container = []
            intermediates_filter = []
            for (
                gainid,
                external_id_container,
            ) in records_batch:
                intermediate_string = ''' WHEN (GainId = {}) THEN {} '''
                intermediate_filter_string = ''' (GainId = {}) '''
                intermediates_filter.append(
                    psycopg.sql.SQL(intermediate_filter_string).format(
                        psycopg.sql.Literal(gainid)
                    )
                )
                intermediates_external_id_container.append(
                    psycopg.sql.SQL(intermediate_string).format(
                        psycopg.sql.Literal(gainid),
                        psycopg.sql.Literal(external_id_container),
                    )
                )
            i_formatted_string_filter = psycopg.sql.SQL(' OR ').join(
                intermediates_filter
            )
            i_formatted_string_external_id_container = psycopg.sql.SQL(
                ''
            ).join(intermediates_external_id_container)
            if len(records_batch) == 0:
                logger.error('The length of records_batch is 0.')
                continue
            sql_string = '''
                UPDATE gain_id_map
                SET
                    salesforce_external_container_id = CASE
                        {}
                    END
                WHERE {};
            '''
            executable_string = psycopg.sql.SQL(sql_string).format(
                i_formatted_string_external_id_container,
                i_formatted_string_filter,
            )
            with conn.cursor() as cur:  # uses server-side cursor by default
                cur.execute(executable_string)
                logger.info(
                    f'Updated ids for batch {batch_id+1} of {len(records_batches_to_update)}.'
                )


@postgres_safe_execution
def upsert_gainid_records_by_ids_internal(
    salesforce_records: list[id_record.IdRecord],
    salesforce_external_container_records: list[id_record.IdRecord],
    ati_records: list[id_record.IdRecord],
    jopari_records: list[id_record.IdRecord],
    filevine_records: list[id_record.IdRecord],
    canonical_object: str,
) -> list[str]:
    global dbname
    # Check that only one of the record lists is not None
    record_lists = [
        salesforce_records,
        salesforce_external_container_records,
        ati_records,
        jopari_records,
        filevine_records,
    ]
    non_empty_lists = [lst for lst in record_lists if lst]

    if len(non_empty_lists) != 1:
        raise Exception(
            "Exactly one of the record lists must contain IdRecords"
        )

    salesforce_ids = [record.id for record in salesforce_records]
    salesforce_external_container_ids = [
        record.id for record in salesforce_external_container_records
    ]
    ati_ids = [record.id for record in ati_records]
    jopari_ids = [record.id for record in jopari_records]
    filevine_ids = [record.id for record in filevine_records]

    # Fetch existing gain records
    existing_gain_records = get_gainid_records_by_ids(
        None,
        salesforce_ids,
        salesforce_external_container_ids,
        ati_ids,
        jopari_ids,
        filevine_ids,
        canonical_object,
    )

    # Create a lookup function for existing gainid
    def lookup_gainid(
        salesforce_id: typing.Optional[str] = None,
        salesforce_external_container_id: typing.Optional[str] = None,
        ati_id: typing.Optional[str] = None,
        jopari_id: typing.Optional[str] = None,
        filevine_id: typing.Optional[str] = None,
    ) -> tuple[typing.Optional[str], typing.Optional[dict[str, typing.Any]]]:
        for record in existing_gain_records:
            if (
                (
                    salesforce_id is None
                    or (
                        record.get('salesforce_record') is not None
                        and salesforce_id == record.get('salesforce_record').id
                    )
                )
                and (
                    salesforce_external_container_id is None
                    or (
                        record.get('salesforce_external_container_record')
                        is not None
                        and salesforce_external_container_id
                        == record.get(
                            'salesforce_external_container_record'
                        ).id
                    )
                )
                and (
                    ati_id is None
                    or (
                        record.get('ati_record') is not None
                        and ati_id == record.get('ati_record').id
                    )
                )
                and (
                    jopari_id is None
                    or (
                        record.get('jopari_record') is not None
                        and jopari_id == record.get('jopari_record').id
                    )
                )
                and (
                    filevine_id is None
                    or (
                        record.get('filevine_record') is not None
                        and filevine_id == record.get('filevine_record').id
                    )
                )
            ):
                return record['gain_record'].id, record
        return None, None

    gainids = []
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        gain_id_map_keys = [
            'gainid',
            'gain_createddatetime',
            'gain_modifieddatetime',
            'salesforce_id',
            'salesforce_createddatetime',
            'salesforce_modifieddatetime',
            'salesforce_external_container_id',
            'salesforce_external_container_createddatetime',
            'salesforce_external_container_modifieddatetime',
            'ati_id',
            'ati_createddatetime',
            'ati_modifieddatetime',
            'jopari_id',
            'jopari_createddatetime',
            'jopari_modifieddatetime',
            'filevine_id',
            'filevine_createddatetime',
            'filevine_modifieddatetime',
            'canonical_object',
        ]
        len_gain_id_map = len(gain_id_map_keys)

        values = []
        max_length = max(
            len(salesforce_records),
            len(salesforce_external_container_records),
            len(ati_records),
            len(jopari_records),
            len(filevine_records),
        )

        for i in range(max_length):
            row: list[typing.Any] = [None] * len_gain_id_map
            row[1] = datetime.datetime.now()
            row[2] = datetime.datetime.now()
            row[-1] = canonical_object

            def update_row_from_existing_record(
                row: list[typing.Any], existing_record: dict[str, typing.Any]
            ):
                # Update the row with the values from the existing record
                if existing_record.get("gain_record"):
                    row[0] = existing_record["gain_record"].id
                    row[1] = (
                        existing_record["gain_record"].creation_time
                        or datetime.datetime.now()
                    )
                if existing_record.get("salesforce_record"):
                    row[3] = existing_record["salesforce_record"].id
                    row[4] = existing_record["salesforce_record"].creation_time
                    row[5] = existing_record[
                        "salesforce_record"
                    ].modification_time
                if existing_record.get("salesforce_external_container_record"):
                    row[6] = existing_record[
                        "salesforce_external_container_record"
                    ].id
                    row[7] = existing_record[
                        "salesforce_external_container_record"
                    ].creation_time
                    row[8] = existing_record[
                        "salesforce_external_container_record"
                    ].modification_time
                if existing_record.get("ati_record"):
                    row[9] = existing_record["ati_record"].id
                    row[10] = existing_record["ati_record"].creation_time
                    row[11] = existing_record["ati_record"].modification_time
                if existing_record.get("jopari_record"):
                    row[12] = existing_record["jopari_record"].id
                    row[13] = existing_record["jopari_record"].creation_time
                    row[14] = existing_record[
                        "jopari_record"
                    ].modification_time
                if existing_record.get("filevine_record"):
                    row[15] = existing_record["filevine_record"].id
                    row[16] = existing_record["filevine_record"].creation_time
                    row[17] = existing_record[
                        "filevine_record"
                    ].modification_time
                return row

            existing_gain_record = None

            if i < len(salesforce_records):
                record = salesforce_records[i]
                _, existing_gain_record = lookup_gainid(
                    salesforce_id=record.id
                )

            if i < len(salesforce_external_container_records):
                record = salesforce_external_container_records[i]
                _, existing_gain_record = lookup_gainid(
                    salesforce_external_container_id=record.id
                )

            if i < len(ati_records):
                record = ati_records[i]
                _, existing_gain_record = lookup_gainid(ati_id=record.id)

            if i < len(jopari_records):
                record = jopari_records[i]
                _, existing_gain_record = lookup_gainid(jopari_id=record.id)

            if i < len(filevine_records):
                record = filevine_records[i]
                _, existing_gain_record = lookup_gainid(filevine_id=record.id)

            if existing_gain_record:
                update_row_from_existing_record(row, existing_gain_record)

            if i < len(salesforce_records):
                record = salesforce_records[i]
                existing_gainid, _ = lookup_gainid(salesforce_id=record.id)
                row[3] = record.id
                row[4] = record.creation_time
                row[5] = record.modification_time

            if i < len(salesforce_external_container_records):
                record = salesforce_external_container_records[i]
                existing_gainid, _ = lookup_gainid(
                    salesforce_external_container_id=record.id
                )
                row[6] = record.id
                row[7] = record.creation_time
                row[8] = record.modification_time

            if i < len(ati_records):
                record = ati_records[i]
                existing_gainid, _ = lookup_gainid(ati_id=record.id)
                row[9] = record.id
                row[10] = record.creation_time
                row[11] = record.modification_time

            if i < len(jopari_records):
                record = jopari_records[i]
                existing_gainid, _ = lookup_gainid(jopari_id=record.id)
                row[12] = record.id
                row[13] = record.creation_time
                row[14] = record.modification_time

            if i < len(filevine_records):
                record = filevine_records[i]
                existing_gainid, _ = lookup_gainid(filevine_id=record.id)
                row[15] = record.id
                row[16] = record.creation_time
                row[17] = record.modification_time

            gainid = (
                existing_gainid
                if existing_gainid
                else str(uuid.uuid4()).replace('-', '')[:16]
            )
            gainids.append(gainid)
            row[0] = gainid
            values.append(row)

        keys_clause = ", ".join(gain_id_map_keys)
        values_clause = ", ".join(
            [
                "(%s)"
                % ", ".join(
                    [
                        "'%s'" % val if val is not None else 'NULL'
                        for val in row
                    ]
                )
                for row in values
            ]
        )

        removal_query = remove_gainid_records_by_ids_query(
            gainids, None, None, None, None, None, canonical_object
        )

        sql_string = (
            f"INSERT INTO gain_id_map ({keys_clause}) VALUES {values_clause}"
        )

        # Concatenate the two SQL strings
        combined_sql_string = removal_query + "; " + sql_string

        executable_string = psycopg.sql.SQL(
            typing.cast(typing_extensions.LiteralString, combined_sql_string)
        )

        with psycopg.ClientCursor(
            conn
        ) as ccur:  # uses client cursor to allow parameters in INSERT INTO statement
            ccur.execute(executable_string)

    return gainids


def upsert_and_map_sourceid_to_gainid(
    salesforce_records: list[id_record.IdRecord],
    salesforce_external_container_records: list[id_record.IdRecord],
    ati_records: list[id_record.IdRecord],
    jopari_records: list[id_record.IdRecord],
    filevine_records: list[id_record.IdRecord],
    canonical_object: str,
) -> dict[str, str]:
    """
    Calls upsert_gainid_records_by_ids_internal and maps source id (ati_id, filevine_id, jopari_id)
    to the corresponding gainid.
    """
    # Call the existing function
    gainids = upsert_gainid_records_by_ids_internal(
        salesforce_records,
        salesforce_external_container_records,
        ati_records,
        jopari_records,
        filevine_records,
        canonical_object,
    )

    # Find the relevant source records
    if ati_records:
        source_records = ati_records
    elif filevine_records:
        source_records = filevine_records
    elif jopari_records:
        source_records = jopari_records
    else:
        source_records = []

    # Map source id to gainid
    sourceid_to_gainid = {
        rec.id: gainid for rec, gainid in zip(source_records, gainids)
    }
    return sourceid_to_gainid


def remove_gainid_records_by_ids(
    gainids: typing.Optional[list[str]],
    salesforce_ids: typing.Optional[list[str]],
    salesforce_external_container_ids: typing.Optional[list[str]],
    ati_ids: typing.Optional[list[str]],
    jopari_ids: typing.Optional[list[str]],
    filevine_ids: typing.Optional[list[str]],
    canonical_object: str,
) -> None:
    removal_query = remove_gainid_records_by_ids_query(
        gainids,
        salesforce_ids,
        salesforce_external_container_ids,
        ati_ids,
        jopari_ids,
        filevine_ids,
        canonical_object,
    )

    executable_string = psycopg.sql.SQL(removal_query)
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        with psycopg.ClientCursor(
            conn
        ) as ccur:  # uses client cursor to allow parameters in INSERT INTO statement
            ccur.execute(executable_string)


def upsert_gainid_records_by_ids(
    salesforce_records: typing.Optional[list[id_record.IdRecord]] = None,
    salesforce_external_container_records: typing.Optional[
        list[id_record.IdRecord]
    ] = None,
    ati_records: typing.Optional[list[id_record.IdRecord]] = None,
    jopari_records: typing.Optional[list[id_record.IdRecord]] = None,
    filevine_records: typing.Optional[list[id_record.IdRecord]] = None,
    canonical_object: str = '',
    batch_size: int = 1000,
) -> list[str]:
    salesforce_records_len = (
        len(salesforce_records) if salesforce_records else 0
    )
    salesforce_external_container_records_len = (
        len(salesforce_external_container_records)
        if salesforce_external_container_records
        else 0
    )
    ati_records_len = len(ati_records) if ati_records else 0
    jopari_records_len = len(jopari_records) if jopari_records else 0
    filevine_records_len = len(filevine_records) if filevine_records else 0
    max_len = max(
        [
            salesforce_records_len,
            salesforce_external_container_records_len,
            ati_records_len,
            jopari_records_len,
            filevine_records_len,
        ]
    )
    result = []
    # Split non-empty list into chunks of maximum 1000 records and process each chunk
    for i in range(0, max_len, batch_size):
        result.extend(
            upsert_gainid_records_by_ids_internal(
                (
                    salesforce_records[i : i + batch_size]
                    if salesforce_records
                    else []
                ),
                (
                    salesforce_external_container_records[i : i + batch_size]
                    if salesforce_external_container_records
                    else []
                ),
                ati_records[i : i + batch_size] if ati_records else [],
                jopari_records[i : i + batch_size] if jopari_records else [],
                (
                    filevine_records[i : i + batch_size]
                    if filevine_records
                    else []
                ),
                canonical_object,
            )
        )
    return result


@postgres_safe_execution
def update_gainid_records_by_sf_ids_internal(
    gainid_records: list[id_record.IdRecord],
    salesforce_records: list[id_record.IdRecord],
) -> None:
    global dbname
    # Ensure that the gainid_records and salesforce_records have the same length
    if len(gainid_records) != len(salesforce_records):
        raise ValueError(
            "The length of gainid_records and salesforce_records must be the same."
        )
    # Generate the VALUES part of the temporary table insert statement
    values = ", ".join(
        f"('{gainid_record.id}', '{salesforce_record.id}', '{salesforce_record.creation_time}')"
        for gainid_record, salesforce_record in zip(
            gainid_records, salesforce_records
        )
    )
    # Construct table names with the prefix
    staging_table = "staging_gain_id_map_%s" % (uuid.uuid4().hex[:8],)
    main_table = "gain_id_map"
    # Create the SQL script
    sql_string = f"""
        -- Step 1: Create temporary table
        CREATE TEMP TABLE IF NOT EXISTS {staging_table} (
            gainid VARCHAR PRIMARY KEY,
            salesforceid VARCHAR,
            createdtime TIMESTAMP
        );

        -- Step 2: Insert records into the temporary table
        INSERT INTO {staging_table} (gainid, salesforceid, createdtime)
        VALUES {values};

        -- Step 3: Perform the update
        UPDATE {main_table}
        SET 
            salesforce_id = staging.salesforceid,
            salesforce_createddatetime = staging.createdtime,
            salesforce_modifieddatetime = staging.createdtime
        FROM {staging_table} AS staging
        WHERE {main_table}.gainid = staging.gainid;

        -- Step 4: Drop the temporary table (optional)
        DROP TABLE IF EXISTS {staging_table};

    """

    executable_string = psycopg.sql.SQL(
        typing.cast(typing_extensions.LiteralString, sql_string)
    )
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        with psycopg.ClientCursor(
            conn
        ) as ccur:  # uses client cursor to allow parameters in remove statement
            ccur.execute(executable_string)


def update_gainid_records_by_sf_ids(
    gainid_records: list[id_record.IdRecord],
    salesforce_records: list[id_record.IdRecord],
    batch_size: int = 100,
) -> None:
    # Ensure that the gainid_records and salesforce_records have the same length
    if len(gainid_records) != len(salesforce_records):
        raise ValueError(
            "The length of gainid_records and salesforce_records must be the same."
        )
    max_len = len(gainid_records)
    # Split non-empty list into chunks of maximum 1000 records and process each chunk
    for i in range(0, max_len, batch_size):
        update_gainid_records_by_sf_ids_internal(
            gainid_records[i : i + batch_size],
            salesforce_records[i : i + batch_size],
        )


@postgres_safe_execution
def get_gainid_records_by_ids_internal(
    gainids: typing.Optional[list[str]],
    salesforce_ids: typing.Optional[list[str]],
    salesforce_external_container_ids: typing.Optional[list[str]],
    ati_ids: typing.Optional[list[str]],
    jopari_ids: typing.Optional[list[str]],
    filevine_ids: typing.Optional[list[str]],
    canonical_object: str,
) -> list[dict[str, id_record.IdRecord]]:
    global dbname
    # Check that only one of the record lists is not None
    record_lists = [
        gainids,
        salesforce_ids,
        salesforce_external_container_ids,
        ati_ids,
        jopari_ids,
        filevine_ids,
    ]
    non_empty_lists = [lst for lst in record_lists if lst]
    if len(non_empty_lists) != 1:
        raise Exception(
            "Exactly one of the record lists must contain IdRecords"
        )
    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        sql_string = '''
            SELECT
                gainid, gain_createddatetime, gain_modifieddatetime,
                salesforce_id, salesforce_createddatetime, salesforce_modifieddatetime,
                salesforce_external_container_id, salesforce_external_container_createddatetime, salesforce_external_container_modifieddatetime,
                ati_id, ati_createddatetime, ati_modifieddatetime,
                jopari_id, jopari_createddatetime, jopari_modifieddatetime,
                filevine_id, filevine_createddatetime, filevine_modifieddatetime
            FROM gain_id_map
        '''
        conditions = []
        if salesforce_ids:
            conditions.append(
                "salesforce_id IN ({})".format(
                    ", ".join("'{}'".format(id) for id in salesforce_ids)
                )
            )
        if salesforce_external_container_ids:
            conditions.append(
                "salesforce_external_container_id IN ({})".format(
                    ", ".join(
                        "'{}'".format(id)
                        for id in salesforce_external_container_ids
                    )
                )
            )
        if ati_ids:
            conditions.append(
                "ati_id IN ({})".format(
                    ", ".join("'{}'".format(id) for id in ati_ids)
                )
            )
        if jopari_ids:
            conditions.append(
                "jopari_id IN ({})".format(
                    ", ".join("'{}'".format(id) for id in jopari_ids)
                )
            )
        if filevine_ids:
            conditions.append(
                "filevine_id IN ({})".format(
                    ", ".join("'{}'".format(id) for id in filevine_ids)
                )
            )
        if gainids:
            conditions.append(
                "gainid IN ({})".format(
                    ", ".join("'{}'".format(id) for id in gainids)
                )
            )
        if canonical_object:
            conditions.append("canonical_object = '%s'" % canonical_object)
        if conditions:
            where_clause = " WHERE {}".format(" AND ".join(conditions))
        else:
            raise Exception(
                "Unable to select from gain_id_map without filters"
            )
        sql_string = sql_string + where_clause
        executable_string = psycopg.sql.SQL(sql_string)
        result = []
        with psycopg.ClientCursor(
            conn
        ) as ccur:  # uses client cursor to allow parameters in SELECT statement
            ccur.execute(executable_string)
            data = ccur.fetchall()
            for entry in data:
                result.append(
                    {
                        "gain_record": id_record.IdRecord(
                            entry[0], entry[1], entry[2]
                        ),
                        "salesforce_record": id_record.IdRecord(
                            entry[3], entry[4], entry[5]
                        ),
                        "salesforce_external_container_record": id_record.IdRecord(
                            entry[6], entry[7], entry[8]
                        ),
                        "ati_record": id_record.IdRecord(
                            entry[9], entry[10], entry[11]
                        ),
                        "jopari_record": id_record.IdRecord(
                            entry[12], entry[13], entry[14]
                        ),
                        "filevine_record": id_record.IdRecord(
                            entry[15], entry[16], entry[17]
                        ),
                    }
                )
        return result


def get_gainid_records_by_ids(
    gainids: typing.Optional[list[str]] = None,
    salesforce_ids: typing.Optional[list[str]] = None,
    salesforce_external_container_ids: typing.Optional[list[str]] = None,
    ati_ids: typing.Optional[list[str]] = None,
    jopari_ids: typing.Optional[list[str]] = None,
    filevine_ids: typing.Optional[list[str]] = None,
    canonical_object: str = '',
    batch_size: int = 1000,
) -> list[dict[str, id_record.IdRecord]]:
    result = []
    gainids_len = len(gainids) if gainids else 0
    salesforce_ids_len = len(salesforce_ids) if salesforce_ids else 0
    salesforce_external_container_ids_len = (
        len(salesforce_external_container_ids)
        if salesforce_external_container_ids
        else 0
    )
    ati_ids_len = len(ati_ids) if ati_ids else 0
    jopari_ids_len = len(jopari_ids) if jopari_ids else 0
    filevine_ids_len = len(filevine_ids) if filevine_ids else 0
    max_len = max(
        [
            gainids_len,
            salesforce_ids_len,
            salesforce_external_container_ids_len,
            ati_ids_len,
            jopari_ids_len,
            filevine_ids_len,
        ]
    )
    # Split non-empty list into chunks of maximum 1000 records and process each chunk
    for i in range(0, max_len, batch_size):
        result.extend(
            get_gainid_records_by_ids_internal(
                gainids[i : i + batch_size] if gainids else [],
                salesforce_ids[i : i + batch_size] if salesforce_ids else [],
                (
                    salesforce_external_container_ids[i : i + batch_size]
                    if salesforce_external_container_ids
                    else []
                ),
                ati_ids[i : i + batch_size] if ati_ids else [],
                jopari_ids[i : i + batch_size] if jopari_ids else [],
                filevine_ids[i : i + batch_size] if filevine_ids else [],
                canonical_object,
            )
        )
    return result


def remove_gainid_records_by_ids_query(
    gainids: typing.Optional[list[str]],
    salesforce_ids: typing.Optional[list[str]],
    salesforce_external_container_ids: typing.Optional[list[str]],
    ati_ids: typing.Optional[list[str]],
    jopari_ids: typing.Optional[list[str]],
    filevine_ids: typing.Optional[list[str]],
    canonical_object: str,
) -> typing_extensions.LiteralString:
    global dbname
    # Check that only one of the record lists is not None
    record_lists = [
        gainids,
        salesforce_ids,
        salesforce_external_container_ids,
        ati_ids,
        jopari_ids,
        filevine_ids,
    ]
    non_empty_lists = [lst for lst in record_lists if lst]
    if len(non_empty_lists) != 1:
        raise Exception(
            "Exactly one of the record lists must contain IdRecords"
        )
    sql_string = '''DELETE FROM gain_id_map'''
    conditions = []
    if salesforce_ids:
        conditions.append(
            "salesforce_id IN ({})".format(
                ", ".join("'{}'".format(id) for id in salesforce_ids)
            )
        )
    if salesforce_external_container_ids:
        conditions.append(
            "salesforce_external_container_id IN ({})".format(
                ", ".join(
                    "'{}'".format(id)
                    for id in salesforce_external_container_ids
                )
            )
        )
    if ati_ids:
        conditions.append(
            "ati_id IN ({})".format(
                ", ".join("'{}'".format(id) for id in ati_ids)
            )
        )
    if jopari_ids:
        conditions.append(
            "jopari_id IN ({})".format(
                ", ".join("'{}'".format(id) for id in jopari_ids)
            )
        )
    if filevine_ids:
        conditions.append(
            "filevine_id IN ({})".format(
                ", ".join("'{}'".format(id) for id in filevine_ids)
            )
        )
    if gainids:
        conditions.append(
            "gainid IN ({})".format(
                ", ".join("'{}'".format(id) for id in gainids)
            )
        )
    if canonical_object:
        conditions.append("canonical_object = '%s'" % canonical_object)
    if conditions:
        where_clause = " WHERE {}".format(" AND ".join(conditions))
    else:
        raise Exception("Unable to delete from gain_id_map without filters")
    sql_string = sql_string + where_clause
    return sql_string


def get_gainids_by_ati_ids(
    ati_ids: list[str], canonical_object: str, default_none: bool = False
) -> list[str]:
    global fallback_timestamp
    if not ati_ids:
        return []
    records = get_gainid_records_by_ids(
        ati_ids=ati_ids,
        canonical_object=canonical_object,
    )
    # if not records:
    #     return [None] * len(ati_ids)
    key_function = lambda record: (
        record['ati_record'].modification_time
        or record['gain_record'].modification_time
        or record['ati_record'].creation_time
        or record['gain_record'].creation_time
        or fallback_timestamp
    )
    # Group records by ati_id
    records_by_ati_id = {}
    for record in records:
        ati_id = record['ati_record'].id
        if ati_id not in records_by_ati_id:
            records_by_ati_id[ati_id] = []
        records_by_ati_id[ati_id].append(record)
    gainids = []
    for ati_id in ati_ids:
        if ati_id in records_by_ati_id:
            latest_record = max(
                records_by_ati_id[ati_id],
                key=key_function,
            )
            gainids.append(latest_record['gain_record'].id)
        else:
            # Use original ID if no gainid found
            if default_none:
                gainids.append(None)
            else:
                gainids.append(ati_id)
    return gainids


def get_gainids_by_jopari_ids(
    jopari_ids: list[str], canonical_object: str, default_none: bool = False
) -> list[str] | list[None]:
    global fallback_timestamp
    if not jopari_ids:
        return []
    records = get_gainid_records_by_ids(
        jopari_ids=jopari_ids,
        canonical_object=canonical_object,
    )
    if not records:
        return [None] * len(jopari_ids)
    key_function = lambda record: (
        record['jopari_record'].modification_time
        or record['gain_record'].modification_time
        or record['jopari_record'].creation_time
        or record['gain_record'].creation_time
        or fallback_timestamp
    )
    # Group records by jopari_id
    records_by_jopari_id = {}
    for record in records:
        jopari_id = record['jopari_record'].id
        if jopari_id not in records_by_jopari_id:
            records_by_jopari_id[jopari_id] = []
        records_by_jopari_id[jopari_id].append(record)
    gainids = []
    for jopari_id in jopari_ids:
        if jopari_id in records_by_jopari_id:
            latest_record = max(
                records_by_jopari_id[jopari_id],
                key=key_function,
            )
            gainids.append(latest_record['gain_record'].id)
        else:
            if default_none:
                gainids.append(None)
            else:
                # Use original ID if no gainid found
                gainids.append(jopari_id)
    return gainids


def get_gainids_by_filevine_ids(
    filevine_ids: list[str], canonical_object: str
) -> list[str] | list[None] | None:
    global fallback_timestamp
    if not filevine_ids:
        return None
    records = get_gainid_records_by_ids(
        filevine_ids=filevine_ids,
        canonical_object=canonical_object,
    )
    if not records:
        return [None] * len(filevine_ids)
    key_function = lambda record: (
        record['filevine_record'].modification_time
        or record['gain_record'].modification_time
        or record['filevine_record'].creation_time
        or record['gain_record'].creation_time
        or fallback_timestamp
    )
    # Group records by filevine_id
    records_by_filevine_id = {}
    for record in records:
        filevine_id = record['filevine_id']
        if filevine_id not in records_by_filevine_id:
            records_by_filevine_id[filevine_id] = []
        records_by_filevine_id[filevine_id].append(record)
    gainids = []
    for filevine_id in filevine_ids:
        if filevine_id in records_by_filevine_id:
            latest_record = max(
                records_by_filevine_id[filevine_id],
                key=key_function,
            )
            gainids.append(latest_record['gain_record'].id)
        else:
            # Use original ID if no gainid found
            gainids.append(filevine_id)
    return gainids


def get_sourceids_by_gainids(
    gainids: list[str],
) -> list[str | None] | list[None] | None:
    global fallback_timestamp
    if not gainids:
        return None
    records = get_gainid_records_by_ids(gainids=gainids)
    if not records:
        return [None] * len(gainids)
    # Create a dictionary to store the latest source_id for each gainid
    source_ids_dict: dict[str, str] = collections.defaultdict(str)
    last_updated_times: dict[str, datetime.datetime | datetime.date | None] = {
        gainid: datetime.datetime.fromtimestamp(0) for gainid in gainids
    }
    for record in records:
        gainid = record['gain_record'].id
        if not gainid:
            continue
        ati_time = (
            record['ati_record'].modification_time
            if record['ati_record'] and record['ati_record'].modification_time
            else fallback_timestamp
        )
        jopari_time = (
            record['jopari_record'].modification_time
            if record['jopari_record']
            and record['jopari_record'].modification_time
            else fallback_timestamp
        )
        filevine_time = (
            record['filevine_record'].modification_time
            if record['filevine_record']
            and record['filevine_record'].modification_time
            else fallback_timestamp
        )
        # salesforce_time = (
        #     record['salesforce_record'].modification_time
        #     if record['salesforce_record']
        #     and record['salesforce_record'].modification_time
        #     else fallback_timestamp
        # )
        if (
            record['ati_record']
            and record['ati_record'].id
            and ati_time > last_updated_times[gainid]
        ):
            source_ids_dict[gainid] = record['ati_record'].id
            last_updated_times[gainid] = ati_time

        if (
            record['jopari_record']
            and record['jopari_record'].id
            and jopari_time > last_updated_times[gainid]
        ):
            source_ids_dict[gainid] = record['jopari_record'].id
            last_updated_times[gainid] = jopari_time
        if (
            record['filevine_record']
            and record['filevine_record'].id
            and filevine_time > last_updated_times[gainid]
        ):
            source_ids_dict[gainid] = record['filevine_record'].id
            last_updated_times[gainid] = filevine_time
        # Enable when SF -> RS arm is established
        # if (
        #     record['salesforce_record']
        #     and record['salesforce_record'].id
        #     and salesforce_time > last_updated_times[gainid]
        # ):
        #     source_ids_dict[gainid] = record['salesforce_record'].id
        #     last_updated_times[gainid] = salesforce_time
    # Generate the result list in the order of the input gainids
    return [source_ids_dict[gainid] for gainid in gainids]


def get_gainid_to_sourceid_map(gainids: list[str]) -> dict[str, str]:
    sourceids = get_sourceids_by_gainids(gainids)
    if sourceids is None or (len(sourceids) != len(gainids)):
        raise ValueError(
            "Lists 'sourceids' and 'gainids' must have the same length."
        )
    source_gain_map = {}
    for source, gain in zip(sourceids, gainids):
        if source and gain:
            source_gain_map[gain] = source
    return source_gain_map


def get_sourcenames_by_gainids(
    gainids: list[str],
) -> list[str] | list[None]:
    global fallback_timestamp
    # Make a single call to get_gainid_records_by_ids with the list of gainids
    records = get_gainid_records_by_ids(gainids=gainids)
    if not records:
        return [None] * len(gainids)
    # Create a dictionary to store the latest source_id for each gainid
    source_names_dict = collections.defaultdict(str)
    last_updated_times: dict[str, datetime.datetime | datetime.date] = {
        gainid: datetime.datetime.fromtimestamp(0) for gainid in gainids
    }
    for record in records:
        gainid = record['gain_record'].id
        if not gainid:
            continue
        ati_time = (
            record['ati_record'].modification_time
            if record['ati_record'] and record['ati_record'].modification_time
            else fallback_timestamp
        )
        jopari_time = (
            record['jopari_record'].modification_time
            if record['jopari_record']
            and record['jopari_record'].modification_time
            else fallback_timestamp
        )
        filevine_time = (
            record['filevine_record'].modification_time
            if record['filevine_record']
            and record['filevine_record'].modification_time
            else fallback_timestamp
        )
        # Enable when SF -> RS arm is established
        # salesforce_time = (
        #     record['salesforce_record'].modification_time
        #     if record['salesforce_record']
        #     and record['salesforce_record'].modification_time
        #     else fallback_timestamp
        # )
        if (
            record['ati_record']
            and record['ati_record'].id
            and ati_time > last_updated_times[gainid]
        ):
            source_names_dict[gainid] = 'ATI'
            last_updated_times[gainid] = ati_time
        if (
            record['jopari_record']
            and record['jopari_record'].id
            and jopari_time > last_updated_times[gainid]
        ):
            source_names_dict[gainid] = 'Jopari'
            last_updated_times[gainid] = jopari_time
        if (
            record['filevine_record']
            and record['filevine_record'].id
            and filevine_time > last_updated_times[gainid]
        ):
            source_names_dict[gainid] = 'Filevine Hostilo'
            last_updated_times[gainid] = filevine_time
        # Enable when SF -> RS arm is established
        # if (
        #     record['salesforce_record']
        #     and record['salesforce_record'].id
        #     and salesforce_time > last_updated_times[gainid]
        # ):
        #     source_names_dict[gainid] = 'Salesforce'
        #     last_updated_times[gainid] = salesforce_time
    # Generate the result list in the order of the input gainids
    return [source_names_dict[gainid] for gainid in gainids]


def get_gainid_to_sourcename_map(gainids: list[str]) -> dict[str, str]:
    sourcenames = get_sourcenames_by_gainids(gainids)
    if len(sourcenames) != len(gainids):
        raise ValueError(
            "Lists 'sourcenames' and 'gainids' must have the same length."
        )
    source_gain_map = {}
    for source, gain in zip(sourcenames, gainids):
        if source and gain:
            source_gain_map[gain] = source
    return source_gain_map


def run_query(query: typing_extensions.LiteralString):
    logger.info("Running query: " + query)

    with psycopg.connect(get_postgres_conn_string(), autocommit=True) as conn:
        with conn.cursor() as ccur:  # use the standard cursor
            ccur.execute(psycopg.sql.SQL(query))

            # Extract column names from the cursor
            columns = [desc[0] for desc in ccur.description]

            # Fetch all results and convert each row to a dict
            rows = ccur.fetchall()
            return [dict(zip(columns, row)) for row in rows]


# endregion
