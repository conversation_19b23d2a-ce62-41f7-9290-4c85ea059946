{% load static %}

<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
    integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
<link rel="stylesheet" type="text/css" href="{% static '/css/styles.css' %}">

<nav class="navbar navbar-expand-lg navbar-dark bg-success">
    <div class="container">
        <a class="navbar-brand" style="color:#04215d" href="https://www.gainservicing.com/" target="_blank">Gain Servicing</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item active">
                    <a class="nav-link" href="{% url 'apphome' %}">Home</a>
                </li>
            </ul>
        </div>
    </div>

</nav>

<div class="row pt-2">
    <div class="col-md-4">
        <div class="card mb-2">
            <img class="card-img-top" src="{% static 'images/providers.png' %}">
            <div class="card-body">
                <h5 class="card-title">The providers map</h5>
                <p class="card-text">This project is about refreshing the providers map data for the Gain Portal.</p>
                <a href="{% url 'providermapdata:home' %}" class="btn btn-primary">
                    Read More
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-2">
            <img class="card-img-top" src="{% static 'images/powerbi.png' %}">
            <div class="card-body">
                <h5 class="card-title">PowerBI reports</h5>
                <p class="card-text">This project is about refreshing the PowerBI reports data.</p>
                <a href="{% url 'providermapdata:home' %}" class="btn btn-primary">
                    Read More
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-2">
            <img class="card-img-top" src="{% static 'images/salesforce.png' %}">
            <div class="card-body">
                <h5 class="card-title">The Salesforece data</h5>
                <p class="card-text">This project is about refreshing the Salesforce data. </p>
                <a href="{% url 'salesforcedata:home' %}" class="btn btn-primary">
                    Read More
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    {% block page_content %}{% endblock %}
</div>

<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
    integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous">
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"
    integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous">
</script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"
    integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous">
</script>