{"_comment": "S3 to Redshfit, only update columns that are in the list.  The list is followed by route, then canonical object.", "ATI_To_Redshift_Upsert": {"plaintiffs": ["gainid", "name", "dateofbirth", "ssn", "taxid", "driverlicense", "gender", "<PERSON><PERSON><PERSON>", "company", "homephone", "cellphone", "businessphone", "otherphone", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "departmentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "medicalfacilities": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "contracttype", "specialties", "description", "portalaccount", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "lawfirms": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "typeoflaw", "description", "employeecountrange", "donotfund", "donotfundtype", "automaticcaseupdaterequest", "nonresponsive", "nonresponsivenote", "portalaccount", "portalrewardsparticipant", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "legalpersonnel": ["gainid", "name", "title", "homephone", "cellphone", "businessphone", "businessphoneext", "otherphone", "fax", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "notes", "lawfirm<PERSON>", "lawfirmname", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "cases": ["gainid", "plaintiffname", "plaintiffdateofbirth", "status", "accidentdate", "injuredbodyparts", "accidentdescription", "accidentstate", "treatmentcompleted", "datetreatmentcompleted", "insurancevendorassigned", "grandtotalamount", "grandtotalnongainadjustment", "grand<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtotaldeductible", "grandtotalcoinsurance", "grandtotalcopayment", "grandtotalbalance", "datesettled", "grandto<PERSON>ettlementamount", "paidto", "notes", "type", "plaintiffid", "attorneyid", "paralegalid", "casemanagerid", "co<PERSON><PERSON><PERSON><PERSON>", "coparalegalid", "cocasemanager<PERSON>", "tailclaimcase", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "billings": ["gainid", "medicalclaimnumber", "dateofservice", "status", "totalamount", "totalnongainadjustment", "totalnongainamou<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "totalgainprenegotiationadjustment", "totalgainprenegotiationamountpaidtoprovider", "totaldeductible", "totalcoinsurance", "totalcopayment", "totalbalance", "totalamountsent", "medicalfacilityaddressline1", "medicalfacilityaddressline2", "medicalfacilityaddresscity", "medicalfacilityaddressstate", "medicalfacilityaddresszip", "paidto", "notes", "gaintype", "type", "caseid", "medicalfacilityid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "charges": ["gainid", "dateofservice", "status", "amount", "cptcode", "cptmodifier", "cptdescription", "nongainadjustment", "<PERSON>gai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gainprenegotiationadjustment", "gainprenegotiationamountpaidtoprovider", "deductible", "coinsurance", "copayment", "balance", "reimbursementrate", "amountsent", "quantity", "billingid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "transactions": ["gainid", "postdatetime", "paymentdatetime", "entrydatetime", "status", "<PERSON><PERSON>son", "amount", "carrierid", "carriername", "carrierinsurancetype", "checknumber", "description", "type", "carccode", "chargeid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "files": ["gainid", "url", "type", "plaintiffid", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "notes": ["gainid", "notecreatorname", "note", "plaintiffid", "medicalfacilityid", "lawfirm<PERSON>", "legalpersonnelid", "caseid", "intakeid", "insuranceid", "lienid", "disbursalid", "billingid", "chargeid", "transactionid", "fileid", "surgeryid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"]}, "AthenaHealth_To_Redshift": {"plaintiffs": ["gainid", "name", "dateofbirth", "ssn", "taxid", "driverlicense", "gender", "<PERSON><PERSON><PERSON>", "company", "homephone", "cellphone", "businessphone", "otherphone", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "departmentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "medicalfacilities": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "contracttype", "specialties", "description", "portalaccount", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "lawfirms": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "typeoflaw", "description", "employeecountrange", "donotfund", "donotfundtype", "automaticcaseupdaterequest", "nonresponsive", "nonresponsivenote", "portalaccount", "portalrewardsparticipant", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "legalpersonnel": ["gainid", "name", "title", "homephone", "cellphone", "businessphone", "businessphoneext", "otherphone", "fax", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "notes", "lawfirm<PERSON>", "lawfirmname", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "cases": ["gainid", "plaintiffname", "plaintiffdateofbirth", "status", "accidentdate", "injuredbodyparts", "accidentdescription", "accidentstate", "treatmentcompleted", "datetreatmentcompleted", "insurancevendorassigned", "grandtotalamount", "grandtotalnongainadjustment", "grand<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtotalbalance", "datesettled", "grandto<PERSON>ettlementamount", "paidto", "notes", "type", "plaintiffid", "attorneyid", "paralegalid", "casemanagerid", "co<PERSON><PERSON><PERSON><PERSON>", "coparalegalid", "cocasemanager<PERSON>", "tailclaimcase", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "billings": ["gainid", "medicalclaimnumber", "dateofservice", "status", "totalamount", "totalnongainadjustment", "totalnongainamou<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "totalbalance", "totalamountsent", "medicalfacilityaddressline1", "medicalfacilityaddressline2", "medicalfacilityaddresscity", "medicalfacilityaddressstate", "medicalfacilityaddresszip", "paidto", "notes", "gaintype", "type", "caseid", "medicalfacilityid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "charges": ["gainid", "dateofservice", "status", "amount", "cptcode", "cptmodifier", "cptdescription", "nongainadjustment", "<PERSON>gai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "balance", "reimbursementrate", "amountsent", "quantity", "billingid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "transactions": ["gainid", "postdatetime", "paymentdatetime", "entrydatetime", "status", "<PERSON><PERSON>son", "amount", "carrierid", "carriername", "carrierinsurancetype", "checknumber", "description", "type", "chargeid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"]}, "Jopari_To_Redshift_Upsert": {"plaintiffs": ["gainid", "name", "dateofbirth", "ssn", "taxid", "driverlicense", "gender", "<PERSON><PERSON><PERSON>", "company", "homephone", "cellphone", "businessphone", "otherphone", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "departmentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "medicalfacilities": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "contracttype", "specialties", "description", "portalaccount", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "lawfirms": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "typeoflaw", "description", "employeecountrange", "donotfund", "donotfundtype", "automaticcaseupdaterequest", "nonresponsive", "nonresponsivenote", "portalaccount", "portalrewardsparticipant", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "legalpersonnel": ["gainid", "name", "title", "homephone", "cellphone", "businessphone", "businessphoneext", "otherphone", "fax", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "notes", "lawfirm<PERSON>", "lawfirmname", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "cases": ["gainid", "plaintiffname", "plaintiffdateofbirth", "status", "accidentdate", "injuredbodyparts", "accidentdescription", "accidentstate", "treatmentcompleted", "datetreatmentcompleted", "insurancevendorassigned", "grandtotalamount", "grandtotalnongainadjustment", "grand<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtotalbalance", "datesettled", "grandto<PERSON>ettlementamount", "paidto", "notes", "type", "plaintiffid", "attorneyid", "paralegalid", "casemanagerid", "co<PERSON><PERSON><PERSON><PERSON>", "coparalegalid", "cocasemanager<PERSON>", "tailclaimcase", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "billings": ["gainid", "medicalclaimnumber", "dateofservice", "status", "totalamount", "totalnongainadjustment", "totalnongainamou<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "totalbalance", "totalamountsent", "medicalfacilityaddressline1", "medicalfacilityaddressline2", "medicalfacilityaddresscity", "medicalfacilityaddressstate", "medicalfacilityaddresszip", "paidto", "notes", "gaintype", "type", "caseid", "medicalfacilityid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "insurances": ["gainid", "companyname", "policyid", "memberid", "groupnumber", "status", "limits", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "liabilityaccepted", "declarationpagereceived", "medpayexhausted", "pipexhausted", "probate", "bankruptcy", "subrogation<PERSON>n", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "drivername", "datesettled", "howcasewassettled", "totalsettlementamount", "billspaid", "<PERSON><PERSON><PERSON>", "attorneyfeeflexible", "referralfeepercentage", "amounttoclient", "settlementnotes", "notes", "type", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "charges": ["gainid", "dateofservice", "status", "amount", "cptcode", "cptmodifier", "cptdescription", "nongainadjustment", "<PERSON>gai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "balance", "reimbursementrate", "amountsent", "quantity", "billingid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "transactions": ["gainid", "postdatetime", "paymentdatetime", "entrydatetime", "status", "<PERSON><PERSON>son", "amount", "carrierid", "carriername", "carrierinsurancetype", "checknumber", "description", "type", "chargeid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "files": ["gainid", "url", "type", "plaintiffid", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"]}, "Hostilo_Filevine_to_Redshift_Upsert": {"plaintiffs": ["gainid", "name", "dateofbirth", "ssn", "taxid", "driverlicense", "gender", "<PERSON><PERSON><PERSON>", "company", "homephone", "cellphone", "businessphone", "otherphone", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "departmentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "medicalfacilities": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "contracttype", "specialties", "description", "portalaccount", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "lawfirms": ["gainid", "name", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "website", "typeoflaw", "description", "employeecountrange", "donotfund", "donotfundtype", "automaticcaseupdaterequest", "nonresponsive", "nonresponsivenote", "portalaccount", "portalrewardsparticipant", "notes", "parentid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "legalpersonnel": ["gainid", "name", "title", "homephone", "cellphone", "businessphone", "businessphoneext", "otherphone", "fax", "primaryemail", "secondaryemail", "primaryaddressline1", "primaryaddressline2", "primaryaddresscity", "primaryaddressstate", "primaryaddresszip", "otheraddressline1", "otheraddressline2", "otheraddresscity", "otheraddressstate", "otheraddresszip", "notes", "lawfirm<PERSON>", "lawfirmname", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "cases": ["gainid", "plaintiffname", "plaintiffdateofbirth", "status", "accidentdate", "injuredbodyparts", "accidentdescription", "accidentstate", "treatmentcompleted", "datetreatmentcompleted", "insurancevendorassigned", "grandtotalamount", "grandtotalnongainadjustment", "grand<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtotalbalance", "datesettled", "grandto<PERSON>ettlementamount", "paidto", "notes", "type", "plaintiffid", "attorneyid", "paralegalid", "casemanagerid", "co<PERSON><PERSON><PERSON><PERSON>", "coparalegalid", "cocasemanager<PERSON>", "tailclaimcase", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "intakes": ["gainid", "accidentdescription", "accidentdatetime", "weresfpremisepicstaken", "waspolicecalled", "waspolicereportgenerated", "werecitationsissued", "clientsvehiclemakemodel", "clientsvehicledamage", "defendantsvehiclemakemodel", "defendantsvehicledamage", "wereautodamagepicstaken", "wasvehicletowed", "vehicledamageestimate", "injuriesdescription", "wereinjurypicstaken", "priorinjuries", "clientjobtitle", "clientemploymentstatus", "clientlostwagesstart", "clientjobduties", "clientlostwagesend", "clientshourlysalaryrate", "clientbankruptcy", "thingsclientcannolongerdo", "thingsclientcandowithpain", "clientlossoflifeenjoyment", "clienthealthhistory", "clientsubsequentaccidents", "clientsubsequentaccidentsexplanation", "clientobservations", "clientonsocialmedia", "clientsocialmedianotes", "doesclienthavehealthinsurance", "doesclienthavesecondhealthinsurance", "type", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "insurances": ["gainid", "companyname", "policyid", "memberid", "groupnumber", "status", "limits", "phone", "fax", "email", "followupemail", "billingaddressline1", "billingaddressline2", "billingaddresscity", "bill<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicaladdressline1", "physicaladdressline2", "physicaladdresscity", "physicaladdressstate", "physicaladdresszip", "liabilityaccepted", "declarationpagereceived", "medpayexhausted", "pipexhausted", "probate", "bankruptcy", "subrogation<PERSON>n", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "drivername", "datesettled", "howcasewassettled", "totalsettlementamount", "billspaid", "<PERSON><PERSON><PERSON>", "attorneyfeeflexible", "referralfeepercentage", "amounttoclient", "settlementnotes", "notes", "type", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "liens": ["gainid", "amountdue", "lienholdername", "lienfilenumber", "originalpaidamount", "isthereexcess", "excessamount", "excludefromsettlement", "notes", "type", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "disbursals": ["gainid", "status", "amountdue", "checknumber", "checkdate", "amountpaid", "type", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "billings": ["gainid", "medicalclaimnumber", "dateofservice", "status", "totalamount", "totalnongainadjustment", "totalnongainamou<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "totalbalance", "totalamountsent", "medicalfacilityaddressline1", "medicalfacilityaddressline2", "medicalfacilityaddresscity", "medicalfacilityaddressstate", "medicalfacilityaddresszip", "paidto", "notes", "gaintype", "type", "caseid", "medicalfacilityid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "charges": ["gainid", "dateofservice", "status", "amount", "cptcode", "cptmodifier", "cptdescription", "nongainadjustment", "<PERSON>gai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "balance", "reimbursementrate", "amountsent", "quantity", "billingid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "transactions": ["gainid", "postdatetime", "paymentdatetime", "entrydatetime", "status", "<PERSON><PERSON>son", "amount", "carrierid", "carriername", "carrierinsurancetype", "checknumber", "description", "type", "chargeid", "relevanttogain", "modifieddatetime", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"], "files": ["gainid", "url", "type", "plaintiffid", "caseid", "relevanttogain", "todelete", "todeletesystem", "deletepreventoverride", "deletepreventover<PERSON><PERSON>son"]}}