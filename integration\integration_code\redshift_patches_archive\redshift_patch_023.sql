ALTER TABLE integration.dev.insurances
ADD COLUMN settlementnotes_new
VARCHAR(10000)
;
UPDATE integration.dev.insurances
SET settlementnotes_new = settlementnotes
;
ALTER TABLE integration.dev.insurances
DROP COLUMN settlementnotes
;
ALTER TABLE integration.dev.insurances
RENAME COLUMN settlementnotes_new TO settlementnotes
;


ALTER TABLE integration.staging.insurances
ADD COLUMN settlementnotes_new
VARCHAR(10000)
;
UPDATE integration.staging.insurances
SET settlementnotes_new = settlementnotes
;
ALTER TABLE integration.staging.insurances
DROP COLUMN settlementnotes
;
ALTER TABLE integration.staging.insurances
RENAME COLUMN settlementnotes_new TO settlementnotes
;


