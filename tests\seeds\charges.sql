

INSERT INTO charges (
    dateofservice, amount, cptcode, cptmodifier, cptdescription, 
    nongainadjustment, nongainamountpaidtoprovider, reimbursementrate, 
    amountsent, quantity, billingid, relevanttogain, sourcecreatedatetime, 
    sourcemodifieddatetime, modifieddatetime, todelete, todeletesystem, 
    deletepreventoverride, deletepreventoverridereason, status, balance, 
    createdatetime, gainid, gainprenegotiationamountpaidtoprovider, 
    gainprenegotiationadjustment
) VALUES (
    '2024-08-16', 162.54, '97110', '', 'Therapeutic Exercise', 
    NULL, NULL, NULL, NULL, 2, '9050d035837c491e', true, NULL, 
    NULL, '2024-08-26 17:07:38', false, '', false, '', '', NULL, NULL,
    '6fac7d34f12f468f', NULL, NULL
);

INSERT INTO gain_id_map (
    gainid, 
    gain_createddatetime, 
    gain_modifieddatetime, 
    ati_id, 
    ati_createddatetime, 
    ati_modifieddatetime, 
    canonical_object
) VALUES (
    '6fac7d34f12f468f',
    '2024-08-26 16:16:48.542351',
    '2024-08-26 16:16:48.542351',
    '180141079',
    '2024-08-26 16:16:48.542351',
    '2024-08-26 16:16:48.542351',
    'charges'
);

INSERT INTO charges (
    cptdescription,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    'Charge to be deleted',
    true,
    true,
    '6fac7d34f12f468g'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '6fac7d34f12f468g',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'charges'
);

INSERT INTO charges (
    dateofservice, amount, cptcode, cptmodifier, cptdescription, 
    nongainadjustment, nongainamountpaidtoprovider, reimbursementrate, 
    amountsent, quantity, billingid, relevanttogain, sourcecreatedatetime, 
    sourcemodifieddatetime, modifieddatetime, todelete, todeletesystem, 
    deletepreventoverride, deletepreventoverridereason, status, balance, 
    createdatetime, gainid, gainprenegotiationamountpaidtoprovider, 
    gainprenegotiationadjustment
) VALUES (
    '2024-08-16', 162.54, '97110', '', 'Charge manual review', 
    NULL, NULL, NULL, NULL, 2, '9050d035837c491e', true, NULL, 
    NULL, '2024-08-26 17:07:38', false, '', false, '', '', NULL, NULL,
    '6fac7d34f12f468h', NULL, NULL
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    filevine_id,
    filevine_createddatetime,
    filevine_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '6fac7d34f12f468h',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '6fac7d34f12f468h',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_charges_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'charges'
);


-- charges to test UpdateCharges
INSERT INTO charges (
    gainid,
    dateofservice,
    amount,
    cptcode,
    cptmodifier,
    cptdescription,
    nongainadjustment,
    nongainamountpaidtoprovider,
    reimbursementrate,
    amountsent,
    quantity,
    billingid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    status,
    createdatetime,
    balance,
    gainprenegotiationamountpaidtoprovider,
    gainprenegotiationadjustment
)
VALUES
(
    '6b7a8f913f2e4d5c', --gainid
    '2025-01-15', --dateofservice
    2000.00, --amount
    '99214', --cptcode
    '25', --cptmodifier
    'Office Visit', --cptdescription
    0.00, --nongainadjustment
    1500.00, --nongainamountpaidtoprovider
    NULL, --reimbursementrate
    1500.00, --amountsent
    1, --quantity
    '7a8b9c0d1e2f3456', --billingid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    '', --status
    NOW(), --createdatetime
    500.00, --balance
    1700.00, --gainprenegotiationamountpaidtoprovider
    300.00 --gainprenegotiationadjustmen
),
-- Reversal
(
    '9f8e7d6c5b4a3210', --gainid
    '2025-01-15', --dateofservice
    2100.00, --amount
    '99214', --cptcode
    '25', --cptmodifier
    'Office Visit', --cptdescription
    0.00, --nongainadjustment
    1500.00, --nongainamountpaidtoprovider
    NULL, --reimbursementrate
    1500.00, --amountsent
    1, --quantity
    'b1c2d3e4f5g6h7i8', --billingid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    '', --status
    NOW(), --createdatetime
    500.00, --balance
    1700.00, --gainprenegotiationamountpaidtoprovider
    300.00 --gainprenegotiationadjustmen
),
--Generates invoice
(
    'a1b2c3d4e5f67890', --gainid
    '2025-01-15', --dateofservice
    7200.00, --amount
    '99214', --cptcode
    '25', --cptmodifier
    'Office Visit', --cptdescription
    0.00, --nongainadjustment
    1500.00, --nongainamountpaidtoprovider
    NULL, --reimbursementrate
    1500.00, --amountsent
    1, --quantity
    '1a2b3c4d5e6f7890', --billingid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    '', --status
    NOW(), --createdatetime
    500.00, --balance
    1700.00, --gainprenegotiationamountpaidtoprovider
    300.00 --gainprenegotiationadjustmen
);

INSERT INTO gain_id_map (
    gainid, 
    gain_createddatetime, 
    gain_modifieddatetime, 
    ati_id, 
    canonical_object
) VALUES (
    '6b7a8f913f2e4d5c',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'charges'
),
(
    'a1b2c3d4e5f67890',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'charges'
),
(
    '9f8e7d6c5b4a3210',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'charges'
);
