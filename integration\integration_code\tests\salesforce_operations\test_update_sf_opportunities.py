import datetime
import typing
from unittest.mock import MagicMock, patch

import pandas as pd
import psycopg
import pytest

import integration.integration_code.salesforce_operations as salesforce_operations


class TestUpdateSfOpportunities:

    @pytest.mark.django_db
    @patch(
        'integration.integration_code.salesforce_operations.get_sf_opportunities_update_rollback_data'
    )
    def test_should_update_opportunity_in_bulk(
        self,
        mock_rollback_data: MagicMock,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_method: MagicMock,
    ):
        """Test that opportunities can be updated in bulk."""
        mock_rollback_data.return_value = pd.DataFrame()

        new_object: dict[typing.<PERSON><PERSON><PERSON>, typing.Any] = {
            "Date_of_Birth__c": "1977-10-18",
            "SSN__c": "",
            "Drivers_License__c": "",
            "Gender__c": "F",
            "Home_Phone__c": "**********",
            "Cell_Phone__c": "**********",
            "Other_Phone__c": "",
            "Plaintiff_Email__c": "",
            "Address__c": "107 Doe Ln",
            "Address_2__c": "",
            "City__c": "Pineville",
            "State__c": "SC",
            "Zip__c": "29468",
            "Case_Status__c": "Still Treating",
            "Date_of_Accident__c": "2021-03-04",
            "Description_of_Accident_Incident__c": "",
            "What_type_of_case__c": "Motor Vehicle Accident",
            "CloseDate": "2024-05-29",
            "Insured_s_Name__c": "",
            "Insurance_Company__c": "",
            "Insurance_Limits__c": "",
            "Insurance_Co_Address__c": "",
            "Insurance_Co_Address_2__c": "",
            "Insurance_Co_City__c": "",
            "Insurance_Co_State__c": "",
            "Insurance_Co_Zipcode__c": "",
            "Insurance_Co_Phone__c": "",
            "Insurance_Co_Fax__c": "",
            "X2_Insured_s_Name__c": "",
            "Insurance_Company_2__c": "",
            "Insurance_Limits_2__c": "",
            "X2_Zipcode__c": "",
            "Insurance_Agent__c": "",
            "ATI_Tail_Claim__c": False,
            "Partner_Account__c": "001Ec000009miJMIAY",
            "Id": "006Ec00000D8RUzIAN",
        }

        salesforce_operations.update_sf_opportunities(
            'gain-servicing',
            'integration/ati/2024-10-23',
            [new_object],
            ['12336'],
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
            True,
            ['12336'],
        )

        data, *_ = mock_sf_action_method.call_args[0]

        assert data[0] == new_object
