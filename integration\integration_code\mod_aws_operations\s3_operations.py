import io
import typing

import boto3
import django.conf

from .. import shared

app_config = django.conf.settings
credentials, settings = app_config.CREDENTIALS, app_config.SETTINGS

aws_session = boto3.Session(
    aws_access_key_id=credentials['integration']['aws']['aws_access_key_id'],
    aws_secret_access_key=credentials['integration']['aws'][
        'aws_secret_access_key'
    ],
    region_name=settings['Common']['AWS']['S3']['region_name'],
)
s3 = aws_session.resource('s3')


def upload_s3_inmemory(
    bucket: str, directory: str, file: str, file_content: bytes
) -> None:
    key_name = f'{directory}/{shared.get_aws_name(file)}'

    buffer = io.BytesIO(file_content)
    buffer.seek(0)  # Ensure we're at the start of the buffer

    # Upload the file content to S3
    s3.meta.client.upload_fileobj(buffer, bucket, key_name)


def download_s3_inmemory(
    bucket: str, key: str, local_location: str | None = None
) -> bytes:
    key_name = key
    if local_location is not None:
        key_name = f'{key}/{shared.get_aws_name(local_location)}'

    buffer = io.BytesIO()
    s3.meta.client.download_fileobj(bucket, key_name, buffer)

    # Seek to the beginning of the BytesIO object
    buffer.seek(0)

    return buffer.getvalue()


def download_s3(bucket: str, key: str, local_location: str) -> None:
    s3.meta.client.download_file(bucket, key, local_location)


def upload_s3(bucket: str, directory: str, file: str) -> None:
    key_name = f'{directory}/{shared.get_aws_name(file)}'
    s3.meta.client.upload_file(Filename=file, Bucket=f'{bucket}', Key=key_name)


def clean_s3(bucket: str, directory: str, files: str) -> None:
    for file in files:
        key_name = f'{directory}/{shared.get_aws_name(file)}'
        s3.Object(bucket, key_name).delete()


def get_s3(bucket: str, prefix: typing.Optional[str] = None) -> list[str]:
    s3_files_list = []
    s3_bucket_resource = s3.Bucket(bucket)
    s3_objects_list = (
        s3_bucket_resource.objects.filter(Prefix=prefix)
        if prefix
        else s3_bucket_resource.objects.all()
    )
    for s3_object in s3_objects_list:
        if not s3_object.key.endswith(
            '/'
        ):  # prevent empty directories from being considered as files
            s3_files_list.append(s3_object.key)
    return s3_files_list


def copy_s3(bucket: str, key: str, new_bucket: str, new_key: str) -> None:
    if not (
        bucket == new_bucket and key == new_key
    ):  # if both bucket and key are the same, then it's the same file => copying the file to the same location causes an error
        s3.meta.client.copy(
            {'Bucket': bucket, 'Key': key}, new_bucket, new_key
        )  # copy only if either bucket or key is different


def move_s3(
    bucket: str, key: str, new_bucket: str, new_key: str
) -> None:  # Cannot move file using boto3, so copy then delete
    if not (
        bucket == new_bucket and key == new_key
    ):  # if both bucket and key are the same, then it's the same file => copying the file to the same location causes an error
        s3.meta.client.copy(
            {'Bucket': bucket, 'Key': key}, new_bucket, new_key
        )  # copy only if either bucket or key is different
        s3.Object(bucket, key).delete()  # if copied, delete original
