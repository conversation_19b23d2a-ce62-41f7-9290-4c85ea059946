import typing
from unittest.mock import MagicMock

import psycopg
import pytest
from psycopg.rows import dict_row

from integration.integration_code import local_operations
from tests import test_helper


class TestUpdateCasesFromBillings:
    # These IDs match the existing test data in the seeds
    CASE_ID = '04b1ace113fe41b1'
    CASE_ID_2 = '04b1ace113fe41b2'
    CASE_ID_3 = '04b1ace113fe41b3'

    BILLING_ID = 'b1c2d3e4f5g6h7i8'
    BILLING_ID_2 = '7a8b9c0d1e2f3456'
    BILLING_ID_3 = '1a2b3c4d5e6f7890'
    TEST_BILLING_1 = 'test_billing_for_case1'
    TEST_BILLING_2 = 'test_billing_for_case2'

    @pytest.mark.django_db
    def test_no_billings(
        self,
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
    ):
        """Should return None when no billings exist"""

        result = local_operations.update_cases_from_billings(["dummy"])

        assert result is None
        assert test_helper.verify_if_log_exists(
            verify_errors, "No data fetched for cases update"
        )

    @pytest.mark.django_db
    def test_valid_billings(self, database: psycopg.Connection[typing.Any]):
        """Should process valid billings and return case IDs"""

        cursor = database.cursor(row_factory=dict_row)

        result = local_operations.update_cases_from_billings([self.BILLING_ID])

        assert result is not None
        assert self.CASE_ID in result

        cursor.execute(f"SELECT * FROM cases WHERE gainid = '{self.CASE_ID}'")
        updated_case = cursor.fetchone()
        assert updated_case is not None

        cursor.execute(
            "SELECT * FROM integrationtimestamps WHERE integrationroute = 'Redshift_Update'"
        )
        timestamp = cursor.fetchone()
        assert timestamp is not None

    @pytest.mark.django_db
    def test_multiple_billings(self, database: psycopg.Connection[typing.Any]):
        """Should process multiple billings and return all case IDs"""

        cursor = database.cursor(row_factory=dict_row)

        result = local_operations.update_cases_from_billings(
            [self.BILLING_ID, self.BILLING_ID_2, self.BILLING_ID_3]
        )

        assert result is not None
        assert self.CASE_ID in result

        cursor.execute(f"SELECT * FROM cases WHERE gainid = '{self.CASE_ID}'")
        updated_case = cursor.fetchone()
        assert updated_case is not None
