import datetime
import typing
from unittest.mock import MagicMock, patch

import pandas as pd
import psycopg
import pytest

import integration.integration_code.salesforce_operations as salesforce_operations


@pytest.fixture
def mock_process_batch_empty():
    """Mock the process_batch function to return an empty response."""
    with patch(
        'integration.integration_code.salesforce_operations.process_batch'
    ) as mock:
        yield mock


class TestUpdateSettledSfObjects:

    @pytest.mark.django_db
    def test_should_update_settled_funding_in_bulk(
        self,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_method: MagicMock,
    ):
        """Test that fundings can be updated with settled status in bulk."""
        test_data = pd.DataFrame(
            {
                'GainId': ['9050d035837c491g'],
                'SalesforceId': ['a01Ec00000AGNhIIAX'],
                'PayoffStatus': ['Paid in Full'],
                'FundingStage': [None],
                'FundingSubStage': [None],
            }
        )

        salesforce_operations.update_settled_sf_objects(
            'billings',
            'Funding__c',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

        data, *_ = mock_sf_action_method.call_args[0]

        expected_data = {
            'Id': 'a01Ec00000AGNhIIAX',
            'Payoff_Status__c': 'Paid in Full',
            'Funding_Stage__c': None,
            'Funding_Sub_Stage__c': None,
        }

        assert data[0] == expected_data

    @pytest.mark.django_db
    def test_should_handle_failed_update_settled_funding(
        self,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_with_error_method: MagicMock,
    ):
        """Test that errors are properly logged when updating settled fundings."""
        test_data = pd.DataFrame(
            {
                'GainId': ['9050d035837c491g', '9050d035837c491h'],
                'SalesforceId': ['a01Ec00000AGNhIIAX', 'a01Ec00000AGNhIIAY'],
                'PayoffStatus': ['Paid in Full', 'Paid in Full'],
                'FundingStage': [None, None],
                'FundingSubStage': [None, None],
            }
        )

        salesforce_operations.update_settled_sf_objects(
            'billings',
            'Funding__c',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

    @pytest.mark.django_db
    def test_should_skip_unsupported_sf_object_types(
        self, database: psycopg.Connection[typing.Any]
    ):
        """Test that unsupported Salesforce object types are skipped."""
        test_data = pd.DataFrame(
            {
                'GainId': ['9050d035837c491g'],
                'SalesforceId': ['001Ec00000nYh1cIAC'],
                'PayoffStatus': ['Paid in Full'],
            }
        )

        # tset with Account object (should be skipped)
        salesforce_operations.update_settled_sf_objects(
            'plaintiffs',
            'Account',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

        # test with opportunity object (should be skipped)
        salesforce_operations.update_settled_sf_objects(
            'cases',
            'Opportunity',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

        # test with charge object (should be skipped)
        salesforce_operations.update_settled_sf_objects(
            'charges',
            'Charge__c',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

        # test with unknown object (should log a message)
        salesforce_operations.update_settled_sf_objects(
            'unknown',
            'Unknown__c',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

    @pytest.mark.django_db
    def test_should_handle_empty_response(
        self,
        database: psycopg.Connection[typing.Any],
        mock_process_batch_empty: MagicMock,
    ):
        """Test that empty responses are handled correctly."""

        test_data = pd.DataFrame(
            {
                'GainId': ['9050d035837c491g'],
                'SalesforceId': ['a01Ec00000AGNhIIAX'],
                'PayoffStatus': ['Paid in Full'],
                'FundingStage': [None],
                'FundingSubStage': [None],
            }
        )

        salesforce_operations.update_settled_sf_objects(
            'billings',
            'Funding__c',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

    @pytest.mark.django_db
    def test_should_update_postgres_sf_modifieddatetime(
        self,
        database: psycopg.Connection[typing.Any],
        mock_sf_action_method: MagicMock,
    ):
        """Test that postgres sf_modifieddatetime is updated correctly."""

        test_data = pd.DataFrame(
            {
                'GainId': ['9050d035837c491g'],
                'SalesforceId': ['a01Ec00000AGNhIIAX'],
                'PayoffStatus': ['Paid in Full'],
                'FundingStage': [None],
                'FundingSubStage': [None],
            }
        )

        salesforce_operations.update_settled_sf_objects(
            'billings',
            'Funding__c',
            'gain-servicing',
            'integration/ati/2024-10-23',
            test_data,
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )
