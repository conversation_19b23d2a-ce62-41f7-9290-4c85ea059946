CREATE TABLE transactions (
    postdatetime TIMESTAMP WITHOUT TIME ZONE,
    paymentdatetime TIMESTAMP WITHOUT TIME ZONE,
    entrydatetime TIMESTAMP WITHOUT TIME ZONE,
    type VARCHAR(100),
    description VARCHAR(1000),
    denialreason VARCHAR(1000),
    amount NUMERIC(10, 4),
    carrierid INTEGER,
    carriername VARCHAR(100),
    carrierinsurancetype VARCHAR(100),
    checknumber VARCHAR(100),
    chargeid VARCHAR(20),
    relevanttogain BOOLEAN,
    sourcecreatedatetime TIMESTAMP WITHOUT TIME ZONE,
    sourcemodifieddatetime TIMESTAMP WITHOUT TIME ZONE,
    modifieddatetime TIMESTAMP WITHOUT TIME ZONE,
    todelete B<PERSON>OLEAN DEFAULT false,
    todeletesystem VARCHAR(100),
    deletepreventoverride BOOLEAN DEFAULT false,
    deletepreventoverridereason VARCHAR(1000),
    status VARCHAR(100) DEFAULT '',
    createdatetime TIMESTAMP WITHOUT TIME ZONE,
    gainid VARCHAR(16) NOT NULL DEFAULT '',
    PRIMARY KEY (gainid),
    FOREIGN KEY (chargeid) REFERENCES charges(gainid)
);