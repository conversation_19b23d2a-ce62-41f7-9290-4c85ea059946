# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning,reportCallIssue=warning
from datetime import datetime, timedelta

from salesforcedata.helper_functions.SfCredentials import SfCredentials


class ResetCaseupdateDate:
    def __init__(self):
        pass

    def reset_caseupdate_date(self, listOFOppIds):
        listOFOppIds = [
            '0061M00001BH96rQAD',
            '0068Y00001HLqfqQAD',
            '0068Y00001HLqj4QAD',
            '0068Y00001F0whSQAR',
            '0061M000012iIvLQAU',
            '0061M00001BHlq9QAD',
        ]
        listOFOppNames = [
            '<PERSON>',
            ' <PERSON> Page',
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
        ]
        listOFOppNamesCaseStatus = [
            "Still Treating",
            "In Litigation",
            "Demand Sent",
            "Still Treating",
            "Gathering Bills and Records",
            "In Litigation",
        ]
        sc = SfCredentials()
        sf = sc.read_sf_credentials(
            './providermapdata/data_update/credentials.json', "production"
        )
        res = {}
        for i, item in enumerate(listOFOppIds):
            if item == '0061M00001BH96rQAD' or item == '0068Y00001HLqfqQAD':
                set_date1 = (datetime.now() - timedelta(days=85)).strftime(
                    '%Y-%m-%d'
                )
                sf.Opportunity.update(
                    item,
                    {
                        'Last_90_Day_Follow_up_Date__c': set_date1,
                        'Case_Status__c': listOFOppNamesCaseStatus[i],
                    },
                )
                res[listOFOppNames[i]] = (
                    "The Last_90_Day date has been updated to {}.".format(
                        set_date1
                    )
                )
            if item == '0068Y00001HLqj4QAD' or item == '0068Y00001F0whSQAR':
                set_date2 = (datetime.now() - timedelta(days=75)).strftime(
                    '%Y-%m-%d'
                )
                sf.Opportunity.update(
                    item,
                    {
                        'Last_90_Day_Follow_up_Date__c': set_date2,
                        'Case_Status__c': listOFOppNamesCaseStatus[i],
                    },
                )
                res[listOFOppNames[i]] = (
                    "The Last_90_Day date has been updated to {}.".format(
                        set_date2
                    )
                )
            if item == '0061M000012iIvLQAU' or item == '0061M00001BHlq9QAD':
                set_date3 = (datetime.now() - timedelta(days=65)).strftime(
                    '%Y-%m-%d'
                )
                sf.Opportunity.update(
                    item,
                    {
                        'Last_90_Day_Follow_up_Date__c': set_date3,
                        'Case_Status__c': listOFOppNamesCaseStatus[i],
                    },
                )
                res[listOFOppNames[i]] = (
                    "The Last_90_Day date has been updated to {};".format(
                        set_date3
                    )
                )
        print(res)
        return res
