{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Docker: Attach to Django",
            "type": "python",
            "request": "attach",
            "connect": {
                "host": "localhost",
                "port": 5678
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "/app"
                }
            ]
        },
        {
            "name": "Local Settings: Internal Web App",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/manage.py",
            "args": [
                "runserver"
            ],
            "envFile": "${workspaceFolder}/.env_local",
            "django": true,
            "justMyCode": true
        },
        {
            "name": "Dev Settings: Internal Web App",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/manage.py",
            "args": [
                "runserver",
                "--settings",
                "GainInternalWebApp.devSettings"
            ],
            "envFile": "${workspaceFolder}/.env_local",
            "django": true,
            "justMyCode": true
        },
        {
            "name": "Staging Settings: Internal Web App",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/manage.py",
            "args": [
                "runserver",
                "--settings",
                "GainInternalWebApp.stagingSettings"
            ],
            "envFile": "${workspaceFolder}/.env_local",
            "django": true,
            "justMyCode": true
        },
        {
            "name": "Prod Settings: Internal Web App",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/manage.py",
            "args": [
                "runserver",
                // "0.0.0.0:8000",
                "--settings",
                "GainInternalWebApp.prodSettings"
            ],
            "envFile": "${workspaceFolder}/.env_prod",
            "django": true,
            "justMyCode": true
        }
    ]
}