CREATE TABLE `integration_audit_log` (
  `audit_log_id` bigint NOT NULL AUTO_INCREMENT,
  `route` varchar(100) NOT NULL,
  `route_section` varchar(1000) NOT NULL,
  `additional_details` longtext,
  `is_error` tinyint(1) NOT NULL,
  `exception_message` longtext,
  `create_date_time` datetime(6) NOT NULL,
  PRIMARY KEY (`audit_log_id`)
);

CREATE TABLE `integration_update_manual_review_data` (
  `update_manual_review_data_id` bigint NOT NULL AUTO_INCREMENT,
  `canonical_object` varchar(100) NOT NULL,
  `salesforce_id` varchar(20) NOT NULL,
  `keys` longtext NOT NULL,
  `redshift_data` longtext NOT NULL,
  `salesforce_data` longtext NOT NULL,
  `review_status` int NOT NULL,
  `create_date_time` datetime(6) NOT NULL,
  `modified_date_time` datetime(6) NOT NULL,
  `redshift_id` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`update_manual_review_data_id`)
);

CREATE TABLE `integration_update_manual_review_log` (
  `update_manual_review_log_id` bigint NOT NULL AUTO_INCREMENT,
  `user` varchar(200) NOT NULL,
  `canonical_object` varchar(100) NOT NULL,
  `salesforce_id` varchar(20) NOT NULL,
  `keys` longtext NOT NULL,
  `reviewed_data` longtext NOT NULL,
  `create_date_time` datetime(6) NOT NULL,
  `update_manual_review_data_id` bigint DEFAULT NULL,
  PRIMARY KEY (`update_manual_review_log_id`),
  KEY `integration_update_m_update_manual_review_70a906e3_fk_integrati` (`update_manual_review_data_id`),
  CONSTRAINT `integration_update_m_update_manual_review_70a906e3_fk_integrati` FOREIGN KEY (`update_manual_review_data_id`) REFERENCES `integration_update_manual_review_data` (`update_manual_review_data_id`)
);

CREATE TABLE `integration_update_manual_review_statuses` (
  `update_manual_review_status_id` bigint NOT NULL AUTO_INCREMENT,
  `update_manual_review_status_name` varchar(100) NOT NULL,
  `update_manual_review_status_description` longtext NOT NULL,
  PRIMARY KEY (`update_manual_review_status_id`)
);

INSERT INTO `integration_update_manual_review_statuses`
(`update_manual_review_status_id`,
`update_manual_review_status_name`,
`update_manual_review_status_description`)
VALUES
(1, "Not Reviewed", ""),
(2, "Manually Reviewed", ""),
(3, "Automatically Reviewed", "");

CREATE TABLE `predictive_analytics_audit_log` (
  `audit_log_id` bigint NOT NULL AUTO_INCREMENT,
  `salesforce_id` varchar(20) NOT NULL,
  `model` varchar(100) NOT NULL,
  `input_parameters` longtext,
  `predicted_value` longtext,
  `is_error` tinyint(1) NOT NULL,
  `exception_message` longtext,
  `create_date_time` datetime(6) NOT NULL,
  PRIMARY KEY (`audit_log_id`)
);
