# pyright: reportArgumentType=warning
import os

import openai


def initialize_openai() -> openai.OpenAI:
    api_key = os.getenv('CREDENTIALS_CHATGPT_API_KEY')
    if api_key is None:
        raise Exception('CREDENTIALS_CHATGPT_API_KEY is not set')
    return openai.OpenAI(api_key=api_key)


def chatGPT_conversation(
    conversation: list[dict[str, str]], model: str = 'gpt-4.1-mini'
) -> list[dict[str, str]]:
    if conversation is None or len(conversation) == 0:
        raise Exception('Conversation is not set')

    try:
        client = initialize_openai()
    except Exception as e:
        raise Exception(f'Failed to initialize OpenAI: {str(e)}')

    response = client.chat.completions.create(
        model=model, messages=conversation
    )
    content = response.choices[0].message.content
    if content is None:
        raise Exception('Response failed to generate')
    role = response.choices[0].message.role
    conversation.append(
        {
            'role': role,
            'content': content,
        }
    )

    return conversation
