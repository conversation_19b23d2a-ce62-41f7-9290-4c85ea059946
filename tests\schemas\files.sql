CREATE TABLE files (
    plaintiffid character varying(20),
    url character varying(1000),
    type character varying(50),
    caseid character varying(20),
    relevanttogain boolean,
    sourcecreatedatetime timestamp without time zone,
    sourcemodifieddatetime timestamp without time zone,
    modifieddatetime timestamp without time zone,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    createdatetime timestamp without time zone,
    gainid character varying(16) NOT NULL DEFAULT '':: character varying,
    PRIMARY KEY (gainid),
    FOREIGN KEY (plaintiffid) REFERENCES plaintiffs(gainid),
    FOREIGN KEY (caseid) REFERENCES cases(gainid)
);