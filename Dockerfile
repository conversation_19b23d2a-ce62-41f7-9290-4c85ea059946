FROM python:3.10

EXPOSE 8000

# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1

# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1

WORKDIR /app

RUN apt-get update && \
    apt-get install gcc ffmpeg libsm6 libxext6 libssl-dev default-libmysqlclient-dev python3-dev -y && \
    python -m pip install --upgrade pip==23.2 && \
    pip install pdm==2.20.*

COPY pyproject.toml ./

RUN pdm lock --prod --python=3.10 --platform=linux && pdm export -f requirements --prod > requirements.txt && \
    pip install --upgrade pip setuptools wheel && pip install --no-build-isolation -r requirements.txt  --timeout 1200

COPY . .

COPY entrypoint.sh /app/entrypoint.sh

RUN chmod +x /app/entrypoint.sh

CMD ["/app/entrypoint.sh"]