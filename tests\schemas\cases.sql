CREATE TABLE cases (
    plaintiffname character varying(100),
    plaintiffdateofbirth date,
    status character varying(100),
    accidentdate date,
    injuredbodyparts character varying(100),
    accidentdescription character varying(1000),
    type character varying(100),
    accidentstate character varying(100),
    plaintiffid character varying(20),
    relevanttogain boolean,
    sourcecreatedatetime timestamp without time zone,
    sourcemodifieddatetime timestamp without time zone,
    modifieddatetime timestamp without time zone,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    attorneyid character varying(20),
    paralegalid character varying(20),
    casemanagerid character varying(20),
    cocounselid character varying(20),
    coparalegalid character varying(20),
    cocasemanagerid character varying(20),
    grandtotalamount numeric(10, 4),
    grandtotalnongainadjustment numeric(10, 4),
    grandtotalnongainamountpaidtoprovider numeric(10, 4),
    grandtotalbalance numeric(10, 4),
    tailclaimcase boolean DEFAULT false,
    datetreatmentcompleted date,
    treatmentcompleted boolean DEFAULT false,
    createdatetime timestamp without time zone,
    insurancevendorassigned boolean DEFAULT false,
    grandtotalsettlementamount numeric(10, 4),
    notes character varying(1000),
    datesettled timestamp without time zone,
    gainid character varying(16) NOT NULL DEFAULT '':: character varying,
    paidto character varying(100) DEFAULT 'Gain':: character varying,
    paidby character varying(100),
    servicingstartdatetime timestamp without time zone,
    servicingenddatetime timestamp without time zone,
    grandtotalgainprenegotiationadjustment numeric(10, 4),
    grandtotalgainprenegotiationamountpaidtoprovider numeric(10, 4),
    PRIMARY KEY (gainid),
    FOREIGN KEY (plaintiffid) REFERENCES plaintiffs(gainid),
    FOREIGN KEY (attorneyid) REFERENCES legalpersonnel(gainid),
    FOREIGN KEY (paralegalid) REFERENCES legalpersonnel(gainid),
    FOREIGN KEY (casemanagerid) REFERENCES legalpersonnel(gainid),
    FOREIGN KEY (cocounselid) REFERENCES legalpersonnel(gainid),
    FOREIGN KEY (coparalegalid) REFERENCES legalpersonnel(gainid),
    FOREIGN KEY (cocasemanagerid) REFERENCES legalpersonnel(gainid)
);