import datetime
import pathlib

import django.conf

from . import (
    aws_operations,
    context,
    jopari_operations,
    local_operations,
    logger_config,
)

logger = logger_config.get_logger()

app_config = django.conf.settings
settings = app_config.SETTINGS


def get_data(
    timestamp: str,
    bucket: str,
    directory: str,
    sub_directory: str,
    move_flag: bool,
    call_type: str,
    all_data: bool,
    charges_update_flag: bool = False,
    billings_update_flag: bool = False,
    cases_update_flag: bool = False,
) -> tuple[set[str], set[str]]:
    auth_succ = jopari_operations.authenticate_jopari_server()
    charges_gainid_set, billings_gainid_set = set(), set()
    if not auth_succ:
        logger.error(
            'Jopari SFTP server authentication failed => data fetch skipped'
        )
        return charges_gainid_set, billings_gainid_set
    local_files = None
    data_to_process, s3_files_to_move = jopari_operations.get_data_jopari(
        timestamp, bucket, directory, call_type, all_data
    )
    if call_type == 'upsert':
        (
            local_files,
            charges_gainid_set,
            billings_gainid_set,
        ) = jopari_operations.process_upsert_data_jopari(
            timestamp,
            bucket,
            directory,
            sub_directory,
            data_to_process,
            charges_update_flag,
            billings_update_flag,
            cases_update_flag,
        )
    if (
        move_flag
    ):  # whether to move files from Unprocessed to Processed => True by default, pass False for QA/testing ease-of-use
        jopari_operations.post_process_data_jopari(bucket, s3_files_to_move)
    if local_files is not None:
        for file in local_files.values():
            pathlib.Path(file).unlink(missing_ok=True)
    return charges_gainid_set, billings_gainid_set


def get_main_upsert(
    move_flag: bool,
    charges_update_flag: bool,
    billings_update_flag: bool,
    cases_update_flag: bool,
    all_data: bool,
) -> None:
    '''
    Get upsert from Jopari, post to Canonical in Redshift
    '''
    context.request_context.set({'route': 'Jopari_To_Redshift_Upsert'})
    timestamp_date = datetime.datetime.now()
    timestamp = timestamp_date.strftime('%Y-%m-%d %H:%M:%S')
    updated_billing_keys = False
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if (
        last_run_time == -1
    ):  # only problem is if all_data=False i.e. delta changes but last run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for Jopari_To_Redshift missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    bucket, directory, sub_directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["Jopari"]["S3"]["sub_directory"]}/upsert',
        f'results/{datetime.date.today()}',
    )
    (
        charges_gainid_set,
        billings_gainid_set,
    ) = get_data(
        timestamp,
        bucket,
        directory,
        sub_directory,
        move_flag,
        'upsert',
        all_data,
        charges_update_flag,
        billings_update_flag,
        cases_update_flag,
    )
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    updated_charge_keys = []
    # update billings using charges aggregating non-gain adjustments and non-gain payments
    if billings_update_flag:
        charges_id_subset = set([gainid for gainid in charges_gainid_set])
        if updated_charge_keys:
            charges_id_subset.update(updated_charge_keys)
        charges_id_subset = list(charges_id_subset)
        updated_billing_keys = local_operations.update_billings_from_charges(
            charges_id_subset
        )
    # update cases using billings aggregating non-gain adjustments and non-gain payments
    if cases_update_flag:
        billings_id_subset = set([gainid for gainid in billings_gainid_set])
        if updated_billing_keys:
            billings_id_subset.update(updated_billing_keys)
        billings_id_subset = list(billings_id_subset)
        local_operations.update_cases_from_billings(billings_id_subset)
    return
