-- Test data for insurances
INSERT INTO insurances (
    gainid,
    companyname,
    policyid,
    memberid,
    groupnumber,
    status,
    limits,
    phone,
    fax,
    email,
    followupemail,
    billingaddressline1,
    billingaddressline2,
    billingaddresscity,
    billingaddressstate,
    billingaddresszip,
    physicaladdressline1,
    physicaladdressline2,
    physicaladdresscity,
    physicaladdressstate,
    physicaladdresszip,
    liabilityaccepted,
    declarationpagereceived,
    medpayexhausted,
    pipexhausted,
    probate,
    bankruptcy,
    subrogationlien,
    otherlien,
    otherlienname,
    drivername,
    datesettled,
    howcasewassettled,
    totalsettlementamount,
    attorneyfee,
    attorneyfeeflexible,
    referralfeepercentage,
    amounttoclient,
    settlementnotes,
    notes,
    type,
    caseid,
    relevanttogain,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    billspaid
) VALUES (
    'insur1234567890', -- gainid
    'ABC Insurance Co', -- companyname
    'POL123456', -- policyid
    'MEM789012', -- memberid
    'GRP345678', -- groupnumber
    'Active', -- status
    '100000', -- limits
    '************', -- phone
    '************', -- fax
    '<EMAIL>', -- email
    '<EMAIL>', -- followupemail
    '123 Insurance Blvd', -- billingaddressline1
    'Suite 400', -- billingaddressline2
    'Insurance City', -- billingaddresscity
    'CA', -- billingaddressstate
    '90210', -- billingaddresszip
    '123 Insurance Blvd', -- physicaladdressline1
    'Suite 400', -- physicaladdressline2
    'Insurance City', -- physicaladdresscity
    'CA', -- physicaladdressstate
    '90210', -- physicaladdresszip
    TRUE, -- liabilityaccepted
    TRUE, -- declarationpagereceived
    FALSE, -- medpayexhausted
    FALSE, -- pipexhausted
    FALSE, -- probate
    FALSE, -- bankruptcy
    FALSE, -- subrogationlien
    FALSE, -- otherlien
    NULL, -- otherlienname
    'John Smith', -- drivername
    '2023-06-15 00:00:00', -- datesettled
    'Negotiation', -- howcasewassettled
    25000.00, -- totalsettlementamount
    8000.00, -- attorneyfee
    TRUE, -- attorneyfeeflexible
    5.00, -- referralfeepercentage
    15000.00, -- amounttoclient
    'Settlement reached after negotiation', -- settlementnotes
    'Standard insurance claim', -- notes
    'Auto', -- type
    '04b1ace113fe41b1', -- caseid
    TRUE, -- relevanttogain
    FALSE, -- todelete
    NULL, -- todeletesystem
    FALSE, -- deletepreventoverride
    NULL, -- deletepreventoverridereason
    TRUE -- billspaid
),
(
    'insur2345678901', -- gainid
    'XYZ Insurance Group', -- companyname
    'POL987654', -- policyid
    'MEM654321', -- memberid
    'GRP123456', -- groupnumber
    'Active', -- status
    '250000', -- limits
    '************', -- phone
    '************', -- fax
    '<EMAIL>', -- email
    '<EMAIL>', -- followupemail
    '456 Insurance Ave', -- billingaddressline1
    'Floor 12', -- billingaddressline2
    'Insurance Town', -- billingaddresscity
    'NY', -- billingaddressstate
    '10001', -- billingaddresszip
    '456 Insurance Ave', -- physicaladdressline1
    'Floor 12', -- physicaladdressline2
    'Insurance Town', -- physicaladdresscity
    'NY', -- physicaladdressstate
    '10001', -- physicaladdresszip
    TRUE, -- liabilityaccepted
    TRUE, -- declarationpagereceived
    FALSE, -- medpayexhausted
    FALSE, -- pipexhausted
    FALSE, -- probate
    FALSE, -- bankruptcy
    FALSE, -- subrogationlien
    FALSE, -- otherlien
    NULL, -- otherlienname
    'Jane Doe', -- drivername
    '2023-07-20 00:00:00', -- datesettled
    'Mediation', -- howcasewassettled
    35000.00, -- totalsettlementamount
    10000.00, -- attorneyfee
    TRUE, -- attorneyfeeflexible
    5.00, -- referralfeepercentage
    22000.00, -- amounttoclient
    'Settlement reached after mediation', -- settlementnotes
    'Complex insurance claim', -- notes
    'Auto', -- type
    '04b1ace113fe41b2', -- caseid
    TRUE, -- relevanttogain
    FALSE, -- todelete
    NULL, -- todeletesystem
    FALSE, -- deletepreventoverride
    NULL, -- deletepreventoverridereason
    TRUE -- billspaid
),
(
    'insur3456789012', -- gainid
    'DEF Insurance Ltd', -- companyname
    'POL456789', -- policyid
    'MEM123789', -- memberid
    'GRP789123', -- groupnumber
    'Active', -- status
    '500000', -- limits
    '************', -- phone
    '************', -- fax
    '<EMAIL>', -- email
    '<EMAIL>', -- followupemail
    '789 Insurance St', -- billingaddressline1
    'Unit 300', -- billingaddressline2
    'Insurance Village', -- billingaddresscity
    'TX', -- billingaddressstate
    '75001', -- billingaddresszip
    '789 Insurance St', -- physicaladdressline1
    'Unit 300', -- physicaladdressline2
    'Insurance Village', -- physicaladdresscity
    'TX', -- physicaladdressstate
    '75001', -- physicaladdresszip
    TRUE, -- liabilityaccepted
    TRUE, -- declarationpagereceived
    FALSE, -- medpayexhausted
    FALSE, -- pipexhausted
    FALSE, -- probate
    FALSE, -- bankruptcy
    FALSE, -- subrogationlien
    FALSE, -- otherlien
    NULL, -- otherlienname
    'Robert Johnson', -- drivername
    '2023-08-10 00:00:00', -- datesettled
    'Arbitration', -- howcasewassettled
    45000.00, -- totalsettlementamount
    15000.00, -- attorneyfee
    TRUE, -- attorneyfeeflexible
    5.00, -- referralfeepercentage
    27000.00, -- amounttoclient
    'Settlement reached after arbitration', -- settlementnotes
    'High-value insurance claim', -- notes
    'Auto', -- type
    '04b1ace113fe41b3', -- caseid
    TRUE, -- relevanttogain
    FALSE, -- todelete
    NULL, -- todeletesystem
    FALSE, -- deletepreventoverride
    NULL, -- deletepreventoverridereason
    TRUE -- billspaid
);

-- Add entries to gain_id_map for the insurance records
INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
) VALUES (
    'insur1234567890',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'insur1234567890',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'insurances'
),
(
    'insur2345678901',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'insur2345678901',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'insurances'
),
(
    'insur3456789012',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'insur3456789012',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'insurances'
);
