[build-system]
requires = ["setuptools<66", "wheel"]
build-backend = "setuptools.build_meta"

[tool.pdm]
distribution = false

[tool.pyright]
typeCheckingMode = "basic"
reportCallIssue = "error"
reportPossiblyUnboundVariable = "warning"
reportOptionalSubscript = "error"
reportArgumentType = "error"
reportOptionalOperand = "warning"
reportOptionalMemberAccess = "warning"
reportAttributeAccessIssue = "error"
reportReturnType = "error"
reportGeneralTypeIssues = "error"
reportAssignmentType = "warning"
reportIndexIssue = "warning"
reportOperatorIssue = "warning"
reportOptionalIterable = "warning"
reportUnusedVariable = "error"
reportUnusedImport = "error"
reportMissingParameterType = "error"
reportMissingTypeArgument = "error"

[tool.black]
line-length = 79
skip-string-normalization = true

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 79

[tool.pdm.scripts]
post_install = { shell = "pre-commit install" }
_.env_file = ".env_local"

[tool.pdm.dev-dependencies]
test = [
    "pytest>=8.3.3",
    "pytest-django>=4.9.0",
    "pytest-cov>=5.0.0",
    "moto[s3]>=5.0.18",
    "pytest-postgresql>=6.1.1",
]
dev = [
    "isort==5.12.0",
    "black==24.8.0",
    "pre-commit==4.0.0",
    "pyright>=1.1.388",
    "pandas-stubs>=2.0.1.230501",
    "django-stubs>=5.1.3",
    "boto3-stubs[s3]>=1.38.38",
    "pytest-snapshot>=0.9.0",
    "freezegun>=1.5.2",
]
[project]
dependencies = [
    "aiohttp==3.8.4",
    "aiosignal==1.3.1",
    "asgiref==3.5.2",
    "asn1crypto==1.5.1",
    "async-timeout==4.0.2",
    "attrs==21.4.0",
    "Authlib==1.0.1",
    "boto3==1.24.35",
    "botocore==1.27.35",
    "cached-property==1.5.2",
    "certifi==2022.6.15",
    "cffi==1.15.1",
    "charset-normalizer==2.0.7",
    "ci-info==0.2.0",
    "click==8.1.3",
    "colorama==0.4.5",
    "configobj==5.0.6",
    "configparser==5.2.0",
    "cryptography==36.0.2",
    "deprecation==2.1.0",
    "distro==1.7.0",
    "Django==4.0.6",
    "django-allow-cidr==0.5.0",
    "django-cors-headers==3.13.0",
    "djangorestframework==3.13.1",
    "etelemetry==0.3.0",
    "faiss-cpu==1.7.4",
    "filelock==3.7.1",
    "fpdf==1.7.2",
    "frozenlist==1.3.3",
    "future==0.18.2",
    "httplib2==0.20.4",
    "idna==3.3",
    "img2pdf==0.4.4",
    "isodate==0.6.1",
    "Jinja2==3.1.2",
    "jmespath==1.0.1",
    "joblib==1.3.2",
    "langchain==0.1.8",
    "langchain-community==0.0.21",
    "langchain-core==0.1.24",
    "langchain-openai==0.0.6",
    "langchainhub==0.1.14",
    "looseversion==1.0.1",
    "lxml==4.9.1",
    "MarkupSafe==2.1.3",
    "mpmath==1.3.0",
    "multidict==6.0.4",
    "mypy-extensions==1.0.0",
    "networkx==2.8.5",
    "nibabel==4.0.1",
    "nipype==1.8.3",
    "openai==1.56.1",
    "opencv-python-headless==********",
    "oscrypto==1.3.0",
    "packaging==23.2",
    "pandas==1.4.3",
    "pathlib==1.0.1",
    "pathspec==0.11.1",
    "pikepdf==6.1.0",
    "Pillow==9.2.0",
    "platformdirs==2.5.2",
    "prov==2.0.0",
    # Install with binary dependency, else will cause error
    # Do NOT change the line below, even if pip freeze generates 2-line entry
    # 2-line variant also causes error
    "psycopg[binary]==3.1.4",
    "pycparser==2.21",
    "pycryptodomex==3.15.0",
    "pydot==1.4.2",
    "PyJWT==2.4.0",
    "PyMuPDF==1.20.1",
    "PyMySQL==1.0.2",
    "pyOpenSSL==22.0.0",
    "pyparsing==3.0.9",
    "PyPDF2==3.0.1",
    "python-dateutil==2.8.2",
    "pytz==2022.1",
    "rdflib==6.1.1",
    "requests==2.28.1",
    "requests-file==1.5.1",
    "requests-toolbelt==0.9.1",
    "s3transfer==0.6.0",
    # Ensure scikit-learn is not upgraded beyond this version due to deserialization of model input scaler.
    # Model input scaler is serialized with scikit-learn==1.3.1
    "scikit-learn<=1.3.1",
    "simple-salesforce==1.12.1",
    "simplejson==3.17.6",
    "six==1.16.0",
    # Install with pandas dependency, else will cause error
    "snowflake-connector-python[pandas]==2.7.9",
    "sqlparse==0.4.2",
    "sympy==1.12",
    "threadpoolctl==3.2.0",
    "toml==0.10.2",
    "tomli==2.0.1",
    "tqdm==4.65.0",
    "traits==6.3.2",
    "tzdata==2022.1",
    "urllib3==1.26.10",
    "yapf==0.32.0",
    "scipy==1.8.1",
    "torch==2.0.1",
    "numpy==1.23.1",
    "zeep==4.1.0",
    "yarl==1.9.2",
    "pyarrow==6.0.1",
    "cachetools>=5.5.0",
    "python-decouple>=3.8",
]


requires-python = "<3.11,>=3.9"
name = "gaininternalwebapp"
version = "0.1.0"
description = "Default template for PDM package"
authors = []
readme = "README.md"
license = { text = "MIT" }
