{"Plaintiffs": {"GainId": 0, "Name": 1, "DateOfBirth": 2, "Ssn": 3, "TaxId": 4, "DriverLicense": 5, "Gender": 6, "MaritalStatus": 7, "Company": 8, "HomePhone": 9, "CellPhone": 10, "BusinessPhone": 11, "OtherPhone": 12, "PrimaryEmail": 13, "SecondaryEmail": 14, "PrimaryAddressLine1": 15, "PrimaryAddressLine2": 16, "PrimaryAddressCity": 17, "PrimaryAddressState": 18, "PrimaryAddressZip": 19, "OtherAddressLine1": 20, "OtherAddressLine2": 21, "OtherAddressCity": 22, "OtherAddressState": 23, "OtherAddressZip": 24, "DepartmentId": 25, "RelevantToGain": 26, "ModifiedDateTime": 27, "ToDelete": 28, "ToDeleteSystem": 29, "DeletePreventOverride": 30, "DeletePreventOverrideReason": 31}, "MedicalFacilities": {"GainId": 0, "Name": 1, "Phone": 2, "Fax": 3, "Email": 4, "FollowUpEmail": 5, "BillingAddressLine1": 6, "BillingAddressLine2": 7, "BillingAddressCity": 8, "BillingAddressState": 9, "BillingAddressZip": 10, "PhysicalAddressLine1": 11, "PhysicalAddressLine2": 12, "PhysicalAddressCity": 13, "PhysicalAddressState": 14, "PhysicalAddressZip": 15, "Website": 16, "ContractType": 17, "Specialties": 18, "Description": 19, "PortalAccount": 20, "Notes": 21, "ParentId": 22, "RelevantToGain": 23, "ModifiedDateTime": 24, "ToDelete": 25, "ToDeleteSystem": 26, "DeletePreventOverride": 27, "DeletePreventOverrideReason": 28}, "LawFirms": {"GainId": 0, "Name": 1, "Phone": 2, "Fax": 3, "Email": 4, "FollowUpEmail": 5, "BillingAddressLine1": 6, "BillingAddressLine2": 7, "BillingAddressCity": 8, "BillingAddressState": 9, "BillingAddressZip": 10, "PhysicalAddressLine1": 11, "PhysicalAddressLine2": 12, "PhysicalAddressCity": 13, "PhysicalAddressState": 14, "PhysicalAddressZip": 15, "Website": 16, "TypeOfLaw": 17, "Description": 18, "EmployeeCountRange": 19, "DoNotFund": 20, "DoNotFundType": 21, "AutomaticCaseUpdateRequest": 22, "NonResponsive": 23, "NonResponsiveNote": 24, "PortalAccount": 25, "PortalRewardsParticipant": 26, "Notes": 27, "ParentId": 28, "RelevantToGain": 29, "ModifiedDateTime": 30, "ToDelete": 31, "ToDeleteSystem": 32, "DeletePreventOverride": 33, "DeletePreventOverrideReason": 34}, "LegalPersonnel": {"GainId": 0, "Name": 1, "Title": 2, "HomePhone": 3, "CellPhone": 4, "BusinessPhone": 5, "BusinessPhoneExt": 6, "OtherPhone": 7, "Fax": 8, "PrimaryEmail": 9, "SecondaryEmail": 10, "PrimaryAddressLine1": 11, "PrimaryAddressLine2": 12, "PrimaryAddressCity": 13, "PrimaryAddressState": 14, "PrimaryAddressZip": 15, "OtherAddressLine1": 16, "OtherAddressLine2": 17, "OtherAddressCity": 18, "OtherAddressState": 19, "OtherAddressZip": 20, "Notes": 21, "LawFirmId": 22, "LawFirmName": 23, "RelevantToGain": 24, "ModifiedDateTime": 25, "ToDelete": 26, "ToDeleteSystem": 27, "DeletePreventOverride": 28, "DeletePreventOverrideReason": 29}, "Cases": {"GainId": 0, "ServicingStartDateTime": 1, "ServicingEndDateTime": 2, "PlaintiffName": 3, "PlaintiffDateOfBirth": 4, "Status": 5, "AccidentDate": 6, "InjuredBodyParts": 7, "AccidentDescription": 8, "AccidentState": 9, "TreatmentCompleted": 10, "DateTreatmentCompleted": 11, "InsuranceVendorAssigned": 12, "GrandTotalAmount": 13, "GrandTotalNonGainAdjustment": 14, "GrandTotalNonGainAmountPaidToProvider": 15, "GrandTotalDeductible": 16, "GrandTotalCoinsurance": 17, "GrandTotalCopayment": 18, "GrandTotalBalance": 19, "DateSettled": 20, "GrandTotalSettlementAmount": 21, "PaidTo": 22, "PaidBy": 23, "Notes": 24, "Type": 25, "PlaintiffId": 26, "AttorneyId": 27, "ParalegalId": 28, "CaseManagerId": 29, "CoCounselId": 30, "CoParalegalId": 31, "CoCaseManagerId": 32, "TailClaimCase": 33, "RelevantToGain": 34, "ModifiedDateTime": 35, "ToDelete": 36, "ToDeleteSystem": 37, "DeletePreventOverride": 38, "DeletePreventOverrideReason": 39}, "Intakes": {"GainId": 0, "AccidentDescription": 1, "AccidentDateTime": 2, "WereSFPremisePicsTaken": 3, "WasPoliceCalled": 4, "WasPoliceReportGenerated": 5, "WereCitationsIssued": 6, "ClientsVehicleMakeModel": 7, "ClientsVehicleDamage": 8, "DefendantsVehicleMakeModel": 9, "DefendantsVehicleDamage": 10, "WereAutoDamagePicsTaken": 11, "WasVehicleTowed": 12, "VehicleDamageEstimate": 13, "InjuriesDescription": 14, "WereInjuryPicsTaken": 15, "PriorInjuries": 16, "ClientJobTitle": 17, "ClientEmploymentStatus": 18, "ClientLostWagesStart": 19, "ClientJobDuties": 20, "ClientLostWagesEnd": 21, "ClientsHourlySalaryRate": 22, "ClientBankruptcy": 23, "ThingsClientCanNoLongerDo": 24, "ThingsClientCanDoWithPain": 25, "ClientLossOfLifeEnjoyment": 26, "ClientHealthHistory": 27, "ClientSubsequentAccidents": 28, "ClientSubsequentAccidentsExplanation": 29, "ClientObservations": 30, "ClientOnSocialMedia": 31, "ClientSocialMediaNotes": 32, "DoesClientHaveHealthInsurance": 33, "DoesClientHaveSecondHealthInsurance": 34, "Type": 35, "CaseId": 36, "RelevantToGain": 37, "ModifiedDateTime": 38, "ToDelete": 39, "ToDeleteSystem": 40, "DeletePreventOverride": 41, "DeletePreventOverrideReason": 42}, "Insurances": {"GainId": 0, "CompanyName": 1, "PolicyId": 2, "MemberId": 3, "GroupNumber": 4, "Status": 5, "Limits": 6, "Phone": 7, "Fax": 8, "Email": 9, "FollowUpEmail": 10, "BillingAddressLine1": 11, "BillingAddressLine2": 12, "BillingAddressCity": 13, "BillingAddressState": 14, "BillingAddressZip": 15, "PhysicalAddressLine1": 16, "PhysicalAddressLine2": 17, "PhysicalAddressCity": 18, "PhysicalAddressState": 19, "PhysicalAddressZip": 20, "LiabilityAccepted": 21, "DeclarationPageReceived": 22, "MedpayExhausted": 23, "PIPExhausted": 24, "Probate": 25, "Bankruptcy": 26, "SubrogationLien": 27, "OtherLien": 28, "OtherLienName": 29, "DriverName": 30, "DateSettled": 31, "HowCaseWasSettled": 32, "TotalSettlementAmount": 33, "BillsPaid": 34, "AttorneyFee": 35, "AttorneyFeeFlexible": 36, "ReferralFeePercentage": 37, "AmountToClient": 38, "SettlementNotes": 39, "Notes": 40, "Type": 41, "CaseId": 42, "RelevantToGain": 43, "ToDelete": 44, "ToDeleteSystem": 45, "DeletePreventOverride": 46, "DeletePreventOverrideReason": 47}, "Liens": {"GainId": 0, "AmountDue": 1, "LienHolderName": 2, "LienFileNumber": 3, "OriginalPaidAmount": 4, "IsThereExcess": 5, "ExcessAmount": 6, "ExcludeFromSettlement": 7, "Notes": 8, "Type": 9, "CaseId": 10, "RelevantToGain": 11, "ToDelete": 12, "ToDeleteSystem": 13, "DeletePreventOverride": 14, "DeletePreventOverrideReason": 15}, "Disbursals": {"GainId": 0, "Status": 1, "AmountDue": 2, "CheckNumber": 3, "CheckDate": 4, "AmountPaid": 5, "Type": 6, "CaseId": 7, "RelevantToGain": 8, "ToDelete": 9, "ToDeleteSystem": 10, "DeletePreventOverride": 11, "DeletePreventOverrideReason": 12}, "Billings": {"GainId": 0, "ServicingStartDateTime": 1, "ServicingEndDateTime": 2, "MedicalClaimNumber": 3, "DateOfService": 4, "Status": 5, "TotalAmount": 6, "TotalNonGainAdjustment": 7, "TotalNonGainAmountPaidToProvider": 8, "TotalGainPreNegotiationAdjustment": 9, "TotalGainPreNegotiationAmountPaidToProvider": 10, "TotalDeductible": 11, "TotalCoinsurance": 12, "TotalCopayment": 13, "TotalBalance": 14, "TotalAmountSent": 15, "MedicalFacilityAddressLine1": 16, "MedicalFacilityAddressLine2": 17, "MedicalFacilityAddressCity": 18, "MedicalFacilityAddressState": 19, "MedicalFacilityAddressZip": 20, "PaidTo": 21, "PaidBy": 22, "Notes": 23, "GainType": 24, "Type": 25, "CaseId": 26, "MedicalFacilityId": 27, "PartnerAccountId": 28, "RelevantToGain": 29, "ModifiedDateTime": 30, "ToDelete": 31, "ToDeleteSystem": 32, "DeletePreventOverride": 33, "DeletePreventOverrideReason": 34}, "Charges": {"GainId": 0, "DateOfService": 1, "Status": 2, "Amount": 3, "CPTCode": 4, "CPTModifier": 5, "CPTDescription": 6, "NonGainAdjustment": 7, "NonGainAmountPaidToProvider": 8, "GainPreNegotiationAdjustment": 9, "GainPreNegotiationAmountPaidToProvider": 10, "Deductible": 11, "Coinsurance": 12, "Copayment": 13, "Balance": 14, "ReimbursementRate": 15, "AmountSent": 16, "Quantity": 17, "BillingId": 18, "RelevantToGain": 19, "ModifiedDateTime": 20, "ToDelete": 21, "ToDeleteSystem": 22, "DeletePreventOverride": 23, "DeletePreventOverrideReason": 24}, "Transactions": {"GainId": 0, "PostDateTime": 1, "PaymentDateTime": 2, "EntryDateTime": 3, "Status": 4, "DenialReason": 5, "Amount": 6, "CarrierId": 7, "CarrierName": 8, "CarrierInsuranceType": 9, "CheckNumber": 10, "Description": 11, "Type": 12, "CARCCode": 13, "ChargeId": 14, "RelevantToGain": 15, "ModifiedDateTime": 16, "ToDelete": 17, "ToDeleteSystem": 18, "DeletePreventOverride": 19, "DeletePreventOverrideReason": 20}, "Files": {"GainId": 0, "Url": 1, "Type": 2, "PlaintiffId": 3, "CaseId": 4, "RelevantToGain": 5, "ModifiedDateTime": 6, "ToDelete": 7, "ToDeleteSystem": 8, "DeletePreventOverride": 9, "DeletePreventOverrideReason": 10}, "Surgery": {"GainId": 0, "Surgeon": 1, "Date": 2, "Charges": 3, "Comments": 4, "Type": 5, "CaseId": 6, "RelevantToGain": 7, "ModifiedDateTime": 8, "ToDelete": 9, "ToDeleteSystem": 10, "DeletePreventOverride": 11, "DeletePreventOverrideReason": 12}, "Notes": {"GainId": 0, "NoteCreatorName": 1, "Note": 2, "PlaintiffId": 3, "MedicalFacilityId": 4, "LawFirmId": 5, "LegalPersonnelId": 6, "CaseId": 7, "IntakeId": 8, "InsuranceId": 9, "LienId": 10, "DisbursalId": 11, "BillingId": 12, "ChargeId": 13, "TransactionId": 14, "FileId": 15, "SurgeryId": 16, "RelevantToGain": 17, "ModifiedDateTime": 18, "ToDelete": 19, "ToDeleteSystem": 20, "DeletePreventOverride": 21, "DeletePreventOverrideReason": 22}}