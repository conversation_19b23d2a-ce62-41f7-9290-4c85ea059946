[[{"gainid": "[AUTO_GENERATED]", "servicingstartdatetime": "2023-01-01 12:00:00", "servicingenddatetime": "", "medicalclaimnumber": "0003557_24-Detaillines-<PERSON><PERSON><PERSON>", "dateofservice": "2023-10-05", "status": "", "totalamount": "1218.0", "totalnongainadjustment": "", "totalnongainamountpaidtoprovider": "", "totalgainprenegotiationadjustment": "", "totalgainprenegotiationamountpaidtoprovider": "", "totaldeductible": "", "totalcoinsurance": "", "totalcopayment": "", "totalbalance": "1218.0", "totalamountsent": "", "medicalfacilityaddressline1": "", "medicalfacilityaddressline2": "", "medicalfacilityaddresscity": "", "medicalfacilityaddressstate": "", "medicalfacilityaddresszip": "", "paidto": "", "paidby": "", "notes": "", "gaintype": "Medical Funding", "type": "Medical Funding", "caseid": "[AUTO_GENERATED]", "medicalfacilityid": "[AUTO_GENERATED]", "partneraccountid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "servicingstartdatetime": "2023-01-01 12:00:00", "servicingenddatetime": "", "plaintiffname": "<PERSON><PERSON>", "plaintiffdateofbirth": "1961-03-26", "status": "", "accidentdate": "2023-09-22", "injuredbodyparts": "", "accidentdescription": "", "accidentstate": "Florida", "treatmentcompleted": "", "datetreatmentcompleted": "", "insurancevendorassigned": "", "grandtotalamount": "", "grandtotalnongainadjustment": "", "grandtotalnongainamountpaidtoprovider": "", "grandtotaldeductible": "", "grandtotalcoinsurance": "", "grandtotalcopayment": "", "grandtotalbalance": "", "datesettled": "", "grandtotalsettlementamount": "", "paidto": "", "paidby": "", "notes": "", "type": "", "plaintiffid": "[AUTO_GENERATED]", "attorneyid": "[AUTO_GENERATED]", "paralegalid": "[AUTO_GENERATED]", "casemanagerid": "[AUTO_GENERATED]", "cocounselid": "[AUTO_GENERATED]", "coparalegalid": "[AUTO_GENERATED]", "cocasemanagerid": "[AUTO_GENERATED]", "tailclaimcase": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "dateofservice": "2023-10-05", "status": "", "amount": "19.0", "cptcode": "97530", "cptmodifier": "Go", "cptdescription": "", "nongainadjustment": "", "nongainamountpaidtoprovider": "", "gainprenegotiationadjustment": "", "gainprenegotiationamountpaidtoprovider": "", "deductible": "", "coinsurance": "", "copayment": "", "balance": "", "reimbursementrate": "", "amountsent": "", "quantity": "1", "billingid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "url": "s3://gain-servicing/integration/jopari/upsert/raw_data/processed/files/1000001954_2024-10-02_16-15-57-2023-01-01_12-00-00.pdf", "type": "<PERSON><PERSON> Ledger", "plaintiffid": "[AUTO_GENERATED]", "caseid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "companyname": "<PERSON><PERSON>", "policyid": "", "memberid": "", "groupnumber": "", "status": "", "limits": "", "phone": "", "fax": "", "email": "", "followupemail": "", "billingaddressline1": "471 <PERSON>", "billingaddressline2": "", "billingaddresscity": "Colusa", "billingaddressstate": "California", "billingaddresszip": "95932", "physicaladdressline1": "", "physicaladdressline2": "", "physicaladdresscity": "", "physicaladdressstate": "", "physicaladdresszip": "", "liabilityaccepted": "", "declarationpagereceived": "", "medpayexhausted": "", "pipexhausted": "", "probate": "", "bankruptcy": "", "subrogationlien": "", "otherlien": "", "otherlienname": "", "drivername": "", "datesettled": "", "howcasewassettled": "", "totalsettlementamount": "", "billspaid": "", "attorneyfee": "", "attorneyfeeflexible": "", "referralfeepercentage": "", "amounttoclient": "", "settlementnotes": "", "notes": "", "type": "Wc", "caseid": "[AUTO_GENERATED]", "relevanttogain": "", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "name": "Integrative Physical Medicine Of Eustis", "phone": "", "fax": "", "email": "", "followupemail": "", "billingaddressline1": "", "billingaddressline2": "", "billingaddresscity": "", "billingaddressstate": "", "billingaddresszip": "", "physicaladdressline1": "2818 S.Bay Street", "physicaladdressline2": "", "physicaladdresscity": "<PERSON><PERSON><PERSON>", "physicaladdressstate": "Florida", "physicaladdresszip": "*********", "website": "", "contracttype": "", "specialties": "", "description": "", "portalaccount": "", "notes": "", "parentid": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}, {"gainid": "[AUTO_GENERATED]", "name": "Accredited Rehabilitation Consultants Inc", "phone": "", "fax": "", "email": "", "followupemail": "", "billingaddressline1": "", "billingaddressline2": "", "billingaddresscity": "", "billingaddressstate": "", "billingaddresszip": "", "physicaladdressline1": "15308 Stonewood Ter", "physicaladdressline2": "", "physicaladdresscity": "<PERSON>", "physicaladdressstate": "California", "physicaladdresszip": "*********", "website": "", "contracttype": "", "specialties": "", "description": "", "portalaccount": "", "notes": "", "parentid": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "name": "<PERSON><PERSON>", "dateofbirth": "1961-03-26", "ssn": "", "taxid": "", "driverlicense": "", "gender": "M", "maritalstatus": "", "company": "", "homephone": "", "cellphone": "", "businessphone": "", "otherphone": "", "primaryemail": "", "secondaryemail": "", "primaryaddressline1": "471 <PERSON>", "primaryaddressline2": "", "primaryaddresscity": "Colusa", "primaryaddressstate": "California", "primaryaddresszip": "95932", "otheraddressline1": "", "otheraddressline2": "", "otheraddresscity": "", "otheraddressstate": "", "otheraddresszip": "", "departmentid": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}]]