CREATE TABLE billings (
    totalamount numeric(10, 4),
    totalamountsent numeric(10, 4),
    medicalclaimnumber character varying(30),
    dateofservice date,
    medicalfacilityid character varying(20),
    medicalfacilityaddressline1 character varying(250),
    medicalfacilityaddressline2 character varying(250),
    medicalfacilityaddresscity character varying(100),
    medicalfacilityaddressstate character varying(100),
    medicalfacilityaddresszip character varying(20),
    gaintype character varying(50),
    type character varying(50),
    caseid character varying(20),
    relevanttogain boolean,
    sourcecreatedatetime timestamp without time zone,
    sourcemodifieddatetime timestamp without time zone,
    modifieddatetime timestamp without time zone,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    totalnongainadjustment numeric(10, 4),
    totalnongainamountpaidtoprovider numeric(10, 4),
    totalbalance numeric(10, 4),
    status character varying(100) DEFAULT '':: character varying,
    createdatetime timestamp without time zone,
    patientresponsibility character varying(20) DEFAULT 'None':: character varying,
    notes character varying(1000),
    gainid character varying(16) NOT NULL DEFAULT '':: character varying,
    paidto character varying(100) DEFAULT 'Gain':: character varying,
    partneraccountid character varying(20),
    paidby character varying(100),
    servicingstartdatetime timestamp without time zone,
    servicingenddatetime timestamp without time zone,
    totalgainprenegotiationadjustment numeric(10, 4),
    totalgainprenegotiationamountpaidtoprovider numeric(10, 4),
    UNIQUE (medicalfacilityid),
    FOREIGN KEY (medicalfacilityid) REFERENCES medicalfacilities(gainid),
    PRIMARY KEY (gainid),
    FOREIGN KEY (caseid) REFERENCES cases(gainid),
    FOREIGN KEY (partneraccountid) REFERENCES medicalfacilities(gainid)
);