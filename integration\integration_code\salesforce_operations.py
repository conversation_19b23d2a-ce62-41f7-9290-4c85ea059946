# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error
import collections
import datetime
import inspect
import json
import time
import typing

import django.conf
import pandas as pd
import simple_salesforce

from . import (
    aws_operations,
    data_review_logger,
    local_operations,
    logger_config,
)
from .models import pipeline, types

logger = logger_config.get_logger(__name__)

app_config = django.conf.settings
credentials = app_config.CREDENTIALS

DEFAULT_TIMEOUT, MAX_RETRIES = 120, 20
with (
    open(
        'integration/integration_code/canonical_model.json', 'rb'
    ) as canonical_map_file,
    open(
        'integration/integration_code/salesforce_query.json', 'rb'
    ) as salesforce_query_file,
):
    canonical = json.load(canonical_map_file)
    salesforce_query = json.load(salesforce_query_file)
sf = None


def authenticate_or_fail_salesforce(test: bool):
    auth_resp = authenticate_salesforce(test)
    if not auth_resp:
        raise Exception('Salesforce authentication failed.')


def authenticate_salesforce(test: bool) -> bool:
    global sf, DEFAULT_TIMEOUT, MAX_RETRIES
    auth = False
    env = 'sandbox' if test else 'production'
    domain = None
    if test:
        domain = 'test'
    username = credentials['integration']['salesforce'][env]['username']
    password = credentials['integration']['salesforce'][env]['password']
    security_token = credentials['integration']['salesforce'][env][
        'security_token'
    ]
    try:
        sf = simple_salesforce.Salesforce(
            username=username,
            password=password,
            security_token=security_token,
            domain=domain,
        )
        sf.session = local_operations.setup_session(
            sf.session, DEFAULT_TIMEOUT, MAX_RETRIES
        )  # set up retries
        auth = True
    except simple_salesforce.exceptions.SalesforceAuthenticationFailed:
        logger.error(
            'Salesforce Authentication Failed',
            extra={
                'username': username,
                'password': password,
                'security_token': security_token,
                'sandbox': test,
            },
        )
    except Exception as e:
        logger.error(
            e,
            extra={
                'username': username,
                'password': password,
                'security_token': security_token,
                'sandbox': test,
            },
        )
    return auth


"""
# NOTE: For Bulk query use only query, no query_all (query_all in bulk fetches deleted/merged records as well); user query_all for non-bulk qeuries
# NOTE: To optimize: use query_all? also, possibly use generators and query_all_iter or use_lazy=True for fetches
# To learn more => https://github.com/simple-salesforce/simple-salesforce
"""

# region Shared


def log_failed_sf(
    redshift_to_sf_changes: list[dict[typing.Hashable, typing.Any]],
    sf_objects_responses: list[dict[str, str]],
    redshift_to_sf_objects_sf_failed: list[dict[typing.Hashable, typing.Any]],
    bucket: str,
    directory: str,
    curr_timestamp: datetime.datetime,
    object_name: str,
):
    count = 0
    for resp in sf_objects_responses:
        if not resp["success"] or not (
            resp["id"] is not None and resp["id"] != ""
        ):
            redshift_to_sf_changes[count]["reason"] = str(resp["errors"])
            redshift_to_sf_objects_sf_failed.append(
                redshift_to_sf_changes[count]
            )
        count += 1
    if len(redshift_to_sf_objects_sf_failed):
        save_sf_objects_data_s3(
            bucket,
            directory,
            pd.DataFrame(redshift_to_sf_objects_sf_failed),
            curr_timestamp,
            object_name,
            action='sf',
            purpose='failed',
        )


def get_sf_action(
    sf: (
        simple_salesforce.Salesforce
        | simple_salesforce.bulk.SFBulkHandler
        | simple_salesforce.SFType
        | None
    ),
    object_name: str,
    action: str,
):
    return getattr(getattr(sf, object_name), action)


def get_sf_bulk(object_name: str) -> simple_salesforce.bulk.SFBulkType:
    assert sf is not None
    return getattr(sf.bulk, object_name)


def get_sf(object_name: str) -> simple_salesforce.api.SFType:
    assert sf is not None
    return getattr(sf, object_name)


def process_batch(
    items: list[dict[typing.Hashable, typing.Any]],
    object_name: str,
    batches_sizes: list[int],
    sf_object_update_responses: list[dict[str, str]],
    redshift_to_sf_object_webapp_failed: list[dict[str, str]],
    batch_index: int = 0,
    action: typing.Literal['update', 'insert'] = 'update',
):
    assert sf is not None
    batches = []
    batch_size = batches_sizes[batch_index]
    for i in range(0, len(items), batch_size):
        batches.append(items[i : i + batch_size])

    for i, bulk in enumerate(batches):
        try:
            start_time = time.time()

            sf_object_update_responses.extend(
                get_sf_action(sf.bulk, object_name, action)(
                    bulk, batch_size=batch_size, use_serial=True
                )
            )
            logger.info(
                f'Took {round((time.time() - start_time), 2)} seconds to {action} {object_name} batch {i+1} of {len(batches)} (batch size: {batch_size}).',
            )
        except Exception as e:
            logger.error(e)
            next_batch_index = batch_index + 1
            if len(batches_sizes) > next_batch_index:
                process_batch(
                    bulk,
                    object_name,
                    batches_sizes,
                    sf_object_update_responses,
                    redshift_to_sf_object_webapp_failed,
                    next_batch_index,
                    action=action,
                )
            else:
                process_individual_items(
                    bulk,
                    object_name,
                    sf_object_update_responses,
                    redshift_to_sf_object_webapp_failed,
                    action=action,
                )


def process_individual_items(
    items: list[dict[str, str]],
    object_name: str,
    sf_object_update_responses: list[dict[str, str]],
    redshift_to_sf_object_webapp_failed: list[dict[str, str]],
    *,
    action: typing.Literal['update', 'insert'] = 'update',
):
    def handle_error(e: Exception):
        redshift_to_sf_object_webapp_failed.append(object)
        object_id = object.get('Id')
        resp = {'id': object_id, 'success': False, 'errors': str(e)}
        sf_object_update_responses.append(resp)
        logger.error(e, extra=object)

    for object in items:
        object_id = object.get('Id')
        try:
            method = action
            response = {}

            if action == 'update':
                del object['Id']

                response = get_sf_action(sf, object_name, method)(
                    object_id, object
                )
            elif action == 'insert':
                method = 'create'

                response = get_sf_action(sf, object_name, method)(object)
                object_id = response.get('id')

            resp = {
                'id': object_id,
                'success': response.get('success'),
            }  # unifying responses between individual update and bulk update
            sf_object_update_responses.append(resp)
        except Exception as e:
            handle_error(e)


def update_sf_objects(
    bucket: str,
    directory: str,
    redshift_to_sf_objects_update: list[dict[typing.Hashable, typing.Any]],
    objects_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    object_name: str,
    table_name: str,
    object_folder_name: str,
    get_sf_objects_update_rollback_data: typing.Callable[
        [list[str]], typing.Optional[pd.DataFrame]
    ],
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
    batches_sizes: typing.Optional[list[int]] = None,
):
    sf_object_update_ids, sf_object_update_responses = [], []
    redshift_to_sf_object_webapp_failed = []
    redshift_to_sf_object_sf_failed = []
    # use this method of default initialization for lists and other mutable sequences instead of passing it in as parameter default value
    # see more here: https://stackoverflow.com/questions/366422/how-can-i-avoid-issues-caused-by-pythons-early-bound-default-parameters-e-g-m
    batches_sizes = [100, 10] if not batches_sizes else batches_sizes
    manual_review_ids = [] if not manual_review_ids else manual_review_ids

    for object in redshift_to_sf_objects_update:
        sf_object_update_ids.append(object['Id'])

    df_update_rollback_data = get_sf_objects_update_rollback_data(
        sf_object_update_ids
    )  # get all rollback data as a dataframe

    if df_update_rollback_data is None:
        logger.error(
            "No objects found for rollback data retrieval.",
            extra={"sf_object_update_ids": sf_object_update_ids},
        )
        raise BaseException('Objects not found')

    save_sf_objects_data_s3(
        bucket,
        directory,
        df_update_rollback_data,
        curr_timestamp,
        object_folder_name,
    )

    process_batch(
        redshift_to_sf_objects_update,
        object_name,
        batches_sizes,
        sf_object_update_responses,
        redshift_to_sf_object_webapp_failed,
    )
    failed_upserts = []
    if not delta:
        if len(sf_object_update_responses) > 0:
            sf_objects_update_sf_ids = {}
            count = 0

            for resp in sf_object_update_responses:
                if resp.get('success', False):
                    sf_objects_update_sf_ids[objects_gainids_update[count]] = (
                        resp['id']
                    )
                else:
                    failed_upsert_data = {
                        'response': resp,
                        'redshift_to_sf_object': redshift_to_sf_objects_update[
                            count
                        ],
                    }
                    failed_upserts.append(failed_upsert_data)
                    redshift_to_sf_object_sf_failed.append(
                        redshift_to_sf_objects_update[count]
                    )

                count += 1

            aws_operations.update_postgres_sf_modifieddatetime(
                table_name,
                sf_objects_update_sf_ids,
                curr_timestamp,
            )
        else:
            logger.error(
                f"The sf.bulk.{object_name}.update does not return any response!",
                extra={
                    'additional_details': redshift_to_sf_objects_update,
                },
            )
    else:
        if len(sf_object_update_responses) > 0:
            sf_objects_update_sf_ids = {}
            manual_review_updated_ids = []
            count = 0

            for resp in sf_object_update_responses:
                if resp.get('success', False):
                    sf_objects_update_sf_ids[objects_gainids_update[count]] = (
                        resp['id']
                    )
                    manual_review_updated_ids.append(manual_review_ids[count])
                else:
                    failed_upsert_data = {
                        'response': resp,
                        'redshift_to_sf_object': redshift_to_sf_objects_update[
                            count
                        ],
                    }
                    failed_upserts.append(failed_upsert_data)
                    redshift_to_sf_object_sf_failed.append(
                        redshift_to_sf_objects_update[count]
                    )

                count += 1

            if manual_review_updated_ids:
                data_review_logger.update_manual_review_data_mark_completed(
                    manual_review_updated_ids, sf_object_update_ids
                )

            aws_operations.update_postgres_sf_modifieddatetime(
                table_name,
                sf_objects_update_sf_ids,
                curr_timestamp,
            )
        else:
            logger.error(
                f"The sf.bulk.{object_name}.update does not return any response!",
                extra={
                    'additional_details': redshift_to_sf_objects_update,
                },
            )

    if failed_upserts:
        save_sf_objects_data_s3(
            bucket,
            directory,
            pd.DataFrame(failed_upserts),
            curr_timestamp,
            object_name,
            action='sf upsert',
            purpose='failed',
        )


def update_sf_objects_manual_review(
    redshift_to_sf_objects_update_manual_review: list[dict[str, str]],
    get_sf_objects_update_rollback_data_func: typing.Callable[
        [list[str]], typing.Optional[pd.DataFrame]
    ],
    canonical_object: str,
    *,
    custom_transform: typing.Optional[
        typing.Callable[[dict[str, typing.Any], dict[str, typing.Any]], None]
    ] = None,
):
    sf_object_update_manual_review_ids, sf_object_data_mapping = [], {}
    for object in redshift_to_sf_objects_update_manual_review:
        sf_object_update_manual_review_ids.append(object['Id'])
    # get all rollback data as a dataframe
    df_object_update_manual_review_sf_data = (
        get_sf_objects_update_rollback_data_func(
            sf_object_update_manual_review_ids
        )
    )

    if df_object_update_manual_review_sf_data is None:
        return

    for row in df_object_update_manual_review_sf_data.iterrows():
        sf_object_data_mapping[row[1][0]] = row[1][1:].to_dict()
    for object in redshift_to_sf_objects_update_manual_review:
        try:
            salesforce_object = sf_object_data_mapping[object['Id']]
            if custom_transform:
                custom_transform(salesforce_object, object)
            data_review_logger.update_manual_review_data(
                canonical_object,
                object['Id'],
                object,
                salesforce_object,
                object['RedshiftId'],
            )
        except Exception as e:
            logger.error(
                e,
                extra={
                    'additional_details': f'Failed to get record type name(?) for {object}',
                },
            )


def get_sf_objects_update_rollback_data(
    sf_ids: list[str], query: str, object_name: str
):
    df = None
    # query in batches of 800 => SF query connector limit is 20,000 characters
    # 800 was chosen => 1 SFId requires 21 characters => 800*21=16,800 => allows for 3,200 other characters in query
    sf_id_batches, salesforce_account_query_result = [], []
    for i in range(
        0, len(sf_ids), 800
    ):  # query in batches of 800 ids to avoid hitting SF character limit of 20,000
        sf_id_batches.append(sf_ids[i : i + 800])
    for batch in sf_id_batches:
        if len(batch) > 1:
            salesforce_rollback_query = query + ' WHERE Id IN {sf_id_list}'
            salesforce_rollback_query = simple_salesforce.format_soql(
                salesforce_rollback_query, sf_id_list=batch
            )
        else:
            salesforce_rollback_query = query + ' WHERE Id = {sf_id}'
            salesforce_rollback_query = simple_salesforce.format_soql(
                salesforce_rollback_query, sf_id=batch[0]
            )
        try:
            salesforce_account_query_result.extend(
                get_sf_bulk(object_name).query(salesforce_rollback_query)
            )
        except Exception as e:
            logger.error(
                e,
                extra={
                    'additional_details': salesforce_rollback_query,
                },
            )
            return df
    if len(salesforce_account_query_result) != 0:
        df = pd.DataFrame(salesforce_account_query_result).drop(
            columns='attributes'
        )
        return df


def insert_sf_objects(
    bucket: str,
    directory: str,
    redshift_to_sf_objects_insert: list[dict[typing.Hashable, typing.Any]],
    objects_gainids_insert: list[str],
    object_name: str,
    table_name: str,
    object_folder_name: str,
    curr_timestamp: datetime.datetime,
    batches_sizes: typing.Optional[list[int]] = None,
):
    action = 'insert'
    sf_objects_insert_responses = []
    sf_objects_insert_rollback = []
    redshift_to_sf_objects_webapp_failed = []
    redshift_to_sf_objects_sf_failed = []
    batches_sizes = [100, 10] if not batches_sizes else batches_sizes

    process_batch(
        redshift_to_sf_objects_insert,
        object_name,
        batches_sizes,
        sf_objects_insert_responses,
        redshift_to_sf_objects_webapp_failed,
        action=action,
    )
    if len(sf_objects_insert_responses) > 0:
        sf_objects_insert_map = []
        count = 0

        for resp in sf_objects_insert_responses:
            if resp.get("success", False) and resp.get("id"):
                sf_objects_insert_map.append(
                    (
                        objects_gainids_insert[count],
                        resp["id"],
                        curr_timestamp,
                    )
                )  # source id, source name, salesforce id and create datetime for "gain_id_map" table
                sf_objects_insert_rollback.append(resp["id"])
            else:
                redshift_to_sf_objects_insert[count]["reason"] = str(
                    resp["errors"]
                )
            count += 1

        if len(sf_objects_insert_map) > 0:
            aws_operations.insert_postgres_sf_map(
                table_name,
                sf_objects_insert_map,
            )

        if len(sf_objects_insert_rollback) > 0:
            save_sf_objects_data_s3(
                bucket,
                directory,
                pd.DataFrame(sf_objects_insert_rollback),
                curr_timestamp,
                object_folder_name,
                action=action,
            )

        if redshift_to_sf_objects_sf_failed:
            save_sf_objects_data_s3(
                bucket,
                directory,
                pd.DataFrame(redshift_to_sf_objects_sf_failed),
                curr_timestamp,
                object_folder_name,
                action='sf',
                purpose='failed',
            )

    else:
        logger.error(
            f"The sf.bulk.{object_name}.{action} does not return any response!",
            extra={
                'additional_details': str(redshift_to_sf_objects_insert),
            },
        )


def upsert_sf_objects(
    bucket: str,
    directory: str,
    redshift_to_sf_objects: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
    update_func: typing.Callable[
        [
            str,
            str,
            list[dict[typing.Hashable, typing.Any]],
            list[str],
            datetime.datetime,
        ],
        None,
    ],
    manual_review_func: typing.Callable[[list[dict[str, str]]], None],
    insert_func: typing.Callable[
        [
            str,
            str,
            list[dict[typing.Hashable, typing.Any]],
            list[str],
            datetime.datetime,
        ],
        None,
    ],
):
    if redshift_to_sf_objects['update']:
        update_func(
            bucket,
            directory,
            redshift_to_sf_objects['update'],
            redshift_to_sf_objects['gainid_update'],
            curr_timestamp,
        )
    if redshift_to_sf_objects['update_manual_review']:
        manual_review_func(
            redshift_to_sf_objects['update_manual_review'],
        )
    if redshift_to_sf_objects['insert']:
        insert_func(
            bucket,
            directory,
            redshift_to_sf_objects['insert'],
            redshift_to_sf_objects['gainid_insert'],
            curr_timestamp,
        )


def query_sf_objects(
    sf_object_name: str,
    condition_names: typing.Optional[list[str]] = None,
    sf_id_list: typing.Optional[list[str]] = None,
    sf_name_list: typing.Optional[list[str]] = None,
    stage_names_to_exclude: typing.Optional[list[str]] = None,
    stage_names_to_exclude_substage: typing.Optional[list[str]] = None,
    substage_names_to_exclude: typing.Optional[list[str]] = None,
) -> list[str]:
    global salesforce_query
    query_result = []
    if sf_object_name not in salesforce_query.keys():
        logger.error(f'Invalid Salesforce object {sf_object_name}.')
        return query_result
    query = '''
        SELECT {:literal}
        FROM {:literal}
    '''
    columns = ','.join(salesforce_query[sf_object_name]['Columns'])
    params = []
    if condition_names:
        for i, condition in enumerate(condition_names):
            if i == 0:
                query += ''' WHERE '''
            else:
                query += ''' AND '''
            query += (
                ''' ( '''
                + salesforce_query[sf_object_name]['ConditionList'][condition][
                    'Condition'
                ]
                + ''' ) '''
            )
            params.extend(
                [
                    sq_param
                    for sq_param in salesforce_query[sf_object_name][
                        'ConditionList'
                    ][condition]['Params']
                ]
            )
    if len(params) > 0:
        if (sf_id_list is None and 'sf_id_list' in params) or (
            sf_id_list is not None and len(sf_id_list) == 0
        ):
            logger.error(f'Invalid sf_id_list: {sf_id_list}.')
            return query_result
        if (sf_name_list is None and 'sf_name_list' in params) or (
            sf_name_list is not None and len(sf_name_list) == 0
        ):
            logger.error(f'Invalid sf_name_list: {sf_name_list}.')
            return query_result
        if (
            stage_names_to_exclude is None
            and 'stage_names_to_exclude' in params
        ) or (
            stage_names_to_exclude is not None
            and len(stage_names_to_exclude) == 0
        ):
            logger.error(
                f'Invalid stage_names_to_exclude: {stage_names_to_exclude}.'
            )
            return query_result

        if (
            stage_names_to_exclude_substage is None
            and 'stage_names_to_exclude_substage' in params
        ) or (
            stage_names_to_exclude_substage is not None
            and len(stage_names_to_exclude_substage) == 0
        ):
            logger.error(
                f'Invalid stage_names_to_exclude_substage: {stage_names_to_exclude_substage}.'
            )
            return query_result

        if (
            substage_names_to_exclude is None
            and 'substage_names_to_exclude' in params
        ) or (
            substage_names_to_exclude is not None
            and len(substage_names_to_exclude) == 0
        ):
            logger.error(
                f'Invalid substage_names_to_exclude: {substage_names_to_exclude}.'
            )
            return query_result

    if sf_id_list is not None and len(sf_id_list) > 0:
        sf_id_list_batches = []
        batch_size = 800
        for i in range(0, len(sf_id_list), batch_size):
            sf_id_list_batches.append(sf_id_list[i : i + batch_size])
        for i, sf_id_list_batch in enumerate(sf_id_list_batches):
            query = simple_salesforce.format_soql(
                query,
                columns,
                sf_object_name,
                sf_id_list=sf_id_list_batch,
                sf_name_list=sf_name_list,
                stage_names_to_exclude=stage_names_to_exclude,
                stage_names_to_exclude_substage=stage_names_to_exclude_substage,
                substage_names_to_exclude=substage_names_to_exclude,
            )  # format_soql will ignore the params that are not in the query
            try:
                query_result.extend(get_sf_bulk(sf_object_name).query(query))
            except Exception as e:
                logger.error(
                    e,
                    extra={
                        'additional_details': f'Bulk query batch {i+1} of {len(sf_id_list_batch)}: '
                        + query,
                    },
                )
    else:
        query = simple_salesforce.format_soql(
            query,
            columns,
            sf_object_name,
            sf_name_list=sf_name_list,
            stage_names_to_exclude=stage_names_to_exclude,
            stage_names_to_exclude_substage=stage_names_to_exclude_substage,
            substage_names_to_exclude=substage_names_to_exclude,
        )  # format_soql will ignore the params that are not in the query
        try:
            query_result.extend(get_sf_bulk(sf_object_name).query(query))
        except Exception as e:
            logger.error(
                e,
                extra={
                    'additional_details': query,
                },
            )
            return query_result
    return query_result


def get_sf_object_delete_rollback_data(
    sf_object_name: str, sf_ids: list[str]
) -> pd.DataFrame | None:
    df = None
    # query in batches of 800 => SF query connector limit is 20,000 characters
    # 800 was chosen => 1 SFId requires 21 characters => 800*21=16,800 => allows for 3,200 other characters in query
    sf_id_batches, salesforce_object_query_result = [], []
    for i in range(
        0, len(sf_ids), 800
    ):  # query in batches of 800 ids to avoid hitting SF character limit of 20,000
        sf_id_batches.append(sf_ids[i : i + 800])
    for batch in sf_id_batches:
        if len(batch) > 1:
            salesforce_rollback_query = '''
                SELECT FIELDS(ALL)
                FROM {sf_object}
                WHERE Id IN {sf_id_list}
            '''
            salesforce_rollback_query = simple_salesforce.format_soql(
                salesforce_rollback_query,
                sf_object=sf_object_name,
                sf_id_list=batch,
            )
        else:
            salesforce_rollback_query = '''
                SELECT FIELDS(ALL)
                FROM {sf_object}
                WHERE Id = {sf_id}
            '''
            salesforce_rollback_query = simple_salesforce.format_soql(
                salesforce_rollback_query,
                sf_object=sf_object_name,
                sf_id=batch[0],
            )
        try:
            salesforce_object_query_result.extend(
                getattr(sf.bulk, sf_object_name).query(
                    salesforce_rollback_query
                )
            )
        except Exception as e:
            logger.error(
                e,
                extra={
                    'additional_details': salesforce_rollback_query,
                },
            )
            return df
    df = pd.DataFrame(salesforce_object_query_result).drop(
        columns='attributes'
    )
    return df


def save_sf_objects_data_s3(
    bucket: str,
    directory: str,
    df: pd.DataFrame,
    curr_timestamp: datetime.datetime,
    object_name: str,
    *,
    action: str = 'update',
    purpose: str = 'rollback',
):
    timestamp_string = curr_timestamp.strftime('%Y-%m-%d-%H-%M-%S')
    file_name = f'integration/integration_code/salesforce_{object_name}_{action}_{purpose}_{timestamp_string}.csv'
    df.to_csv(file_name, index=False)
    aws_operations.upload_s3(bucket, directory, file_name)
    local_operations.clean_up_file(file_name)


def delete_sf_objects_filter(
    sf_object_name: str, redshift_object_sf_ids: list[str]
) -> tuple[list[str], list[str]]:
    # Function call to general `get_sf_data` from SF (individual queries in get_opportunity_data, get_funding_data, etc.)
    # This function can be re-used in Upsert get as well
    filtered_result_sf_ids = []  # To collect filtered results
    excluded_sf_ids = []
    # If "Account", is due to Plaintiff Account, which does not have any SF side restrictions
    if sf_object_name == 'Account':
        return (redshift_object_sf_ids, excluded_sf_ids)
    elif sf_object_name in ['Opportunity', 'Funding__c', 'Charge__c']:
        query_result = query_sf_objects(
            sf_object_name,
            [
                'IdFilter',
                'ExcludeOpportunityStage',
                'ExcludeOpportunitySubStageInGivenStage',
            ],
            sf_id_list=redshift_object_sf_ids,
            stage_names_to_exclude=[
                'Settled',
                'Pending Payment from Law Firm',
            ],
            stage_names_to_exclude_substage=['HCP Historic'],
            substage_names_to_exclude=['Withdrawn by Gain'],
        )
        if len(query_result) > 0:
            df = pd.DataFrame(query_result).drop(columns='attributes')
            filtered_result_sf_ids = list(set(df['Id']))
        excluded_sf_ids = list(
            set(redshift_object_sf_ids) - set(filtered_result_sf_ids)
        )
    else:
        logger.info(
            f'Unknown Salesforce object {sf_object_name}. Skipping withdraw.'
        )
    return (filtered_result_sf_ids, excluded_sf_ids)


def delete_sf_objects(
    canonical_object_name: str,
    sf_object_name: str,
    bucket: str,
    directory: str,
    redshift_to_sf_objects_to_delete_data: dict[str, dict[str, str]],
    curr_timestamp: datetime.datetime,
) -> None:
    hard_delete = (
        False  # Actually delete object, instead of marking it as "Withdrawn"
    )
    sf_object_delete_responses = []
    redshift_object_batches, redshift_to_sf_object_webapp_failed = [], []
    batch_size, small_batch_size = 100, 10
    sf_objects_to_redshift_to_delete_data = dict(
        zip(
            redshift_to_sf_objects_to_delete_data[
                canonical_object_name
            ].values(),
            redshift_to_sf_objects_to_delete_data[
                canonical_object_name
            ].keys(),
        )
    )
    redshift_object_sf_ids = list(
        redshift_to_sf_objects_to_delete_data[canonical_object_name].values()
    )  # List of Salesforce Ids
    redshift_object_sf_ids, excluded_sf_ids = delete_sf_objects_filter(
        sf_object_name, redshift_object_sf_ids
    )  # Filter any SF records which should not be deleted
    objects_delete_source_keys = []
    for sf_id in redshift_object_sf_ids:
        objects_delete_source_keys.append(
            sf_objects_to_redshift_to_delete_data[sf_id]
        )  # To change to DF later, and use df column name to extract Source Id instead
    objects_excluded_source_keys = []
    for sf_id in excluded_sf_ids:
        objects_excluded_source_keys.append(
            sf_objects_to_redshift_to_delete_data[sf_id]
        )  # To change to DF later, and use df column name to extract Source Id instead
    if hard_delete:
        # Because deleting, need to generate and save rollback first
        redshift_object_sf_data = [
            {'Id': redshift_object_data_mappings_sf_id}
            for redshift_object_data_mappings_sf_id in redshift_object_sf_ids
        ]  # To change to DF later, and use df column name to extract Salesforce Id instead
        sf_object_rollback_data = get_sf_object_delete_rollback_data(
            sf_object_name, redshift_object_sf_ids
        )
        if sf_object_rollback_data is None:
            logger.error(
                'No Salesforce rollback data retrieved',
                extra={
                    'additional_details': f'No Salesforce {sf_object_name} rollback data retrieved',
                },
            )
            return
        save_sf_objects_data_s3(
            bucket,
            directory,
            sf_object_rollback_data,
            curr_timestamp,
            sf_object_name,
        )
        # Rollback done, proceed to delete
    else:
        if sf_object_name == 'Account':
            return  # If "Account", is due to Plaintiff Account, which does not require any SF side action
        elif sf_object_name == 'Opportunity':
            redshift_object_sf_data = [
                {
                    'Id': redshift_object_data_mappings_sf_id,
                    'StageName': 'HCP Historic',
                    'Sub_Stage__c': 'Withdrawn by Provider',
                }
                for redshift_object_data_mappings_sf_id in redshift_object_sf_ids
            ]  # To change to DF later, and use df column name to extract Salesforce Id instead
        elif sf_object_name == 'Funding__c':
            redshift_object_sf_data = [
                {
                    'Id': redshift_object_data_mappings_sf_id,
                    'Funding_Stage__c': 'Historic',
                    'Funding_Sub_Stage__c': 'Withdrawn by Provider',
                }
                for redshift_object_data_mappings_sf_id in redshift_object_sf_ids
            ]  # To change to DF later, and use df column name to extract Salesforce Id instead
        elif sf_object_name == 'Charge__c':
            redshift_object_sf_data = [
                {
                    'Id': redshift_object_data_mappings_sf_id,
                    'Stage__c': 'Withdrawn',
                }
                for redshift_object_data_mappings_sf_id in redshift_object_sf_ids
            ]  # To change to DF later, and use df column name to extract Salesforce Id instead
        else:
            logger.info(
                f'Unknown Salesforce object {sf_object_name}. Skipping withdraw.'
            )
            return
    for i in range(0, len(redshift_object_sf_data), batch_size):
        redshift_object_batches.append(
            redshift_object_sf_data[i : i + batch_size]
        )
    for i, bulk in enumerate(redshift_object_batches):
        try:
            start_time = time.perf_counter()
            if hard_delete:
                sf_object_delete_responses.extend(
                    get_sf_bulk(sf_object_name).delete(
                        bulk, batch_size=1000, use_serial=True
                    )
                )
            else:
                sf_object_delete_responses.extend(
                    get_sf_bulk(sf_object_name).update(
                        bulk, batch_size=1000, use_serial=True
                    )
                )
            logger.info(
                f'Took {round((time.perf_counter() - start_time), 2)} seconds to delete object batch {i+1} of {len(redshift_object_batches)} (batch size: {len(bulk)}).'
            )
        except Exception as e:
            logger.error(e)
            redshift_object_small_batches = []
            for j in range(0, len(bulk), small_batch_size):
                redshift_object_small_batches.append(
                    bulk[j : j + small_batch_size]
                )
            for j, small_bulk in enumerate(redshift_object_small_batches):
                try:
                    start_time = time.perf_counter()
                    if hard_delete:
                        sf_object_delete_responses.extend(
                            get_sf_bulk(sf_object_name).delete(
                                small_bulk, batch_size=1000, use_serial=True
                            )
                        )
                    else:
                        sf_object_delete_responses.extend(
                            get_sf_bulk(sf_object_name).update(
                                small_bulk, batch_size=1000, use_serial=True
                            )
                        )
                    logger.info(
                        f'Took {round((time.perf_counter() - start_time), 2)} seconds to delete object small batch {j+1} of {len(redshift_object_small_batches)} (small batch size: {len(small_bulk)}).'
                    )
                except Exception as e:
                    logger.error(e)
                    for sf_object in small_bulk:
                        try:
                            if hard_delete:
                                sf_object_delete_responses.extend(
                                    getattr(sf, sf_object_name).delete(
                                        sf_object['Id'], sf_object
                                    )
                                )
                            else:
                                sf_object_delete_responses.append(
                                    getattr(sf, sf_object_name).update(
                                        sf_object['Id'], sf_object
                                    )
                                )
                        except Exception as e:
                            redshift_to_sf_object_webapp_failed.append(
                                sf_object
                            )
                            logger.error(
                                e,
                                extra={
                                    'additional_details': sf_object,
                                },
                            )
    if len(sf_object_delete_responses) > 0:
        sf_object_delete_map = {}
        sf_object_delete_override_map = {}
        count = 0
        for resp in sf_object_delete_responses:
            if resp['success'] and (
                resp['id'] is not None and resp['id'] != ''
            ):
                sf_object_delete_map[objects_delete_source_keys[count]] = (
                    curr_timestamp
                )
                # NOTE: value not used for updatemodifieddatetime
            else:
                # Capture source id, source name and reason unable to delete from Salesforce, to be used for update on canonical table
                reason = (
                    'Failed to Delete - '
                    if hard_delete
                    else 'Failed to mark as Withdrawn - '
                )
                reason += str(resp['errors'])  # sf bulk success False response
                sf_object_delete_override_map[
                    objects_delete_source_keys[count]
                ] = reason
            count += 1
        if len(sf_object_delete_map) > 0:  # update successful rows
            aws_operations.update_postgres_sf_modifieddatetime(
                canonical_object_name,
                sf_object_delete_map,
                curr_timestamp,
            )
            if hard_delete:
                # NOTE: Function `local_operations.get_canonical_object_name` not created yet, to be made soon
                aws_operations.delete_postgres_sf_map(
                    canonical_object_name,
                    list(sf_object_delete_map.keys()),
                )
        if len(sf_object_delete_override_map) > 0:  # update unsuccessful rows
            aws_operations.update_postgres_sf_deletepreventoverride(
                canonical_object_name, sf_object_delete_override_map
            )
    else:
        logger.error(
            f'The sf.bulk.{sf_object_name}.{"delete" if hard_delete else "update"} operation did not return any response!',
            extra={
                'additional_details': str(
                    redshift_to_sf_objects_to_delete_data
                ),
            },
        )

    if len(objects_excluded_source_keys) > 0:
        reason = 'Records belonging to Opportunities in Settled, Pending Payment from Law Firm, and HCP Historic (not Withdrawn by Provider) stages in Salesforce are excluded from withdrawal'  # Should be replaced if there are any other reasons to exclude from withdrawal
        sf_object_excluded_override_map = dict.fromkeys(
            objects_excluded_source_keys, reason
        )  # Filter any SF records which should not be deleted
        aws_operations.update_postgres_sf_deletepreventoverride(
            canonical_object_name, sf_object_excluded_override_map
        )
    return


def update_settled_sf_objects(
    canonical_object_name: str,
    sf_object_name: str,
    bucket: str,
    directory: str,
    redshift_to_sf_objects_to_update_settled_data: pd.DataFrame,
    curr_timestamp: datetime.datetime,
) -> None:
    sf_object_update_settled_responses = []
    redshift_to_sf_objects_webapp_failed = []
    redshift_object_sf_data: list[dict[typing.Hashable, typing.Any]] = []
    objects_update_settled_gain_ids = (
        redshift_to_sf_objects_to_update_settled_data['GainId'].tolist()
    )
    if sf_object_name == 'Account':
        return  # If "Account", is due to Plaintiff Account, which does not require any SF side action
    elif sf_object_name == 'Opportunity':
        return
    elif sf_object_name == 'Funding__c':
        redshift_object_sf_data = [
            {
                'Id': row.SalesforceId,
                'Payoff_Status__c': row.PayoffStatus,
            }
            for row in redshift_to_sf_objects_to_update_settled_data.itertuples()
        ]
    elif sf_object_name == 'Charge__c':
        return
    else:
        logger.warning(
            f'Unknown Salesforce object {sf_object_name}. Skipping Settlement.'
        )
        return

    process_batch(
        redshift_object_sf_data,
        sf_object_name,
        [100, 10],
        sf_object_update_settled_responses,
        redshift_to_sf_objects_webapp_failed,
        action='update',
    )

    if len(sf_object_update_settled_responses) > 0:
        sf_object_update_settled_map = {}
        sf_failed_update_settled_map = pd.DataFrame(
            columns=['GainId', 'Reason']
        )
        count = 0
        for resp in sf_object_update_settled_responses:
            if resp['success'] and (
                resp['id'] is not None and resp['id'] != ''
            ):
                sf_object_update_settled_map[
                    objects_update_settled_gain_ids[count]
                ] = curr_timestamp
                # NOTE: value not used for updatemodifieddatetime
            else:
                # Capture source id, source name and reason unable to settle from Salesforce, to be used for update on canonical table
                reason = 'Failed to Settle -'
                reason += str(resp['errors'])  # sf bulk success False response
                sf_failed_update_settled_map = pd.concat(
                    [
                        sf_failed_update_settled_map,
                        pd.DataFrame(
                            [
                                {
                                    'GainId': objects_update_settled_gain_ids[
                                        count
                                    ],
                                    'Reason': reason,
                                }
                            ]
                        ),
                    ],
                    ignore_index=True,
                )

            count += 1
        if len(sf_object_update_settled_map) > 0:  # update successful rows
            aws_operations.update_postgres_sf_modifieddatetime(
                canonical_object_name,
                sf_object_update_settled_map,
                curr_timestamp,
            )
        if len(sf_failed_update_settled_map) > 0:
            save_sf_objects_data_s3(
                bucket,
                directory,
                sf_failed_update_settled_map,
                curr_timestamp,
                sf_object_name,
                action='update_settled',
                purpose='failed',
            )
    else:
        logger.error(
            f'The sf.bulk.{sf_object_name}.update_settled operation did not return any response!',
            extra={
                'additional_details': str(
                    redshift_to_sf_objects_to_update_settled_data
                )
            },
        )
    return


# endregion


# region Accounts
def get_sf_lawfirms_data() -> dict[str, dict[str, str]]:
    salesforce_lawfirm_query = '''
        SELECT Id, 
            Name, 
            Phone, 
            Email__c, 
            BillingStreet, 
            BillingCity, 
            BillingState, 
            BillingPostalCode
        FROM Account
        WHERE RecordType.Name = 'Law Firm'
    '''
    salesforce_lawfirm_query = simple_salesforce.format_soql(
        salesforce_lawfirm_query
    )
    result = {
        'lawfirm_unique_key_group': {},
        'lawfirm_phone_group': {},
        'lawfirm_email_group': {},
    }
    try:
        salesforce_lawfirm_query_result = get_sf_bulk('Account').query(
            salesforce_lawfirm_query
        )
        df_lawfirm = pd.DataFrame(salesforce_lawfirm_query_result)
        if not df_lawfirm.empty and 'attributes' in df_lawfirm.columns:
            df_lawfirm.drop(columns='attributes', inplace=True)
        df_lawfirm['Phone'] = df_lawfirm['Phone'].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            )
        )
        df_lawfirm['UniqueKey'] = (
            df_lawfirm['BillingStreet'].apply(lambda x: str(x).split(' ')[0])
            + df_lawfirm['BillingCity']
            + df_lawfirm['BillingState'].apply(
                lambda x: local_operations.state_name_to_code_map.get(
                    str(x).title(), str(x)
                )
            )
            + df_lawfirm['BillingPostalCode']
            + df_lawfirm['Phone']
        ).apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        df_lawfirm['Name'] = df_lawfirm['Name'].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        df_lawfirm['Phone'] = df_lawfirm['Phone'].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        df_lawfirm['Email__c'] = df_lawfirm['Email__c'].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        result['lawfirm_unique_key_group'] = (
            df_lawfirm.groupby('UniqueKey', as_index=True)['Id']
            .agg(lambda x: set(x))
            .to_dict()
        )
        result['lawfirm_phone_group'] = (
            df_lawfirm.groupby('Phone', as_index=True)['Id']
            .agg(lambda x: set(x))
            .to_dict()
        )
        result['lawfirm_email_group'] = (
            df_lawfirm.groupby('Email__c', as_index=True)['Id']
            .agg(lambda x: set(x))
            .to_dict()
        )
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_lawfirm_query,
            },
        )
        return {}
    return result


def get_sf_medicalfacilities_map():
    (
        name_type_map,
        name_id_map,
        locationid_id_map,
        root_name_type_map,
        root_name_id_map,
        id_parentid_map,
    ) = ({}, {}, {}, {}, {}, {})
    salesforce_medicalfacilities_query = '''
            SELECT Name, Id, Gain_ID__c, ParentId, RecordType.Name
            FROM Account
            WHERE RecordType.Name = 'Medical Facility'
        '''

    try:
        sf_medicalfacilities_query_result = get_sf_bulk('Account').query(
            salesforce_medicalfacilities_query
        )
        for result in sf_medicalfacilities_query_result:
            root_name_type_map[result['Name']] = result['RecordType']['Name']
            root_name_id_map[result['Name']] = result['Id']
            name_type_map[result['Name']] = result['RecordType']['Name']
            name_id_map[result['Name']] = result['Id']
            id_parentid_map[result['Id']] = result[
                'ParentId'
            ]  # to potentially set medical facility as district level
            if (
                result['Gain_ID__c'] and result['Gain_ID__c'] != ''
            ):  # if not empty
                locationid_id_map[result['Gain_ID__c']] = result['Id']
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_medicalfacilities_query,
            },
        )
    return {
        'root_name_type_map': root_name_type_map,
        'root_name_id_map': root_name_id_map,
        'name_type_map': name_type_map,
        'name_id_map': name_id_map,
        'id_parentid_map': id_parentid_map,
        'locationid_id_map': locationid_id_map,
    }


def get_sf_accounts_data(redshift_sources: list[str]):
    (
        salesforce_sources,
        name_type_map,
        name_id_map,
        locationid_id_map,
        root_name_type_map,
        root_name_id_map,
        id_parentid_map,
    ) = (set(), {}, {}, {}, {}, {}, {})
    # Need root mappings separately because only root account id can be
    # used for opportunity and funding search
    for source in redshift_sources:
        salesforce_sources.add(
            local_operations.get_client_salesforce_name(source)
        )
    if not salesforce_sources:
        logger.error(
            'The number of sources retrieved from Redshift is 0',
            extra={
                'additional_details': str(redshift_sources),
            },
        )
        return {
            'root_name_type_map': root_name_type_map,
            'root_name_id_map': root_name_id_map,
            'name_type_map': name_type_map,
            'name_id_map': name_id_map,
            'id_parentid_map': id_parentid_map,
            'locationid_id_map': locationid_id_map,
        }
    salesforce_root_accounts_query = '''
        SELECT Name, Id, Gain_ID__c, ParentId, RecordType.Name
        FROM Account
        WHERE
            Name IN {sources}
    '''
    salesforce_root_accounts_query = simple_salesforce.format_soql(
        salesforce_root_accounts_query, sources=salesforce_sources
    )
    salesforce_accounts_query = '''
        SELECT Name, Id, Gain_ID__c, ParentId, RecordType.Name
        FROM Account
        WHERE
            Name IN {sources}
            OR Root_Parent_Name__c IN {sources}
    '''
    salesforce_accounts_query = simple_salesforce.format_soql(
        salesforce_accounts_query, sources=salesforce_sources
    )
    try:
        sf_root_accounts_query_result = get_sf_bulk('Account').query(
            salesforce_root_accounts_query
        )
        for result in sf_root_accounts_query_result:
            root_name_type_map[result['Name']] = result['RecordType']['Name']
            root_name_id_map[result['Name']] = result['Id']
        sf_accounts_query_result = get_sf_bulk('Account').query(
            salesforce_accounts_query
        )
        for result in sf_accounts_query_result:
            name_type_map[result['Name']] = result['RecordType']['Name']
            name_id_map[result['Name']] = result['Id']
            id_parentid_map[result['Id']] = result[
                'ParentId'
            ]  # to potentially set medical facility as district level
            if (
                result['Gain_ID__c'] and result['Gain_ID__c'] != ''
            ):  # if not empty
                locationid_id_map[result['Gain_ID__c']] = result['Id']
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_accounts_query,
            },
        )
    return {
        'root_name_type_map': root_name_type_map,
        'root_name_id_map': root_name_id_map,
        'name_type_map': name_type_map,
        'name_id_map': name_id_map,
        'id_parentid_map': id_parentid_map,
        'locationid_id_map': locationid_id_map,
    }


def get_sf_plaintiffaccount_data():
    (
        plaintiff_accounts_nameDOB_res,
        plaintiff_account_id_DOA_res,
        plaintiff_accounts_check_last_modified_date_before_update,
    ) = (collections.defaultdict(list), collections.defaultdict(set), {})
    salesforce_plaintiff_accounts_query = '''
        SELECT Id, Name, Date_of_Birth__c, RecordTypeId, LastModifiedDate, (SELECT Date_of_Accident__c FROM Plaintiff_Details__r)
        FROM Account
        WHERE RecordType.Name = 'Plaintiff'
    '''
    try:
        salesforce_plaintiff_accounts_query_result = get_sf_bulk(
            'Account'
        ).query(salesforce_plaintiff_accounts_query)
        salesforce_plaintiff_accounts_result = pd.DataFrame(
            salesforce_plaintiff_accounts_query_result
        ).drop(
            columns='attributes'
        )  # sf.bulk query returns the number of milliseconds since January 1st 1970, needs to convert it to the datetime format
        salesforce_plaintiff_accounts_result['LastModifiedDate'] = (
            salesforce_plaintiff_accounts_result['LastModifiedDate'].map(
                lambda x: datetime.datetime.fromtimestamp(x / 1000.0)
            )
        )
        for row in salesforce_plaintiff_accounts_result.itertuples(index=True):
            plaintiff_accounts_check_last_modified_date_before_update[
                row.Id
            ] = row.LastModifiedDate
            if row.Date_of_Birth__c is not None:  # don't include blank DOBs
                plaintiff_accounts_nameDOB_res[
                    (row.Name.lower(), row.Date_of_Birth__c)
                ].append(row.Id)
            if (
                row.Plaintiff_Details__r is not None
            ):  # don't add blank DOAs to map of plaintiff ID to DOA
                curr_id_DOA = plaintiff_account_id_DOA_res[row.Id]
                for record in row.Plaintiff_Details__r['records']:
                    curr_id_DOA.add(record['Date_of_Accident__c'])
                plaintiff_account_id_DOA_res[row.Id] = curr_id_DOA
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_plaintiff_accounts_query,
            },
        )
    return {
        'plaintiff_accounts_nameDOB': plaintiff_accounts_nameDOB_res,
        'plaintiff_account_id_DOA': plaintiff_account_id_DOA_res,
        'plaintiff_accounts_check_last_modified_date_before_update': plaintiff_accounts_check_last_modified_date_before_update,
    }


def get_sf_medicalfacilities_data():
    salesforce_medicalfacilities_query = '''
        SELECT Id, 
            Name, 
            Phone, 
            Email__c, 
            BillingStreet, 
            BillingCity, 
            BillingState, 
            BillingPostalCode
        FROM Account
        WHERE RecordType.Name = 'Medical Facility'
    '''
    salesforce_medicalfacilities_query = simple_salesforce.format_soql(
        salesforce_medicalfacilities_query
    )
    result = {
        'medicalfacility_unique_key_group': {},
        'medicalfacility_phone_group': {},
        'medicalfacility_email_group': {},
    }
    try:
        salesforce_medicalfacilities_query_result = get_sf_bulk(
            'Account'
        ).query(salesforce_medicalfacilities_query)
        df_medicalfacilities = pd.DataFrame(
            salesforce_medicalfacilities_query_result
        )

        if (
            not df_medicalfacilities.empty
            and 'attributes' in df_medicalfacilities.columns
        ):
            df_medicalfacilities.drop(columns='attributes', inplace=True)
        df_medicalfacilities['Phone'] = df_medicalfacilities['Phone'].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            )
        )
        df_medicalfacilities['UniqueKey'] = (
            df_medicalfacilities['BillingStreet'].apply(
                lambda x: str(x).split(' ')[0]
            )
            + df_medicalfacilities['BillingCity']
            + df_medicalfacilities['BillingState'].apply(
                lambda x: local_operations.state_name_to_code_map.get(
                    str(x).title(), str(x)
                )
            )
            + df_medicalfacilities['BillingPostalCode']
        ).apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        df_medicalfacilities['Name'] = df_medicalfacilities['Name'].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        df_medicalfacilities['Phone'] = df_medicalfacilities['Phone'].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        df_medicalfacilities['Email__c'] = df_medicalfacilities[
            'Email__c'
        ].apply(
            lambda x: local_operations.remove_nonalphanumeric_characters(
                str(x)
            ).lower()
        )
        result['medicalfacility_unique_key_group'] = (
            df_medicalfacilities.groupby('UniqueKey', as_index=True)['Id']
            .agg(lambda x: set(x))
            .to_dict()
        )
        result['medicalfacility_phone_group'] = (
            df_medicalfacilities.groupby('Phone', as_index=True)['Id']
            .agg(lambda x: set(x))
            .to_dict()
        )
        result['medicalfacility_email_group'] = (
            df_medicalfacilities.groupby('Email__c', as_index=True)['Id']
            .agg(lambda x: set(x))
            .to_dict()
        )
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_medicalfacilities_query,
            },
        )
        return {}
    return result


def get_sf_accounts_update_rollback_data(sf_ids: list[str]):
    return get_sf_objects_update_rollback_data(
        sf_ids,
        '''
        SELECT  Id, 
                Name, 
                Date_of_Birth__c, 
                Lead_Source__c, 
                Lead_Source_Name__c, 
                Keywords__c, 
                RecordTypeId, 
                RecordType.Name
        FROM Account
        ''',
        'Account',
    )


def update_sf_plaintiffaccounts(
    bucket: str,
    directory: str,
    redshift_to_sf_accounts_update: list[dict[typing.Hashable, typing.Any]],
    plaintiffs_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_accounts_update,
        plaintiffs_gainids_update,
        curr_timestamp,
        'Account',
        'plaintiffs',
        'accounts',
        get_sf_accounts_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_plaintiffaccounts_manual_review(
    redshift_to_sf_accounts_update_manual_review: list[dict[str, typing.Any]],
):
    def custom_transform(
        salesforce_account: dict[str, typing.Any],
        account: dict[str, typing.Any],
    ):
        account['RecordTypeName'], salesforce_account['RecordTypeName'] = (
            salesforce_account['RecordType']['Name'],
            salesforce_account['RecordType']['Name'],
        )  # for new added field RecordType.Name
        del salesforce_account['RecordType']

    update_sf_objects_manual_review(
        redshift_to_sf_accounts_update_manual_review,
        get_sf_accounts_update_rollback_data,
        'Plaintiffs',
        custom_transform=custom_transform,
    )


def insert_sf_plaintiffaccounts(
    bucket: str,
    directory: str,
    redshift_to_sf_accounts_insert: list[dict[typing.Hashable, typing.Any]],
    plaintiffs_gainids_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_accounts_insert,
        plaintiffs_gainids_insert,
        'Account',
        'plaintiffs',
        'account',
        curr_timestamp,
    )


def upsert_sf_plaintiffaccounts(
    bucket: str,
    directory: str,
    redshift_data_to_sf_plaintiffaccounts: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_plaintiffaccounts,
        curr_timestamp,
        update_sf_plaintiffaccounts,
        update_sf_plaintiffaccounts_manual_review,
        insert_sf_plaintiffaccounts,
    )


def create_sf_plaintiffaccount(record: dict[str, typing.Any]):
    try:
        plaintiff_account_response = get_sf('Account').create(record)
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': record,
            },
        )
        return None
    return plaintiff_account_response


def get_type_columns(typed_dict_cls: typing.Any, as_list: bool = False):
    """
    Extract column names from a TypedDict class.
    If a field's type is another TypedDict, append its fields with dot notation.
    """
    result = []

    # For the functional TypedDict (created with TypedDict())
    if hasattr(typed_dict_cls, "__annotations__"):
        annotations = typed_dict_cls.__annotations__
    # For class-based TypedDict
    else:
        # Use get_type_hints to get the annotations
        annotations = typing.get_type_hints(typed_dict_cls)

    for key, value_type in annotations.items():
        # Check if the type is a TypedDict
        origin = typing.get_origin(value_type)
        if (
            origin is None
            and inspect.isclass(value_type)
            and issubclass(value_type, dict)
            and hasattr(value_type, "__annotations__")
        ):
            # It's a TypedDict class
            nested_fields = get_type_columns(value_type, as_list=True)
            # Add dot notation fields
            for nested_field in nested_fields:
                if nested_field:  # Skip empty fields
                    result.append(f"{key}.{nested_field}")
        else:
            # No nested fields, add the current field
            result.append(key)

    return ", ".join(result) if not as_list else result


def get_sf_medicalfacilities_update_rollback_data(
    sf_ids: list[str],
) -> typing.Optional[pd.DataFrame]:
    columns = get_type_columns(types.MedicalFacilityRollbackData)
    return get_sf_objects_update_rollback_data(
        sf_ids,
        f'SELECT {columns} FROM Account',
        'Account',
    )


def update_sf_medicalfacilities(
    bucket: str,
    directory: str,
    redshift_to_sf_medicalfacilities_update: list[
        dict[typing.Hashable, typing.Any]
    ],
    medicalfacilities_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_medicalfacilities_update,
        medicalfacilities_gainids_update,
        curr_timestamp,
        'Account',
        'medicalfacilities',
        'medicalfacilities',
        get_sf_medicalfacilities_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_medicalfacilities_manual_review(
    redshift_to_sf_medicalfacilities_update_manual_review: list[
        dict[str, str]
    ],
):
    update_sf_objects_manual_review(
        redshift_to_sf_medicalfacilities_update_manual_review,
        get_sf_medicalfacilities_update_rollback_data,
        'MedicalFacilities',
    )


def insert_sf_medicalfacilities(
    bucket: str,
    directory: str,
    redshift_to_sf_medicalfacilities_insert: list[
        dict[typing.Hashable, typing.Any]
    ],
    medicalfacilities_gainids_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_medicalfacilities_insert,
        medicalfacilities_gainids_insert,
        'Account',
        'medicalfacilities',
        'medicalfacilities',
        curr_timestamp,
    )


def upsert_sf_medicalfacilities(
    bucket: str,
    directory: str,
    redshift_data_to_sf_medicalfacilities: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_medicalfacilities,
        curr_timestamp,
        update_sf_medicalfacilities,
        update_sf_medicalfacilities_manual_review,
        insert_sf_medicalfacilities,
    )


def get_sf_lawfirms_update_rollback_data(
    sf_ids: list[str],
) -> typing.Optional[pd.DataFrame]:
    return get_sf_objects_update_rollback_data(
        sf_ids,
        '''SELECT 
            Id, Name, Phone, Fax, Email__c, Follow_up_email_for_entire_account__c,
            BillingStreet, BillingCity, BillingState,
            BillingPostalCode, ShippingStreet, ShippingCity, ShippingState,
            ShippingPostalCode, Website, Type_of_Law__c, Description,
            Employee_Size_Range__c, DNF__c, DNF_Type__c,
            Automatic_Case_Update_Requests__c, Non_Responsive__c,
            Difficult_Case_Update_Notes__c, Portal_Account__c, Portal_Rewards__c,
            Gain_ID__c
        FROM Account''',
        'Account',
    )


def update_sf_lawfirms(
    bucket: str,
    directory: str,
    redshift_to_sf_lawfirm_update: list[dict[typing.Hashable, typing.Any]],
    lawfirms_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_lawfirm_update,
        lawfirms_gainids_update,
        curr_timestamp,
        'Account',
        'lawfirms',
        'lawfirms',
        get_sf_lawfirms_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_lawfirms_manual_review(
    redshift_to_sf_lawfirms_update_manual_review: list[dict[str, str]],
):
    update_sf_objects_manual_review(
        redshift_to_sf_lawfirms_update_manual_review,
        get_sf_lawfirms_update_rollback_data,
        'LawFirms',
    )


def insert_sf_lawfirms(
    bucket: str,
    directory: str,
    redshift_to_sf_lawfirm_insert: list[dict[typing.Hashable, typing.Any]],
    lawfirms_gainids_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_lawfirm_insert,
        lawfirms_gainids_insert,
        'Account',
        'lawfirms',
        'lawfirm',
        curr_timestamp,
    )


def upsert_sf_lawfirms(
    bucket: str,
    directory: str,
    redshift_data_to_sf_lawfirms: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_lawfirms,
        curr_timestamp,
        update_sf_lawfirms,
        update_sf_lawfirms_manual_review,
        insert_sf_lawfirms,
    )


# endregion


# region Contacts
def get_sf_contact_data(lawfirm_ids: list[str]) -> dict[str, typing.Any]:
    if len(lawfirm_ids) == 0:
        return {'contact_phone_group': {}, 'contact_email_group': {}}
    sql = '''
        SELECT Id, AccountId, Name, Phone, MobilePhone, HomePhone, OtherPhone, 
        Personal_Phone_Number__c, Account.Phone, Email, Personal_Email__c
        FROM Contact 
        WHERE Account.RecordType.Name = 'Law Firm'
        AND AccountId IN {lawfirm_ids}
    '''
    result = {'contact_phone_group': {}, 'contact_email_group': {}}
    phone_fields = [
        'Phone',
        'MobilePhone',
        'HomePhone',
        'OtherPhone',
        'Personal_Phone_Number__c',
        'AccountPhone',
    ]
    email_fields = ['Email', 'Personal_Email__c']
    try:
        salesforce_contacts_result = []
        sf_id_list_batches = []
        batch_size = 800
        lawfirm_ids = list(lawfirm_ids)
        for i in range(0, len(lawfirm_ids), batch_size):
            sf_id_list_batches.append(lawfirm_ids[i : i + batch_size])
        for i, sf_id_list_batch in enumerate(sf_id_list_batches):
            query = simple_salesforce.format_soql(
                sql, lawfirm_ids=sf_id_list_batch
            )
            try:
                salesforce_contacts_result.extend(
                    get_sf_bulk('Contact').query(query)
                )
            except Exception as e:
                logger.error(
                    e,
                    extra={
                        'additional_details': query,
                    },
                )
        for sf_contact in salesforce_contacts_result:
            sf_contact['AccountPhone'] = sf_contact['Account']['Phone']
        df_contacts = pd.DataFrame(salesforce_contacts_result)
        if not df_contacts.empty:
            if 'attributes' in df_contacts.columns:
                df_contacts = df_contacts.drop(columns='attributes')
            if 'Account' in df_contacts.columns:
                df_contacts = df_contacts.drop(columns='Account')

        for row in df_contacts.itertuples():
            for phone_field in phone_fields:
                if (
                    getattr(row, phone_field) is None
                    or getattr(row, phone_field) == ''
                ):
                    continue
                phone = local_operations.remove_nonalphanumeric_characters(
                    getattr(row, phone_field)
                ).lower()
                if row.AccountId == '' or row.AccountId is None:
                    continue
                if row.AccountId not in result['contact_phone_group']:
                    result['contact_phone_group'][row.AccountId] = {}
                if phone not in result['contact_phone_group'][row.AccountId]:
                    result['contact_phone_group'][row.AccountId][phone] = set()
                result['contact_phone_group'][row.AccountId][phone].add(row.Id)

            for email_field in email_fields:
                if (
                    getattr(row, email_field) is None
                    or getattr(row, email_field) == ''
                ):
                    continue
                email = local_operations.remove_nonalphanumeric_characters(
                    getattr(row, email_field)
                ).lower()
                if row.AccountId == '' or row.AccountId is None:
                    continue
                if row.AccountId not in result['contact_email_group']:
                    result['contact_email_group'][row.AccountId] = {}
                if email not in result['contact_email_group'][row.AccountId]:
                    result['contact_email_group'][row.AccountId][email] = set()
                result['contact_email_group'][row.AccountId][email].add(row.Id)

    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': sql,
            },
        )
    return result


def get_sf_contacts_update_rollback_data(
    sf_ids: list[str],
) -> typing.Optional[pd.DataFrame]:
    return get_sf_objects_update_rollback_data(
        sf_ids,
        '''SELECT 
            Id, FirstName, LastName, Title, HomePhone, MobilePhone, Phone, OtherPhone, Fax, Email, Personal_Email__c, 
            MailingStreet, MailingCity, MailingState, MailingPostalCode, MailingCountry,
            OtherStreet, OtherCity, OtherState, OtherPostalCode, OtherCountry, AccountId, Gain_ID__c
        FROM Contact''',
        'Contact',
    )


def update_sf_contacts(
    bucket: str,
    directory: str,
    redshift_to_sf_contact_update: list[dict[typing.Hashable, typing.Any]],
    contacts_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_contact_update,
        contacts_gainids_update,
        curr_timestamp,
        'Contact',
        'legalpersonnel',
        'contacts',
        get_sf_contacts_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_contacts_manual_review(
    redshift_to_sf_contacts_update_manual_review: list[dict[str, typing.Any]],
):
    update_sf_objects_manual_review(
        redshift_to_sf_contacts_update_manual_review,
        get_sf_contacts_update_rollback_data,
        'LegalPersonnel',
    )


def insert_sf_contacts(
    bucket: str,
    directory: str,
    redshift_to_sf_contact_insert: list[dict[typing.Hashable, typing.Any]],
    contacts_gainids_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_contact_insert,
        contacts_gainids_insert,
        'Contact',
        'legalpersonnel',
        'contact',
        curr_timestamp,
    )


def upsert_sf_contacts(
    bucket: str,
    directory: str,
    redshift_data_to_sf_contacts: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_contacts,
        curr_timestamp,
        update_sf_contacts,
        update_sf_contacts_manual_review,
        insert_sf_contacts,
    )


# endregion


# region Account Opportunity Relation
def get_sf_account_opportunity_relation(
    redshift_cases_data_mappings: dict[str, dict[str, str]],
    medical_facility_list: list[str],
):
    df_salesforce_account_opportunity_relations = pd.DataFrame()
    # Query in batches of 800 => SF query connector limit is 20,000 characters
    # 800 was chosen => 1 SFId requires 21 characters => 800*21=16,800 => allows for 3,200 other characters in query
    sf_id_batches, salesforce_account_opportunity_relations = [], []
    redshift_case_data = redshift_cases_data_mappings['cases']
    sf_ids = list(redshift_case_data.values())
    for i in range(
        0, len(sf_ids), 800
    ):  # query in batches of 800 ids to avoid hitting SF character limit of 20,000
        sf_id_batches.append(sf_ids[i : i + 800])
    for batch in sf_id_batches:
        salesforce_rollback_query = '''
            SELECT 
                Id, 
                Treatment_Complete__C, 
                Insurance_Vendor__c, 
                Tail_Claim__c, 
                OpportunityID__c, 
                Account_Name__r.Root_Parent_Account__c
            FROM AccountOpportunityRelation__c
            WHERE Account_Name__r.Root_Parent_Account__c in {medical_facility}
            AND OpportunityID__c in {sf_id_list}
        '''
        salesforce_rollback_query = simple_salesforce.format_soql(
            salesforce_rollback_query,
            medical_facility=medical_facility_list,
            sf_id_list=batch,
        )
        try:
            fetch_salesforce_law_firm_opportunities = get_sf_bulk(
                'AccountOpportunityRelation__c'
            ).query(salesforce_rollback_query, lazy_operation=True)
            if fetch_salesforce_law_firm_opportunities:
                for list_results in fetch_salesforce_law_firm_opportunities:
                    salesforce_account_opportunity_relations.extend(
                        list_results
                    )
        except Exception as e:
            logger.error(
                e,
                extra={
                    'additional_details': salesforce_rollback_query,
                },
            )
            return df_salesforce_account_opportunity_relations
    if len(salesforce_account_opportunity_relations) != 0:
        df_salesforce_account_opportunity_relations = pd.json_normalize(
            salesforce_account_opportunity_relations
        )
        df_salesforce_account_opportunity_relations = (
            df_salesforce_account_opportunity_relations.drop(
                columns=[
                    'attributes.type',
                    'attributes.url',
                    'Account_Name__r.attributes.type',
                    'Account_Name__r.attributes.url',
                ]
            )
        )
        df_salesforce_account_opportunity_relations = df_salesforce_account_opportunity_relations.rename(
            columns={
                'Account_Name__r.Root_Parent_Account__c': 'Root_Parent_Account__c',
            }
        )
    return df_salesforce_account_opportunity_relations


def update_sf_account_opportunity_relation(
    bucket: str,
    directory: str,
    sf_account_opportunity_update: list[dict[typing.Hashable, typing.Any]],
    curr_timestamp: datetime.datetime,
    original_sf_df: pd.DataFrame,
):
    save_sf_objects_data_s3(
        bucket,
        directory,
        original_sf_df,
        curr_timestamp,
        'account_opportunity_relation',
    )  # store all rollback data
    acc_opp_relation_update_responses = []
    acc_opp_relation_update_failed = []

    process_batch(
        sf_account_opportunity_update,
        'AccountOpportunityRelation__c',
        [100, 10],
        acc_opp_relation_update_responses,
        acc_opp_relation_update_failed,
        action='update',
    )

    log_failed_sf(
        sf_account_opportunity_update,
        acc_opp_relation_update_responses,
        acc_opp_relation_update_failed,
        bucket,
        directory,
        curr_timestamp,
        'account_opportunity_relation',
    )


# endregion


# region Opportunities
def get_sf_opportunity_data(sf_ids: list[str]):
    (
        opportunities_DOB_DOA_res,
        opportunities_DOB_DOA_email_res,
        opportunities_DOB_DOA_phone_res,
        opportunities_DOB_DOA_name_res,
        opportunity_salesforce_id_modifieddate,
        opportunity_account_id,
        opportunity_attorney_id,
        opportunity_case_status,
        opportunity_stage_name,
    ) = (
        collections.defaultdict(list),
        collections.defaultdict(list),
        collections.defaultdict(list),
        collections.defaultdict(list),
        {},
        {},
        {},
        {},
        {},
    )
    if len(sf_ids) > 1:
        salesforce_law_firm_opportunities_query = '''
            SELECT 
                Id, Name, Date_of_Birth__c, Date_of_Accident__c, LastModifiedDate, 
                Home_Phone__c, Cell_Phone__c, Other_Phone__c, Plaintiff_Email__c
            FROM Opportunity
            WHERE
                (AccountId IN {sf_id_list} OR Account.ParentId IN {sf_id_list} OR Medical_Facility_P__c IN {sf_id_list} OR Partner_Account__c IN {sf_id_list})
                AND (NOT Account.Name LIKE '%NO LONGER%') AND (NOT (Name LIKE '%NO LONGER%' OR Name LIKE '%Duplicate%'))
        '''
        salesforce_law_firm_opportunities_query = (
            simple_salesforce.format_soql(
                salesforce_law_firm_opportunities_query, sf_id_list=sf_ids
            )
        )
        salesforce_medical_facility_opportunities_query = '''
            SELECT 
                Plaintiff__c, Plaintiff__r.Name, Plaintiff__r.Date_of_Birth__c, Plaintiff__r.Date_of_Accident__c, Plaintiff__r.LastModifiedDate,
                Plaintiff__r.Home_Phone__c, Plaintiff__r.Cell_Phone__c, Plaintiff__r.Other_Phone__c, Plaintiff__r.Plaintiff_Email__c,
                Plaintiff__r.AccountId, Plaintiff__r.Attorney__c, Plaintiff__r.Case_Status__c, Plaintiff__r.StageName
            FROM Funding__c
            WHERE Medical_Facility__c IN {sf_id_list} OR Partner_Account__c IN {sf_id_list}
        '''
        salesforce_medical_facility_opportunities_query = (
            simple_salesforce.format_soql(
                salesforce_medical_facility_opportunities_query,
                sf_id_list=sf_ids,
            )
        )
    elif len(sf_ids) == 1:
        salesforce_law_firm_opportunities_query = '''
            SELECT 
                Id, Name, Date_of_Birth__c, Date_of_Accident__c, LastModifiedDate, 
                Home_Phone__c, Cell_Phone__c, Other_Phone__c, Plaintiff_Email__c
            FROM Opportunity
            WHERE
                (AccountId = {sf_id} OR Account.ParentId = {sf_id} OR Medical_Facility_P__c = {sf_id} OR Partner_Account__c = {sf_id})
                AND (NOT Account.Name LIKE '%NO LONGER%') AND (NOT (Name LIKE '%NO LONGER%' OR Name LIKE '%Duplicate%'))
        '''
        salesforce_law_firm_opportunities_query = (
            simple_salesforce.format_soql(
                salesforce_law_firm_opportunities_query, sf_id=sf_ids[0]
            )
        )
        salesforce_medical_facility_opportunities_query = '''
            SELECT 
                Plaintiff__c, Plaintiff__r.Name, Plaintiff__r.Date_of_Birth__c, Plaintiff__r.Date_of_Accident__c, Plaintiff__r.LastModifiedDate,
                Plaintiff__r.Home_Phone__c, Plaintiff__r.Cell_Phone__c, Plaintiff__r.Other_Phone__c, Plaintiff__r.Plaintiff_Email__c,
                Plaintiff__r.AccountId, Plaintiff__r.Attorney__c, Plaintiff__r.Case_Status__c, Plaintiff__r.StageName
            FROM Funding__c
            WHERE Medical_Facility__c = {sf_id} OR Partner_Account__c = {sf_id}
        '''
        salesforce_medical_facility_opportunities_query = (
            simple_salesforce.format_soql(
                salesforce_medical_facility_opportunities_query,
                sf_id=sf_ids[0],
            )
        )
    else:
        logger.error(
            'Salesforce ids empty, no opportunities found',
            extra={
                'additional_details': str(sf_ids),
            },
        )
        return {
            'opportunities_DOB_DOA': opportunities_DOB_DOA_res,
            'opportunities_DOB_DOA_email': opportunities_DOB_DOA_email_res,
            'opportunities_DOB_DOA_phone': opportunities_DOB_DOA_phone_res,
            'opportunities_DOB_DOA_name': opportunities_DOB_DOA_name_res,
            'opportunities_salesforce_id_modifiedtime': opportunity_salesforce_id_modifieddate,
            'opportunity_account_id': opportunity_account_id,
            'opportunity_attorney_id': opportunity_attorney_id,
            'opportunity_case_status': opportunity_case_status,
            'opportunity_stage_name': opportunity_stage_name,
        }
    try:
        salesforce_opportunities_result = []

        fetch_salesforce_law_firm_opportunities = get_sf_bulk(
            'opportunity'
        ).query(salesforce_law_firm_opportunities_query, lazy_operation=True)
        fetch_salesforce_medical_facility_opportunities = get_sf_bulk(
            'Funding__c'
        ).query(
            salesforce_medical_facility_opportunities_query,
            lazy_operation=True,
        )

        # Conversion of fetched bulk data to dataframe
        if fetch_salesforce_law_firm_opportunities:
            salesforce_law_firm_opportunities = []
            for list_results in fetch_salesforce_law_firm_opportunities:
                salesforce_law_firm_opportunities.extend(list_results)
            if len(salesforce_law_firm_opportunities) != 0:
                df_salesforce_law_firm_opportunities = pd.DataFrame(
                    salesforce_law_firm_opportunities
                )
                df_salesforce_law_firm_opportunities = (
                    df_salesforce_law_firm_opportunities.drop(
                        columns=['attributes']
                    )
                )
                df_salesforce_law_firm_opportunities[
                    'LastModifiedDate'
                ] = df_salesforce_law_firm_opportunities[
                    'LastModifiedDate'
                ].apply(
                    lambda x: datetime.datetime.isoformat(
                        datetime.datetime.fromtimestamp(x / 1000)
                    )
                )
                salesforce_opportunities_result.append(
                    df_salesforce_law_firm_opportunities
                )

        # Conversion of fetched bulk data to dataframe
        if fetch_salesforce_medical_facility_opportunities:
            salesforce_medical_facility_opportunities = []
            for (
                list_results
            ) in fetch_salesforce_medical_facility_opportunities:
                salesforce_medical_facility_opportunities.extend(list_results)
            # pd.json_normalize to flatten the object(Plaintiff__r) into columns

            if len(salesforce_medical_facility_opportunities) != 0:
                df_salesforce_medical_facility_opportunities = (
                    pd.json_normalize(
                        salesforce_medical_facility_opportunities
                    )
                )
                df_salesforce_medical_facility_opportunities = (
                    df_salesforce_medical_facility_opportunities.drop(
                        columns=[
                            'attributes.type',
                            'attributes.url',
                            'Plaintiff__r.attributes.type',
                            'Plaintiff__r.attributes.url',
                        ]
                    )
                )
                df_salesforce_medical_facility_opportunities = df_salesforce_medical_facility_opportunities.rename(
                    columns={
                        'Plaintiff__c': 'Id',
                        'Plaintiff__r.Name': 'Name',
                        'Plaintiff__r.Date_of_Birth__c': 'Date_of_Birth__c',
                        'Plaintiff__r.Date_of_Accident__c': 'Date_of_Accident__c',
                        'Plaintiff__r.LastModifiedDate': 'LastModifiedDate',
                        'Plaintiff__r.Home_Phone__c': 'Home_Phone__c',
                        'Plaintiff__r.Cell_Phone__c': 'Cell_Phone__c',
                        'Plaintiff__r.Other_Phone__c': 'Other_Phone__c',
                        'Plaintiff__r.Plaintiff_Email__c': 'Plaintiff_Email__c',
                        'Plaintiff__r.AccountId': 'AccountId',
                        'Plaintiff__r.Attorney__c': 'Attorney__c',
                        'Plaintiff__r.Case_Status__c': 'Case_Status__c',
                        'Plaintiff__r.StageName': 'StageName',
                    }
                )
                df_salesforce_medical_facility_opportunities[
                    'LastModifiedDate'
                ] = df_salesforce_medical_facility_opportunities[
                    'LastModifiedDate'
                ].apply(
                    lambda x: datetime.datetime.isoformat(
                        datetime.datetime.fromtimestamp(x / 1000)
                    )
                )
                df_salesforce_medical_facility_opportunities = (
                    df_salesforce_medical_facility_opportunities.sort_values(
                        by=['LastModifiedDate']
                    )
                    .drop_duplicates(subset=['Id'], keep='last')
                    .reset_index(drop=True)
                )
                salesforce_opportunities_result.append(
                    df_salesforce_medical_facility_opportunities
                )

        if salesforce_opportunities_result:
            opportunities_result = pd.concat(
                salesforce_opportunities_result
            ).reset_index(drop=True)
            for row in opportunities_result.itertuples(index=False):
                if (
                    row.Date_of_Birth__c is not None
                    and row.Date_of_Accident__c is not None
                ):
                    opportunities_DOB_DOA_res[
                        (row.Date_of_Birth__c, row.Date_of_Accident__c)
                    ].append(row.Id)

                    if row.Name is not None:
                        opportunities_DOB_DOA_name_res[
                            (
                                row.Date_of_Birth__c,
                                row.Date_of_Accident__c,
                                row.Name.lower(),
                            )
                        ].append(row.Id)

                    if row.Plaintiff_Email__c is not None:
                        opportunities_DOB_DOA_email_res[
                            (
                                row.Date_of_Birth__c,
                                row.Date_of_Accident__c,
                                row.Plaintiff_Email__c,
                            )
                        ].append(row.Id)

                    if row.Home_Phone__c is not None:
                        opportunities_DOB_DOA_phone_res[
                            (
                                row.Date_of_Birth__c,
                                row.Date_of_Accident__c,
                                row.Home_Phone__c,
                            )
                        ].append(row.Id)

                    if row.Cell_Phone__c is not None:
                        opportunities_DOB_DOA_phone_res[
                            (
                                row.Date_of_Birth__c,
                                row.Date_of_Accident__c,
                                row.Cell_Phone__c,
                            )
                        ].append(row.Id)

                    if row.Other_Phone__c is not None:
                        opportunities_DOB_DOA_phone_res[
                            (
                                row.Date_of_Birth__c,
                                row.Date_of_Accident__c,
                                row.Other_Phone__c,
                            )
                        ].append(row.Id)

                    if row.LastModifiedDate is not None:
                        opportunity_salesforce_id_modifieddate[row.Id] = (
                            row.LastModifiedDate
                        )

                    opportunity_account_id[row.Id] = row.AccountId
                    opportunity_attorney_id[row.Id] = row.Attorney__c

                    if row.Case_Status__c is not None:
                        opportunity_case_status[row.Id] = row.Case_Status__c

                    if row.StageName is not None:
                        opportunity_stage_name[row.Id] = row.StageName

        else:
            logger.error(
                'No Salesforce opportunities retrieved',
                extra={
                    'additional_details': salesforce_law_firm_opportunities_query
                    + "\r\n"
                    + salesforce_medical_facility_opportunities_query,
                },
            )
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_law_firm_opportunities_query
                + "\r\n"
                + salesforce_medical_facility_opportunities_query,
            },
        )
    return {
        'opportunities_DOB_DOA': opportunities_DOB_DOA_res,
        'opportunities_DOB_DOA_email': opportunities_DOB_DOA_email_res,
        'opportunities_DOB_DOA_phone': opportunities_DOB_DOA_phone_res,
        'opportunities_DOB_DOA_name': opportunities_DOB_DOA_name_res,
        'opportunities_salesforce_id_modifiedtime': opportunity_salesforce_id_modifieddate,
        'opportunity_account_id': opportunity_account_id,
        'opportunity_attorney_id': opportunity_attorney_id,
        'opportunity_case_status': opportunity_case_status,
        'opportunity_stage_name': opportunity_stage_name,
    }


def get_sf_opportunities_update_rollback_data(
    sf_ids: list[str],
) -> typing.Optional[pd.DataFrame]:
    columns = get_type_columns(types.OpportunityRollbackData)
    return get_sf_objects_update_rollback_data(
        sf_ids,
        f'SELECT {columns} FROM Opportunity',
        'Opportunity',
    )


def update_sf_opportunities(
    bucket: str,
    directory: str,
    redshift_to_sf_opportunities_update: list[
        dict[typing.Hashable, typing.Any]
    ],
    opportunities_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_opportunities_update,
        opportunities_gainids_update,
        curr_timestamp,
        'Opportunity',
        'cases',
        'opportunities',
        get_sf_opportunities_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_opportunities_manual_review(
    redshift_to_sf_opportunities_update_manual_review: list[dict[str, str]],
):
    def custom_transform(
        salesforce_opportunity: dict[str, typing.Any],
        opportunity: dict[str, typing.Any],
    ):
        opportunity['Law_Firm_Account_Name__c'] = salesforce_opportunity[
            'Law_Firm_Account_Name__c'
        ]  # for new added field Law_Firm Account Id
        if (
            'Plaintiff_Account__r' in salesforce_opportunity
            and salesforce_opportunity['Plaintiff_Account__r'] is not None
            and 'Name' in salesforce_opportunity['Plaintiff_Account__r']
        ):
            plaintiff_account_name = salesforce_opportunity[
                'Plaintiff_Account__r'
            ]['Name']
            (
                opportunity['PlaintiffAccountName'],
                salesforce_opportunity['PlaintiffAccountName'],
            ) = (
                plaintiff_account_name,
                plaintiff_account_name,
            )  # for new added field Plaintiff Account Name
            del salesforce_opportunity['Plaintiff_Account__r']
        else:
            (
                opportunity['PlaintiffAccountName'],
                salesforce_opportunity['PlaintiffAccountName'],
            ) = (None, None)
        if (
            'Medical_Facility_P__r' in salesforce_opportunity
            and salesforce_opportunity['Medical_Facility_P__r'] is not None
            and 'Name' in salesforce_opportunity['Medical_Facility_P__r']
        ):
            medical_facility_name = salesforce_opportunity[
                'Medical_Facility_P__r'
            ]['Name']
            (
                opportunity['MedicalFacilityName'],
                salesforce_opportunity['MedicalFacilityName'],
            ) = (
                medical_facility_name,
                medical_facility_name,
            )  # for new added field Medical_Facility_P__r
            del salesforce_opportunity['Medical_Facility_P__r']
        else:
            (
                opportunity['MedicalFacilityName'],
                salesforce_opportunity['MedicalFacilityName'],
            ) = (None, None)
        if (
            'Partner_Account__r' in salesforce_opportunity
            and salesforce_opportunity['Partner_Account__r'] is not None
            and 'Name' in salesforce_opportunity['Partner_Account__r']
        ):
            partner_account_name = salesforce_opportunity[
                'Partner_Account__r'
            ]['Name']
            (
                opportunity['PartnerAccountName'],
                salesforce_opportunity['PartnerAccountName'],
            ) = (
                partner_account_name,
                partner_account_name,
            )  # for new added field Partner_Account__r
            del salesforce_opportunity['Partner_Account__r']
        else:
            (
                opportunity['PartnerAccountName'],
                salesforce_opportunity['PartnerAccountName'],
            ) = (None, None)

    update_sf_objects_manual_review(
        redshift_to_sf_opportunities_update_manual_review,
        get_sf_opportunities_update_rollback_data,
        'Cases',
        custom_transform=custom_transform,
    )


def insert_sf_opportunities(
    bucket: str,
    directory: str,
    redshift_to_sf_opportunities_insert: list[
        dict[typing.Hashable, typing.Any]
    ],
    cases_gainids_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_opportunities_insert,
        cases_gainids_insert,
        'Opportunity',
        'cases',
        'opportunity',
        curr_timestamp,
    )


def upsert_sf_opportunities(
    bucket: str,
    directory: str,
    redshift_data_to_sf_opportunities: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_opportunities,
        curr_timestamp,
        update_sf_opportunities,
        update_sf_opportunities_manual_review,
        insert_sf_opportunities,
    )


# endregion


# region Fundings
def get_sf_funding_data(sf_ids: list[str]):
    (
        fundings_mapping_res,
        fundings_potential_duplicates_check_before_insert,
        fundings_check_last_modified_date_before_update,
        fundings_check_stage_sub_stage_before_update,
    ) = (collections.defaultdict(list), {}, {}, {})
    if not sf_ids:
        logger.error(
            'Salesforce ids empty, no fundings found',
            extra={
                'additional_details': sf_ids,
            },
        )
        return {
            'fundings_mapping_res': fundings_mapping_res,
            'fundings_potential_duplicates_check_before_insert': fundings_potential_duplicates_check_before_insert,
            'fundings_check_last_modified_date_before_update': fundings_check_last_modified_date_before_update,
        }
    salesforce_law_firm_fundings_query = '''
        SELECT
            Id, 
            Name, 
            Plaintiff__c, 
            Medical_Facility__c, 
            Partner_Account__c, 
            Date_of_Service__c, 
            Invoice_Amount__c, 
            Medical_Claim_Number__c, 
            LastModifiedDate,
            Funding_Stage__c,
            Funding_Sub_Stage__c
        FROM Funding__c
        WHERE Plaintiff__r.AccountId IN {sf_id_list}
    '''
    salesforce_medical_facility_fundings_query = '''
        SELECT
            Id, 
            Name, 
            Plaintiff__c, 
            Medical_Facility__c, 
            Partner_Account__c, 
            Date_of_Service__c, 
            Invoice_Amount__c, 
            Medical_Claim_Number__c, 
            LastModifiedDate,
            Funding_Stage__c,
            Funding_Sub_Stage__c
        FROM Funding__c
        WHERE Medical_Facility__c IN {sf_id_list} OR Partner_Account__c IN  {sf_id_list}
    '''
    salesforce_law_firm_fundings_query = simple_salesforce.format_soql(
        salesforce_law_firm_fundings_query, sf_id_list=sf_ids
    )
    salesforce_medical_facility_fundings_query = simple_salesforce.format_soql(
        salesforce_medical_facility_fundings_query, sf_id_list=sf_ids
    )
    try:
        salesforce_fundings_result = []
        salesforce_law_firm_fundings_result = sf.query_all(
            salesforce_law_firm_fundings_query
        )  # bulk queries return datetime incorrectly as timestamp
        salesforce_medical_facility_fundings_result = sf.query_all(
            salesforce_medical_facility_fundings_query
        )  # bulk queries not allowed with aggregate functions
        if salesforce_law_firm_fundings_result:
            salesforce_fundings_result.extend(
                salesforce_law_firm_fundings_result['records']
            )
        if salesforce_medical_facility_fundings_result:
            salesforce_fundings_result.extend(
                salesforce_medical_facility_fundings_result['records']
            )
        if salesforce_fundings_result:
            fundings_result = pd.DataFrame(salesforce_fundings_result).drop(
                columns='attributes'
            )
            # Update last modified dates mapping
            fundings_check_last_modified_date_before_update.update(
                fundings_result.set_index('Id')['LastModifiedDate'].to_dict()
            )
            fundings_check_stage_sub_stage_before_update.update(
                fundings_result.set_index('Id')[
                    ['Funding_Stage__c', 'Funding_Sub_Stage__c']
                ].to_dict(orient='index')
            )

            # Filter rows with required fields
            valid_fundings = fundings_result[
                fundings_result['Plaintiff__c'].notna()
                & (
                    fundings_result['Medical_Facility__c'].notna()
                    | fundings_result['Partner_Account__c'].notna()
                )
                & fundings_result['Date_of_Service__c'].notna()
                & fundings_result['Invoice_Amount__c'].notna()
            ]
            fundings_mapping_res.update(
                valid_fundings.groupby(
                    [
                        'Plaintiff__c',
                        'Medical_Facility__c',
                        'Partner_Account__c',
                        'Date_of_Service__c',
                        'Medical_Claim_Number__c',
                        'Invoice_Amount__c',
                    ]
                )['Id']
                .agg(list)
                .to_dict()
            )
            # Update duplicates check mapping
            duplicate_check_tuples = valid_fundings.apply(
                lambda x: (
                    x['Plaintiff__c'],
                    x['Medical_Facility__c'],
                    x['Partner_Account__c'],
                    x['Date_of_Service__c'],
                    x['Medical_Claim_Number__c'],
                ),
                axis=1,
            )
            fundings_potential_duplicates_check_before_insert.update(
                dict(
                    zip(
                        duplicate_check_tuples,
                        zip(
                            valid_fundings['Id'],
                            valid_fundings['Invoice_Amount__c'],
                        ),
                    )
                )
            )
        else:
            logger.error(
                'No Salesforce fundings retrieved',
                extra={
                    'additional_details': salesforce_law_firm_fundings_query
                    + '\r\n'
                    + salesforce_medical_facility_fundings_query,
                },
            )
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_law_firm_fundings_query
                + '\r\n'
                + salesforce_medical_facility_fundings_query,
            },
        )
    return {
        'fundings_mapping_res': fundings_mapping_res,
        'fundings_potential_duplicates_check_before_insert': fundings_potential_duplicates_check_before_insert,
        'fundings_check_last_modified_date_before_update': fundings_check_last_modified_date_before_update,
        'fundings_check_stage_sub_stage_before_update': fundings_check_stage_sub_stage_before_update,
    }


def get_sf_fundings_update_rollback_data(
    sf_ids: list[str],
) -> typing.Optional[pd.DataFrame]:
    columns = get_type_columns(types.FundingsRollbackData)
    return get_sf_objects_update_rollback_data(
        sf_ids,
        f'SELECT {columns} FROM Funding__c',
        'Funding__c',
    )


def update_sf_fundings(
    bucket: str,
    directory: str,
    redshift_to_sf_fundings_update: list[dict[typing.Hashable, typing.Any]],
    billings_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_fundings_update,
        billings_gainids_update,
        curr_timestamp,
        'Funding__c',
        'billings',
        'fundings',
        get_sf_fundings_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_fundings_manual_review(
    redshift_to_sf_fundings_update_manual_review: list[dict[str, str]],
):
    def custom_transform(
        salesforce_funding: dict[str, typing.Any],
        funding: dict[str, typing.Any],
    ):
        if (
            'RecordType' in salesforce_funding
            and salesforce_funding['RecordType'] is not None
            and 'Name' in salesforce_funding['RecordType']
        ):
            record_type_name = salesforce_funding['RecordType']['Name']
            (
                funding['RecordTypeName'],
                salesforce_funding['RecordTypeName'],
            ) = (
                record_type_name,
                record_type_name,
            )  # for new added field Record Type Name
        else:
            (
                funding['RecordTypeName'],
                salesforce_funding['RecordTypeName'],
            ) = (None, None)
        if (
            'Medical_Facility__r' in salesforce_funding
            and salesforce_funding['Medical_Facility__r'] is not None
            and 'Name' in salesforce_funding['Medical_Facility__r']
        ):
            medical_facility_name = salesforce_funding['Medical_Facility__r'][
                'Name'
            ]
            (
                funding['MedicalFacilityName'],
                salesforce_funding['MedicalFacilityName'],
            ) = (
                medical_facility_name,
                medical_facility_name,
            )  # for new added field Medical Facility Name
            del salesforce_funding['Medical_Facility__r']
        else:
            (
                funding['MedicalFacilityName'],
                salesforce_funding['MedicalFacilityName'],
            ) = (None, None)
        if (
            'Medical_Location__r' in salesforce_funding
            and salesforce_funding['Medical_Location__r'] is not None
            and 'Name' in salesforce_funding['Medical_Location__r']
        ):
            medical_location_name = salesforce_funding['Medical_Location__r'][
                'Name'
            ]
            (
                funding['MedicalLocationName'],
                salesforce_funding['MedicalLocationName'],
            ) = (
                medical_location_name,
                medical_location_name,
            )  # for new added field Medical Location Name
            del salesforce_funding['Medical_Location__r']
        else:
            (
                funding['MedicalLocationName'],
                salesforce_funding['MedicalLocationName'],
            ) = (None, None)
        if (
            'Partner_Account__r' in salesforce_funding
            and salesforce_funding['Partner_Account__r'] is not None
            and 'Name' in salesforce_funding['Partner_Account__r']
        ):
            partner_account_name = salesforce_funding['Partner_Account__r'][
                'Name'
            ]
            (
                funding['PartnerAccountName'],
                salesforce_funding['PartnerAccountName'],
            ) = (
                partner_account_name,
                partner_account_name,
            )  # for new added field Partner Account Name
            del salesforce_funding['Partner_Account__r']
        else:
            (
                funding['PartnerAccountName'],
                salesforce_funding['PartnerAccountName'],
            ) = (None, None)

    update_sf_objects_manual_review(
        redshift_to_sf_fundings_update_manual_review,
        get_sf_fundings_update_rollback_data,
        'Billings',
        custom_transform=custom_transform,
    )


def insert_sf_fundings(
    bucket: str,
    directory: str,
    redshift_to_sf_fundings_insert: list[dict[typing.Hashable, typing.Any]],
    billings_gainids_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_fundings_insert,
        billings_gainids_insert,
        'Funding__c',
        'billings',
        'funding',
        curr_timestamp,
    )


def upsert_sf_fundings(
    bucket: str,
    directory: str,
    redshift_data_to_sf_fundings: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_fundings,
        curr_timestamp,
        update_sf_fundings,
        update_sf_fundings_manual_review,
        insert_sf_fundings,
    )


# endregion


# region Charges
def get_sf_charge_data(sf_ids: list[str]):
    (
        charges_billing_mapping_res,
        charges_externalsource_mapping_res,
        charges_salesforce_id_modifiedtime,
    ) = (collections.defaultdict(list), {}, {})
    if len(sf_ids) > 1:
        salesforce_medical_facility_charges_query = '''
            SELECT 
                Id, Funding__c, Charge_Id__c, LastModifiedDate
            FROM Charge__c
            WHERE Funding__r.Medical_Facility__c IN {sf_id_list} OR Funding__r.Partner_Account__c IN {sf_id_list}
        '''
        salesforce_medical_facility_charges_query = (
            simple_salesforce.format_soql(
                salesforce_medical_facility_charges_query, sf_id_list=sf_ids
            )
        )
    elif len(sf_ids) == 1:
        salesforce_medical_facility_charges_query = '''
            SELECT 
                Id, Funding__c, Charge_Id__c, LastModifiedDate
            FROM Charge__c
            WHERE Funding__r.Medical_Facility__c = {sf_id} OR Funding__r.Partner_Account__c = {sf_id}
        '''
        salesforce_medical_facility_charges_query = (
            simple_salesforce.format_soql(
                salesforce_medical_facility_charges_query, sf_id=sf_ids[0]
            )
        )
    else:
        logger.error(
            'Salesforce ids empty, no charges found',
            extra={
                'additional_details': str(sf_ids),
            },
        )
        return {
            'charges_billing_mapping_res': charges_billing_mapping_res,
            'charges_externalsource_mapping_res': charges_externalsource_mapping_res,
            'charges_salesforce_id_modifiedtime': charges_salesforce_id_modifiedtime,
        }
    try:
        salesforce_charges_result = []
        salesforce_medical_facility_charges_result = sf.query_all(
            salesforce_medical_facility_charges_query
        )  # bulk queries not allowed with aggregate functions
        if salesforce_medical_facility_charges_result:
            salesforce_charges_result.extend(
                salesforce_medical_facility_charges_result['records']
            )
        if salesforce_charges_result:
            charges_result = pd.DataFrame(salesforce_charges_result).drop(
                columns='attributes'
            )
            # Create mappings directly from DataFrame operations instead of iterating
            charges_billing_mapping_res.update(
                charges_result.set_index('Id')['Funding__c'].to_dict()
            )
            charges_externalsource_mapping_res.update(
                charges_result[charges_result['Charge_Id__c'].notna()]
                .set_index('Charge_Id__c')['Id']
                .to_dict()
            )
            charges_salesforce_id_modifiedtime.update(
                charges_result.set_index('Id')['LastModifiedDate'].to_dict()
            )
        else:
            logger.error(
                'No Salesforce charges retrieved',
                extra={
                    'additional_details': salesforce_medical_facility_charges_query,
                },
            )
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': salesforce_medical_facility_charges_query
            },
        )
    return {
        'charges_billing_mapping_res': charges_billing_mapping_res,
        'charges_externalsource_mapping_res': charges_externalsource_mapping_res,
        'charges_salesforce_id_modifiedtime': charges_salesforce_id_modifiedtime,
    }


def get_sf_charges_update_rollback_data(
    sf_ids: list[str],
) -> typing.Optional[pd.DataFrame]:
    columns = get_type_columns(types.ChargesRollbackData)
    return get_sf_objects_update_rollback_data(
        sf_ids,
        f'SELECT {columns} FROM Charge__c',
        'Charge__c',
    )


def update_sf_charges(
    bucket: str,
    directory: str,
    redshift_to_sf_charges_update: list[dict[typing.Hashable, typing.Any]],
    charges_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_charges_update,
        charges_gainids_update,
        curr_timestamp,
        'Charge__c',
        'charges',
        'charges',
        get_sf_charges_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_charges_manual_review(
    redshift_to_sf_charges_update_manual_review: list[dict[str, str]],
):
    update_sf_objects_manual_review(
        redshift_to_sf_charges_update_manual_review,
        get_sf_charges_update_rollback_data,
        'Charges',
    )


def insert_sf_charges(
    bucket: str,
    directory: str,
    redshift_to_sf_charges_insert: list[dict[typing.Hashable, typing.Any]],
    charges_gainids_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_charges_insert,
        charges_gainids_insert,
        'Charge__c',
        'charges',
        'charge',
        curr_timestamp,
    )


def upsert_sf_charges(
    bucket: str,
    directory: str,
    redshift_data_to_sf_charges: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_charges,
        curr_timestamp,
        update_sf_charges,
        update_sf_charges_manual_review,
        insert_sf_charges,
    )


# endregion


# region ContentVersions
def get_sf_file_names(sf_ids: list[str]):
    salesforce_filename_map = {}
    if len(sf_ids) == 0:
        return salesforce_filename_map
    else:  # query in batches of 800 => SF query connector limit is 20,000 characters
        # 800 was chosen => 1 SFId requires 21 characters => 800*21=16,800 => allows for 3,200 other characters in query
        sf_id_batches = []
        salesforce_file_names = []
        for i in range(
            0, len(sf_ids), 800
        ):  # query in batches of 800 ids to avoid hitting SF character limit of 20,000
            sf_id_batches.append(sf_ids[i : i + 800])
        for batch in sf_id_batches:
            if len(batch) > 1:
                salesforce_file_query = '''
                    SELECT Title
                    FROM ContentVersion
                    WHERE
                        Id IN {sf_id_list}
                '''
                salesforce_file_query = simple_salesforce.format_soql(
                    salesforce_file_query, sf_id_list=batch
                )
            else:
                salesforce_file_query = '''
                    SELECT Title
                    FROM ContentVersion
                    WHERE
                        Id = {sf_id}
                '''
                salesforce_file_query = simple_salesforce.format_soql(
                    salesforce_file_query, sf_id=batch[0]
                )
            try:
                salesforce_file_names.extend(
                    get_sf_bulk('ContentVersion').query(salesforce_file_query)
                )
            except Exception as e:
                logger.error(
                    e,
                    extra={
                        'additional_details': salesforce_file_query,
                    },
                )
                return salesforce_filename_map
    for name in salesforce_file_names:
        if (
            '-' in name['Title'].rsplit("_", 1)[1]
        ):  # if file name does not have a version number
            salesforce_filename_map[name['Title']] = name['Title']
        else:  # if salesforce file name did have a version number
            salesforce_filename_map[name['Title'].rsplit("_", 1)[0]] = name[
                'Title'
            ]
    return salesforce_filename_map


def get_sf_files_update_rollback_data(sf_ids: list[str]):
    return get_sf_objects_update_rollback_data(
        sf_ids,
        '''SELECT 
            Id, 
            Title, 
            Document_Type__c, 
            PathOnClient, 
            FirstPublishLocationId, 
            Law_Firm__c, 
            Medical_Facility__c, 
            Opportunity__c
        FROM ContentVersion''',
        'ContentVersion',
    )


def update_sf_files(
    bucket: str,
    directory: str,
    redshift_to_sf_files_update: list[dict[typing.Hashable, typing.Any]],
    files_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: typing.Optional[list[str]] = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_files_update,
        files_gainids_update,
        curr_timestamp,
        'ContentVersion',
        'files',
        'files',
        get_sf_files_update_rollback_data,
        delta,
        manual_review_ids,
        [10],
    )


def update_sf_files_manual_review(
    redshift_to_sf_files_update_manual_review: list[dict[str, str]],
):
    def custom_transform(
        salesforce_file: dict[str, typing.Any], file: dict[str, typing.Any]
    ):
        if 'VersionData' in file:
            file.pop('VersionData')

    update_sf_objects_manual_review(
        redshift_to_sf_files_update_manual_review,
        get_sf_files_update_rollback_data,
        'Files',
        custom_transform=custom_transform,
    )


def insert_sf_files(
    bucket: str,
    directory: str,
    redshift_to_sf_files_insert: list[dict[typing.Hashable, typing.Any]],
    files_gainid_insert: list[str],
    curr_timestamp: datetime.datetime,
):
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_files_insert,
        files_gainid_insert,
        'ContentVersion',
        'files',
        'file',
        curr_timestamp,
        [5, 2],
    )


def upsert_sf_files(
    bucket: str,
    directory: str,
    redshift_data_to_sf_files: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
):
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_files,
        curr_timestamp,
        update_sf_files,
        update_sf_files_manual_review,
        insert_sf_files,
    )


# endregion


# region ContentNote
def get_sf_notes_update_rollback_data(sf_ids: list[str]):
    return get_sf_objects_update_rollback_data(
        sf_ids,
        '''SELECT 
            Id, 
            Title
        FROM ContentNote''',
        'ContentNote',
    )


def update_sf_notes(
    bucket: str,
    directory: str,
    redshift_to_sf_notes_update: list[dict[typing.Hashable, typing.Any]],
    notes_gainids_update: list[str],
    curr_timestamp: datetime.datetime,
    delta: bool = False,
    manual_review_ids: list[str] | None = None,
):
    update_sf_objects(
        bucket,
        directory,
        redshift_to_sf_notes_update,
        notes_gainids_update,
        curr_timestamp,
        'ContentNote',
        'notes',
        'notes',
        get_sf_notes_update_rollback_data,
        delta,
        manual_review_ids,
    )


def update_sf_notes_manual_review(
    redshift_to_sf_notes_update_manual_review: list[dict[str, str]],
) -> None:
    update_sf_objects_manual_review(
        redshift_to_sf_notes_update_manual_review,
        get_sf_notes_update_rollback_data,
        'Notes',
    )


def insert_sf_notes(
    bucket: str,
    directory: str,
    redshift_to_sf_notes_insert: list[dict[typing.Hashable, typing.Any]],
    notes_gainid_insert: list[str],
    curr_timestamp: datetime.datetime,
) -> None:
    insert_sf_objects(
        bucket,
        directory,
        redshift_to_sf_notes_insert,
        notes_gainid_insert,
        'ContentNote',
        'notes',
        'note',
        curr_timestamp,
    )
    redshift_notes_sf_data, _ = aws_operations.query_postgres_sf_map(
        mappings=["notes"],
        id_subset=notes_gainid_insert,
    )
    redshift_notes_sf_map = redshift_notes_sf_data['notes']
    notes_caseids_df = aws_operations.get_postgres_case_map_using_notes(
        notes_gainid_insert
    )
    caseids = notes_caseids_df['CaseId'].unique().tolist()
    notes_caseids_map = notes_caseids_df.set_index('GainId').to_dict()[
        'CaseId'
    ]
    redshift_cases_sf_data, _ = aws_operations.query_postgres_sf_map(
        mappings=["cases"],
        id_subset=caseids,
    )
    redshift_cases_sf_map = redshift_cases_sf_data['cases']
    redshift_to_sf_contentdocumentlink_insert = []
    for note_id, sf_id in redshift_notes_sf_map.items():
        if sf_id is None or sf_id == '':
            continue
        case_id = notes_caseids_map.get(note_id, None)
        if case_id is None or case_id == '':
            continue
        case_sf_id = redshift_cases_sf_map.get(case_id, None)
        if case_sf_id is None or case_sf_id == '':
            continue
        record = {
            'ContentDocumentId': sf_id,
            'LinkedEntityId': case_sf_id,
            'ShareType': 'V',
            'Visibility': 'InternalUsers',
        }
        redshift_to_sf_contentdocumentlink_insert.append(record)
    sf_objects_insert_responses = []
    redshift_to_sf_objects_webapp_failed = []
    redshift_to_sf_objects_sf_failed = []
    process_batch(
        redshift_to_sf_contentdocumentlink_insert,
        'ContentDocumentLink',
        [100, 10],
        sf_objects_insert_responses,
        redshift_to_sf_objects_webapp_failed,
        action='insert',
    )
    if len(sf_objects_insert_responses) == 0:
        logger.error(
            f"The sf.bulk.ContentDocumentLink.insert does not return any response!",
            extra={
                'additional_details': str(
                    redshift_to_sf_contentdocumentlink_insert
                ),
            },
        )
        return

    log_failed_sf(
        redshift_to_sf_contentdocumentlink_insert,
        sf_objects_insert_responses,
        redshift_to_sf_objects_sf_failed,
        bucket,
        directory,
        curr_timestamp,
        'notes',
    )


def upsert_sf_notes(
    bucket: str,
    directory: str,
    redshift_data_to_sf_notes: pipeline.AffectedData,
    curr_timestamp: datetime.datetime,
) -> None:
    upsert_sf_objects(
        bucket,
        directory,
        redshift_data_to_sf_notes,
        curr_timestamp,
        update_sf_notes,
        update_sf_notes_manual_review,
        insert_sf_notes,
    )


# endregion


# region ExternalIDContainers
def get_externalidcontainer_primary_gain_id_map(
    sf_ids: list[str], source: str = 'ATI'
):
    if source != 'ATI':
        raise NotImplementedError('Not implemented source')

    if not sf_ids or len(sf_ids) == 0:
        return pd.DataFrame()
    sql = '''
        SELECT 
            Id, 
            Primary_Gain_ID__c
        From External_ID_Container__c
        WHERE Id IN {sf_id_list}
    '''
    sql = simple_salesforce.format_soql(sql, sf_id_list=sf_ids)
    result = pd.DataFrame()
    try:
        sf_result = get_sf_bulk('External_ID_Container__c').query(sql)
        df_eid_container = pd.DataFrame(sf_result).drop(columns=['attributes'])

        return df_eid_container[['Id', 'Primary_Gain_ID__c']]
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': sql,
            },
        )
    return result


# endregion


# region Other functions
def get_sf_id(sf_object: str, account_name: str):
    account_sf_id = None
    account_id_query = '''
        SELECT Id, Name
        FROM {obj:literal}
        WHERE Name = {name}
        LIMIT 1
    '''
    account_id_query = simple_salesforce.format_soql(
        account_id_query,
        obj=sf_object,
        name=local_operations.get_client_salesforce_name(account_name),
    )
    try:
        account_id_query_res = sf.query(account_id_query)
        account_sf_id = dict(dict(account_id_query_res)['records'][0])['Id']
    except simple_salesforce.exceptions.SalesforceMalformedRequest:
        logger.error(
            f'{sf_object} Id query was not generated properly. There was a malformed request.',
            extra={
                'additional_details': account_id_query,
            },
        )
    except Exception as e:
        logger.error(e, extra={'additional_details': account_id_query})
    return account_sf_id


def verify_record_exists(sf_object: str, sf_id: str):
    record_id_query = '''
        SELECT Id
        FROM {obj:literal}
        WHERE Id = {id}
        LIMIT 1
    '''
    record_id_query = simple_salesforce.format_soql(
        record_id_query,
        obj=sf_object,
        id=sf_id,
    )

    query_response = sf.query(record_id_query)
    return query_response['totalSize'] > 0


def verify_records_exists(sf_object: str, sf_ids: list[str]):
    record_id_query = '''
        SELECT Id
        FROM {obj:literal}
        WHERE Id IN {id_list}
    '''
    record_id_query = simple_salesforce.format_soql(
        record_id_query,
        obj=sf_object,
        id_list=sf_ids,
    )

    query_response = sf.query(record_id_query)
    return query_response['records']


def get_sf_record_type_id(sf_object: str, record_type_name: str) -> str:
    record_type_id = ''
    record_id_query = '''
        SELECT RecordTypeId
        FROM {obj:literal}
        WHERE RecordType.Name = {name}
        LIMIT 1
    '''
    record_id_query = simple_salesforce.format_soql(
        record_id_query, obj=sf_object, name=record_type_name
    )
    try:
        record_id_result = sf.query(record_id_query)
        record_type_id = dict(dict(record_id_result)['records'][0])[
            'RecordTypeId'
        ]
    except simple_salesforce.exceptions.SalesforceMalformedRequest:
        logger.error(
            f'{sf_object} Record Type Id query was not generated properly. There was a malformed request.',
            extra={'additional_details': record_id_query},
        )
    except Exception as e:
        logger.error(e, extra={'additional_details': record_id_query})
    return record_type_id


def get_object_fields(object_name: str):
    object_describe = get_sf_action(sf, object_name, 'describe')()

    return {item['name']: item['label'] for item in object_describe['fields']}


# endregion
