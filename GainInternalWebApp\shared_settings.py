import os

from decouple import config


def allowedHostsTesting(hosts: list[str]):
    if os.getenv('IS_TESTING', False) or os.getenv('ALLOW_LOCALHOST', False):
        hosts.extend(['localhost', '127.0.0.1'])


def databasesTesting(databases: dict[str, dict[str, str]]):
    if os.getenv('IS_TESTING', False):
        databases['default'] = {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': ':memory:',
        }


def get_environment_credentials(dummy: str):
    return {
        "dummy": dummy,
        "integration": {
            "aws": {
                "aws_access_key_id": config(
                    'CREDENTIALS_INTEGRATION_AWS_ACCESS_KEY_ID'
                ),
                "aws_secret_access_key": config(
                    'CREDENTIALS_INTEGRATION_AWS_SECRET_ACCESS_KEY',
                ),
                "redshift": {
                    "user": config(
                        'CREDENTIALS_INTEGRATION_AWS_REDSHIFT_USER',
                    ),
                    "password": config(
                        'CREDENTIALS_INTEGRATION_AWS_REDSHIFT_PASSWORD',
                    ),
                },
            },
            "filevine": {
                "test": {
                    "api_key": config(
                        'CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_KEY',
                    ),
                    "api_secret": config(
                        'CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_SECRET',
                    ),
                },
                "hostilo": {
                    "api_key": config(
                        'CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_KEY',
                    ),
                    "api_secret": config(
                        'CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_SECRET',
                    ),
                },
            },
            "athenahealth": {
                "api": {
                    "test": {
                        "client": config(
                            'CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_CLIENT',
                        ),
                        "secret": config(
                            'CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_SECRET',
                        ),
                    },
                    "preview": {
                        "client": config(
                            'CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_CLIENT',
                        ),
                        "secret": config(
                            'CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_SECRET',
                        ),
                    },
                    "production": {
                        "client": config(
                            'CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_CLIENT',
                        ),
                        "secret": config(
                            'CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_SECRET',
                        ),
                    },
                },
                "dataview": {
                    "username": config(
                        'CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_USERNAME',
                    ),
                    "password": config(
                        'CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_PASSWORD',
                    ),
                    "account": config(
                        'CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_ACCOUNT'
                    ),
                    "warehouse": config(
                        'CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_WAREHOUSE'
                    ),
                    "db": config(
                        'CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_DB'
                    ),
                    "schema": config(
                        'CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_SCHEMA'
                    ),
                },
            },
            "salesforce": {
                "sandbox": {
                    "username": config(
                        'CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_USERNAME'
                    ),
                    "password": config(
                        'CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_PASSWORD'
                    ),
                    "security_token": config(
                        'CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_SECURITY_TOKEN'
                    ),
                },
                "production": {
                    "username": config(
                        'CREDENTIALS_INTEGRATION_SALESFORCE_USERNAME'
                    ),
                    "password": config(
                        'CREDENTIALS_INTEGRATION_SALESFORCE_PASSWORD'
                    ),
                    "security_token": config(
                        'CREDENTIALS_INTEGRATION_SALESFORCE_SECURITY_TOKEN'
                    ),
                },
            },
            "ati": {},
        },
        "parsing": {
            "aws": {
                "aws_access_key_id": config(
                    'CREDENTIALS_PARSING_AWS_ACCESS_KEY_ID'
                ),
                "aws_secret_access_key": config(
                    'CREDENTIALS_PARSING_AWS_SECRET_ACCESS_KEY'
                ),
                "region_name": config('CREDENTIALS_PARSING_AWS_REGION_NAME'),
            }
        },
    }
