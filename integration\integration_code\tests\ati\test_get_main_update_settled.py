import typing

import psycopg
import pytest


class TestGetMainUpdateSettled:
    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "processed_data", [{"source": "ati"}], indirect=True
    )
    def test_should_get_main_update_settled(
        self,
        processed_data: dict[str, typing.Any],
        database: psycopg.Connection[typing.Any],
    ):
        """Test that the main update settled function correctly processes ATI data."""
        import integration.integration_code.ati as ati

        ati.get_main_update_settled(
            move_flag=True,
            test=True,
            all_data=True,
        )
