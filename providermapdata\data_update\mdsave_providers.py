# pyright: reportArgumentType=warning
import json

import boto3
import pandas as pd

with open('./providermapdata/data_update/credentials.json', 'rb') as f:
    credentials = json.load(f)

# Access to s3 buckets
access_key = credentials['aws-s3']['access_key']
secret_access_key = credentials['aws-s3']['secret_access_key']
s3_client = boto3.client(
    's3', aws_access_key_id=access_key, aws_secret_access_key=secret_access_key
)


def return_mdsave_providers():
    response = s3_client.get_object(
        Bucket="gain-servicing", Key="MDSave/MDSaveProviders.csv"
    )
    status = response.get("ResponseMetadata", {}).get("HTTPStatusCode")
    if status == 200:
        print("Successful S3 get_object response. Status - {}".format(status))
        df_MDSaveProviders = pd.read_csv(
            response.get("Body"), sep=',', index_col=False, low_memory=False
        )
        usefulColumns = [
            'ProviderName',
            'ProviderSpecialty',
            'State',
            'City',
            'Street',
            'ZipCode',
            'Latitude',
            'Longitude',
            'ImageUrl',
        ]
        df_MDSaveProviders = df_MDSaveProviders[usefulColumns]

        # Assumption is MDSave Accept PI = Yes
        df_MDSaveProviders.insert(1, 'AcceptPI', 1)
        df_MDSaveProviders.insert(3, 'OtherSpecialties', '')
        df_MDSaveProviders.insert(
            4,
            'Address',
            df_MDSaveProviders.loc[:, 'Street']
            + ', '
            + df_MDSaveProviders.loc[:, 'City']
            + ', '
            + df_MDSaveProviders.loc[:, 'State'],
        )
        df_MDSaveProviders.insert(11, 'Phone', '')
        df_MDSaveProviders.insert(12, 'Website', '')
        # ProviderType = 1 means gain providers, 2 means other providers
        # Accept PI is Yes, hence Provider Type is 1 i.e. Gain Provider
        df_MDSaveProviders.insert(13, 'ProviderType', 1)

        # Update the provider type value based on whether it accept PI or not
        df_MDSaveProviders.loc[
            df_MDSaveProviders.AcceptPI == 1, 'ProviderType'
        ] = 1
        df_MDSaveProviders.loc[
            df_MDSaveProviders.AcceptPI == 0, 'ProviderType'
        ] = 2
        df_MDSaveProviders.loc[:, 'DiscountSelfPay'] = 1
        df_MDSaveProviders.loc[:, 'PreferredProvider'] = 0
        df_MDSaveProviders[~df_MDSaveProviders['AcceptPI'] == -1]
        return df_MDSaveProviders
    else:
        print(
            "Unsuccessful S3 get_object response. Status - {}".format(status)
        )
