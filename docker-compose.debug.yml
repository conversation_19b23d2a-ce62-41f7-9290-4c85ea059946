version: '3.4'

services:
  vscodedjangodocker:
    image: vscodedjangodocker
    build:
      context: .
      dockerfile: ./Dockerfile
    command: [ "sh", "-c", "pip install debugpy -t /tmp && python /tmp/debugpy --wait-for-client --listen 0.0.0.0:5678 manage.py runserver 0.0.0.0:8000" ]
    ports:
      - 8000:8000
      - 5678:5678
    volumes:
      - .:/app
    environment:
      ENCRYPTION_KEY: ${ECRYPTION_KEY}
      COMPOSE_HTTP_TIMEOUT: 1200
