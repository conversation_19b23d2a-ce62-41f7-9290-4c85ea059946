import typing
from urllib.parse import urlencode

import django.urls
import psycopg
import pytest
from django.test import Client

from . import models


class TestIntegrationViews:
    @pytest.mark.django_db
    def test_review_counts(self, authorized_client: Client):
        url = django.urls.reverse('integration:review_counts')
        response = authorized_client.get(url)

        assert response.status_code == 200
        response_json = response.json()
        assert response_json['message'] == 'Reviews counts'

    def test_should_get_reviewed_data(self, authorized_client: Client):
        review_data = {
            'canonical_object': 'Cases',
            'salesforce_id': '0065C000009zo8WQAQ',
            'redshift_id': '1',
            'keys': 'Name | Date_of_Birth__c | SSN__c | Drivers_License__c | Gender__c | Home_Phone__c | Cell_Phone__c | Other_Phone__c | Plaintiff_Email__c | Address__c | Address_2__c | City__c | State__c | Zip__c | Case_Status__c | Date_of_Accident__c | Description_of_Accident_Incident__c | StageName | CloseDate | Plaintiff_Account__c | Insured_s_Name__c | Insurance_Company__c | Insurance_Limits__c | Insurance_Co_Address__c | Insurance_Co_Address_2__c | Insurance_Co_City__c | Insurance_Co_State__c | Insurance_Co_Zipcode__c | Insurance_Co_Phone__c | Insurance_Co_Fax__c | X2_Insured_s_Name__c | Insurance_Company_2__c | Insurance_Limits_2__c | X2_Zipcode__c | Insurance_Agent__c | AccountId | Medical_Facility_P__c | Law_Firm_Account_Name__c | PlaintiffAccountName | MedicalFacilityName',
            'redshift_data': 'Blake Barrom | 1993-09-19 |  |  | M | (************* |  |  | <EMAIL> | 456 East Valley Road Northeast |  | Rydal | GA | 30171 | Still Treating | 2019-10-30 |  | New | 2023-01-25 | 0015C00000tzPXJQA2 |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  | 0015C00000pExNdQAK | 001G000001kjk6YIAQ | No Law Firm | Blake Barrom | Resurgens Orthopaedics - HCP',
            'salesforce_data': 'Blake Barrom | 1993-09-19 |  |  | m | (************* |  |  | <EMAIL> | 456 East Valley Road Northeast |  | Rydal | GA | 30171 | Still Treating | 2019-10-30 |  | New | 2023-01-24 | 0015C00000tzPXJQA2 |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  | 0015C00000pExNdQAK | 001G000001kjk6YIAQ | No Law Firm | Blake Barrom | Resurgens Orthopaedics - HCP',
            'review_status': 1,
        }
        models.update_manual_review_data.objects.create(**review_data)
        query_params = {'canonical_object': 'Cases'}
        url = django.urls.reverse('integration:review_opportunity_update')

        response = authorized_client.get(f"{url}?{urlencode(query_params)}")

        assert response.status_code == 200

    @pytest.mark.django_db
    def test_should_paginate_unreviewed_manual_review_data(
        self, authorized_client: Client
    ):
        models.update_manual_review_data.objects.bulk_create(
            [
                models.update_manual_review_data(
                    canonical_object='ObjectA',
                    salesforce_id='SF1',
                    redshift_id='R1',
                    keys='K1',
                    redshift_data='Data1',
                    salesforce_data='DataA',
                    review_status=1,
                ),
                models.update_manual_review_data(
                    canonical_object='ObjectA',
                    salesforce_id='SF2',
                    redshift_id='R2',
                    keys='K2',
                    redshift_data='Data2',
                    salesforce_data='DataB',
                    review_status=1,
                ),
                models.update_manual_review_data(
                    canonical_object='ObjectB',
                    salesforce_id='SF3',
                    redshift_id='R3',
                    keys='K3',
                    redshift_data='Data3',
                    salesforce_data='DataC',
                    review_status=1,
                ),
                models.update_manual_review_data(
                    canonical_object='ObjectB',
                    salesforce_id='SF4',
                    redshift_id='R4',
                    keys='K4',
                    redshift_data='Data4',
                    salesforce_data='DataD',
                    review_status=0,
                ),
                models.update_manual_review_data(
                    canonical_object='ObjectA',
                    salesforce_id='SF5',
                    redshift_id='R5',
                    keys='K5',
                    redshift_data='Data5',
                    salesforce_data='DataE',
                    review_status=1,
                ),
            ]
        )

        query_params = {
            'canonical_object': 'ObjectA',
            'page_index': 0,
            'page_size': 2,
        }

        url = django.urls.reverse('integration:review_opportunity_update')

        response = authorized_client.get(f"{url}?{urlencode(query_params)}")
        result = response.json()

        assert response.status_code == 200
        assert len(result) == 2

    def test_should_get_object_fields(self, authorized_client: Client):
        url = django.urls.reverse('integration:get_object_fields')
        response = authorized_client.get(url)

        assert response.status_code == 200
        response_json = response.json()
        assert response_json['message'] == 'Object fields'

    def test_should_remove_notfound_salesforce_records(
        self, authorized_client: Client
    ):
        url = django.urls.reverse('integration:remove_salesforce_notfound')
        response = authorized_client.post(url)

        assert response.status_code == 200

    def test_should_update_charges_from_transactions(
        self,
        authorized_client: Client,
        database: psycopg.Connection[typing.Any],
    ):

        query_params = {"Transactions": []}
        url = django.urls.reverse(
            'integration:update_charges_from_transactions'
        )

        response = authorized_client.post(
            url, query_params, content_type="application/json"
        )

        assert response.status_code == 200
        response_json = response.json()
        assert response_json == '1'

    @pytest.mark.django_db
    def test_should_update_duplicates_from_salesforce(
        self,
        authorized_client: Client,
        database: psycopg.Connection[typing.Any],
    ):
        """
        Test case for the `duplicates_from_salesforce` API endpoint.
        """
        gain_id = "0g3756f1b7214de7"
        salesforce_id = "001Ec00000nYh1cIAC"
        payload = {
            "Plaintiffs": [
                {"gainid": gain_id, "external_id_container": salesforce_id}
            ]
        }

        url = django.urls.reverse("integration:duplicates_from_salesforce")

        response = authorized_client.patch(
            url, payload, content_type="application/json"
        )

        # Assertions
        assert response.status_code == 200
        response_json = response.json()
        assert response_json == {
            "message": "Successfully updated records.",
            "code": 200,
        }

    @pytest.mark.django_db
    def test_should_return_404_when_data_missing(
        self, authorized_client: Client
    ):
        """
        Test case for when no data is provided in the request.
        """

        url = django.urls.reverse("integration:duplicates_from_salesforce")

        response = authorized_client.patch(
            url, {}, content_type="application/json"
        )

        assert response.status_code == 404
        response_json = response.json()
        assert response_json == {
            "message": "Records Not Found.",
            "code": 404,
        }
