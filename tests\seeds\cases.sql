

INSERT INTO cases (
    plaintiffname,
    plaintiffdateofbirth,
    status,
    accidentdate,
    injuredbodyparts,
    accidentdescription,
    type,
    accidentstate,
    plaintiffid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    attorneyid,
    paralegalid,
    casemanagerid,
    cocounselid,
    coparalegalid,
    cocasemanagerid,
    grandtotalamount,
    grandtotalnongainadjustment,
    grandtotalnongainamountpaidtoprovider,
    grandtotalbalance,
    tailclaimcase,
    datetreatmentcompleted,
    treatmentcompleted,
    createdatetime,
    insurancevendorassigned,
    grandtotalsettlementamount,
    notes,
    datesettled,
    gainid,
    paidto,
    paidby,
    servicingstartdatetime,
    servicingenddatetime,
    grandtotalgainprenegotiationadjustment,
    grandtotalgainprenegotiationamountpaidtoprovider
) VALUES (
    'Akina Harris',
    '2003-05-21',
    'Settled - Not Yet Disbursed',
    '2023-02-15',
    '',
    '',
    '',
    '',
    '0bf956f1b7214de7',
    true,
    NULL,  -- sourcecreatedatetime
    NULL,  -- sourcemodifieddatetime
    '2024-09-17 16:27:38.313946',
    false,
    '',
    false,
    '',
    'b82db42702c2497d',
    'b82db42702c2497d',
    'b82db42702c2497d',
    NULL,
    NULL,
    NULL,
    NULL,  -- grandtotalamount
    NULL,  -- grandtotalnongainadjustment
    NULL,  -- grandtotalnongainamountpaidtoprovider
    NULL,  -- grandtotalbalance
    NULL,  -- tailclaimcase
    NULL,  -- datetreatmentcompleted
    NULL,  -- treatmentcompleted
    NULL,  -- createdatetime
    NULL,  -- insurancevendorassigned
    14400,
    '',
    '2023-07-10 00:00:00',
    '04b1ace113fe41b1',
    '',
    NULL,  -- paidby
    NULL,  -- servicingstartdatetime
    NULL,  -- servicingenddatetime
    NULL,  -- grandtotalgainprenegotiationadjustment
    NULL   -- grandtotalgainprenegotiationamountpaidtoprovider
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
) VALUES (
    '04b1ace113fe41b1',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '04b1ace113fe41b1',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'cases'
);

INSERT INTO cases (
    plaintiffname,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    'Case to be deleted',
    true,
    true,
    '04b1ace113fe41b2'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '04b1ace113fe41b2',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'cases'
);

INSERT INTO cases (
    plaintiffname,
    plaintiffdateofbirth,
    status,
    accidentdate,
    injuredbodyparts,
    accidentdescription,
    type,
    accidentstate,
    plaintiffid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    attorneyid,
    paralegalid,
    casemanagerid,
    cocounselid,
    coparalegalid,
    cocasemanagerid,
    grandtotalamount,
    grandtotalnongainadjustment,
    grandtotalnongainamountpaidtoprovider,
    grandtotalbalance,
    tailclaimcase,
    datetreatmentcompleted,
    treatmentcompleted,
    createdatetime,
    insurancevendorassigned,
    grandtotalsettlementamount,
    notes,
    datesettled,
    gainid,
    paidto,
    paidby,
    servicingstartdatetime,
    servicingenddatetime,
    grandtotalgainprenegotiationadjustment,
    grandtotalgainprenegotiationamountpaidtoprovider
) VALUES (
    'Cases manual review',
    '2003-05-21',
    'Settled - Not Yet Disbursed',
    '2023-02-15',
    '',
    '',
    '',
    '',
    '0bf956f1b7214de7',
    true,
    NULL,  -- sourcecreatedatetime
    NULL,  -- sourcemodifieddatetime
    '2024-09-17 16:27:38.313946',
    false,
    '',
    false,
    '',
    'b82db42702c2497d',
    'b82db42702c2497d',
    'b82db42702c2497d',
    NULL,
    NULL,
    NULL,
    NULL,  -- grandtotalamount
    NULL,  -- grandtotalnongainadjustment
    NULL,  -- grandtotalnongainamountpaidtoprovider
    NULL,  -- grandtotalbalance
    true,  -- tailclaimcase
    NULL,  -- datetreatmentcompleted
    NULL,  -- treatmentcompleted
    NULL,  -- createdatetime
    NULL,  -- insurancevendorassigned
    14400,
    '',
    '2023-07-10 00:00:00',
    '04b1ace113fe41b3',
    '',
    NULL,  -- paidby
    NULL,  -- servicingstartdatetime
    NULL,  -- servicingenddatetime
    NULL,  -- grandtotalgainprenegotiationadjustment
    NULL   -- grandtotalgainprenegotiationamountpaidtoprovider
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    filevine_id,
    filevine_createddatetime,
    filevine_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '04b1ace113fe41b3',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '04b1ace113fe41b3',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_cases_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'cases'
);