import typing
from unittest.mock import MagicMock

import psycopg
import pytest

from integration.integration_code import salesforce_local_operations
from tests import test_helper


class TestDuplicatesFromSalesforce:

    @pytest.mark.django_db
    def test_update_duplicates_from_salesforce(
        self,
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
    ):
        """Test that duplicates from Salesforce are updated correctly."""
        gain_id = "0g3756f1b7214de7"
        salesforce_id = "001Ec00000nYh1cIAC"

        payload = {
            "Plaintiffs": [
                {"gainid": gain_id, "external_id_container": salesforce_id}
            ]
        }

        salesforce_local_operations.duplicates_from_salesforce(payload)

        updated_record = test_helper.get_object(
            database, 'gain_id_map', [gain_id]
        )

        assert test_helper.verify_if_log_exists(
            verify_errors, 'Updated ids for batch 1 of 1.'
        )
        assert (
            updated_record['salesforce_external_container_id'] == salesforce_id
        )

    def test_raises_error_for_invalid_object_in_duplicates_sf(
        self, verify_errors: MagicMock
    ):
        """Test that an error is raised when an invalid object is provided."""
        invalid_payload = {
            "Invalid_Canonical": [
                {"gainid": "invalid", "external_id_container": "invalid"}
            ]
        }

        with pytest.raises(ValueError):
            salesforce_local_operations.duplicates_from_salesforce(
                invalid_payload
            )

        assert test_helper.verify_if_log_exists(
            verify_errors,
            f'Failed to update following data: {str(invalid_payload)}',
        )
