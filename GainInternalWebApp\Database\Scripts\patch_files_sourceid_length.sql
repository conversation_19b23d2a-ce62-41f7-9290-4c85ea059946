ALTER TABLE dev.files_insertdata_map DROP CONSTRAINT files_insertdata_map_sourceid_fkey;
ALTER TABLE dev.files DROP CONSTRAINT files_sourceid_key;
ALTER TABLE dev.files ALTER COLUMN sourceid TYPE varchar(100) ;
ALTER TABLE dev.files ADD CONSTRAINT files_sourceid_key UNIQUE (sourceid);

ALTER TABLE dev.files_insertdata_map DROP CONSTRAINT files_insertdata_map_sourceid_key;
ALTER TABLE dev.files_insertdata_map ALTER COLUMN sourceid TYPE varchar(100) ;
ALTER TABLE dev.files_insertdata_map ADD CONSTRAINT files_insertdata_map_sourceid_key UNIQUE (sourceid);
ALTER TABLE dev.files_insertdata_map ADD CONSTRAINT files_insertdata_map_sourceid_fkey FOREIGN KEY (sourceid) REFERENCES dev.files(sourceid);