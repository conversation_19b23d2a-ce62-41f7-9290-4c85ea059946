{% load static %}

<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
    integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
<link rel="stylesheet" type="text/css" href="{% static '/css/styles.css' %}">

<nav class="navbar navbar-expand-lg navbar-dark bg-success">
    <div class="container">
        <a class="navbar-brand" style="color:#04215d" href="https://www.gainservicing.com/" target="_blank">Gain
            Servicing</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item active">
                    <a class="nav-link" href="{% url 'apphome' %}">Home</a>
                </li>
            </ul>
        </div>
    </div>

</nav>

<div class="row pt-2">
    <div class="col-md-6">
        <div class="card mb-2">
            <div class="row card-body">
                <div class="col-md-8">
                    <img style="width:600px;height:400px; object-fit: fill; float: left;"
                        src="{% static 'images/salesforce.png' %}">
                </div>

                <div class="col-md-4">
                    <h2 class="card-title">The provider map data refreshing</h2>
                    <p class="card-text">This project is about for automatically refreshing the providers map data and
                        store the data in AWS Redshift for the usage of the Gain Portal provider map searching.</p>

                    <p class="card-text">For each step of the data refreshing, please make sure the data shape
                        looks correct before you do the refreshing.</p>
                    <a href="https://portal.gainservicing.com/providersmap/search/0018Y00002fc2G8QAI/-/-/Physical%20Therapy"
                        target="_blank" class="btn btn-success d-flex justify-content-center">
                        Gain Portal provider map page
                    </a>
                </div>
            </div>

            <div class="card-body">
                <h2 class="card-title">
                    <p>The data is now refreshinging<span></span></p>
                </h2>
                <h5 class="card-title">Last refreshing time: {{OverallShape}}</h5>
                <h5 class="card-title">The data format is: {{OverallShape}}</h5>
                <p class="card-text">Last refreshing time: {{RefreshingTime}}</p>
                <button type="button" class="btn btn-primary">Show the differences between previous refresh and current
                    refresh</button>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="row-10">
            <div class="card mb-2">
                <div class="card-body">
                    <h5 class="card-title">Step 1:<p id="Step1Refresh" class= "pl-2" style="display:none;color:blue; font-weight:bold">The data is now
                            refreshinging<span></span></p>
                    </h5>
                    <p class="card-title">This step is for combining Definitive Healthcare providers,
                        MDSave providers and Gain Servicing Salesforce providers into one CSV file and store it in AWS
                        S3 bucket.</p>
                    <p class="card-text">Last data refreshing time: {{RefreshingTime}}</p>
                    <p class="card-text">The data shape after the last refreshing time is: {{RefreshingTime}}</p>
                    <p class="card-text">The latest data refreshing time: {{RefreshingTime}}</p>
                    <p class="card-text">The data shape after the latest refreshing: {{RefreshingTime}}</p>
                    <p class="card-text">The latest data {{RefreshingTime}} 10 rows and 0 colums {{RefreshingTime}}</p>
                    <a type="button" class="btn btn-primary justify-content-center" href="{% url 'providermapdata:data_refreshing' %}" onclick="showStep1RefreshMessage()">Combine all
                        providers data to
                        S3</a>
                </div>
            </div>
        </div>
        <div class="row-10">
            <div class="card mb-2">
                <div class="card-body">
                    <h5 class="card-title">Step 2:</h5>
                    <p class="card-title">This step is for combining Definitive Healthcare providers,
                        MDSave providers and Gain Servicing Salesforce providers into one CSV file and store it in AWS
                        S3 bucket.</p>
                    <p class="card-text">Last refreshing time: {{RefreshingTime}}</p>
                    <button type="button" class="btn btn-primary justify-content-center">Copy data from S3 to the backup
                        tables
                    </button>
                </div>
            </div>
        </div>
        <div class="row-10">
            <div class="card mb-2">
                <div class="card-body">
                    <h5 class="card-title">Step 3:</h5>
                    <p class="card-title">This step is for combining Definitive Healthcare providers,
                        MDSave providers and Gain Servicing Salesforce providers into one CSV file and store it in AWS
                        S3 bucket.</p>
                    <p class="card-text">Last refreshing time: {{RefreshingTime}}</p>
                    <button type="button" class="btn btn-primary justify-content-center">Copy data from backup tables to
                        actual provider table</button>
                </div>
            </div>
        </div>

        <div class="row-10" id="welcomeDiv" style="display:none;">
            <div class="card mb-2">
                <h5 class="card-title">The data is starting refreshing...</h5>
                <h5 class="card-title">Last refreshing time: {{DataShape}}</h5>
                <h5 class="card-title">Last refreshing time: {{OverallShape}}</h5>
                <h5 class="card-title">The data format is: {{OverallShape}}</h5>
                <p class="card-text">Last refreshing time: {{RefreshingTime}}</p>
                <button type="button" class="btn btn-primary">Click Me!</button>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<div class="container">
    {% block page_content %}{% endblock %}
</div>

<script src="{% static '/js/app.js' %}"></script>
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
    integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous">
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"
    integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous">
</script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"
    integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous">
</script>