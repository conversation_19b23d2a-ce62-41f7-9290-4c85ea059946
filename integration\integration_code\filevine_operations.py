import datetime
import typing

import cachetools
import requests

import integration.exceptions as exceptions

from . import (
    aws_operations,
    filevine_local_operations,
    local_operations,
    logger_config,
    models,
    shared,
)

api_url = None
session = None
access_token = None
refresh_token = None
api_response_cache = cachetools.TTLCache(maxsize=1000, ttl=300)  # 5 minutes
PERSONAL_INJURY_PROJECT_TYPE = 'PII'


def get_environment(
    filevine_name: str,
    filevine_environments: dict[str, typing.Any],
) -> str:
    func_name = shared.get_func_name()
    filevine_environment = (
        filevine_name if filevine_name in filevine_environments else None
    )
    if not filevine_environment:
        raise exceptions.IntegrationInternalBadRequestException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=f'Filevine environment {filevine_name} not found in settings',
            )
        )
    return filevine_environment


def initialize(api: str) -> None:
    global api_url
    api_url = api


def authenticate_request_creation(
    timestamp: datetime.datetime,
    credentials_key: str,
    credentials_secret: str,
    mode: str = 'key',
) -> tuple[str, dict[str, str], dict[str, str]]:
    timestamp_string = local_operations.get_utc_timestamp_string(timestamp)
    url = f'{api_url}/session'
    headers = {'Content-Type': 'application/json'}
    body = {
        'mode': mode,
        'apiKey': credentials_key,
        'apiHash': local_operations.get_md5_hash(
            f'{credentials_key}/{timestamp_string}/{credentials_secret}'
        ),
        'apiTimestamp': timestamp_string,
    }
    return (url, headers, body)


def post_authentication_token_extraction(
    response: requests.Response, resp_code: int
) -> None:
    global access_token, refresh_token
    func_name = shared.get_func_name()
    session_details = response.json()
    access_token = session_details.get('accessToken', None)
    refresh_token = session_details.get('refreshToken', None)
    missing_tokens = []
    if not access_token:
        missing_tokens.append('access token')
    if not refresh_token:
        missing_tokens.append('refresh token')
    if missing_tokens:
        raise exceptions.IntegrationExternalAuthenticationException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, message: {response.text}',
                is_error=True,
                exception_message=f'Filevine authentication failed: No {", ".join(missing_tokens)}',
            )
        )


def authenticate(
    timestamp: datetime.datetime,
    credentials_key: str,
    credentials_secret: str,
    mode: str = 'key',
) -> None:
    func_name = shared.get_func_name()
    url, headers, body = authenticate_request_creation(
        timestamp, credentials_key, credentials_secret, mode
    )
    response = requests.post(url, json=body, headers=headers)
    resp_code = response.status_code
    if resp_code != 200:
        raise exceptions.IntegrationExternalAuthenticationException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, message: {response.text}',
                is_error=True,
                exception_message='Filevine authentication failed',
            )
        )
    post_authentication_token_extraction(response, resp_code)


def setup_session() -> None:
    global session, access_token, refresh_token
    func_name = shared.get_func_name()
    # Create a base session
    session = local_operations.setup_session(
        auth_token=access_token, default_timeout=10, backoff_factor=4
    )  # Filevine project document GET calls require a timeout of 10 seconds
    # Add custom header to the session
    # Custom header, hence not in the local_operations.setup_session function
    # This header is required for all requests to the Filevine API
    # Value is the refresh token
    if not refresh_token:
        raise exceptions.IntegrationInternalException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message='No refresh token found',
            )
        )
    session.headers['x-fv-sessionid'] = refresh_token


@local_operations.rate_limited(
    max_calls_per_minute=300,  # 5 calls per second
)  # Filevine API rate limit is 320 calls per minute
def _get_call(
    func_name: str,
    url: str,
    params: typing.Optional[dict[str, int]] = None,
) -> requests.Response:
    '''
    Generic GET call to Filevine API. Used by all GET calls to Filevine API.
    Use only within this module.
    '''
    global session, api_response_cache
    if not session:
        raise exceptions.IntegrationInternalException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message='Session not initialized',
            )
        )
    # Check cache first
    cached_response = local_operations.check_call_cache(
        api_response_cache, url, params if params else {}
    )
    if cached_response:
        return cached_response
    if not params:
        params = {'limit': 100, 'offset': 0}
    # Make the actual API call
    try:
        resp = session.get(url, params=params)
    except requests.exceptions.HTTPError as http_error:
        resp = http_error.response
        if resp is None:
            # If response is None, it means the request was not successful
            raise exceptions.IntegrationExternalConnectionException(
                logger_config.AuditLog(
                    route_section=func_name,
                    is_error=True,
                    exception_message='Response not received from Filevine API',
                )
            )
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp.status_code}, message: {resp.text}',
                is_error=True,
                exception_message=f'HTTP Error in Filevine GET call: {http_error}',
            )
        )
    except requests.exceptions.RequestException as request_exception:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=f'Request Exception in Filevine GET call: {request_exception}',
            )
        )
    except Exception as e:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=f'Unknown error in Filevine GET call: {e}',
            )
        )
    # Cache the response if the call was successful
    if resp.status_code == 200:
        local_operations.cache_response(api_response_cache, url, params, resp)
    return resp


def get_project_list(
    params: typing.Optional[models.views.FileVineParams] = None,
) -> dict[str, typing.Any]:
    global session
    func_name = shared.get_func_name()
    url = f'{api_url}/core/projects'
    resp = _get_call(func_name, url, params)
    if resp.status_code != 200:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=(
                    f'Status: {resp.status_code}, message: {resp.text}'
                ),
                is_error=True,
                exception_message=(
                    'Error in fetching project list, further data will not be fetched'
                ),
            )
        )
    project_list = resp.json()
    return project_list


def get_project_ids(project_list: dict[str, typing.Any]) -> list[str]:
    func_name = shared.get_func_name()
    project_list_items = project_list.get('items', None)
    if project_list_items is None:  # Error in fetching projects
        raise exceptions.IntegrationExternalDataException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message='No projects returned in `items` key of project list',
            )
        )
    project_ids = [
        project_list_item['projectId']['native']
        for project_list_item in project_list_items
        if project_list_item['projectTypeCode'] == PERSONAL_INJURY_PROJECT_TYPE
    ]
    return project_ids


def get_project(project_id: str) -> tuple[dict[str, typing.Any], str]:
    global session
    func_name = shared.get_func_name()
    path = f'{api_url}/core/projects/{project_id}'
    resp = _get_call(func_name, path, {})  # No params for this call
    resp_code = resp.status_code
    if resp_code != 200:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, message: {resp.text}',
                is_error=True,
                exception_message=f'Error in fetching project {project_id}',
            )
        )
    project = resp.json()
    client_id = project.get('clientId', None)
    if not client_id:
        raise exceptions.IntegrationExternalDataException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=f'No client id found for project {project_id}',
            )
        )
    client_id = client_id['native']
    return (project, client_id)


def get_project_client(
    client_id: str,
    params: typing.Optional[dict[str, int]] = None,
) -> dict[str, typing.Any]:
    func_name = shared.get_func_name()
    url = f'{api_url}/core/contacts/{client_id}'
    resp = _get_call(func_name, url, params)
    resp_code = resp.status_code
    if resp_code != 200:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, message: {resp.text}',
                is_error=True,
                exception_message=f'Error in fetching client {client_id}',
            )
        )
    client_details = resp.json()
    return client_details


def get_project_url(
    project_id: str,
    detail_category: str,
    detail_type: str,
) -> str:
    func_name = shared.get_func_name()
    url = f'{api_url}/core/projects/{project_id}'
    known_detail_categories = ['details', 'collections', 'non-collections']
    if detail_category not in known_detail_categories:
        raise exceptions.IntegrationInternalException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=(
                    f'Unknown detail category {detail_category} passed for project {project_id}'
                ),
            )
        )
    known_detail_types = {
        'details': [
            'contacts',
            'documents',
            'team',
        ],
        'collections': [
            'disbursals',
            'insurance',
            'liens',
            'meds',
            'negotiations',
            'payments',
        ],
        'non-collections': [
            'intakes',
        ],
    }
    if detail_type not in known_detail_types[detail_category]:
        raise exceptions.IntegrationInternalException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=(
                    f'Unknown detail type {detail_type} passed for project {project_id}'
                ),
            )
        )
    if detail_category == 'details':
        url += f'/{detail_type}'
    elif detail_category == 'collections':
        url += f'/collections/{detail_type}'
    # TBD: Unsure what non-collections endpoint is
    # elif detail_type == 'non-collections':
    #     url += f'/non-collections/{detail_name}'
    return url


def get_project_details(
    project_id: str,
    detail_type: str,
    params: typing.Optional[dict[str, int]] = None,
) -> list[dict[str, typing.Any]]:
    func_name = shared.get_func_name()
    detail_category = 'details'
    url = get_project_url(project_id, detail_category, detail_type)
    resp = _get_call(func_name, url, params)
    resp_code = resp.status_code
    if resp_code != 200:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, message: {resp.text}',
                is_error=True,
                exception_message=(
                    f'Error in fetching {detail_type} details for project {project_id}'
                ),
            )
        )
    details = resp.json()
    details_list = details.get('items', None)
    # TODO: Decide what to do if no items key in details
    # if not details_list:
    #     raise exceptions.IntegrationExternalDataException(
    #         logger.AuditLog(
    #             route=
    #             route_section=func_name,
    #             is_error=True,
    #             exception_message=(
    #                 f'No `items` key in {detail_type} details for project {project_id}'
    #             ),
    #         )
    #     )
    return details_list


def get_project_collections(
    project_id: str,
    collection_name: str,
    params: typing.Optional[dict[str, int]] = None,
) -> list[dict[str, typing.Any]]:
    func_name = shared.get_func_name()
    detail_category = 'collections'
    url = get_project_url(project_id, detail_category, collection_name)
    resp = _get_call(func_name, url, params)
    resp_code = resp.status_code
    if resp_code != 200:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, message: {resp.text}',
                is_error=True,
                exception_message=(
                    f'Error in fetching {collection_name} collection for project {project_id}'
                ),
            )
        )
    collection = resp.json()
    collection_list = collection.get('items', None)
    # TODO: Decide what to do if no items key in collection
    # if not details_list:
    #     raise exceptions.IntegrationExternalDataException(
    #         logger.AuditLog(
    #             route=
    #             route_section=func_name,
    #             is_error=True,
    #             exception_message=(
    #                 f'No `items` key in {detail_type} collection for project {project_id}'
    #             ),
    #         )
    #     )
    return collection_list


def get_document_download_url(
    document_id: str,
) -> str:
    func_name = shared.get_func_name()
    url = f'{api_url}/core/documents/{document_id}/locator'
    resp = _get_call(func_name, url, {})  # No params for this call
    resp_code = resp.status_code
    if resp_code != 200:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, message: {resp.text}',
                is_error=True,
                exception_message=(
                    f'Error in fetching document URL for document {document_id}'
                ),
            )
        )
    details = resp.json()
    doc_url = details.get('url', None)
    if not doc_url:
        raise exceptions.IntegrationExternalDataException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=(f'No URL found for document {document_id}'),
            )
        )
    return doc_url


def download_file(url: str, filename: str) -> None:
    """
    Download a file from a given URL and save it to a file.

    :param url: The URL of the file to download.
    :param filename: The name of the file where the file will be saved.
    """
    func_name = shared.get_func_name()
    resp = requests.get(url)
    resp_code = resp.status_code
    if resp_code != 200:
        raise exceptions.IntegrationExternalConnectionException(
            logger_config.AuditLog(
                route_section=func_name,
                additional_details=f'Status: {resp_code}, reason: {resp.reason}',
                is_error=True,
                exception_message=(
                    f'Error in downloading file from URL {url} to file {filename}'
                ),
            )
        )
    try:
        with open(filename, 'wb') as file:
            file.write(resp.content)
    except Exception as e:
        local_operations.clean_up_file(filename)
        raise exceptions.IntegrationInternalException(
            logger_config.AuditLog(
                route_section=func_name,
                is_error=True,
                exception_message=f'Error in downloading file from URL\t{e}',
            )
        )


def get_all(
    test: bool,
    skip_files: bool,
    params: typing.Optional[models.views.FileVineParams] = None,
) -> list[dict[str, dict[str, typing.Any] | list[dict[str, typing.Any]]]]:
    # Only use params for project list call
    # For other calls, params will be handled within _get_call function
    project_list = get_project_list(params)
    project_ids = get_project_ids(project_list)
    if test:
        project_ids = [11196932]
    projects_raw_data = []
    for project_id in project_ids:
        project, client_id = get_project(str(project_id))
        # Client Id embedded in project level data
        # Client details not returned in project details contact call
        # Hence, need to get client details separately
        # But it is of type contact, hence call same function
        client_details = get_project_client(
            client_id
        )  # `client` is a reserved keyword
        contacts = get_project_details(str(project_id), detail_type='contacts')
        team = get_project_details(str(project_id), detail_type='team')
        # TBD: Unsure how to retrieve non-collection sections
        # intakes = get_project_non_collections(
        #      str(project_id), non_collection_type='intakes'
        # )
        insurances = get_project_collections(
            str(project_id), collection_name='insurance'
        )  # collection type `insurance` is NOT plural in API
        disbursals = get_project_collections(
            str(project_id), collection_name='disbursals'
        )
        liens = get_project_collections(
            str(project_id), collection_name='liens'
        )
        meds = get_project_collections(str(project_id), collection_name='meds')
        payments = get_project_collections(
            str(project_id), collection_name='payments'
        )
        if skip_files:
            documents = []
        else:
            documents = get_project_details(
                str(project_id), detail_type='documents'
            )
            for document in documents:
                document_id = document['documentId']['native']
                document_url = get_document_download_url(document_id)
                document['documentURL'] = document_url
        project_raw_data = {
            'client_details': client_details,
            'contacts': contacts,
            'team': team,
            'project': project,
            # 'intakes': intakes,  # TBD: Intakes, look at non-collections comments
            'insurances': insurances,
            'liens': liens,
            'disbursals': disbursals,
            'meds': meds,
            'payments': payments,
            'documents': documents,
        }
        projects_raw_data.append(project_raw_data)
    return projects_raw_data


def process_all(
    bucket: str,
    directory: str,
    timestamp: datetime.datetime,
    filevine_name: str,
    projects_raw_data: list[
        dict[str, dict[str, typing.Any] | list[dict[str, typing.Any]]]
    ],
) -> tuple[dict[str, str], set[str]]:
    files = {}
    insurances_gainid_set = set()
    s3_data = None  # Default value
    if filevine_name == 'hostilo':
        (
            s3_data,
            insurances_gainid_set,
        ) = filevine_local_operations.hostilo_filevine_upsert_generate_s3_data(
            bucket, directory, timestamp, projects_raw_data
        )
        files = filevine_local_operations.hostilo_filevine_upsert_generate_csv(
            timestamp, s3_data
        )
    for file in files.values():
        aws_operations.upload_s3(bucket, directory, file)
    for mapping, file in files.items():
        aws_operations.s3_to_postgres(bucket, directory, file, mapping)
    return (files, insurances_gainid_set)
