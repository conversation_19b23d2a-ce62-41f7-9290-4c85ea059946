CREATE TABLE lawfirms (
    name character varying(250),
    phone character varying(100),
    fax character varying(100),
    email character varying(100),
    followupemail character varying(100),
    billingaddressline1 character varying(250),
    billingaddressline2 character varying(250),
    billingaddresscity character varying(100),
    billingaddressstate character varying(100),
    billingaddresszip character varying(20),
    physicaladdressline1 character varying(250),
    physicaladdressline2 character varying(250),
    physicaladdresscity character varying(100),
    physicaladdressstate character varying(100),
    physicaladdresszip character varying(20),
    website character varying(1000),
    typeoflaw character varying(1000),
    description character varying(1000),
    employeecountrange character varying(1000),
    donotfund boolean,
    donotfundtype character varying(100),
    automaticcaseupdaterequest boolean,
    nonresponsive boolean,
    nonresponsivenote character varying(1000),
    portalaccount boolean,
    portalrewardsparticipant boolean,
    parentid character varying(100),
    relevanttogain boolean,
    sourcecreatedatetime timestamp without time zone,
    sourcemodifieddatetime timestamp without time zone,
    createdatetime timestamp without time zone DEFAULT ('now':: character varying):: timestamp without time zone,
    modifieddatetime timestamp without time zone,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    notes character varying(1000),
    gainid character varying(16) NOT NULL DEFAULT '':: character varying,
    PRIMARY KEY (gainid)
);