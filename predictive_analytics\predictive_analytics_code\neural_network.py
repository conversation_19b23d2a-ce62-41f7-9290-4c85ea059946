# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning
import typing

import numpy as np
import torch
import torch.backends.mps


class Net(torch.nn.Module):
    def __init__(
        self,
        config: dict[str, typing.Any],
        n_inputs: int,
        n_outputs: int,
    ) -> None:
        super(Net, self).__init__()
        self.set_device(config.get('cpu_only', False))
        self.learning_rate = config.get('learning_rate', 0.001)
        self.activation = (
            torch.nn.functional.relu
            if config.get('activation_func', 'relu') == 'relu'
            else torch.nn.functional.tanh
        )
        self.dropout = torch.nn.Dropout(config.get('drop_out_frac', 0.2))
        self.l1_lambda = config.get('l1_lambda', 0.0)
        self.l2_lambda = config.get('l2_lambda', 0.0)
        self.build_model_fc_layers(
            n_inputs,
            config['network_structure'],
            n_outputs,
        )
        self.to(self.device)

    def set_device(
        self,
        cpu_only: bool,
    ) -> None:
        self.device = torch.device('cpu')
        if cpu_only:
            return
        if torch.cuda.is_available():
            self.device = torch.device('cuda')
        elif torch.backends.mps.is_available():
            self.device = torch.device('mps')

    def build_model_fc_layers(
        self,
        n_inputs: int,
        network_structure: list[int],
        n_outputs: int,
    ) -> None:
        self.fc_layers = torch.nn.ModuleList()
        self.fc_layers.append(torch.nn.Linear(n_inputs, network_structure[0]))
        self.fc_layers.append(torch.nn.BatchNorm1d(network_structure[0]))
        for layer_no in range(len(network_structure) - 1):
            self.fc_layers.append(
                torch.nn.Linear(
                    network_structure[layer_no],
                    network_structure[layer_no + 1],
                )
            )
            self.fc_layers.append(
                torch.nn.BatchNorm1d(network_structure[layer_no + 1]),
            )
        self.fc_layers.append(
            torch.nn.Linear(network_structure[-1], n_outputs)
        )
        for layer_no in range(0, len(self.fc_layers) - 2, 2):
            torch.nn.init.kaiming_normal_(
                self.fc_layers[layer_no].weight,  # type: ignore
                mode='fan_in',
                nonlinearity=self.activation.__name__,
            )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        for i in range(0, len(self.fc_layers) - 2, 2):
            x = self.fc_layers[i](x)
            x = self.fc_layers[i + 1](x)
            x = self.activation(x)
            x = self.dropout(x)
        x = self.fc_layers[-1](x)
        return x

    def optimizer_init(
        self,
        config: dict[str, typing.Any],
    ) -> (
        torch.optim.Adam
        | torch.optim.SGD
        | torch.optim.Adagrad
        | torch.optim.Adadelta
        | torch.optim.RMSprop
        | torch.optim.AdamW
    ):
        if not any(param.requires_grad for param in self.parameters()):
            raise ValueError("No trainable parameters found in the model.")
        optimizer_type = config['optimizer_type']
        if optimizer_type == 'sgd':
            return torch.optim.SGD(
                self.parameters(),
                lr=config['learning_rate'],
                weight_decay=self.l2_lambda,
                momentum=config['momentum'],
                nesterov=config['nesterov'],
            )
        elif optimizer_type == 'adamw':
            return torch.optim.AdamW(
                self.parameters(),
                lr=config['learning_rate'],
                weight_decay=self.l2_lambda,
            )
        elif optimizer_type == 'adagrad':
            return torch.optim.Adagrad(
                self.parameters(),
                lr=config['learning_rate'],
                weight_decay=self.l2_lambda,
            )
        elif optimizer_type == 'adadelta':
            return torch.optim.Adadelta(
                self.parameters(),
                lr=config['learning_rate'],
                weight_decay=self.l2_lambda,
            )
        elif optimizer_type == 'rmsprop':
            return torch.optim.RMSprop(
                self.parameters(),
                lr=config['learning_rate'],
                weight_decay=self.l2_lambda,
                momentum=config['momentum'],
            )
        return torch.optim.Adam(
            self.parameters(),
            lr=config['learning_rate'],
            weight_decay=self.l2_lambda,
        )

    def _train_model(
        self,
        config: dict[str, typing.Any],
        X: torch.Tensor,
        T: torch.Tensor,
    ) -> typing.Generator[float, None, None]:
        """
        Private method containing the core training logic.

        Args:
            config: Configuration dictionary
            X: Input features tensor
            T: Target values tensor

        Yields:
            float: Average loss for each epoch
        """
        self.train()
        X, T = X.to(self.device), T.to(self.device)
        optimizer = self.optimizer_init(config)
        optimizer_state = config.get("optimizer_state_dict", None)
        if optimizer_state is not None:
            optimizer.load_state_dict(optimizer_state)
        loss_fn = torch.nn.functional.mse_loss
        num_samples = len(X)
        num_folds = config["num_folds"]
        fold_size = num_samples // num_folds

        for epoch in range(config["epochs"]):
            permutation = torch.randperm(num_samples)

            total_loss = 0.0
            for fold in range(num_folds):
                fold_start = fold * fold_size
                fold_end = (
                    (fold + 1) * fold_size
                    if fold < num_folds - 1
                    else num_samples
                )

                # validation_indices = permutation[fold_start:fold_end]
                train_indices = torch.cat(
                    (permutation[:fold_start], permutation[fold_end:])
                )

                X_train, T_train = X[train_indices], T[train_indices]
                # X_val, T_val = X[validation_indices], T[validation_indices]

                optimizer.zero_grad()

                Y_train = self.forward(X_train)
                train_loss = torch.sqrt(loss_fn(Y_train, T_train))

                # L1 regularization
                l1_regularization = 0.0
                for param in self.parameters():
                    l1_regularization += self.l1_lambda * param.abs().sum()
                # Reference: https://stackoverflow.com/questions/58172188/
                # how-to-add-l1-regularization-to-pytorch-nn-model
                losses = train_loss + l1_regularization

                losses.backward()
                optimizer.step()

                total_loss += train_loss.item()

            avg_loss = total_loss / num_folds
            print(f"Epoch {epoch} Loss: {avg_loss}")
            yield avg_loss

    def train_model(
        self,
        config: dict[str, typing.Any],
        X: torch.Tensor,
        T: torch.Tensor,
    ) -> typing.Tuple[
        dict[typing.Any, typing.Any],
        dict[typing.Any, typing.Any],
        dict[typing.Any, typing.Any],
        float | None,
    ]:
        """
        Train the model using cross-validation.

        Args:
            config: Configuration dictionary
            X: Input features tensor
            T: Target values tensor

        Returns:
            typing.Tuple containing
            (config, model_state_dict, optimizer_state_dict, avg_loss)
        """
        # Initialize optimizer to get its state
        optimizer = self.optimizer_init(config)
        optimizer_state = config.get("optimizer_state_dict", None)
        if optimizer_state is not None:
            optimizer.load_state_dict(optimizer_state)

        avg_loss = None
        for avg_loss in self._train_model(config, X, T):
            pass  # Consume all epochs

        return (
            config,
            self.state_dict(),
            optimizer.state_dict(),
            avg_loss,
        )

    def eval_model(
        self,
        X: torch.Tensor,
        T: torch.Tensor,
    ) -> tuple[np.ndarray[typing.Any, typing.Any], float]:
        self.eval()
        loss_fn = torch.nn.functional.mse_loss
        with torch.no_grad():
            X, T = X.to(self.device), T.to(self.device)
            Y = self.forward(X)
            loss = torch.sqrt(loss_fn(Y, T))
            print(f"Test loss: {loss.item():.4f}")
        return Y.cpu().numpy(), loss.item()

    def inference_model(
        self,
        X: torch.Tensor,
    ) -> np.ndarray[typing.Any, typing.Any]:
        self.eval()
        with torch.no_grad():
            X = X.to(self.device)
            Y = self.forward(X)
        return Y.cpu().numpy()
