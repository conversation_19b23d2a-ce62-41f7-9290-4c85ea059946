ALTER TABLE integration.dev.insurances
ADD COLUMN policyid_new
VARCHAR(100)
;
UPDATE integration.dev.insurances
SET policyid_new = policyid
;
ALTER TABLE integration.dev.insurances
DROP COLUMN policyid
;
ALTER TABLE integration.dev.insurances
RENAME COLUMN policyid_new TO policyid
;


ALTER TABLE integration.dev.liens
ADD COLUMN lienfilenumber_new
VARCHAR(100)
;
UPDATE integration.dev.liens
SET lienfilenumber_new = lienfilenumber
;
ALTER TABLE integration.dev.liens
DROP COLUMN lienfilenumber
;
ALTER TABLE integration.dev.liens
RENAME COLUMN lienfilenumber_new TO lienfilenumber
;


ALTER TABLE integration.staging.insurances
ADD COLUMN policyid_new
VARCHAR(100)
;
UPDATE integration.staging.insurances
SET policyid_new = policyid
;
ALTER TABLE integration.staging.insurances
DROP COLUMN policyid
;
ALTER TABLE integration.staging.insurances
RENAME COLUMN policyid_new TO policyid
;


ALTER TABLE integration.staging.liens
ADD COLUMN lienfilenumber_new
VARCHAR(100)
;
UPDATE integration.staging.liens
SET lienfilenumber_new = lienfilenumber
;
ALTER TABLE integration.staging.liens
DROP COLUMN lienfilenumber
;
ALTER TABLE integration.staging.liens
RENAME COLUMN lienfilenumber_new TO lienfilenumber
;

