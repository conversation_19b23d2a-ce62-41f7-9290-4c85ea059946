import os

from cryptography.fernet import Fernet


class secure_password_manager:
    @staticmethod
    def encrypt(plaintext: str):
        cipher = <PERSON><PERSON><PERSON>(secure_password_manager.get_key_from_environment())
        plaintext_bytes = plaintext.encode('utf-8')
        return cipher.encrypt(plaintext_bytes)

    @staticmethod
    def decrypt(ciphertext: bytes):
        cipher = Fernet(secure_password_manager.get_key_from_environment())
        return cipher.decrypt(ciphertext).decode('utf-8')

    @staticmethod
    def get_key_from_environment():
        """
        Loads the encryption key from the environment variable 'ENCRYPTION_KEY'.

        Returns:
            bytes: The encryption key.

        Raises:
            ValueError: If the 'ENCRYPTION_KEY' environment variable is not set.
        """
        key = os.environ.get('ENCRYPTION_KEY')
        if not key:
            raise ValueError("Missing 'ENCRYPTION_KEY' environment variable.")
        return key.encode('utf-8')  # Convert to bytes
