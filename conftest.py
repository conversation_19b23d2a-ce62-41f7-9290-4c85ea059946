import pytest
from django.contrib.auth.models import User
from django.test import Client

from tests.context_fixtures import *
from tests.database_fixtures import *
from tests.s3_fixtures import *
from tests.salesforce_fixtures import *


@pytest.fixture
def authorized_client(django_user_model: User, client: Client):
    username = "user"
    password = "password"
    user = django_user_model.objects.create_user(
        username=username, password=password
    )
    client.force_login(user)
    return client
