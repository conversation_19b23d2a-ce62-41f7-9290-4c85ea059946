

INSERT INTO plaintiffs (
    name,
    dateofbirth,
    ssn,
    taxid,
    driverlicense,
    gender,
    maritalstatus,
    company,
    homephone,
    cellphone,
    businessphone,
    otherphone,
    primaryemail,
    secondaryemail,
    primaryaddressline1,
    primaryaddressline2,
    primaryaddresscity,
    primaryaddressstate,
    primaryaddresszip,
    otheraddressline1,
    otheraddressline2,
    otheraddresscity,
    otheraddressstate,
    otheraddresszip,
    departmentid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    createdatetime,
    gainid
) VALUES (
    'Crystal L Meyer',
    '1968-07-22',
    '',
    '',
    '',
    'F',
    '',
    '',
    '**********',
    '**********',
    '',
    '',
    '',
    '',
    '3800 White Lake Rd',
    '',
    'White Lake',
    'Michigan',
    '48383',
    '',
    '',
    '',
    '',
    '',
    '',
    true,
    NULL,
    NULL,
    '2024-08-26 17:07:38',
    false,
    '',
    false,
    '',
    NULL,
    '0bf956f1b7214de7'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    canonical_object
) VALUES (
    '0bf956f1b7214de7',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'plaintiffs'
);

INSERT INTO plaintiffs (
    name,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    'Plaintiff to be deleted',
    true,
    true,
    '0bf956f1b7214de8'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '0bf956f1b7214de8',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'plaintiffs'
);

INSERT INTO plaintiffs (
    name,
    dateofbirth,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    'Plaintiff manual review',
    '1968-07-22',
    false,
    true,
    '0bf956f1b7214de9'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '0bf956f1b7214de9',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_plaintiff_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'plaintiffs'
);



INSERT INTO plaintiffs (
    name,
    dateofbirth,
    ssn,
    taxid,
    driverlicense,
    gender,
    maritalstatus,
    company,
    homephone,
    cellphone,
    businessphone,
    otherphone,
    primaryemail,
    secondaryemail,
    primaryaddressline1,
    primaryaddressline2,
    primaryaddresscity,
    primaryaddressstate,
    primaryaddresszip,
    otheraddressline1,
    otheraddressline2,
    otheraddresscity,
    otheraddressstate,
    otheraddresszip,
    departmentid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    createdatetime,
    gainid -- gainid
) VALUES (
    'John Doe', -- name
    '1985-06-15', -- dateofbirth
    '*********', -- ssn
    'A1B2C3D4', -- taxid
    '********', -- driverlicense
    'M', -- gender
    'Single', -- maritalstatus
    'Doe Inc.', -- company
    '555-1234', -- homephone
    '555-5678', -- cellphone
    '555-9012', -- businessphone
    '555-3456', -- otherphone
    '<EMAIL>', -- primaryemail
    '<EMAIL>', -- secondaryemail
    '123 Main St', -- primaryaddressline1
    'Apt 4B', -- primaryaddressline2
    'New York', -- primaryaddresscity
    'NY', -- primaryaddressstate
    '10001', -- primaryaddresszip
    '456 Elm St', -- otheraddressline1
    'Suite 12', -- otheraddressline2
    'Los Angeles', -- otheraddresscity
    'CA', -- otheraddressstate
    '90001', -- otheraddresszip
    'DPT10', -- departmentid
    'Yes', -- relevanttogain
    NULL, -- sourcecreatedatetime
    NULL, -- sourcemodifieddatetime
    '2025-03-18 10:00:00', -- modifieddatetime
    'No', -- todelete
    'No', -- todeletesystem
    'Yes', -- deletepreventoverride
    'Legal Hold', -- deletepreventoverridereason
    NULL, -- createdatetime
    '0g3756f1b7214de7' -- gainid
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    canonical_object
) VALUES (
    '0g3756f1b7214de7', -- gainid
    NOW(), -- gain_createddatetime
    NOW(), -- gain_modifieddatetime
    'plaintiffs' -- canonical_object
);
