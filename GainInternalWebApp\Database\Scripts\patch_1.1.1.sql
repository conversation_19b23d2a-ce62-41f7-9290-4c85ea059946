ALTER TABLE dev.plaintiffs_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.plaintiffs_insertdata_map DROP CONSTRAINT plaintiffs_insertdata_map_salesforceid_key;

ALTER TABLE dev.cases_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.cases_insertdata_map DROP CONSTRAINT cases_insertdata_map_salesforceid_key;

ALTER TABLE dev.legalpersonnel_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.legalpersonnel_insertdata_map DROP CONSTRAINT legalpersonnel_insertdata_map_salesforceid_key;

ALTER TABLE dev.billings_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.billings_insertdata_map DROP CONSTRAINT billings_insertdata_map_salesforceid_key;

ALTER TABLE dev.charges_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.charges_insertdata_map DROP CONSTRAINT charges_insertdata_map_salesforceid_key;

ALTER TABLE dev.transactions_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.transactions_insertdata_map DROP CONSTRAINT transactions_insertdata_map_salesforceid_key;

ALTER TABLE dev.files_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.files_insertdata_map DROP CONSTRAINT files_insertdata_map_salesforceid_key;

ALTER TABLE dev.surgery_insertdata_map ADD COLUMN salesforceexternalidcontainer varchar(20) UNIQUE;
ALTER TABLE dev.surgery_insertdata_map DROP CONSTRAINT surgery_insertdata_map_salesforceid_key;