from unittest.mock import patch

import pytest


@pytest.fixture
def mock_advisory_lock_success():
    with patch(
        'integration.integration_code.aws_operations.postgres_advisory_lock'
    ) as mock_lock:
        mock_lock.return_value.__enter__.return_value = None
        mock_lock.return_value.__exit__.return_value = None
        yield mock_lock


@pytest.fixture
def mock_advisory_lock_failure():
    with patch(
        'integration.integration_code.aws_operations.postgres_advisory_lock'
    ) as mock_lock:
        mock_lock.side_effect = Exception("LOCK_NOT_ACQUIRED")
        yield mock_lock
