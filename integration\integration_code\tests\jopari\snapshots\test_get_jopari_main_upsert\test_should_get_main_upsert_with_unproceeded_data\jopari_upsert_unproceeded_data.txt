[[{"gainid": "[AUTO_GENERATED]", "servicingstartdatetime": "2023-01-01 12:00:00", "servicingenddatetime": "", "medicalclaimnumber": "077456873890-1", "dateofservice": "2025-01-24", "status": "", "totalamount": "2627.74", "totalnongainadjustment": "", "totalnongainamountpaidtoprovider": "", "totalgainprenegotiationadjustment": "", "totalgainprenegotiationamountpaidtoprovider": "", "totaldeductible": "", "totalcoinsurance": "", "totalcopayment": "", "totalbalance": "2627.74", "totalamountsent": "", "medicalfacilityaddressline1": "", "medicalfacilityaddressline2": "", "medicalfacilityaddresscity": "", "medicalfacilityaddressstate": "", "medicalfacilityaddresszip": "", "paidto": "", "paidby": "", "notes": "", "gaintype": "Medical Funding", "type": "Medical Funding", "caseid": "[AUTO_GENERATED]", "medicalfacilityid": "[AUTO_GENERATED]", "partneraccountid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "servicingstartdatetime": "2023-01-01 12:00:00", "servicingenddatetime": "", "plaintiffname": "<PERSON>", "plaintiffdateofbirth": "1988-05-19", "status": "", "accidentdate": "2025-01-03", "injuredbodyparts": "", "accidentdescription": "", "accidentstate": "Georgia", "treatmentcompleted": "", "datetreatmentcompleted": "", "insurancevendorassigned": "", "grandtotalamount": "", "grandtotalnongainadjustment": "", "grandtotalnongainamountpaidtoprovider": "", "grandtotaldeductible": "", "grandtotalcoinsurance": "", "grandtotalcopayment": "", "grandtotalbalance": "", "datesettled": "", "grandtotalsettlementamount": "", "paidto": "", "paidby": "", "notes": "", "type": "", "plaintiffid": "[AUTO_GENERATED]", "attorneyid": "[AUTO_GENERATED]", "paralegalid": "[AUTO_GENERATED]", "casemanagerid": "[AUTO_GENERATED]", "cocounselid": "[AUTO_GENERATED]", "coparalegalid": "[AUTO_GENERATED]", "cocasemanagerid": "[AUTO_GENERATED]", "tailclaimcase": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "dateofservice": "2025-01-24", "status": "", "amount": "929.78", "cptcode": "99204", "cptmodifier": "25", "cptdescription": "", "nongainadjustment": "", "nongainamountpaidtoprovider": "", "gainprenegotiationadjustment": "", "gainprenegotiationamountpaidtoprovider": "", "deductible": "", "coinsurance": "", "copayment": "", "balance": "", "reimbursementrate": "", "amountsent": "", "quantity": "1", "billingid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}, {"gainid": "[AUTO_GENERATED]", "dateofservice": "2025-01-24", "status": "", "amount": "562.33", "cptcode": "72100", "cptmodifier": "", "cptdescription": "", "nongainadjustment": "", "nongainamountpaidtoprovider": "", "gainprenegotiationadjustment": "", "gainprenegotiationamountpaidtoprovider": "", "deductible": "", "coinsurance": "", "copayment": "", "balance": "", "reimbursementrate": "", "amountsent": "", "quantity": "1", "billingid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}, {"gainid": "[AUTO_GENERATED]", "dateofservice": "2025-01-24", "status": "", "amount": "533.65", "cptcode": "72040", "cptmodifier": "", "cptdescription": "", "nongainadjustment": "", "nongainamountpaidtoprovider": "", "gainprenegotiationadjustment": "", "gainprenegotiationamountpaidtoprovider": "", "deductible": "", "coinsurance": "", "copayment": "", "balance": "", "reimbursementrate": "", "amountsent": "", "quantity": "1", "billingid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}, {"gainid": "[AUTO_GENERATED]", "dateofservice": "2025-01-24", "status": "", "amount": "504.78", "cptcode": "72070", "cptmodifier": "", "cptdescription": "", "nongainadjustment": "", "nongainamountpaidtoprovider": "", "gainprenegotiationadjustment": "", "gainprenegotiationamountpaidtoprovider": "", "deductible": "", "coinsurance": "", "copayment": "", "balance": "", "reimbursementrate": "", "amountsent": "", "quantity": "1", "billingid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}, {"gainid": "[AUTO_GENERATED]", "dateofservice": "2025-01-24", "status": "", "amount": "97.2", "cptcode": "T2003", "cptmodifier": "", "cptdescription": "", "nongainadjustment": "", "nongainamountpaidtoprovider": "", "gainprenegotiationadjustment": "", "gainprenegotiationamountpaidtoprovider": "", "deductible": "", "coinsurance": "", "copayment": "", "balance": "", "reimbursementrate": "", "amountsent": "", "quantity": "1", "billingid": "[AUTO_GENERATED]", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "companyname": "<PERSON>", "policyid": "", "memberid": "", "groupnumber": "", "status": "", "limits": "", "phone": "", "fax": "", "email": "", "followupemail": "", "billingaddressline1": "2578 Lavender Ct", "billingaddressline2": "", "billingaddresscity": "Decatur", "billingaddressstate": "Georgia", "billingaddresszip": "30034", "physicaladdressline1": "", "physicaladdressline2": "", "physicaladdresscity": "", "physicaladdressstate": "", "physicaladdresszip": "", "liabilityaccepted": "", "declarationpagereceived": "", "medpayexhausted": "", "pipexhausted": "", "probate": "", "bankruptcy": "", "subrogationlien": "", "otherlien": "", "otherlienname": "", "drivername": "", "datesettled": "", "howcasewassettled": "", "totalsettlementamount": "", "billspaid": "", "attorneyfee": "", "attorneyfeeflexible": "", "referralfeepercentage": "", "amounttoclient": "", "settlementnotes": "", "notes": "", "type": "Am", "caseid": "[AUTO_GENERATED]", "relevanttogain": "", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "name": "Gain Auto & Personal Injury (All States) (Auto Only)", "phone": "", "fax": "", "email": "", "followupemail": "", "billingaddressline1": "Po Box 5000", "billingaddressline2": "", "billingaddresscity": "<PERSON>", "billingaddressstate": "Alabama", "billingaddresszip": "36526", "physicaladdressline1": "", "physicaladdressline2": "", "physicaladdresscity": "", "physicaladdressstate": "", "physicaladdresszip": "", "website": "", "typeoflaw": "", "description": "", "employeecountrange": "", "donotfund": "", "donotfundtype": "", "automaticcaseupdaterequest": "", "nonresponsive": "", "nonresponsivenote": "", "portalaccount": "", "portalrewardsparticipant": "", "notes": "", "parentid": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "name": "Default Legal Gain Auto & Personal Injury (All States) (Auto Only)", "title": "", "homephone": "", "cellphone": "", "businessphone": "", "businessphoneext": "", "otherphone": "", "fax": "", "primaryemail": "", "secondaryemail": "", "primaryaddressline1": "", "primaryaddressline2": "", "primaryaddresscity": "", "primaryaddressstate": "", "primaryaddresszip": "", "otheraddressline1": "Po Box 5000", "otheraddressline2": "", "otheraddresscity": "<PERSON>", "otheraddressstate": "", "otheraddresszip": "36526", "notes": "", "lawfirmid": "[AUTO_GENERATED]", "lawfirmname": "Gain Auto & Personal Injury (All States) (Auto Only)", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "name": "College Park", "phone": "", "fax": "", "email": "", "followupemail": "", "billingaddressline1": "", "billingaddressline2": "", "billingaddresscity": "", "billingaddressstate": "", "billingaddresszip": "", "physicaladdressline1": "2300 Camp Creek Pkwy", "physicaladdressline2": "", "physicaladdresscity": "Atlanta", "physicaladdressstate": "Georgia", "physicaladdresszip": "*********", "website": "", "contracttype": "", "specialties": "", "description": "", "portalaccount": "", "notes": "", "parentid": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}, {"gainid": "[AUTO_GENERATED]", "name": "Georgia Spine And Orthopaedics", "phone": "", "fax": "", "email": "", "followupemail": "", "billingaddressline1": "", "billingaddressline2": "", "billingaddresscity": "", "billingaddressstate": "", "billingaddresszip": "", "physicaladdressline1": "11650 Alpharetta Hwy", "physicaladdressline2": "", "physicaladdresscity": "<PERSON><PERSON><PERSON>", "physicaladdressstate": "Georgia", "physicaladdresszip": "*********", "website": "", "contracttype": "", "specialties": "", "description": "", "portalaccount": "", "notes": "", "parentid": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}], [{"gainid": "[AUTO_GENERATED]", "name": "<PERSON>", "dateofbirth": "1988-05-19", "ssn": "", "taxid": "", "driverlicense": "", "gender": "M", "maritalstatus": "", "company": "", "homephone": "", "cellphone": "", "businessphone": "", "otherphone": "", "primaryemail": "", "secondaryemail": "", "primaryaddressline1": "2578 Lavender Ct", "primaryaddressline2": "", "primaryaddresscity": "Decatur", "primaryaddressstate": "Georgia", "primaryaddresszip": "30034", "otheraddressline1": "", "otheraddressline2": "", "otheraddresscity": "", "otheraddressstate": "", "otheraddresszip": "", "departmentid": "", "relevanttogain": "True", "modifieddatetime": "2023-01-01 12:00:00", "todelete": "False", "todeletesystem": "", "deletepreventoverride": "False", "deletepreventoverridereason": ""}]]