import django.db.models

# Create your models here.


class audit_log(django.db.models.Model):
    # include meta parameter to exclude model from being migrated, useful if model is already a table
    # class Meta:
    #     managed = False

    audit_log_id = django.db.models.BigAutoField(primary_key=True)
    salesforce_id = django.db.models.CharField(max_length=20)
    model = django.db.models.CharField(max_length=100)
    input_parameters = django.db.models.TextField(null=True, default=None)
    predicted_value = django.db.models.TextField(null=True, default=None)
    is_error = django.db.models.BooleanField(default=False)
    exception_message = django.db.models.TextField(null=True, default=None)
    create_date_time = django.db.models.DateTimeField(auto_now_add=True)
