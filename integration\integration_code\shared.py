import inspect
import pathlib
import time
import typing
from functools import wraps

import pandas as pd

from .models import types


def get_func_name() -> str:
    func_name = 'Unknown Function'
    frame = inspect.currentframe()
    if frame:
        caller_frame = frame.f_back

        if caller_frame:
            func_name = caller_frame.f_code.co_name
            caller_file = caller_frame.f_code.co_filename

            func_name = f'{pathlib.Path(caller_file).stem} | {func_name}'
    return func_name


def verify_is_valid(
    data: dict[str, typing.Optional[pd.DataFrame]], canonical: str
):
    df = data.get(canonical)
    return df is not None and not df.empty


def is_df_valid(df: pd.DataFrame | None) -> typing.TypeGuard[pd.DataFrame]:
    return df is not None and not df.empty


def timing_decorator(func: typing.Callable[[typing.Any], typing.Any]):
    @wraps(func)
    def wrapper(*args: tuple[typing.Any], **kwargs: dict[str, typing.Any]):
        start_time = time.perf_counter()  # Start timer
        result = func(*args, **kwargs)  # Run the actual function
        end_time = time.perf_counter()  # End timer
        elapsed_time = end_time - start_time  # Calculate elapsed time
        print(
            f"Function '{func.__name__}' took {elapsed_time:.4f} seconds to complete."
        )
        return result  # Return the original function result

    return wrapper


def get_first_value(
    input: (
        types.Scalar | pd.Series  # pyright: ignore[reportMissingTypeArgument]
    ),
) -> typing.Any:
    if isinstance(input, pd.Series):
        return input.values[0]
    return input
