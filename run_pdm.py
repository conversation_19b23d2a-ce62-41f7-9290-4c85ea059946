#!/usr/bin/env python3
import os
import shutil
import subprocess
import sys


def find_pdm():
    """Find the correct PDM executable, prioritizing global PDM, then local venv."""
    # Check for globally installed PDM
    pdm_path = shutil.which("pdm")
    if pdm_path:
        return pdm_path

    # Check for local virtual environment installation
    venv_pdm = (
        os.path.join("venv", "Scripts", "pdm.exe")
        if os.name == "nt"
        else os.path.join("venv", "bin", "pdm")
    )
    if os.path.isfile(venv_pdm) and os.access(venv_pdm, os.X_OK):
        return venv_pdm

    return None


def main():
    pdm_exec = find_pdm()
    if not pdm_exec:
        print(
            "Error: PDM is not installed globally or in the virtual environment.",
            file=sys.stderr,
        )
        sys.exit(1)

    # Execute the PDM command with arguments passed to this script
    result = subprocess.run([pdm_exec, *sys.argv[1:]], check=False)
    sys.exit(result.returncode)


if __name__ == "__main__":
    main()
