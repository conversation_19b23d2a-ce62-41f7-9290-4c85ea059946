CREATE TABLE legalpersonnel (
    name character varying(250),
    title character varying(100),
    lawfirmid character varying(100),
    lawfirmname character varying(100),
    homephone character varying(100),
    cellphone character varying(100),
    businessphone character varying(100),
    businessphoneext character varying(20),
    otherphone character varying(100),
    fax character varying(100),
    primaryemail character varying(100),
    secondaryemail character varying(100),
    primaryaddressline1 character varying(250),
    primaryaddressline2 character varying(250),
    primaryaddresscity character varying(100),
    primaryaddressstate character varying(100),
    primaryaddresszip character varying(20),
    otheraddressline1 character varying(250),
    otheraddressline2 character varying(250),
    otheraddresscity character varying(100),
    otheraddressstate character varying(100),
    otheraddresszip character varying(20),
    notes character varying(1000),
    relevanttogain boolean,
    sourcecreatedatetime timestamp without time zone,
    sourcemodifieddatetime timestamp without time zone,
    createdatetime timestamp without time zone DEFAULT ('now':: character varying):: timestamp without time zone,
    modifieddatetime timestamp without time zone,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    gainid character varying(16) NOT NULL DEFAULT '':: character varying,
    PRIMARY KEY (gainid),
    FOREIGN KEY (lawfirmid) REFERENCES lawfirms(gainid)
);