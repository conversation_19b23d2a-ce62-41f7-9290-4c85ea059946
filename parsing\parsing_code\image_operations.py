# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning,reportArgumentType=warning,reportAttributeAccessIssue=warning
import logging
import pathlib

import cv2
import fitz
import numpy as np

# Get logger for this module
logger = logging.getLogger(__name__)


def crop_contour(img, img_copy):
    contours, _ = cv2.findContours(
        img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
    )
    c = max(contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(c)
    return img_copy[y : y + h, x : x + w]


def crop(img):
    count = 0
    img = img[100:]
    while (
        img.shape[0] / img.shape[1] < 1.1601303639
    ):  # This is to standardize aspect ratio
        img = img[:, :-1]
        count += 1
    return img, count


def crop_vals(img, count):
    img = img[100:]
    img = img[:, :-count]
    return img


def remove_blobs(img):
    # Do connected components processing
    nlabels, labels, stats, _ = cv2.connectedComponentsWithStats(
        img, None, None, None, 8, cv2.CV_32S
    )
    # Get CC_STAT_AREA component as stats[label, COLUMN]
    areas = stats[1:, cv2.CC_STAT_AREA]
    result = np.zeros((labels.shape), np.uint8)
    for i in range(0, nlabels - 1):
        if (
            areas[i] >= 250
        ):  # Remove white blobs smaller than this area (in pixels)
            result[labels == i + 1] = 255
    return result


def remove_elements(img, borders=True, lines=False):
    img = remove_blobs(img)
    if borders == True:
        # Add 1 pixel white border all around
        pad = cv2.copyMakeBorder(
            img, 1, 1, 1, 1, cv2.BORDER_CONSTANT, value=255
        )
        h, w = pad.shape
        # Create zeros mask 2 pixels larger in each dimension
        mask = np.zeros([h + 2, w + 2], np.uint8)
        # floodfill outer white border with black
        img = cv2.floodFill(pad, mask, (0, 0), 0, (5), (0), flags=8)[1]
        # Remove border
        img = img[1 : h - 1, 1 : w - 1]
    if lines == True:
        # Remove horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 1))
        remove_horizontal = cv2.morphologyEx(
            img, cv2.MORPH_OPEN, horizontal_kernel, iterations=2
        )
        cnts = cv2.findContours(
            remove_horizontal, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        cnts = cnts[0] if len(cnts) == 2 else cnts[1]
        for c in cnts:
            cv2.drawContours(img, [c], -1, (0, 0, 0), 5)
        # Remove vertical lines
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 250))
        remove_vertical = cv2.morphologyEx(
            img, cv2.MORPH_OPEN, vertical_kernel, iterations=2
        )
        cnts = cv2.findContours(
            remove_vertical, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        cnts = cnts[0] if len(cnts) == 2 else cnts[1]
        for c in cnts:
            cv2.drawContours(img, [c], -1, (0, 0, 0), 5)
    return img


def zoom(file, z_factor=4):
    doc = fitz.open(file)
    page = doc.load_page(0)  # Page number
    pix = page.get_pixmap(matrix=fitz.Matrix(z_factor, z_factor))
    doc_img = f'{file[:-4]}.png'
    pix.save(doc_img)
    return doc_img


def write_img(name, image, z=0.35, iter=1):
    kernel = np.ones((2, 2), np.uint8)
    # image = cv2.erode(image, kernel, iterations=iter)
    image = cv2.dilate(image, kernel, iterations=iter)
    image = cv2.bitwise_not(image)
    cv2.imwrite(name, image)
    zoom(name, z_factor=z)
    return


def chunk(doc_img, img):
    dob_chunk = img[
        int(img.shape[0] * 0.04) : int(img.shape[0] * 0.08),
        int(img.shape[1] * 0.37) : int(img.shape[1] * 0.51),
    ]
    dob_chunk = remove_elements(dob_chunk)
    write_img(f'{doc_img[:-4]} - first chunk.png', dob_chunk)
    dates_chunk = img[int(img.shape[0] * 0.63) : int(img.shape[0] * 0.903), :]
    for i in range(1, 7):
        intermediate_chunk = dates_chunk[
            int(dates_chunk.shape[0] * (i / 8)) : int(
                dates_chunk.shape[0] * ((i + 1) / 8)
            ),
            :,
        ]
        intermediate_chunk_1 = intermediate_chunk[
            :, : int(intermediate_chunk.shape[1] * 0.12)
        ]
        intermediate_chunk_1 = remove_elements(intermediate_chunk_1)
        write_img(
            f'{doc_img[:-4]} - intermediate chunk {i+1} - sub-chunk 1.png',
            intermediate_chunk_1,
            z=0.32,
            iter=4,
        )
        intermediate_chunk_2 = intermediate_chunk[
            :,
            int(intermediate_chunk.shape[1] * 0.3) : int(
                intermediate_chunk.shape[1] * 0.4
            ),
        ]
        intermediate_chunk_2 = remove_elements(intermediate_chunk_2)
        write_img(
            f'{doc_img[:-4]} - intermediate chunk {i+1} - sub-chunk 2.png',
            intermediate_chunk_2,
            iter=2,
        )
        intermediate_chunk_3 = intermediate_chunk[
            :,
            int(intermediate_chunk.shape[1] * 0.63) : int(
                intermediate_chunk.shape[1] * 0.73
            ),
        ]
        intermediate_chunk_3 = remove_elements(intermediate_chunk_3)
        write_img(
            f'{doc_img[:-4]} - intermediate chunk {i+1} - sub-chunk 3.png',
            intermediate_chunk_3,
            z=0.5,
            iter=2,
        )
    last_chunk = dates_chunk[int(dates_chunk.shape[0] * 7 / 8) :, :]
    last_chunk_1 = last_chunk[
        :, int(last_chunk.shape[1] * 0.25) : int(last_chunk.shape[1] * 0.45)
    ]
    last_chunk_1 = remove_elements(last_chunk_1)
    write_img(f'{doc_img[:-4]} - last chunk - sub-chunk 1.png', last_chunk_1)
    last_chunk_2 = last_chunk[
        :, int(last_chunk.shape[1] * 0.63) : int(last_chunk.shape[1] * 0.77)
    ]
    last_chunk_2 = remove_elements(last_chunk_2)
    write_img(
        f'{doc_img[:-4]} - last chunk - sub-chunk 2.png', last_chunk_2, z=0.4
    )
    last_chunk_3 = last_chunk[
        :, int(last_chunk.shape[1] * 0.78) : int(last_chunk.shape[1] * 0.9)
    ]
    last_chunk_3 = remove_elements(last_chunk_3)
    write_img(f'{doc_img[:-4]} - last chunk - sub-chunk 3.png', last_chunk_3)
    pathlib.Path(doc_img).unlink(missing_ok=True)
    return


def binarize(img):
    img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)  # Gray scale
    img = cv2.threshold(img, 30, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[
        1
    ]  # Otsu thresholding + binary thresholding
    img = cv2.bitwise_not(img)
    return img


def remove_red(img):
    img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    lower, upper = np.array([0, 50, 50]), np.array([50, 255, 255])
    mask_0 = cv2.inRange(img_hsv, lower, upper)
    lower, upper = np.array([130, 50, 50]), np.array([180, 255, 255])
    mask_1 = cv2.inRange(img_hsv, lower, upper)
    mask = mask_0 + mask_1
    img_temp = cv2.bitwise_and(img, img, mask=mask)
    img_temp = cv2.cvtColor(img_temp, cv2.COLOR_BGR2GRAY)
    img_temp = cv2.threshold(img_temp, 10, 255, cv2.THRESH_BINARY)[1]
    img_temp = ~img_temp
    return img_temp


def image_process(doc_img):
    img = cv2.imread(doc_img)
    temp = img.copy()  # "temp" is needed for crop_contour function
    temp = binarize(temp)
    img = crop_contour(temp, img)
    del temp
    img_red = remove_red(img)
    img, removed = crop(img)
    img_red = crop_vals(img_red, removed)
    img = binarize(img)

    # Check if img_red has any data before processing
    if img_red.size == 0:
        logger.warning(
            "Image processing failed: Red channel extraction returned empty array. "
            "This may result in poor OCR quality and parsing errors."
        )
    elif np.min(img_red) < 200:
        img = cv2.bitwise_and(img, img_red)
        logger.debug("Applied red channel filtering to improve image quality")
    else:
        logger.debug(
            "Skipped red channel filtering - image quality appears good"
        )

    del img_red
    chunk(doc_img, img)
    return


def convert_to_image(file):
    try:
        doc_img = zoom(file, 8)
        image_process(doc_img)
        logger.debug(f"Successfully processed image from file: {file}")
    except Exception as e:
        logger.error(
            f"Image processing failed for file {file}: {str(e)}. "
            "This will likely cause parsing errors downstream."
        )
        raise  # Re-raise the exception so the calling code can handle it
    return
