-- Use this patch if testing in an environment where
-- <PERSON> Firm is not already in the LawFirms table


INSERT
    INTO integration.dev.LawFirms (
        GainID,
        Name,
        Phone,
        Fax,
        BillingAddressLine1,
        BillingAddressCity,
        BillingAddressState,
        BillingAddressZip,
        PhysicalAddressLine1,
        PhysicalAddressCity,
        PhysicalAddressState,
        PhysicalAddressZip,
        Website,
        TypeOfLaw,
        Description,
        PortalAccount,
        RelevantToGain,
        ModifiedDateTime
    )
    VALUES (
        '366e1db3dc1da840',
        '<PERSON> Law Firm',
        '(*************',
        '(*************',
        '33 Park of Commerce Blvd',
        'Savannah',
        'Georgia',
        '31405',
        '33 Park of Commerce Blvd',
        'Savannah',
        'Georgia',
        '31405',
        'https://www.mikehostilolawfirm.com',
        'Personal Injury',
        'Personal Injury and car accident lawyers licensed in Georgia, South Carolina, Alabama, and Florida.',
        TRUE,
        TRUE,
        CURRENT_TIMESTAMP
    )
;

INSERT
    INTO integration.dev.gain_id_map (
        gainid,
        gain_createddatetime,
        gain_modifieddatetime,
        salesforce_id,
        salesforce_createddatetime,
        salesforce_modifieddatetime,
        canonical_object
    )
    VALUES (
        '366e1db3dc1da840',
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        '001G000001XqF10IAF',  -- Change this to the correct Salesforce ID
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        'lawfirms' -- NOTE: This should be lowercase
    )
;


INSERT
    INTO integration.staging.LawFirms (
        GainID,
        Name,
        Phone,
        Fax,
        BillingAddressLine1,
        BillingAddressCity,
        BillingAddressState,
        BillingAddressZip,
        PhysicalAddressLine1,
        PhysicalAddressCity,
        PhysicalAddressState,
        PhysicalAddressZip,
        Website,
        TypeOfLaw,
        Description,
        PortalAccount,
        RelevantToGain,
        ModifiedDateTime
    )
    VALUES (
        '366e1db3dc1da840',
        'Mike Hostilo Law Firm',
        '(*************',
        '(*************',
        '33 Park of Commerce Blvd',
        'Savannah',
        'Georgia',
        '31405',
        '33 Park of Commerce Blvd',
        'Savannah',
        'Georgia',
        '31405',
        'https://www.mikehostilolawfirm.com',
        'Personal Injury',
        'Personal Injury and car accident lawyers licensed in Georgia, South Carolina, Alabama, and Florida.',
        TRUE,
        TRUE,
        CURRENT_TIMESTAMP
    )
;

INSERT
    INTO integration.staging.gain_id_map (
        gainid,
        gain_createddatetime,
        gain_modifieddatetime,
        salesforce_id,
        salesforce_createddatetime,
        salesforce_modifieddatetime,
        canonical_object
    )
    VALUES (
        '366e1db3dc1da840',
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        '001G000001XqF10IAF',  -- Change this to the correct Salesforce ID
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        'lawfirms' -- NOTE: This should be lowercase
    )
;

