# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning,reportArgumentType=warning
import base64
import io
from datetime import date

import PyPDF2
from fpdf import Template


class AttyAckGetMergedTemplates:
    outputData = {
        "pdfContents": [],
        "uniqueIDs": [],
        "autoSign": [],
        "contractChoice": [],
    }
    replaceTextMap = {
        '__FIRM_NAME__': 'lawFirmName',
        '__PROVIDER_NAME__': 'providerName',
    }

    def __init__(self):
        self.outputData = {
            "pdfContents": [],
            "uniqueIDs": [],
            "autoSign": [],
            "contractChoice": [],
        }
        self.replaceTextMap = {
            '__FIRM_NAME__': 'lawFirmName',
            '__PROVIDER_NAME__': 'providerName',
        }

    def AttyAckGetMergedTemplates(self, listOfSFData):
        # Loop through sf data list and replace RTF text with data
        # sfData format:
        # String fields: uniqueID, providerName, providerAutoSign, lawFirmName
        # picklist fields: tableRowData {"OpportunityRowData", "FundingRowData"}, contractOption {"Attorney and Provider", "Attorney and Gain"}
        # list fields: relatedObjectData
        # sfData format:
        # String fields: uniqueID, providerName, providerAutoSign, lawFirmName
        # picklist fields: tableRowData {"OpportunityRowData", "FundingRowData"}, contractOption {"Attorney and Provider", "Attorney and Gain"}
        # list fields: relatedObjectData
        for sfData in listOfSFData:
            missing_data = False
            if None in [
                sfData['uniqueID'],
                sfData['lawFirmName'],
                sfData['providerName'],
                sfData['tableRowData'],
                sfData['contractOption'],
            ]:
                missing_data = True
            elif '' in [
                sfData['uniqueID'],
                sfData['lawFirmName'],
                sfData['providerName'],
                sfData['tableRowData'],
                sfData['contractOption'],
            ]:
                missing_data = True
            elif (
                sfData['relatedObjectData'] is None
                or len(sfData['relatedObjectData']) == 0
            ):
                missing_data = True

            if missing_data == True:
                self.outputData['pdfContents'].append('')
                self.outputData['uniqueIDs'].append('')
            else:
                # open txt file and read content to linemap
                contentLineMap = {}
                providerAutoSign = sfData['providerAutoSign']
                # {'Num':0, 'Value':''}

                # choose template to pull from
                templateToUse = ''
                if sfData['contractOption'] == 'Express Funding':
                    templateToUse = 'salesforcedata/attyack_get_merged_templates/Gain EFA Atty Acknowledgement.txt'
                elif sfData['contractOption'] == 'Attorney and Provider':
                    templateToUse = 'salesforcedata/attyack_get_merged_templates/Provider Attorney Acknowledgement.txt'
                elif sfData['contractOption'] == 'Attorney and Gain':
                    templateToUse = 'salesforcedata/attyack_get_merged_templates/Gain Attorney Acknowledgement.txt'

                with open(templateToUse, encoding='latin-1') as file:
                    textContent = file.read()
                    fileLines = textContent.split('\n')
                    counter = 0
                    firmLine = 0
                    providerLine = 0
                    rowDataLine = 0
                    rowHeaderLine = 0
                    rowDataLine = 0
                    rowHeaderLine = 0
                    sertifiSignALine = 0
                    sertifiDateALine = 0
                    sertifiSignBLine = 0
                    sertifiDateBLine = 0
                    for line in fileLines:
                        if (
                            sfData['tableRowData'] == 'OpportunityRowData'
                            and sfData['contractOption'] == 'Attorney and Gain'
                            and counter == 16
                        ):
                            line = line.replace(
                                'Patient\'s receivables', 'Patient'
                            )
                        elif (
                            sfData['tableRowData'] == 'OpportunityRowData'
                            and sfData['contractOption']
                            == 'Attorney and Provider'
                            and counter == 17
                        ):
                            line = line.replace(
                                'Patient\'s receivables', 'Patient'
                            )

                        if '__FIRM_NAME__' in line:
                            firmLine = counter
                        elif '__PROVIDER_NAME__' in line:
                            providerLine = counter
                        elif '__ROW_HEADER__' in line:
                            rowHeaderLine = counter
                        elif 'ROW_DATA' in line:
                            rowDataLine = counter
                        elif '__ROW_HEADER__' in line:
                            rowHeaderLine = counter
                        elif 'ROW_DATA' in line:
                            rowDataLine = counter
                        elif '[[SertifiSignature_1]]' in line:
                            sertifiSignALine = counter
                        elif '[[SertifiDate_1]]' in line:
                            sertifiDateALine = counter
                        elif '[[SertifiSignature_2]]' in line:
                            sertifiSignBLine = counter
                        elif '[[SertifiDate_2]]' in line:
                            sertifiDateBLine = counter
                        contentLineMap.setdefault(counter, line.strip())
                        counter += 1

                # replace text data in RTF content given key value mapping
                replaceFirmStr = contentLineMap[firmLine]
                replaceFirmStr = replaceFirmStr.replace(
                    '__FIRM_NAME__',
                    sfData[self.replaceTextMap['__FIRM_NAME__']],
                )
                contentLineMap[firmLine] = replaceFirmStr
                if (
                    sfData['contractOption'] == 'Attorney and Gain'
                    or sfData['contractOption'] == 'Express Funding'
                ):  # only Attorney Signature
                    # replace provider name in top paragraph
                    replaceProviderStr = contentLineMap[providerLine]
                    replaceProviderStr = replaceProviderStr.replace(
                        '__PROVIDER_NAME__',
                        sfData[self.replaceTextMap['__PROVIDER_NAME__']],
                    )
                    contentLineMap[providerLine] = replaceProviderStr
                    # Signature 1 = Gain
                    replaceSignA = contentLineMap[sertifiSignALine]
                    replaceSignA = replaceSignA.replace(
                        '[[SertifiSignature_1]]', 'Jonathan Razza'
                    )
                    replaceDateA = contentLineMap[sertifiDateALine]
                    currentDate = date.today()
                    replaceDateA = replaceDateA.replace(
                        '[[SertifiDate_1]]', currentDate.strftime("%m/%d/%Y")
                    )
                    contentLineMap[sertifiSignALine] = replaceSignA
                    contentLineMap[sertifiDateALine] = replaceDateA

                    # switch signature 2 to signature 1
                    replaceSignB = contentLineMap[sertifiSignBLine]
                    replaceSignB = replaceSignB.replace(
                        '[[SertifiSignature_2]]', '[[SertifiSignature_1]]'
                    )
                    replaceDateB = contentLineMap[sertifiDateBLine]
                    replaceDateB = replaceDateB.replace(
                        '[[SertifiDate_2]]', '[[SertifiDate_1]]'
                    )
                    contentLineMap[sertifiSignBLine] = replaceSignB
                    contentLineMap[sertifiDateBLine] = replaceDateB

                elif providerAutoSign not in ('', None):
                    # Replace Provider Name
                    replaceProviderStr = contentLineMap[providerLine]
                    replaceProviderStr = replaceProviderStr.replace(
                        '__PROVIDER_NAME__',
                        sfData[self.replaceTextMap['__PROVIDER_NAME__']],
                    )
                    contentLineMap[providerLine] = replaceProviderStr

                    # Signature 1 = provider
                    replaceSignA = contentLineMap[sertifiSignALine]
                    replaceSignA = replaceSignA.replace(
                        '[[SertifiSignature_1]]', providerAutoSign
                    )
                    replaceDateA = contentLineMap[sertifiDateALine]
                    currentDate = date.today()
                    replaceDateA = replaceDateA.replace(
                        '[[SertifiDate_1]]', currentDate.strftime("%m/%d/%Y")
                    )
                    contentLineMap[sertifiSignALine] = replaceSignA
                    contentLineMap[sertifiDateALine] = replaceDateA
                    # switch signature 2 to signature 1
                    replaceSignB = contentLineMap[sertifiSignBLine]
                    replaceSignB = replaceSignB.replace(
                        '[[SertifiSignature_2]]', '[[SertifiSignature_1]]'
                    )
                    replaceDateB = contentLineMap[sertifiDateBLine]
                    replaceDateB = replaceDateB.replace(
                        '[[SertifiDate_2]]', '[[SertifiDate_1]]'
                    )
                    contentLineMap[sertifiSignBLine] = replaceSignB
                    contentLineMap[sertifiDateBLine] = replaceDateB
                else:
                    replaceProviderStr = contentLineMap[providerLine]
                    replaceProviderStr = replaceProviderStr.replace(
                        '__PROVIDER_NAME__',
                        sfData[self.replaceTextMap['__PROVIDER_NAME__']],
                    )
                    contentLineMap[providerLine] = replaceProviderStr

                # add object row data to the temp template
                replaceRowHeader = contentLineMap[rowHeaderLine]
                if sfData['tableRowData'] == 'OpportunityRowData':
                    # replace row headers (__ROW_HEADER__)
                    replaceRowHeader = replaceRowHeader.replace(
                        '__ROW_HEADER__',
                        'Plaintiff Name | Date of Accident | Date of Birth',
                    )
                    contentLineMap[rowHeaderLine] = replaceRowHeader
                    contentLineMap = self.addPlaintiffRowData(
                        sfData['relatedObjectData'],
                        contentLineMap,
                        rowDataLine,
                    )
                elif sfData['tableRowData'] == 'FundingRowData':
                    # replace row headers (__ROW_HEADER__)
                    replaceRowHeader = replaceRowHeader.replace(
                        '__ROW_HEADER__',
                        'Plaintiff Name | Date of Accident | Date of Birth | Date of Service',
                    )
                    contentLineMap[rowHeaderLine] = replaceRowHeader
                    contentLineMap = self.addFundingRowData(
                        sfData['relatedObjectData'],
                        contentLineMap,
                        rowDataLine,
                    )

                base64pdfContent = self.load_pdf_with_template(
                    contentLineMap, providerAutoSign, sfData['contractOption']
                )

                # outputData
                self.outputData['contractChoice'].append(
                    sfData['contractOption']
                )
                self.outputData['pdfContents'].append(base64pdfContent)
                self.outputData['uniqueIDs'].append(sfData['uniqueID'])
                if providerAutoSign not in ('', None):
                    self.outputData['autoSign'].append(True)
                else:
                    self.outputData['autoSign'].append(False)

    # =========================GetMergedTemplate Methods=====================================================================
    def addPlaintiffRowData(
        self, relatedClients, contentLineMap, plaintiffLine
    ):
        for clientData in relatedClients:
            rowText = ''
            replaceString = "__ROW_DATA__"
            if 'Name' in clientData:
                if clientData['Date_of_Accident__c'] in ('', None):
                    clientData['Date_of_Accident__c'] = '---'
                if clientData['Date_of_Birth__c'] in ('', None):
                    clientData['Date_of_Birth__c'] = '---'
                if clientData['Name'] is not None and clientData['Name'] != '':
                    rowText = f"{clientData['Name']} | {clientData['Date_of_Accident__c']} | {clientData['Date_of_Birth__c']}"

                    # add row data to the content map
                    contentLineMap[plaintiffLine] = rowText
                    contentLineMap[plaintiffLine + 1] = replaceString
                    plaintiffLine += 1
        contentLineMap[plaintiffLine] = ''

        return contentLineMap

    def addFundingRowData(
        self, relatedFundings, contentLineMap, plaintiffLine
    ):
        for fundingData in relatedFundings:
            rowText = ''
            replaceString = "__ROW_DATA__"
            if (
                'Plaintiff__r' in fundingData
                and 'Date_of_Service__c' in fundingData
            ):
                if fundingData['Plaintiff__r']['Date_of_Accident__c'] in (
                    '',
                    None,
                ):
                    fundingData['Plaintiff__r']['Date_of_Accident__c'] = '---'
                if fundingData['Plaintiff__r']['Date_of_Birth__c'] in (
                    '',
                    None,
                ):
                    fundingData['Plaintiff__r']['Date_of_Birth__c'] = '---'
                if fundingData['Date_of_Service__c'] in ('', None):
                    fundingData['Date_of_Service__c'] = '---'
                if fundingData['Plaintiff__r']['Name'] not in ('', None):
                    rowText = f"{fundingData['Plaintiff__r']['Name']} | {fundingData['Plaintiff__r']['Date_of_Accident__c']} | {fundingData['Plaintiff__r']['Date_of_Birth__c']} | {fundingData['Date_of_Service__c']}"

                    # add row data to the content map
                    contentLineMap[plaintiffLine] = rowText
                    contentLineMap[plaintiffLine + 1] = replaceString
                    plaintiffLine += 1
        contentLineMap[plaintiffLine] = ''

        return contentLineMap

    # =========================SUPPORTING METHODS=====================================================================

    # fpdf template csv format:
    # ('name','type','x1','y1','x2','y2','font','size','bold','italic','underline','foreground','background','align','text','priority', 'multiline')
    # pdf xy limits: {215,279}
    def load_pdf_with_template(
        self, linemap, providerAutoSign, contractOption
    ):
        pdfTemplate = Template(
            format="A4", title="Gain Attorney Acknowledgement 1"
        )
        pdfTemplate2 = Template(
            format="A4", title="Gain Attorney Acknowledgement 2"
        )
        pdfTemplate3 = Template(
            format="A4", title="Gain Attorney Acknowledgement 3"
        )

        if contractOption == 'Express Funding':
            pdfTemplate.parse_csv(
                "salesforcedata/attyack_get_merged_templates/fpdpf_template_pg_1_efa.csv"
            )
            pg1elementNames = [
                'header',
                'infoa',
                'infob',
                'infoc',
                'bullet1a',
                'bullet1b',
                'bullet1c',
                'bullet2a',
                'bullet2b',
                'bullet2c',
                'bullet2d',
                'bullet3a',
                'bullet3b',
                'bullet4a',
                'bullet4b',
                'signbelow',
                'hcheader',
                'hcby',
                'hcname',
                'hcdate',
                'firmheader',
                'firmby',
                'firmname',
                'firmdate',
            ]
        elif contractOption == 'Attorney and Gain':
            pdfTemplate.parse_csv(
                "salesforcedata/attyack_get_merged_templates/fpdpf_template_pg_1_gain.csv"
            )
            pg1elementNames = [
                'header',
                'infoa',
                'infob',
                'infoc',
                'bullet1a',
                'bullet1b',
                'bullet1c',
                'bullet1d',
                'bullet1e',
                'bullet2a',
                'bullet2b',
                'bullet3a',
                'bullet3b',
                'bullet4a',
                'bullet4b',
                'bullet5a',
                'bullet5b',
                'bullet5c',
                'bullet5d',
                'bullet5e',
                'signbelow',
                'hcheader',
                'hcby',
                'hcname',
                'hcdate',
                'firmheader',
                'firmby',
                'firmname',
                'firmdate',
            ]
        elif providerAutoSign not in ('', None):
            pdfTemplate.parse_csv(
                "salesforcedata/attyack_get_merged_templates/fpdpf_template_pg_1_auto.csv"
            )
            pg1elementNames = [
                'header',
                'infoa',
                'infob',
                'infoc',
                'bullet1a',
                'bullet1b',
                'bullet1c',
                'bullet1d',
                'bullet2a',
                'bullet2b',
                'bullet3a',
                'bullet3b',
                'bullet3c',
                'bullet4a',
                'bullet4b',
                'bullet4c',
                'bullet5a',
                'bullet5b',
                'bullet5c',
                'bullet5d',
                'signbelow',
                'hcheader',
                'hcby',
                'hcname',
                'hcdate',
                'firmheader',
                'firmby',
                'firmname',
                'firmdate',
            ]
        else:
            pg1elementNames = [
                'header',
                'infoa',
                'infob',
                'infoc',
                'bullet1a',
                'bullet1b',
                'bullet1c',
                'bullet1d',
                'bullet2a',
                'bullet2b',
                'bullet3a',
                'bullet3b',
                'bullet3c',
                'bullet4a',
                'bullet4b',
                'bullet4c',
                'bullet5a',
                'bullet5b',
                'bullet5c',
                'bullet5d',
                'signbelow',
                'hcheader',
                'hcby',
                'hcname',
                'hcdate',
                'firmheader',
                'firmby',
                'firmname',
                'firmdate',
            ]
            pdfTemplate.parse_csv(
                "salesforcedata/attyack_get_merged_templates/fpdpf_template_pg_1.csv"
            )
        pdfTemplate.add_page()

        # set variables for loop
        counter = 0
        pageNum = 1
        pg2elementNames = [
            'headertwo',
            'instr',
            'bank',
            'accType',
            'accName',
            'achnum',
            'accnum',
            'please',
            'mail',
            'Gain',
            'street',
            'suite',
            'state',
        ]
        pg3elementNames = ['headerthree', 'rowLabel']

        pg1Len = len(pg1elementNames)
        pg2Len = len(pg2elementNames)
        pg3Len = len(pg3elementNames)
        # set elements using linemap
        for key in range(len(linemap)):
            if counter >= 0 and counter <= pg1Len and pageNum == 1:
                pdfTemplate[pg1elementNames[counter]] = linemap[key]
                counter += 1
                if counter == pg1Len:
                    pageNum = 2
                    counter = 0

            elif counter >= 0 and counter < pg2Len and pageNum == 2:
                if counter == 0:
                    pdfTemplate2.parse_csv(
                        "salesforcedata/attyack_get_merged_templates/fpdpf_template_pg_2.csv"
                    )
                    pdfTemplate2.add_page()
                    pdfTemplate2[pg2elementNames[counter]] = linemap[key]
                    counter += 1

                elif counter == pg2Len - 1:
                    pdfTemplate2[pg2elementNames[counter]] = linemap[key]
                    pageNum = 3
                    counter = 0
                else:
                    pdfTemplate2[pg2elementNames[counter]] = linemap[key]
                    counter += 1
            elif counter >= 0 and counter < pg3Len and pageNum == 3:
                if counter == 0:
                    pdfTemplate3.parse_csv(
                        "salesforcedata/attyack_get_merged_templates/fpdpf_template_pg_3.csv"
                    )
                    pdfTemplate3.add_page()
                    pdfTemplate3[pg3elementNames[counter]] = linemap[key]
                    counter += 1
                elif counter == 1:
                    pdfTemplate3[pg3elementNames[counter]] = linemap[key]
                    counter += 1
                    template3oldy2 = pdfTemplate3.elements[1]['y2'] + 5
            elif counter >= pg3Len and pageNum == 3 and linemap[key] != '':
                newy2 = template3oldy2 + 4
                pdfTemplate3.elements.append(
                    {
                        'name': 'row' + str(counter),
                        'type': 'T',
                        'x1': 25.00,
                        'y1': template3oldy2,
                        'x2': 193.0,
                        'y2': newy2,
                        'font': 'times',
                        'size': 12,
                        'bold': 0,
                        'italic': 0,
                        'underline': 0,
                        'foreground': 0,
                        'background': 0,
                        'align': 'C',
                        'text': linemap[key],
                        'priority': 0,
                    }
                )
                template3oldy2 = newy2 + 1
                counter += 1

        # merge pdf contents
        base64pdfcontent = self.merge_pdfs(
            pdfTemplate, pdfTemplate2, pdfTemplate3
        )

        # Return the PDF content as a string
        return base64pdfcontent

    # output base64 content
    def merge_pdfs(self, pdfTemplate1, pdfTemplate2, pdfTemplate3):
        # create 2 pdf files and then open them
        pdfTemplate1.render(
            'salesforcedata/attyack_get_merged_templates/pg1.pdf'
        )
        pdfTemplate2.render(
            'salesforcedata/attyack_get_merged_templates/pg2.pdf'
        )
        pdfTemplate3.render(
            'salesforcedata/attyack_get_merged_templates/pg3.pdf'
        )

        # Create a PDF writer object
        pdf_writer = PyPDF2.PdfWriter()

        # Add the pages the PDFs to the writer
        with (
            open(
                'salesforcedata/attyack_get_merged_templates/pg1.pdf', 'rb'
            ) as fileOne,
            open(
                'salesforcedata/attyack_get_merged_templates/pg2.pdf', 'rb'
            ) as fileTwo,
            open(
                'salesforcedata/attyack_get_merged_templates/pg3.pdf', 'rb'
            ) as fileThree,
        ):
            pdf_reader1 = PyPDF2.PdfReader(fileOne)
            for page_num in range(len(pdf_reader1.pages)):
                page = pdf_reader1.pages[page_num]
                pdf_writer.add_page(page)

            pdf_reader2 = PyPDF2.PdfReader(fileTwo)
            for page_num in range(len(pdf_reader2.pages)):
                page = pdf_reader2.pages[page_num]
                pdf_writer.add_page(page)

            pdf_reader3 = PyPDF2.PdfReader(fileThree)
            for page_num in range(len(pdf_reader3.pages)):
                page = pdf_reader3.pages[page_num]
                pdf_writer.add_page(page)

        # Create a byte stream for the merged PDF
        merged_pdf_stream = io.BytesIO()
        pdf_writer.write(merged_pdf_stream)
        # pdf_writer.write('salesforcedata/attyack_get_merged_templates/merged_test.pdf')

        pdf_writer.close()

        # Convert the merged PDF to base64
        merged_pdf_base64 = base64.b64encode(
            merged_pdf_stream.getvalue()
        ).decode('utf-8')
        return merged_pdf_base64

    def getOutputData(self):
        return self.outputData
