DROP TABLE IF EXISTS integration.dev.gain_id_map;
CREATE TABLE integration.dev.gain_id_map (
    gainid VARCHAR(50) PRIMARY KEY,
    gain_createddatetime TIMESTAMP,
    gain_modifieddatetime TIMESTAMP,
    salesforce_id VARCHAR(50),
    salesforce_createddatetime TIMESTAMP,
    salesforce_modifieddatetime TIMESTAMP,
    salesforce_external_container_id VARCHAR(50),
    salesforce_external_container_createddatetime TIMESTAMP,
    salesforce_external_container_modifieddatetime TIMESTAMP,
    ati_id VARCHAR(100),
    ati_createddatetime TIMESTAMP,
    ati_modifieddatetime TIMESTAMP,
    jopari_id VARCHAR(100),
    jopari_createddatetime TIMESTAMP,
    jopari_modifieddatetime TIMESTAMP,
    filevine_id VARCHAR(100),
    filevine_createddatetime TIMESTAMP,
    filevine_modifieddatetime TIMESTAMP,
    canonical_object VARCHAR(50)
)
DISTSTYLE KEY
DISTKEY (gainid)
COMPOUND SORTKEY (gainid, ati_id, jopari_id, salesforce_external_container_id, salesforce_id, filevine_id);


DROP TABLE IF EXISTS integration.staging.gain_id_map;
CREATE TABLE integration.staging.gain_id_map (
    gainid VARCHAR(50) PRIMARY KEY,
    gain_createddatetime TIMESTAMP,
    gain_modifieddatetime TIMESTAMP,
    salesforce_id VARCHAR(50),
    salesforce_createddatetime TIMESTAMP,
    salesforce_modifieddatetime TIMESTAMP,
    salesforce_external_container_id VARCHAR(50),
    salesforce_external_container_createddatetime TIMESTAMP,
    salesforce_external_container_modifieddatetime TIMESTAMP,
    ati_id VARCHAR(100),
    ati_createddatetime TIMESTAMP,
    ati_modifieddatetime TIMESTAMP,
    jopari_id VARCHAR(100),
    jopari_createddatetime TIMESTAMP,
    jopari_modifieddatetime TIMESTAMP,
    filevine_id VARCHAR(100),
    filevine_createddatetime TIMESTAMP,
    filevine_modifieddatetime TIMESTAMP,
    canonical_object VARCHAR(50)
)
DISTSTYLE KEY
DISTKEY (gainid)
COMPOUND SORTKEY (gainid, ati_id, jopari_id, salesforce_external_container_id, salesforce_id, filevine_id);


DROP TABLE IF EXISTS integration.prod.gain_id_map;
CREATE TABLE integration.prod.gain_id_map (
    gainid VARCHAR(50) PRIMARY KEY,
    gain_createddatetime TIMESTAMP,
    gain_modifieddatetime TIMESTAMP,
    salesforce_id VARCHAR(50),
    salesforce_createddatetime TIMESTAMP,
    salesforce_modifieddatetime TIMESTAMP,
    salesforce_external_container_id VARCHAR(50),
    salesforce_external_container_createddatetime TIMESTAMP,
    salesforce_external_container_modifieddatetime TIMESTAMP,
    ati_id VARCHAR(100),
    ati_createddatetime TIMESTAMP,
    ati_modifieddatetime TIMESTAMP,
    jopari_id VARCHAR(100),
    jopari_createddatetime TIMESTAMP,
    jopari_modifieddatetime TIMESTAMP,
    filevine_id VARCHAR(100),
    filevine_createddatetime TIMESTAMP,
    filevine_modifieddatetime TIMESTAMP,
    canonical_object VARCHAR(50)
)
DISTSTYLE KEY
DISTKEY (gainid)
COMPOUND SORTKEY (gainid, ati_id, jopari_id, salesforce_external_container_id, salesforce_id, filevine_id);


DROP TABLE IF EXISTS integration.main.gain_id_map;
CREATE TABLE integration.main.gain_id_map (
    gainid VARCHAR(50) PRIMARY KEY,
    gain_createddatetime TIMESTAMP,
    gain_modifieddatetime TIMESTAMP,
    salesforce_id VARCHAR(50),
    salesforce_createddatetime TIMESTAMP,
    salesforce_modifieddatetime TIMESTAMP,
    salesforce_external_container_id VARCHAR(50),
    salesforce_external_container_createddatetime TIMESTAMP,
    salesforce_external_container_modifieddatetime TIMESTAMP,
    ati_id VARCHAR(100),
    ati_createddatetime TIMESTAMP,
    ati_modifieddatetime TIMESTAMP,
    jopari_id VARCHAR(100),
    jopari_createddatetime TIMESTAMP,
    jopari_modifieddatetime TIMESTAMP,
    filevine_id VARCHAR(100),
    filevine_createddatetime TIMESTAMP,
    filevine_modifieddatetime TIMESTAMP,
    canonical_object VARCHAR(50)
)
DISTSTYLE KEY
DISTKEY (gainid)
COMPOUND SORTKEY (gainid, ati_id, jopari_id, salesforce_external_container_id, salesforce_id, filevine_id);


