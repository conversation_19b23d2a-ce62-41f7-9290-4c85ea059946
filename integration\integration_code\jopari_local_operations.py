import copy
import datetime
import hashlib
import itertools
import json
import math
import pathlib
import typing

import numpy as np
import pandas as pd
import PyPDF2

from . import aws_operations, id_record, local_operations, shared
from .utils import file_utils

tpg_medicalfacilities = [
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
    '**********',
]

tpg_root_medicalfacility_id = 'fe5b7244b5bf406a'

with (
    open(
        'integration/integration_code/canonical_model.json', 'rb'
    ) as canonical_map_file,
    open(
        'integration/integration_code/canonical_data_model.json', 'rb'
    ) as canonical_data_map_file,
    open(
        'integration/integration_code/state_name_mapping.json',
        'rb',
    ) as state_name_mapping_file,
):
    canonical = json.load(canonical_map_file)
    canonical_data = json.load(canonical_data_map_file)
    state_name_mapping = json.load(state_name_mapping_file)
    state_code_to_name_map = state_name_mapping["state_code_to_name_map"]
    state_name_to_code_map = state_name_mapping["state_name_to_code_map"]


def generate_hash_from_row(
    row: pd.Series,  # pyright: ignore[reportMissingTypeArgument]
    fields: list[str],
) -> str:
    combined_string = ''
    for field in fields:
        if field not in row or row[field] is None:
            return ''

        if (
            field in ('servicefacilitystate', 'billingproviderstate')
            and row.get(field) in state_code_to_name_map
        ):
            value = state_code_to_name_map[row[field]]
        elif field in ('servicefacilityaddress', 'billingprovideraddress'):
            value = local_operations.extract_address_number(row[field])
        elif field in ('servicefacilityzip', 'billingproviderzipcode'):
            value = local_operations.extract_zip_code(row[field])
        else:
            value = row[field]
        combined_string += str(value)

    clean_string = local_operations.remove_nonalphanumeric_characters(
        combined_string.lower()
    )
    return local_operations.get_md5_hash(clean_string).title()


def parse_location_line(location_line: str):
    """
    Parses a location line expected to contain city, state, and zip.
    Supports two formats:
      Format 1: "CITY, STATE ZIP" (e.g., "MERCED, CA 953403702")
      Format 2: "CITY STATE ZIP"  (e.g., "DALLAS TX 753120490")
    Returns a tuple (city, state, zip_code).
    """
    if ',' in location_line:
        parts = location_line.split(',')
        if len(parts) >= 2:
            city = parts[0].strip()
            state_zip = parts[1].strip()
            tokens = state_zip.split()
            if len(tokens) >= 2:
                return city, tokens[0], tokens[1]
    # Fallback: assume the last two tokens are state and zip.
    tokens = location_line.split()
    if len(tokens) >= 3:
        return " ".join(tokens[:-2]), tokens[-2], tokens[-1]
    return "", "", ""


def extract_header_block(lines: list[str]):
    """
    Extracts a header block starting at line 8 (index 7).

    Expected patterns:
      - 1-line block: [firm name only]
      - 4-line block: [location, addressline1, firm name, numeric]
      - 5-line block: [location, addressline1, addressline2, firm name, numeric]
    """
    candidate = lines[7:12]

    if len(candidate) >= 1 and candidate[1].replace(" ", "").isdigit():
        return [candidate[0]]  # Only firm name
    elif len(candidate) >= 5 and candidate[4].replace(" ", "").isdigit():
        return candidate[:5]
    elif len(candidate) >= 4 and candidate[3].replace(" ", "").isdigit():
        return candidate[:4]

    raise ValueError("Header block does not match expected patterns.")


def extract_address_from_pdf(pdf_path: str):
    """
    Reads a PDF file and extracts header information starting at line 8.

    Expected header layout (after skipping the first 7 nonempty lines):
      - Line 8: Location info (city, state, zip)
      - Next 1 or 2 lines: Address lines
      - Next line: Firm (provider) name (this line is immediately before a numeric line)
      - Final line: Numeric value (ignored)

    Returns a dictionary with the keys:
      "city", "state", "zip", "addressline1", "addressline2", and "name".

    If any required field (city, state, zip, or addressline1) is empty or parsing fails,
    the method returns None.
    """
    # Step 1: Read PDF and extract text from all pages.
    full_text = ""
    with open(pdf_path, "rb") as f:
        reader = PyPDF2.PdfReader(f)
        for page in reader.pages:
            page_text = page.extract_text()
            if page_text:
                full_text += page_text + "\n"

    # Do not crop off any text; we only care about skipping the first 7 lines.
    # Step 2: Split text into nonempty lines.
    lines = [line.strip() for line in full_text.splitlines() if line.strip()]

    if len(lines) < 8:
        return None  # Not enough lines

    # Step 3: Extract the header block (starting at line 8, index 7).
    try:
        header = extract_header_block(lines)
    except Exception:
        return None

    if len(header) == 1:
        return {
            "city": '',
            "state": '',
            "zip": '',
            "addressline1": '',
            "addressline2": '',
            "name": header[1],
        }  # Parse the location info from the first header line.

    city, state, zip_code = parse_location_line(header[0])

    # Determine address lines and firm name based on header block length.
    if len(header) == 4:
        # Pattern: [location, addressline1, firm name, numeric]
        addressline1 = header[1]
        addressline2 = ""
        firm_name = header[2]
    elif len(header) == 5:
        # Pattern: [location, addressline1, addressline2, firm name, numeric]
        addressline1 = header[1]
        addressline2 = header[2]
        firm_name = header[3]
    else:
        return None  # Unexpected header block length.

    # Return None if any required field is empty.
    if not city or not state or not zip_code or not addressline1:
        return None

    return {
        "city": city,
        "state": state,
        "zip": zip_code,
        "addressline1": addressline1,
        "addressline2": addressline2,
        "name": firm_name,
    }


def extract_attorney_info_from_s3_pdfs(
    s3_pdf_paths: list[str],
) -> pd.DataFrame:
    """
    Extracts attorney details from a list of PDF files stored on S3 and returns a DataFrame.

    Args:
        s3_pdf_paths (list): A list of S3 object keys (paths within the bucket).
        bucket_name (str): The name of the S3 bucket.

    Returns:
        pd.DataFrame: DataFrame containing extracted attorney details along with filenames.
    """
    extracted_data = []

    for s3_key in s3_pdf_paths:
        try:
            # Extract filename from the path
            filename = pathlib.Path(s3_key).name

            # Extract attorney details
            extracted_address = extract_address_from_pdf(s3_key)

            if extracted_address:  # Only add if extraction is successful
                extracted_data.append(
                    {
                        "filename": filename,
                        "attorneyfirm": extracted_address.get("name", ""),
                        "attorneyaddress1": extracted_address.get(
                            "addressline1", ""
                        ),
                        "attorneyaddress2": extracted_address.get(
                            "addressline2", ""
                        ),
                        "attorneycity": extracted_address.get("city", ""),
                        "attorneystate": extracted_address.get("state", ""),
                        "attorneyzip": extracted_address.get("zip", ""),
                        "attorneyphone": "",
                        "attorneyfirstname": "",
                        "attorneylastname": "",
                    }
                )
        except Exception as e:
            print(
                f"Failed to process {s3_key}: {str(e)}"
            )  # Logging error (optional)

    return pd.DataFrame(extracted_data)


def jopari_generate_plaintiffs_upsert_fallback_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input['name'] = (
        df_input['insuredfirstname'].fillna('')
        + ' '
        + df_input['insuredlastname'].fillna('')
    )

    columns_mapping = {
        'patientcontrolnumber': 'sourceid',
        'insuredgender': 'gender',
        'insuredssn': 'ssn',
        'insuredstreetaddress': 'primaryaddressline1',
        'insuredcity': 'primaryaddresscity',
        'insuredstate': 'primaryaddressstate',
        'insuredzip': 'primaryaddresszip',
        'insuredbirthdate': 'dateofbirth',
    }

    df_input.rename(
        columns=columns_mapping,
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_input['dateofbirth'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['dateofbirth'], inplace=True
    )  # remove NaN source id rows
    df_input['dateofbirth'] = pd.to_datetime(df_input['dateofbirth'])
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]

    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='plaintiffs'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    df_output.drop(
        columns=['sourcemodifieddatetime', 'sourcecreatedatetime'],
        inplace=True,
    )

    return df_output


def jopari_generate_plaintiffs_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input['name'] = (
        df_input['patientfirstname'].fillna('')
        + ' '
        + df_input['patientlastname'].fillna('')
    )

    columns_mapping = {
        'patientaccountnumber': 'sourceid',
        'patientgendercode': 'gender',
        'patientaddressinformation': 'primaryaddressline1',
        'patientadditionaladdressinformation': 'primaryaddressline2',
        'patientcityname': 'primaryaddresscity',
        'patientstateorprovincecode': 'primaryaddressstate',
        'patientpostalcode': 'primaryaddresszip',
        'patientdateofbirth': 'dateofbirth',
        'patientssn': 'ssn',
    }

    df_input.rename(
        columns=columns_mapping,
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_input['dateofbirth'] = pd.to_datetime(df_input['dateofbirth'])
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]

    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='plaintiffs'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    df_output.drop(
        columns=['sourcemodifieddatetime', 'sourcecreatedatetime'],
        inplace=True,
    )

    return df_output


def jopari_generate_medicalfacilities_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)

    df_input['relevanttogain'] = True

    df_input['sourceid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            [
                'servicefacilityaddress',
                'servicefacilitycity',
                'servicefacilitystate',
                'servicefacilityzip',
            ],
        ),
        axis=1,
    )

    if 'npi' in df_input.columns:
        df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'parentid'
        ] = tpg_root_medicalfacility_id

    column_mapping = {
        'servicefacilityaddress': 'physicaladdressline1',
        'servicefacilitycity': 'physicaladdresscity',
        'servicefacilitystate': 'physicaladdressstate',
        'servicefacilityzip': 'physicaladdresszip',
    }

    # Check if 'servicefacilityname' column exists in df_input
    if 'servicefacilityname' in df_input.columns:
        column_mapping['servicefacilityname'] = 'name'
    else:
        df_input['name'] = None

    df_input.rename(
        columns=column_mapping,
        inplace=True,
    )

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_input['name'] = df_input.apply(
        lambda row: (
            row['name']
            if pd.notna(row.get('name')) and row['name'].strip() != ''
            else 'Jopari #' + row['sourceid'][:8]
        ),
        axis=1,
    )
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]

    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='medicalfacilities'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    df_output.drop(
        columns=['sourcemodifieddatetime', 'sourcecreatedatetime'],
        inplace=True,
    )

    return df_output


def jopari_generate_partneraccounts_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)

    df_input['relevanttogain'] = True

    df_input['sourceid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            [
                'billingprovideraddress',
                'billingprovidercity',
                'billingproviderstate',
                'billingproviderzipcode',
            ],
        ),
        axis=1,
    )

    if 'npi' in df_input.columns:
        df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'parentid'
        ] = tpg_root_medicalfacility_id

    column_mapping = {
        'billingprovideraddress': 'physicaladdressline1',
        'billingprovidercity': 'physicaladdresscity',
        'billingproviderstate': 'physicaladdressstate',
        'billingproviderzipcode': 'physicaladdresszip',
    }

    # Check if 'servicefacilityname' column exists in df_input
    if 'billingprovidername' in df_input.columns:
        column_mapping['billingprovidername'] = 'name'
    else:
        df_input['name'] = None

    df_input.rename(
        columns=column_mapping,
        inplace=True,
    )

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows

    df_input.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_input['name'] = df_input.apply(
        lambda row: (
            row['name']
            if pd.notna(row.get('name')) and row['name'].strip() != ''
            else 'Jopari #' + row['sourceid'][:8]
        ),
        axis=1,
    )

    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]

    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='medicalfacilities'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    df_output.drop(
        columns=['sourcemodifieddatetime', 'sourcecreatedatetime'],
        inplace=True,
    )

    return df_output


def jopari_generate_cases_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    local_files: list[str],
    file_to_legalpersonnel_map: dict[typing.Any, str] | None,
    timestamp: str,
):
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True

    df_input['plaintiffname'] = (
        df_input['patientfirstname'].fillna('')
        + ' '
        + df_input['patientlastname'].fillna('')
    )

    df_input['sourceid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            ['patientaccountnumber', 'illnessinjurydate'],
        ),
        axis=1,
    )

    def extract_filenames_from_attachments(attachments: str) -> list[str]:
        try:
            attachment_list = json.loads(attachments)
            return [
                attachment['AttachmentControlNumber_06']
                for attachment in attachment_list
                if 'AttachmentControlNumber_06' in attachment
            ]
        except (json.JSONDecodeError, TypeError):
            return []

    # Apply extraction function to attachments column

    df_input['filenames'] = df_input['attachments'].apply(
        extract_filenames_from_attachments
    )

    df_input['matching_files'] = df_input['filenames'].apply(
        lambda filenames_list: (
            [
                file
                for file in local_files
                if any(
                    pathlib.Path(file).name.startswith(prefix)
                    for prefix in filenames_list
                )
            ]
            if filenames_list
            else []
        )
    )

    file_to_legalpersonnel_map_lower = {
        file.lower(): value
        for file, value in (file_to_legalpersonnel_map or {}).items()
    }

    # Group by 'sourceid', keeping the last occurrence while merging 'matching_files'
    df_input = df_input.groupby('sourceid', as_index=False).agg(
        {
            'matching_files': lambda x: list(
                set(itertools.chain.from_iterable(x))
            ),  # Flatten and remove duplicates
            **{
                col: 'last'
                for col in df_input.columns
                if col != 'matching_files' and col != 'sourceid'
            },  # Keep last values
        }
    )

    if (
        'npi' in df_input.columns
        and df_input['npi'].isin(tpg_medicalfacilities).any()
    ):
        df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'attorneyid'
        ] = df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'matching_files'
        ].apply(
            lambda files: next(
                [
                    file_to_legalpersonnel_map_lower[
                        pathlib.Path(file.lower()).name
                    ]
                ][0]
                for file in (files or [])
                if pathlib.Path(file.lower()).name
                in file_to_legalpersonnel_map_lower
            )
        )

        df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'paralegalid'
        ] = df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'attorneyid'
        ]

        df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'casemanagerid'
        ] = df_input.loc[
            df_input['npi'].isin(tpg_medicalfacilities), 'attorneyid'
        ]

    columns_mapping = {
        'patientaccountnumber': 'plaintiffid',
        'illnessinjurydate': 'accidentdate',
    }

    df_input.rename(
        columns=columns_mapping,
        inplace=True,
    )

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]

    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols
            and df_input[col].dtype == 'object'
            and col not in ('attorneyid', 'paralegalid', 'casemanagerid')
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if (
                        'source' not in col
                        and 'datetime' in col
                        and 'servicingend' not in col
                    )
                    else np.nan
                )
            )
        )

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    (
        df_output['plaintiffdateofbirth'],
        df_output['accidentdate'],
    ) = pd.to_datetime(df_input['patientdateofbirth']), pd.to_datetime(
        df_input['accidentdate']
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='cases'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.drop(
        columns=['sourcemodifieddatetime', 'sourcecreatedatetime'],
        inplace=True,
    )

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'plaintiffid',
        'plaintiffs',
        aws_operations.get_gainids_by_jopari_ids,
    )

    columns_to_update = [
        'cocounselid',
        'coparalegalid',
        'cocasemanagerid',
    ]

    # Update each column
    for column in columns_to_update:
        local_operations.assign_gainids_to_dataframe_column(
            df_output,
            column,
            'legalpersonnel',
            aws_operations.get_gainids_by_jopari_ids,
        )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def jopari_generate_lawfirms_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    bucket: str,
    directory: str,
    timestamp: str,
) -> tuple[pd.DataFrame, dict[typing.Any, str]]:
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input['attorneystate'] = df_input['attorneystate'].apply(
        lambda x: str(x).upper() if pd.notnull(x) else x
    )
    df_input = df_input.replace(r'^\s*$', np.nan, regex=True)
    attorney_lawfirm_map = (
        aws_operations.get_legalpersonnel_lawfirm_sourceid_map('Jopari')
    )
    manual_review_indices = df_input[
        (
            df_input['attorneyaddress1'].isna()
            | df_input['attorneycity'].isna()
            | df_input['attorneystate'].isna()
            | df_input['attorneyzip'].isna()
        )
    ].index.tolist()
    df_input_manual_review_missing_info = df_input.loc[manual_review_indices]

    if not df_input_manual_review_missing_info.empty:
        timestamp_string = timestamp.replace(' ', '_').replace(':', '-')
        date_string = datetime.datetime.now().strftime('%Y-%m-%d')
        filename = f'integration/integration_code/missing_info_manual_review_{timestamp_string}.csv'
        df_input_manual_review_missing_info.to_csv(filename, index=False)
        aws_operations.upload_s3(
            bucket,
            f'{directory}/lawfirms_manual_review/' + date_string,
            filename,
        )
        file_utils.clean_up_file(filename)
    df_input.drop(manual_review_indices, inplace=True)
    rs_lawfirm_data = aws_operations.get_rs_lawfirm_data_sourceid_map('Jopari')
    df_input['sourceid'] = None
    for row in df_input.itertuples(index=True):
        index = row.Index  # Extract index from the named tuple

        # If we have a pre-existing mapping for the attorney in RS, use the lawfirm source id from the mapping.
        # This logic is in place because we can reliably use attorney id to determine the law firms. If an attorney
        # changes law firms, then a new attorney id will be assigned to them. Thus, if attorney id is the same and
        # law firm id changes, we assume that new law firm id is a duplicate. This is useful in cases where the law
        # address changes, but the attorney id remains the same. In such cases, we can avoid creating unnecessary
        # duplicates by identifying the lawfirm using the attorney id.
        if (
            'attorneythirdpartyattorneyid' in row
            and (row.attorneythirdpartyattorneyid is not None)
            and (row.attorneythirdpartyattorneyid != '')
            and (
                row.attorneythirdpartyattorneyid
                in attorney_lawfirm_map['sourceid'].tolist()
            )
        ):
            df_input.at[index, 'sourceid'] = shared.get_first_value(
                attorney_lawfirm_map.loc[
                    (
                        (
                            attorney_lawfirm_map['sourceid']
                            == row.attorneythirdpartyattorneyid
                        )
                        & (attorney_lawfirm_map['sourcename'] == 'Jopari')
                    ),
                    'lawfirmsourceid',
                ]
            )
        else:
            row_attorneyphone = (
                local_operations.remove_nonalphanumeric_characters(
                    str(row.attorneyphone)
                )
                if row.attorneyphone is not None
                else None
            )
            if (
                (row_attorneyphone is not None)
                and (row_attorneyphone != '')
                and (row_attorneyphone in rs_lawfirm_data['phone'].tolist())
            ):
                # Skip the law firm ingestion if the law firm with same phone number already exists in RS
                continue
            else:
                df_input.at[index, 'sourceid'] = hashlib.sha1(
                    local_operations.remove_nonalphanumeric_characters(
                        str(row.attorneyaddress1).split(' ')[0]
                        + str(row.attorneycity)
                        + state_name_to_code_map.get(
                            str(row.attorneystate).title(),
                            str(row.attorneystate),
                        )
                        + str(row.attorneyzip)
                        + str(row.attorneyphone)
                    )
                    .lower()
                    .encode('utf-8')
                ).hexdigest()[:20]
    # If the attorneyfirm is missing, we will use the attorneyfirstname and attorneylastname to create the lawfirm name.
    blank_attorney_firm_indices = df_input[
        df_input['attorneyfirm'].isna()
    ].index.tolist()

    if blank_attorney_firm_indices:
        df_input.loc[blank_attorney_firm_indices, 'attorneyfirm'] = (
            df_input.loc[blank_attorney_firm_indices, 'attorneyfirstname']
            + ' '
            + df_input.loc[blank_attorney_firm_indices, 'attorneylastname']
        )

    df_input.rename(
        columns={
            'attorneyfirm': 'name',
            'attorneyaddress1': 'billingaddressline1',
            'attorneyaddress2': 'billingaddressline2',
            'attorneycity': 'billingaddresscity',
            'attorneystate': 'billingaddressstate',
            'attorneyzip': 'billingaddresszip',
            'attorneyphone': 'phone',
            'attorneyfax': 'fax',
            'filename': 'notes',
        },
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols
            and df_input[col].dtype == 'object'
            and col != 'sourceid'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    filename_to_sourceid_map = df_output.set_index('notes')[
        'sourceid'
    ].to_dict()

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    # Assigned False manually since default value is not assigned if the column exists in csv
    df_output['todelete'] = False
    df_output['deletepreventoverride'] = False

    # Code to Get createdatetime from redshift. This is to be removed and replaced with a more efficient solution.
    # lawfirms_ids = set([(x, y) for x, y in zip(df_output['sourceid'], df_output['sourcename'])])
    # rs_createdatetime = aws_operations.get_redshift_createdatetime(
    #     'LawFirms', lawfirms_ids
    # )
    # if not rs_createdatetime.empty:
    #     rs_createdatetime['tuple_key'] = rs_createdatetime.apply(lambda x: (x['sourceid'], x['sourcename']), axis=1)
    #     rs_createdatetime_dict = rs_createdatetime.dropna(subset=['createdatetime']).set_index('tuple_key')['createdatetime'].to_dict()
    # else:
    #     rs_createdatetime_dict = {}
    # df_output['createdatetime'] = df_output.apply(lambda x: rs_createdatetime_dict.get((x['sourceid'], x['sourcename']), x['createdatetime']), axis=1)
    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='lawfirms'
    )

    # Assign the returned gainids to the DataFrame
    sourceid_to_gainid_map = dict(zip(df_output['sourceid'], gainids))

    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    filename_to_gainid_map = {
        filename: sourceid_to_gainid_map[sourceid]
        for filename, sourceid in filename_to_sourceid_map.items()
        if sourceid in sourceid_to_gainid_map
    }

    df_output['notes'] = None

    columns_to_drop = [
        'sourcemodifieddatetime',
        'sourcecreatedatetime',
    ]

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output, filename_to_gainid_map


def jopari_generate_legalpersonnel_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    filename_to_gainid_map: dict[typing.Any, str] | None,
    timestamp: str,
) -> tuple[pd.DataFrame, dict[typing.Any, str] | None]:
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True

    df_input.rename(
        columns={
            'name': 'lawfirmname',
            'billingaddressline1': 'otheraddressline1',
            'billingaddressline2': 'otheraddressline2',
            'billingaddresscity': 'otheraddresscity',
            'billingaddressstate': 'otheraddressstate',
            'billingaddresszip': 'otheraddresszip',
            'gainid': 'lawfirmid',
        },
        inplace=True,
    )

    df_input['name'] = 'Default Legal ' + df_input['lawfirmname'].fillna(
        ''
    ).astype(str)
    df_input['sourceid'] = df_input['lawfirmid'].fillna('').astype(str) + '_LP'

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols
            and df_input[col].dtype == 'object'
            and col != 'lawfirmid'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    # Assigned False manually since default value is not assigned if the column exists in csv
    df_output['todelete'] = False
    df_output['deletepreventoverride'] = False
    # Code to Get createdatetime from redshift. This is to be removed and replaced with a more efficient solution.
    # legalpersonnel_ids = set([(x, y) for x, y in zip(df_output['sourceid'], df_output['sourcename'])])
    # rs_createdatetime = aws_operations.get_redshift_createdatetime(
    #     'LegalPersonnel', legalpersonnel_ids
    # )
    # if not rs_createdatetime.empty:
    #     rs_createdatetime['tuple_key'] = rs_createdatetime.apply(lambda x: (x['sourceid'], x['sourcename']), axis=1)
    #     rs_createdatetime_dict = rs_createdatetime.dropna(subset=['createdatetime']).set_index('tuple_key')['createdatetime'].to_dict()
    # else:
    #     rs_createdatetime_dict = {}
    # df_output['createdatetime'] = df_output.apply(lambda x: rs_createdatetime_dict.get((x['sourceid'], x['sourcename']), x['createdatetime']), axis=1)
    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='legalpersonnel'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    lawfirmid_to_gainid_map = df_output.set_index('lawfirmid')[
        'gainid'
    ].to_dict()

    if filename_to_gainid_map is not None:
        filename_to_gainid_map = {
            filename: lawfirmid_to_gainid_map[lawfirmid]
            for filename, lawfirmid in filename_to_gainid_map.items()
            if lawfirmid in lawfirmid_to_gainid_map
        }

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output, filename_to_gainid_map


def jopari_generate_insurances_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)

    df_input['sourceid'] = df_input.apply(
        lambda row: ('JopariInsurer' + row['patientaccountnumber']),
        axis=1,
    )

    columns_mapping = {
        'insuredstreetaddress': 'billingaddressline1',
        'insuredcity': 'billingaddresscity',
        'insuredstate': 'billingaddressstate',
        'insuredzip': 'billingaddresszip',
    }

    df_input['companyname'] = (
        df_input['insuredfirstname'].fillna('')
        + ' '
        + df_input['insuredlastname'].fillna('')
    )

    # Check if 'insurancetypecode' column exists in df_input
    if 'insurancetypecode' in df_input.columns:
        columns_mapping['insurancetypecode'] = 'type'
    else:
        df_input['type'] = ''

    df_input.rename(
        columns=columns_mapping,
        inplace=True,
    )

    df_input['caseid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            ['patientaccountnumber', 'illnessinjurydate'],
        ),
        axis=1,
    )

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]

    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='insurances'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'caseid',
        'cases',
        aws_operations.get_gainids_by_jopari_ids,
    )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    df_output.drop(
        columns=['sourcemodifieddatetime', 'sourcecreatedatetime'],
        inplace=True,
    )

    return df_output

    # Function to extract the first date


def extract_first_date(date_str: str) -> datetime.date:
    first_date = date_str.split('-')[0]  # Take the first part before '-'
    return pd.to_datetime(first_date, format='%Y%m%d').date()


def jopari_generate_billings_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    (
        df_input['relevanttogain'],
        df_input['type'],
        df_input['gaintype'],
    ) = (
        True,
        'Medical Funding',
        'Medical Funding',
    )  # TBD? Make Gain Type customized based on contract type in Salesforce

    df_input['medicalclaimnumber'] = df_input['patientcontrolnumber'].str[:30]

    # case sourceid (patientaccountnumber + DOA)
    df_input['caseid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            ['patientaccountnumber', 'illnessinjurydate'],
        ),
        axis=1,
    )

    column_mapping = {
        'patientcontrolnumber': 'sourceid',
        'servicedate': 'dateofservice',
        'totalclaimchargeamount': 'totalamount',
    }

    df_input.rename(
        columns=column_mapping,
        inplace=True,
    )

    if (
        'billingdescription' in df_input.columns
        and 'claimsupplementalinformation' in df_input.columns
    ):
        df_input['notes'] = df_input['billingdescription'].combine_first(
            df_input['claimsupplementalinformation']
        )
    elif 'billingdescription' in df_input.columns:
        df_input['notes'] = df_input['billingdescription']
    elif 'claimsupplementalinformation' in df_input.columns:
        df_input['notes'] = df_input['claimsupplementalinformation']

    df_input.drop(
        columns=['billingdescription', 'claimsupplementalinformation'],
        inplace=True,
        errors='ignore',
    )

    if 'totalamount' in df_input.columns:
        df_input['totalamount'] = (
            df_input['totalamount'].astype(float).round(4)
        )
    else:
        df_input['totalamount'] = None

    # Only process when amountpaidbypatient when it exists (in prof file but not inst file)
    if 'amountpaidbypatient' in df_input.columns:
        column_mapping['amountpaidbypatient'] = (
            'totalnongainamountpaidtoprovider'
        )
    else:
        df_input['totalnongainamountpaidtoprovider'] = None

    if 'totalnongainadjustment' in df_input.columns:
        df_input['totalnongainadjustment'] = (
            df_input['totalnongainadjustment'].astype(float).round(4)
        )
    else:
        df_input['totalnongainadjustment'] = None

    if 'totalnongainamountpaidtoprovider' in df_input.columns:
        df_input['totalnongainamountpaidtoprovider'] = (
            df_input['totalnongainamountpaidtoprovider'].astype(float).round(4)
        )
    else:
        df_input['totalnongainamountpaidtoprovider'] = None

    if 'totalbalance' in df_input.columns:
        df_input['totalbalance'] = (
            df_input['totalbalance'].astype(float).round(4)
        )
    else:
        df_input['totalbalance'] = None

    # Calculate totalbalance
    df_input['totalbalance'] = df_input.apply(
        lambda row: (
            (
                row['totalamount']
                - (
                    row['totalnongainadjustment']
                    if pd.notnull(row['totalnongainadjustment'])
                    else 0
                )
                - (
                    row['totalnongainamountpaidtoprovider']
                    if pd.notnull(row['totalnongainamountpaidtoprovider'])
                    else 0
                )
            )
            if pd.notnull(row['totalamount'])
            else row['totalbalance']
        ),
        axis=1,
    )

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows

    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if (
                        'source' not in col
                        and 'datetime' in col
                        and 'servicingend' not in col
                    )
                    else np.nan
                )
            )
        )

    df_output['dateofservice'] = df_output['dateofservice'].apply(
        extract_first_date
    )

    df_output['medicalfacilityid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            [
                'servicefacilityaddress',
                'servicefacilitycity',
                'servicefacilitystate',
                'servicefacilityzip',
            ],
        ),
        axis=1,
    )

    df_output['partneraccountid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            [
                'billingprovideraddress',
                'billingprovidercity',
                'billingproviderstate',
                'billingproviderzipcode',
            ],
        ),
        axis=1,
    )

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    # once duplicate rows are dropped, only unique billings gain ids
    # remain and it will be used to combining with medical facility dataframe

    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='billings'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'caseid',
        'cases',
        aws_operations.get_gainids_by_jopari_ids,
    )

    columns_to_update = ['medicalfacilityid', 'partneraccountid']

    # Update each column
    for column in columns_to_update:
        local_operations.assign_gainids_to_dataframe_column(
            df_output,
            column,
            'medicalfacilities',
            aws_operations.get_gainids_by_jopari_ids,
        )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def jopari_generate_charges_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True

    df_input['serviceprocedurecode'] = df_input.apply(
        lambda row: (
            (
                str(row.get('serviceprocedurecode', ''))
                + '#'
                + str(row.get('serviceprocedurecodemodifier', ''))
            )
            if row.get('serviceprocedurecodequalifier')
            is not None  # Check if not None
            and pd.notna(
                row.get('serviceprocedurecodequalifier')
            )  # Check if not NaN
            else row.get(
                'serviceprocedurecode', ''
            )  # Otherwise, return just the serviceprocedurecode
        ),
        axis=1,
    )

    column_mapping = {
        'patientcontrolnumber': 'billingid',
        'servicedate': 'dateofservice',
        'chargeamount': 'amount',
        'serviceprocedurecode': 'rawcptcode',
        'serviceunitsbilled': 'quantity',
        'priorpaymentamount': 'nongainamountpaidtoprovider',
    }

    if 'chargeamount' in df_input.columns:
        column_mapping['chargeamount'] = 'amount'
    else:
        df_input['amount'] = None

    if 'priorpaymentamount' in df_input.columns:
        column_mapping['priorpaymentamount'] = 'nongainamountpaidtoprovider'
    else:
        df_input['nongainamountpaidtoprovider'] = None

    df_input.rename(
        columns=column_mapping,
        inplace=True,
    )

    def parse_cptcode(json_string: str) -> str:
        if (
            not json_string
            or (isinstance(json_string, float) and math.isnan(json_string))
            or not local_operations.is_valid_json(json_string)
        ):
            return local_operations.clean_cpt_code(json_string)

        try:
            data = json.loads(json_string)
            return local_operations.clean_cpt_code(
                data.get('ProcedureCode_02', '')
            )
        except json.JSONDecodeError:
            return json_string

    def parse_cptmodifier(json_string: str) -> str:
        if (
            not json_string
            or (isinstance(json_string, float) and math.isnan(json_string))
            or not local_operations.is_valid_json(json_string)
        ):
            try:
                return json_string.split('#')[1]
            except:
                return json_string

        try:
            data = json.loads(json_string)
            return data.get('ProcedureModifier_03', '')
        except json.JSONDecodeError:
            return json_string

    df_input['cptcode'] = df_input['rawcptcode'].apply(parse_cptcode)
    df_input['cptmodifier'] = df_input['rawcptcode'].apply(parse_cptmodifier)

    df_input['sourceid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            [
                'billingid',
                'illnessinjurydate',
                'dateofservice',
                'cptmodifier',
                'cptcode',
                'amount',
            ],
        ),
        axis=1,
    )

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows

    if 'amount' in df_input.columns:
        df_input['amount'] = df_input['amount'].astype(float).round(4)
    else:
        df_input['amount'] = None

    if 'nongainamountpaidtoprovider' in df_input.columns:
        df_input['nongainamountpaidtoprovider'] = (
            df_input['nongainamountpaidtoprovider'].astype(float).round(4)
        )
    else:
        df_input['nongainamountpaidtoprovider'] = None

    df_input['dateofservice'] = pd.to_datetime(df_input['dateofservice'])
    # normal set operation will yield col in a random order during iteration and will cause a conflict with df
    # if createdatetitme/modifieddatetime come before other cols in df_input, it will try to add a scalar into an empty df, causing nan
    # Instead of directly converting, I utilized a set to keep track of the duplicate columns while preserving the column order.
    # This will ensure column insertion from the original df_input to come first, defining the height of the dataframe before inserting a scalar value.

    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    df_output['dateofservice'] = pd.to_datetime(
        df_output['dateofservice'], format='%Y%m%d'
    )
    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='charges'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'billingid',
        'billings',
        aws_operations.get_gainids_by_jopari_ids,
    )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = [
        'sourcemodifieddatetime',
        'sourcecreatedatetime',
        'rawcptcode',
    ]

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def jopari_generate_files_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    df_filenames: pd.DataFrame,
    bucket: str,
    timestamp: str,
):
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True

    df_input['plaintiffid'] = df_input['patientaccountnumber']

    df_input['caseid'] = df_input.apply(
        lambda row: generate_hash_from_row(
            row,
            ['plaintiffid', 'illnessinjurydate'],
        ),
        axis=1,
    )

    # Extract AttachmentControlNumber_06 as filename from attachments field
    def extract_filenames_from_attachments(attachments: str):
        try:
            attachment_list = json.loads(attachments)
            return [
                attachment['AttachmentControlNumber_06']
                for attachment in attachment_list
                if 'AttachmentControlNumber_06' in attachment
            ]
        except (json.JSONDecodeError, TypeError):
            return []

    # Extract AttachmentReportTypeCode_01 as filename from attachments field
    def extract_filetypes_from_attachments(attachments: str):
        try:
            attachment_list = json.loads(attachments)
            return [
                attachment['AttachmentReportTypeCode_01']
                for attachment in attachment_list
                if 'AttachmentReportTypeCode_01' in attachment
            ]
        except (json.JSONDecodeError, TypeError):
            return []

    # Apply extraction function to attachments column

    df_input['filenames'] = df_input['attachments'].apply(
        extract_filenames_from_attachments
    )

    df_input['filetypes'] = df_input['attachments'].apply(
        extract_filetypes_from_attachments
    )

    # Explode the filenames column so each attachment gets its own row
    df_input = df_input.explode(['filenames', 'filetypes'])

    column_mapping = {'filenames': 'sourceid'}

    df_input.rename(
        columns=column_mapping,
        inplace=True,
    )

    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows

    df_filenames['fileid'] = df_filenames['path'].apply(
        lambda x: pathlib.Path(x).name.split('_')[0]
    )

    df_input = pd.merge(
        df_input,
        df_filenames,
        left_on='sourceid',
        right_on='fileid',
        how='inner',
    )

    df_input = df_input.drop(columns='fileid')

    df_input['url'] = df_input["path"].apply(
        lambda path: f's3://{bucket}/{path.lower()}'
    )

    df_input['type'] = df_input['filetypes'].map(
        lambda x: canonical_data['Jopari']['ToCanonical']['DocumentTypes'].get(
            x, 'Other'
        )
    )

    df_output.loc[
        (df_output['plaintiffid'].notnull()) & (df_output['type'].notnull()),
        'relevanttogain',
    ] = True
    df_output.loc[
        (df_output['plaintiffid'].isna()) | (df_output['type'].isna()),
        'relevanttogain',
    ] = False

    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols
            and df_input[col].dtype == 'object'
            and col != 'url'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    gainids = aws_operations.upsert_gainid_records_by_ids(
        jopari_records=source_ids, canonical_object='files'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'plaintiffid',
        'plaintiffs',
        aws_operations.get_gainids_by_jopari_ids,
    )

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'caseid',
        'cases',
        aws_operations.get_gainids_by_jopari_ids,
    )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def jopari_generate_s3_upsert_data(
    data: dict[str, pd.DataFrame | None],
    bucket: str,
    directory: str,
    timestamp: str,
) -> dict[str, pd.DataFrame | None]:
    global canonical

    data_cases = data.get('cases')
    if shared.is_df_valid(data_cases):
        data_cases['accidentstate'] = data_cases.apply(
            lambda row: (
                row['accidentstate']
                if pd.notnull(row.get('accidentstate'))
                else (
                    row['servicefacilitystate']
                    if pd.notnull(row.get('servicefacilitystate'))
                    else (
                        row['patientstateorprovincecode']
                        if pd.notnull(row.get('patientstateorprovincecode'))
                        else (
                            row['insuredstate']
                            if pd.notnull(row.get('insuredstate'))
                            else ''
                        )
                    )
                )
            ),
            axis=1,
        )
        data_cases['accidentstate'].replace('', np.nan, inplace=True)

    data['plaintiffs_fallback'] = copy.deepcopy(data['plaintiffs'])
    data['partner_accounts'] = copy.deepcopy(data['medicalfacilities'])
    df_files = copy.deepcopy(data['cases'])

    data['lawfirms'] = extract_attorney_info_from_s3_pdfs(
        data['local_files'].path.tolist()
    )

    df_plaintiffs = (
        jopari_generate_plaintiffs_upsert_data(
            typing.cast(pd.DataFrame, data['plaintiffs']),
            pd.DataFrame(columns=canonical['Plaintiffs']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'plaintiffs')
        else None
    )

    df_plaintiffs_fallback = (
        jopari_generate_plaintiffs_upsert_fallback_data(
            typing.cast(pd.DataFrame, data['plaintiffs_fallback']),
            pd.DataFrame(columns=canonical['Plaintiffs']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'plaintiffs_fallback')
        else None
    )

    if df_plaintiffs is not None and df_plaintiffs_fallback is not None:
        df_plaintiffs = pd.concat(
            [df_plaintiffs, df_plaintiffs_fallback], ignore_index=True
        )

    df_medicalfacilities = (
        jopari_generate_medicalfacilities_upsert_data(
            typing.cast(pd.DataFrame, data['medicalfacilities']),
            pd.DataFrame(columns=canonical['MedicalFacilities']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'medicalfacilities')
        else None
    )

    df_partneraccounts = (
        jopari_generate_partneraccounts_upsert_data(
            typing.cast(pd.DataFrame, data['partner_accounts']),
            pd.DataFrame(columns=canonical['MedicalFacilities']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'partner_accounts')
        else None
    )

    if df_medicalfacilities is not None and df_partneraccounts is not None:
        df_medicalfacilities = pd.concat(
            [df_medicalfacilities, df_partneraccounts], ignore_index=True
        )

        df_medicalfacilities.drop_duplicates(
            subset=[
                'name',
                'billingaddressline1',
                'billingaddressline2',
                'billingaddresscity',
                'billingaddressstate',
                'billingaddresszip',
            ],
            keep='first',  # Keep the first occurrence and drop subsequent duplicates
            inplace=True,
        )

    # Define the patient-insured mapping
    patient_insured_mapping = {
        "patientlastname": "insuredlastname",
        "patientfirstname": "insuredfirstname",
        "patientdateofbirth": "insuredbirthdate",
        "patientgendercode": "insuredgender",
        "patientaddressinformation": "insuredstreetaddress",
        "patientcityname": "insuredcity",
        "patientstateorprovincecode": "insuredstate",
        "patientpostalcode": "insuredzip",
        "patientaccountqualifier": "insuredidqualifier",
        "patientaccountnumber": "patientcontrolnumber",
        "patientssn": "insuredssn",
    }

    # Define the servicefacility-billingprovider mapping
    service_billing_mapping = {
        "servicefacilityaddress": "billingprovideraddress",
        "servicefacilitycity": "billingprovidercity",
        "servicefacilitystate": "billingproviderstate",
        "servicefacilityzip": "billingproviderzipcode",
        "servicefacilityname": "billingprovidername",
    }

    # List of categories to update
    categories = ["cases", "billings", "charges", "insurances"]

    # Loop through each category in the data
    for category in categories:
        df = data.get(category)
        if df is not None and not df.empty:
            # Apply patient-insured mapping
            for (
                patient_field,
                insured_field,
            ) in patient_insured_mapping.items():
                # Fill insured field with patient field where insured field is NaN or None
                df[insured_field].fillna(df[patient_field], inplace=True)

                # Fill patient field with insured field where patient field is NaN or None
                df[patient_field].fillna(df[insured_field], inplace=True)

            # Apply servicefacility-billingprovider mapping
            for (
                service_field,
                billing_field,
            ) in service_billing_mapping.items():
                # Fill billing provider field with service facility field where billing provider field is NaN or None
                df[billing_field].fillna(df[service_field], inplace=True)

                # Fill service facility field with billing provider field where service facility field is NaN or None
                df[service_field].fillna(df[billing_field], inplace=True)

            # Assign the modified DataFrame back to the original data
            data[category] = df

    df_files = copy.deepcopy(data['cases'])

    df_lawfirms, filename_to_gainid_map = (
        jopari_generate_lawfirms_upsert_data(
            data['lawfirms'],
            pd.DataFrame(columns=canonical['LawFirms']),
            bucket,
            directory,
            timestamp,
        )
        if shared.verify_is_valid(data, 'lawfirms')
        else (None, None)
    )

    df_legalpersonnel_input = copy.deepcopy(df_lawfirms)

    if df_legalpersonnel_input is None:
        df_legalpersonnel, filename_to_gainid_map = None, None
    else:
        df_legalpersonnel, filename_to_gainid_map = (
            jopari_generate_legalpersonnel_upsert_data(
                df_legalpersonnel_input,
                pd.DataFrame(columns=canonical['LegalPersonnel']),
                (
                    {}
                    if filename_to_gainid_map is None
                    else filename_to_gainid_map
                ),
                timestamp,
            )
        )

    df_cases = (
        jopari_generate_cases_upsert_data(
            typing.cast(pd.DataFrame, data['cases']),
            pd.DataFrame(columns=canonical['Cases']),
            data['local_files'].path.tolist(),
            filename_to_gainid_map,
            timestamp,
        )
        if shared.verify_is_valid(data, 'cases')
        else None
    )
    df_insurances = (
        jopari_generate_insurances_upsert_data(
            typing.cast(pd.DataFrame, data['insurances']),
            pd.DataFrame(columns=canonical['Insurances']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'insurances')
        else None
    )
    df_billings = (
        jopari_generate_billings_upsert_data(
            typing.cast(pd.DataFrame, data['billings']),
            pd.DataFrame(columns=canonical['Billings']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'billings')
        else None
    )
    df_charges = (
        jopari_generate_charges_upsert_data(
            typing.cast(pd.DataFrame, data['charges']),
            pd.DataFrame(columns=canonical['Charges']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'charges')
        else None
    )
    df_output_files = (
        jopari_generate_files_upsert_data(
            df_files,
            pd.DataFrame(columns=canonical['Files']),
            typing.cast(pd.DataFrame, data['files']),
            bucket,
            timestamp,
        )
        if shared.verify_is_valid(data, 'files') and df_files is not None
        else None
    )

    for file_path_str in data['local_files'].path.tolist():
        path = pathlib.Path(file_path_str)

        try:
            path.unlink()
        except FileNotFoundError:
            pass  # File already deleted or doesn't exist
        except Exception as e:
            print(f"Error deleting {file_path_str}: {e}")

    return {  # return Canonical-compliant data, to be written to CSV and moved to S3, then copied to Redshift; returns None if incoming data is empty or null
        'plaintiffs': df_plaintiffs,
        'medicalfacilities': df_medicalfacilities,
        'lawfirms': df_lawfirms,
        'legalpersonnel': df_legalpersonnel,
        'cases': df_cases,
        'insurances': df_insurances,
        'billings': df_billings,
        'charges': df_charges,
        'files': df_output_files,
    }


def jopari_generate_upsert_csv(
    all_s3_data: dict[str, pd.DataFrame | None], timestamp: str
):
    files = {}
    timestamp_string = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    for data in all_s3_data:
        if (
            all_s3_data[data] is None or all_s3_data[data].empty
        ):  # if data is empty or missing for a canonical object
            continue
        all_s3_data[data].to_csv(
            f'integration/integration_code/jopari_{data}_{timestamp_string}.csv',
            index=False,
        )
        files[data] = (
            f'integration/integration_code/jopari_{data}_{timestamp_string}.csv'
        )
    return files
