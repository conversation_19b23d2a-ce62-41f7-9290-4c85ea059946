import json
import typing

import django.db
from django.db.models.query import QuerySet

from .. import models
from . import logger_config

with (
    open(
        'integration/integration_code/excluded_keys.json', 'rb'
    ) as excluded_keys_file,
):
    excluded_keys = json.load(excluded_keys_file)["names"]


def invalidate_manual_review_data_by_sfid(salesforce_id: str):
    logger_config.reset_db_connection()
    # Find existing row by Salesforce ID
    try:
        updated_count = models.update_manual_review_data.objects.filter(
            salesforce_id=salesforce_id
        ).update(review_status=4)
        return updated_count
    except:
        pass


def update_manual_review_data(
    canonical_object: str,
    salesforce_id: str,
    redshift_data: dict[str, typing.Any],
    salesforce_data: dict[str, typing.Any],
    redshift_id: str,
    review_status: int = 1,
):
    global excluded_keys

    def remove_additional_field(field: str):
        if (
            field in redshift_data
        ):  # additional field salesforce id is not necessary for logging
            del redshift_data[
                field
            ]  # is being logged as a separate column in table

    def stringify_data(data: dict[str, typing.Any]):
        return ' | '.join(
            ['' if val is None else str(val) for val in list(data.values())]
        )  # delimiter separated values format # ' | '.join(f'{k}: {v}' for k, v in data.items()) # key-value pair format

    def clean_data(data: typing.Any):
        if isinstance(data, str):
            cleaned_data = data.lower().strip()
            try:
                cleaned_data = float(cleaned_data)
            except ValueError:
                pass
            return cleaned_data
        return data

    additional_fields = [
        'Id',
        'RedshiftId',
        'Gain_ID__c',
        'Root_Parent_Account__c',
        'PartnerAccountName',
        'MedicalLocationName',
        'Content',
    ]
    for field in additional_fields:
        remove_additional_field(field)

    real_diff = False
    keys = list(redshift_data.keys())

    for entry in keys:
        if entry not in excluded_keys and clean_data(
            salesforce_data[entry]
        ) != clean_data(redshift_data[entry]):
            real_diff = True
            break

    if not real_diff:
        return

    salesforce_data = {
        rs_key: salesforce_data[rs_key] for rs_key in keys
    }  # if None return empty dictionary else filter salesforce row to only include data fields in redshift row
    keys_string = ' | '.join(keys)  # transform each value into DB-ready format
    redshift_data_string = stringify_data(redshift_data)
    salesforce_data_string = stringify_data(salesforce_data)
    # Close the current database connection to force a new one on the next query
    logger_config.reset_db_connection()
    with django.db.connection.cursor() as _:
        update_manual_review_data_entry = models.update_manual_review_data(
            canonical_object=canonical_object,
            salesforce_id=salesforce_id,
            redshift_id=redshift_id,
            keys=keys_string,
            redshift_data=redshift_data_string,
            salesforce_data=salesforce_data_string,
            review_status=review_status,
        )
        update_manual_review_data_entry.save()
    return


def update_manual_review_log(
    user: str,
    manual_review_id: list[str],
    canonical_object: str,
    reviewed_data: list[dict[str, typing.Any]],
):
    keys = list(reviewed_data[0].keys())
    keys_string = ' | '.join(keys)
    count = 0  # used to create 1:1 mapping between SF Ids and reviewed records
    salesforce_ids = []
    for data in reviewed_data:
        if 'Id' in data:  # additional field salesforce id is not necessary
            salesforce_ids.append(data['Id'])
            del data['Id']  # is being logged separately in  manual review
        reviewed_data_string = ' | '.join(
            ['' if val is None else str(val) for val in list(data.values())]
        )
        # Close the current database connection to force a new one on the next query
        logger_config.reset_db_connection()
        with django.db.connection.cursor() as _:
            update_manual_review_log_entry = models.update_manual_review_log(
                user=user,
                update_manual_review_data_id=manual_review_id[count],
                canonical_object=canonical_object,
                salesforce_id=salesforce_ids[count],
                keys=keys_string,
                reviewed_data=reviewed_data_string,
            )
            update_manual_review_log_entry.save()
        data['Id'] = salesforce_ids[
            count
        ]  # 1:1 mapping between SF Ids and reviewed records
        count += 1
    # reviewed_data['Id'] = salesforce_id # because a shallow copy is created/passed in, deleting Id deletes the Id from the record which is necessary for update later => re-insert key-value pair
    return


def update_manual_review_data_mark_completed(
    manual_review_ids: list[str], sf_ids: list[str]
):
    manual_review_data_entry_current = (
        models.update_manual_review_data.objects.filter(
            update_manual_review_data_id__in=manual_review_ids
        )
    )
    review_times = {}
    for item in manual_review_data_entry_current:
        if review_times.get(item.salesforce_id, None) is None:
            review_times[item.salesforce_id] = item.modified_date_time
        elif review_times[item.salesforce_id] < item.modified_date_time:
            review_times[item.salesforce_id] = item.modified_date_time
    update_manual_review_data_entry = (
        models.update_manual_review_data.objects.filter(
            salesforce_id__in=sf_ids
        )
    )  # retrieve records from database
    for item in update_manual_review_data_entry:
        if review_times.get(item.salesforce_id, None) is not None:
            if item.modified_date_time >= review_times[item.salesforce_id]:
                update_manual_review_data_entry = update_manual_review_data_entry.exclude(
                    update_manual_review_data_id=item.update_manual_review_data_id
                )
    manual_review_data_entry_current.update(
        review_status=2
    )  # mark as reviewed in database
    update_manual_review_data_entry.update(
        review_status=3
    )  # mark as automatically reviewed in database
    return


def get_unreviewed_manual_review_data_base(
    canonical_object: str | None,
    *,
    page_index: typing.Optional[int] = None,
    page_size: typing.Optional[int] = None,
):
    from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator

    parameters: dict[str, typing.Any] = {'review_status': 1}
    if canonical_object:
        parameters['canonical_object'] = canonical_object

    query_set = models.update_manual_review_data.objects.filter(**parameters)
    if page_index is not None and page_size is not None:
        paginator = Paginator(query_set, page_size)
        try:
            page = paginator.page(page_index + 1)
        except PageNotAnInteger:
            page = paginator.page(1)
        except EmptyPage:
            return []

        return page.object_list

    return query_set


def get_unreviewed_manual_review_data(
    canonical_object: str | None,
    *,
    page_index: typing.Optional[int] = None,
    page_size: typing.Optional[int] = None,
):
    return list(
        get_unreviewed_manual_review_data_base(
            canonical_object, page_index=page_index, page_size=page_size
        )
    )


def get_unreviewed_manual_review_count(canonical_object: str):
    unreviewed_manual_review_data = get_unreviewed_manual_review_data_base(
        canonical_object
    )
    if isinstance(unreviewed_manual_review_data, QuerySet):
        return unreviewed_manual_review_data.count()
    return len(unreviewed_manual_review_data)
