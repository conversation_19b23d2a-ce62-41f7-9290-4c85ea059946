import dataclasses
import logging
import os
import typing

import django.db

from .. import models
from . import context


@dataclasses.dataclass
class AuditLog:
    route_section: str = 'Function not passed'
    additional_details: typing.Optional[str] = None
    is_error: int = 0
    exception_message: typing.Optional[str] = None


def reset_db_connection():
    django.db.close_old_connections()


def audit_log(
    route_section: str = 'Function not passed',
    additional_details: typing.Optional[str] = None,
    is_error: int = 0,
    exception_message: typing.Optional[str | Exception] = None,
):
    request_context = context.request_context.get()
    # Close the current database connection to force a new one on the next query
    reset_db_connection()
    route = request_context.get('route')
    assert route is not None, 'Route not passed'
    with django.db.connection.cursor() as _:
        audit_log_entry = models.audit_log(
            route=route,
            route_section=route_section,
            additional_details=additional_details,
            is_error=is_error,
            exception_message=exception_message,
        )
        audit_log_entry.save()


class AuditDBHandler(logging.Handler):
    def emit(self, record: logging.LogRecord):
        try:
            route_section = (
                getattr(record, "route_section", None)
                or record.module + ' | ' + record.funcName
            )
            additional_details = getattr(record, "additional_details", None)
            is_error = 1 if record.levelname == 'ERROR' else 0
            exception_message = record.message

            audit_log(
                route_section=route_section,
                additional_details=additional_details,
                is_error=is_error,
                exception_message=exception_message,
            )

        except Exception:
            self.handleError(record)


log_level = os.getenv("LOG_LEVEL", "INFO").upper()
log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
date_format = "%Y-%m-%d %H:%M:%S"

formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

# Console Handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
console_handler.setLevel(log_level)

# DB Handler
audit_db_handler = AuditDBHandler()
audit_db_handler.setLevel(log_level)

# App Logger
app_logger = logging.getLogger()
app_logger.setLevel(log_level)
app_logger.addHandler(console_handler)
app_logger.addHandler(audit_db_handler)


def get_logger(name: str) -> logging.Logger:
    return logging.getLogger(name)
