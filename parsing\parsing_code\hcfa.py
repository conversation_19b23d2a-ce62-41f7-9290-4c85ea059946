# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning,reportArgumentType=warning
import datetime
import glob
import json
import pathlib
import sys

import django.conf

from . import aws_operations, local_operations

app_config = django.conf.settings
credentials, app_settings = app_config.CREDENTIALS, app_config.SETTINGS
parsing_settings = app_settings['Parsing']


def parse(bucket, directory, pdf):
    patient = {}
    files = aws_operations.write_s3(
        bucket, directory, pdf
    )  # Send to s3 bucket
    dob, dos = aws_operations.textract(
        bucket, directory, sorted(files)
    )  # Run textract
    patient['dob'] = local_operations.date_clean(dob)
    dates, dos = local_operations.date_transform(dos)
    totals = local_operations.totals_transform(dos)
    patient['dos'], patient['totals'] = dates, totals
    return patient, sorted(files)


def write_json(name, data):
    with open(name, 'w') as out:
        json.dump(data, out, indent=4)
    out.close()


def main(file):
    try:
        pdf = glob.glob(f'{file}')[0]
        bucket = app_settings['Common']['AWS']['S3']['bucket']
        directory = f"{parsing_settings['AWS']['S3']['directory']}/{datetime.date.today()}"
        patient, s3_files = parse(bucket, directory, pdf)
        patient = local_operations.fix_patient(
            patient
        )  # Assumption-based fixes
        validate = local_operations.validate_patient(patient)
        patient['error'] = validate
        patient = local_operations.portal_transform(patient)
        local_operations.clean_up(pdf)  # Clean files
        aws_operations.clean_s3(bucket, directory, s3_files)
        result_file = f'{pdf[:-4]}.json'
        write_json(result_file, patient)
        if patient['error'] == 0:
            aws_operations.write_s3_result(bucket, directory, result_file)
            return 0
        else:
            write_json(f'{pdf[:-4]} - FAILED.json', patient)
            aws_operations.write_s3_result(
                bucket, directory, f'{pdf[:-4]} - FAILED.json'
            )
            pathlib.Path(f'{pdf[:-4]} - FAILED.json').unlink(missing_ok=True)
            return 1
    except Exception as e:
        write_json(f'{file[:-4]} - ERROR.json', str(e))
        aws_operations.write_s3_result(
            bucket, directory, f'{pdf[:-4]} - ERROR.json'
        )
        local_operations.clean_up(pdf)
        aws_operations.clean_s3(bucket, directory, s3_files)
        return -1


if __name__ == '__main__':
    if len(sys.argv) > 1:
        res = main(sys.argv[1] + '.pdf')
    else:
        print('File name missing')
    if res == 0:
        print('Parse successful')
    elif res == 1:
        print('Parse unsuccessful')
    else:
        print('Unknown error occured')
