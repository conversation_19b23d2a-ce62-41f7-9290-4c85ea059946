import csv
import typing

import django.conf
import pandas as pd

from . import (
    ati_local_operations,
    aws_operations,
    local_operations,
    logger_config,
    shared,
    shared_operations,
)

logger = logger_config.get_logger(__name__)

app_config = django.conf.settings
credentials = app_config.CREDENTIALS

sftp_session = None
tsv_cols = {
    'plaintiffs': {
        'CaseID': 'str',
        'MedicalRecordNumber': 'str',
        'PatientFirstName': 'str',
        'PatientMiddleName': 'str',
        'PatientLastName': 'str',
        'PatientGender': 'str',
        'PatientAddress1': 'str',
        'PatientAddress2': 'str',
        'PatientCity': 'str',
        'PatientState': 'str',
        'PatientZip': 'str',
        'PatientHomePhone': 'str',
        'PatientCellPhone': 'str',
        'PatientDOB': 'str',
        'PatientSSN': 'str',
    },
    'medicalfacilities': {
        'CaseID': 'str',
        'ClaimID': 'str',
        'LocationID': 'str',
        'LocationAddress': 'str',
        'LocationCity': 'str',
        'LocationState': 'str',
        'LocationZip': 'str',
    },
    'lawfirms': {
        'AttorneyThirdPartyAttorneyID': 'str',
        'AttorneyFirstName': 'str',
        'AttorneyMiddleName': 'str',
        'AttorneyLastName': 'str',
        'AttorneyFirm': 'str',
        'AttorneyAddress1': 'str',
        'AttorneyAddress2': 'str',
        'AttorneyCity': 'str',
        'AttorneyState': 'str',
        'AttorneyZip': 'str',
        'AttorneyPhone': 'str',
        'AttorneyFax': 'str',
    },
    'legalpersonnel': {
        'AttorneyThirdPartyAttorneyID': 'str',
        'GuarFirstName': 'str',
        'GuarLastName': 'str',
        'GuarRelationship': 'str',
        'AttorneyFirstName': 'str',
        'AttorneyMiddleName': 'str',
        'AttorneyLastName': 'str',
        'AttorneyFirm': 'str',
        'AttorneyAddress1': 'str',
        'AttorneyAddress2': 'str',
        'AttorneyCity': 'str',
        'AttorneyState': 'str',
        'AttorneyZip': 'str',
        'AttorneyPhone': 'str',
        'AttorneyExt': 'str',
        'AttorneyCellPhone': 'str',
        'AttorneyFax': 'str',
        'AttorneyNotes': 'str',
        'AttorneyEmail': 'str',
    },
    'cases': {
        'ClaimID': 'str',
        'CaseID': 'str',
        'MedicalRecordNumber': 'str',
        'AccountType': 'str',
        'DateOfService': 'str',
        'DateOfInjury': 'str',
        'PatientFirstName': 'str',
        'PatientMiddleName': 'str',
        'PatientLastName': 'str',
        'PatientDOB': 'str',
        'FirstDateOfService': 'str',
        'LocationID': 'str',
        'LocationAddress': 'str',
        'LocationCity': 'str',
        'LocationState': 'str',
        'LocationZip': 'str',
        'AttorneyThirdPartyAttorneyID': 'str',
        'TailClaim': 'str',
        'PatientStatus': 'str',
        'DischargeDate': 'str',
        'InsuranceVendorAssigned': 'str',
    },
    'billings': {
        'ClaimID': 'str',
        'CaseID': 'str',
        'ChargeAmount': 'float',
    },
    'charges': {
        'ChargeDetailID': 'str',
        'PostDate': 'str',
        'ChargeAmount': 'float',
        'HCPC': 'str',
        'HCPCDescription': 'str',
        'Units': 'float',
        'ClaimID': 'str',
    },
    'transactions': {
        'TransactionID': 'str',
        'ChargeDetailID': 'str',
        'ClaimID': 'str',
        'CarrierID': 'str',
        'CarrierName': 'str',
        'CarrierInsuranceType': 'str',
        'CheckNumber': 'str',
        'PostDate': 'str',
        'PaymentDate': 'str',
        'EntryDate': 'str',
        'TransactionType': 'str',
        'TransactionsCodeDesc': 'str',
        'DenialReason': 'str',
        'TransactionAmount': 'float',
        'CARCCode': 'str',
    },
    'notes': {
        'CaseID': 'str',
        'LogNotes': 'str',
        'UserName': 'str',
        'CreatedDate': 'str',
    },
}
tsv_date_cols = {
    'plaintiffs': ['PatientDOB'],
    'cases': ['DateOfInjury', 'DateOfService', 'PatientDOB', 'DischargeDate'],
    'charges': ['PostDate'],
    'transactions': ['PostDate', 'PaymentDate', 'EntryDate'],
    'notes': ['CreatedDate'],
}

canonical_objects = {
    'plaintiffs': {},
    'medicalfacilities': {},
    'lawfirms': {},
    'legalpersonnel': {},
    'cases': {},
    'billings': {},
    'charges': {},
    'transactions': {},
    'files': {
        'additional_parameters': lambda all_s3_data: {
            'new_case_ids': (
                list(all_s3_data['cases']['caseid'])
                if shared.verify_is_valid(all_s3_data, 'cases')
                else None
            )
        }
    },
    'notes': {},
}


def get_from_ati(
    timestamp: str,
    canonical_object: str,
    bucket: str,
    directory: str,
    call_type: str,
    all_data: bool,
    new_case_ids: typing.Optional[list[str]] = None,
) -> tuple[pd.DataFrame, list[str], list[str]]:
    global tsv_cols
    df, unprocessed_files, processed_files = pd.DataFrame(), [], []
    timestamp_string = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    is_files = True if canonical_object == 'files' else False
    data_file_path = (
        f'integration/integration_code/ati_{canonical_object}_{call_type}.txt'
    )
    try:
        unprocessed_files = shared_operations.get_unprocessed_files_from_s3(
            directory, canonical_object, bucket, all_data
        )
        if not unprocessed_files:
            return (df, unprocessed_files, processed_files)
        if is_files:
            unprocessed_files = (
                ati_local_operations.exclude_files_when_cases_not_found(
                    unprocessed_files, new_case_ids
                )
            )
        for file in unprocessed_files:
            if not is_files:
                aws_operations.download_s3(
                    bucket=bucket,
                    key=file,
                    local_location=data_file_path,
                )
                date_cols = (
                    tsv_date_cols[canonical_object]
                    if call_type == 'upsert'
                    and canonical_object in tsv_date_cols
                    else []
                )  # We do not have PostDate in withdrawal file, so we passing empty list.
                df_temp = pd.read_csv(
                    data_file_path,
                    sep="|",
                    quoting=csv.QUOTE_NONE,
                    usecols=lambda col: col
                    in tsv_cols[canonical_object].keys(),
                    dtype=tsv_cols[
                        canonical_object
                    ],  # pyright: ignore[reportArgumentType]
                    parse_dates=date_cols,
                    encoding="latin-1",
                )  # The values for usecols should be in the csv file. Used a callable to only use the columns in the file.
                df = pd.concat(
                    [df, df_temp], ignore_index=True, sort=False
                )  # keep appending tsv file data to df
                local_operations.clean_up_file(data_file_path)
            processed_file = file.replace('unprocessed', 'processed')
            processed_files.append(
                f'{processed_file[:-4]}-{timestamp_string}{processed_file[-4:]}'
            )  # NOTE: removing the timestamp will cause copy_s3 and move_s3 functions to not work correctly
        if is_files:
            df = pd.DataFrame(processed_files, columns=['path'])
        if df is None or df.empty:
            logger.error(f'No data fetched for {canonical_object}')
        else:
            df.columns = [s.lower() for s in (df.columns)]
    except Exception as e:
        local_operations.clean_up_file(data_file_path)
        logger.error(e)
    return (df, unprocessed_files, processed_files)


def get_data_ati(
    timestamp: str, bucket: str, directory: str, call_type: str, all_data: bool
) -> tuple[dict[str, pd.DataFrame | None], dict[str, list[str]]]:
    # when trying to do delta ingestion, all_data being False will fetch only data after the timestamp
    all_s3_data = {}
    all_s3_files = {}

    for canonical_object in canonical_objects:
        additional_parameters_callback = canonical_objects[
            canonical_object
        ].get('additional_parameters', lambda x: {})

        additional_parameters = additional_parameters_callback(all_s3_data)
        # data location is excluding sub_directory
        (
            data,
            unprocessed_files,
            processed_files,
        ) = get_from_ati(
            timestamp,
            canonical_object,
            bucket,
            directory,
            call_type,
            all_data,
            **additional_parameters,
        )
        all_s3_data[canonical_object] = data
        all_s3_files[f'{canonical_object}_processed_files'] = processed_files
        all_s3_files[f'{canonical_object}_unprocessed_files'] = (
            unprocessed_files
        )

    return (all_s3_data, all_s3_files)


def process_upsert_data_ati(
    timestamp: str,
    bucket: str,
    directory: str,
    sub_directory: str,
    data: dict[str, pd.DataFrame | None],
    charges_update_flag: bool,
    billings_update_flag: bool,
    cases_update_flag: bool,
):
    all_s3_data = ati_local_operations.ati_generate_s3_upsert_data(
        data, bucket, directory, timestamp
    )  # data generation
    transactions_gainid_set = set()
    charges_gainid_set = set()
    billings_gainid_set = set()
    if charges_update_flag:
        all_s3_data_transactions = all_s3_data.get('transactions')
        if shared.is_df_valid(all_s3_data_transactions):
            transactions_gainid_set.update(all_s3_data_transactions['gainid'])
        # We may receive update on pre-existing charges. For these charges, we may not receive the
        # corresponding transactions. So, we need to fetch the transactions from Redshift for these charges.
        all_s3_data_charges = all_s3_data.get('charges')
        if shared.is_df_valid(all_s3_data_charges):
            df_rs_transactions_from_updated_charges = (
                aws_operations.get_redshift_transactions_from_update_charges(
                    all_s3_data_charges['gainid'].unique().tolist(),
                )
            )
            if shared.is_df_valid(df_rs_transactions_from_updated_charges):
                transactions_gainid_set.update(
                    df_rs_transactions_from_updated_charges[
                        'transactiongainid'
                    ]
                )
    all_s3_data_charges = all_s3_data.get('charges')
    if billings_update_flag and shared.is_df_valid(all_s3_data_charges):
        charges_gainid_set.update(all_s3_data_charges['gainid'])
    all_s3_data_billings = all_s3_data.get('billings')
    if cases_update_flag and shared.is_df_valid(all_s3_data_billings):
        billings_gainid_set.update(all_s3_data_billings['gainid'])
    files = ati_local_operations.ati_generate_upsert_csv(
        all_s3_data, timestamp
    )  # data storage
    directory = f'{directory}/{sub_directory}'  # result location is including sub_directory
    for file in files.values():
        aws_operations.upload_s3(bucket, directory, file)
    for mapping, file in files.items():  # Redshift changes
        aws_operations.s3_to_postgres(bucket, directory, file, mapping)
    return (
        files,
        transactions_gainid_set,
        charges_gainid_set,
        billings_gainid_set,
    )


def process_delete_data_ati(
    timestamp: str, data: dict[str, pd.DataFrame | None]
) -> None:
    ati_local_operations.ati_mark_redshift_delete_data(timestamp, data)


def process_update_settled_data_ati(
    timestamp: str, data: dict[str, pd.DataFrame | None]
) -> None:
    ati_local_operations.ati_mark_redshift_update_settled_data(timestamp, data)


def post_process_data_ati(
    bucket: str, all_s3_files: dict[str, list[str]]
) -> None:
    # After Redshift changes are complete, move raw data files from unprocessed to processed
    for canonical in canonical_objects:
        for unprocessed_file, processed_file in zip(
            all_s3_files[f'{canonical}_unprocessed_files'],
            all_s3_files[f'{canonical}_processed_files'],
        ):
            aws_operations.move_s3(
                bucket,
                unprocessed_file,
                bucket,
                processed_file,
            )
