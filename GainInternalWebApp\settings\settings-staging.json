{"dummy": "set-stag", "Common": {"AWS": {"S3": {"bucket": "gain-servicing-staging", "region_name": "us-east-2"}}}, "Parsing": {"AWS": {"S3": {"directory": "healthcare_parser"}}}, "Integration": {"AthenaHealth": {"S3": {"sub_directory": "athenahealth"}, "Sources": {"Test": {"ContextId": 1959778, "ContextName": "<PERSON><PERSON> Servicing"}, "Resurgens": {"ContextId": 15749, "ContextName": "GA - Resurgens Orthopaedics"}, "Georgia Bone & Joint": {"ContextId": 24010, "ContextName": "GA - Georgia Bone & Joint Surgeons, P.C."}}, "URLs": {"preview": {"auth": "https://api.preview.platform.athenahealth.com/oauth2/v1/token", "api": "https://api.preview.platform.athenahealth.com/v1"}, "production": {"auth": "https://api.platform.athenahealth.com/oauth2/v1/token", "api": "https://api.platform.athenahealth.com/v1/"}}}, "ATI": {"S3": {"sub_directory": "ati"}}, "Jopari": {"S3": {"sub_directory": "j<PERSON><PERSON>"}}, "AWS": {"Postgres": {"dbname": "integration", "host": "gainservicing-staging.c2mpur1fy6pe.us-east-2.rds.amazonaws.com", "port": "5432", "user": "adminStag", "password": "53f27mNHd1BFvPTlbgkrN2zO"}, "S3": {"directory": "integration"}}, "Filevine": {"Environments": {"hostilo": "hostilo", "test": "test"}, "S3": {"sub_directory": "filevine"}, "URLs": {"base": "https://api.filevine.io", "hostilo": "https://us-shard-f.api.filevineapp.com", "test": "https://stoplight.io/mocks/filevine/v2/182542"}}, "Salesforce": {"S3": {"sub_directory": "salesforce"}}}, "Opensearch": {"Enabled": "true", "Hosts": "https://search-opensearchdomai-1lsycmrlnrua-a4qcdnwoagj3cihkq66uu3emui.us-east-1.es.amazonaws.com", "IndexName": "gaininternalwebapp-logs-staging"}}