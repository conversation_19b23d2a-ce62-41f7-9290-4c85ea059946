-- Insert sample data into transaction
INSERT INTO transactions (
    gainid,
    postdatetime,
    paymentdatetime,
    entrydatetime,
    type,
    description,
    denialreason,
    amount,
    carrierid,
    carriername,
    carrierinsurancetype,
    checknumber,
    chargeid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    status,
    createdatetime
)
VALUES
--CHARGE W/ MULTIPLE TRANSACTION
(
    '3f2e4d5c6b7a8f91', --gainid
    NOW(), --postdatetime
    NOW(), --paymentdatetime
    NOW(), --entrydatetime
    'Payment', --type
    'Medical bill payment', --description
    NULL, --denialreason
    300.00, --amount
    7, --carrierid
    'Carrier A', --carriername
    'Health', --carrierinsurancetype
    'CHK123456', --checknumber
    '6b7a8f913f2e4d5c', --chargeid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    'Completed', --status
    NOW() --createdatetim
),
(
    '4d5e6f7a8b9c0123', --gainid
    NOW(), --postdatetime
    NOW(), --paymentdatetime
    NOW(), --entrydatetime
    'Payment', --type
    'Medical bill payment', --description
    NULL, --denialreason
    700.00, --amount
    7, --carrierid
    'Carrier A', --carriername
    'Health', --carrierinsurancetype
    'CHK123456', --checknumber
    '6b7a8f913f2e4d5c', --chargeid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    'Completed', --status
    NOW() --createdatetim
),
(
    '5e6f7a8b9c0d1234', --gainid
    NOW(), --postdatetime
    NOW() + INTERVAL '3 days', --paymentdatetime
    NOW(), --entrydatetime
    'Payment', --type
    'Patient Payment', --description
    NULL, --denialreason
    500.00, --amount
    7, --carrierid
    'Carrier A', --carriername
    'Health', --carrierinsurancetype
    'CHK123456', --checknumber
    '6b7a8f913f2e4d5c', --chargeid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    'Completed', --status
    NOW() --createdatetim
),
--INVOICEABLE TRANSACTION
(
    '5d4c6b7a8f93', --gainid
    NOW(), --postdatetime
    NOW() + INTERVAL '3 days', --paymentdatetime
    NOW(), --entrydatetime
    'Payment', --type
    'Patient Payment', --description
    NULL, --denialreason
    1500.00, --amount
    7, --carrierid
    'Carrier A', --carriername
    'Health', --carrierinsurancetype
    'CHK123456', --checknumber
    'a1b2c3d4e5f67890', --chargeid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    'Completed', --status
    NOW() --createdatetim
),
--REVERSAL TRANSACTION
(
    '4e3d5c6b7a8f92', --gainid
    NOW(), --postdatetime
    NOW(), --paymentdatetime
    NOW(), --entrydatetime
    'Reversal', --type
    'Medical bill payment', --description
    NULL, --denialreason
    1500.00, --amount
    7, --carrierid
    'Carrier A', --carriername
    'Health', --carrierinsurancetype
    'CHK123456', --checknumber
    '9f8e7d6c5b4a3210', --chargeid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    'Completed', --status
    NOW() --createdatetim
)
;



INSERT INTO gain_id_map (
    gainid, 
    gain_createddatetime, 
    gain_modifieddatetime, 
    ati_id, 
    canonical_object
) VALUES (
    '3f2e4d5c6b7a8f91',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'transactions'
),
(
    '4e3d5c6b7a8f92',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'transactions'
),
(
    '5d4c6b7a8f93',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'transactions'
)
;


