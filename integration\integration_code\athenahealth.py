import datetime
import json
import pathlib

from . import (
    athenahealth_operations,
    aws_operations,
    context,
    local_operations,
    logger_config,
)

logger = logger_config.get_logger(__name__)

with open('settings.json', 'rb') as f:
    settings = json.load(f)
    settings = settings['Integration']['AthenaHealth']


def get_dataview(
    timestamp: str,
    bucket: str,
    directory: str,
    last_run_time: datetime.datetime | datetime.time | None,
    all_data: bool,
):
    auth_succ = athenahealth_operations.authenticate_dataview()
    if not auth_succ:
        logger.error('DataView authentication failed => data fetch skipped')
        return
    files = athenahealth_operations.get_data_dataview(
        timestamp, bucket, directory, last_run_time, all_data
    )
    for file in files.values():
        pathlib.Path(file).unlink(missing_ok=True)
    return


def get_api(
    timestamp: str,
    bucket: str,
    directory: str,
    athenahealth_name: str,
    test: bool,
    last_run_time: datetime.datetime | datetime.time | None,
    all_data: bool,
):
    try:
        auth_url, api_url = (
            (
                settings['URLs']['preview']['auth'],
                settings['URLs']['preview']['api'],
            )
            if test
            else (
                settings['URLs']['production']['auth'],
                settings['URLs']['production']['api'],
            )
        )
    except KeyError:
        logger.error(
            'API initialization failed => API URLs not found in settings'
        )
        return
    athenahealth_operations.initialize_api(auth_url, api_url)
    succ, token, err = athenahealth_operations.authenticate_api()
    if succ and token:
        try:
            context_id, client_canonical_name = settings['Sources'][
                athenahealth_name
            ]['ContextId'], local_operations.get_client_canonical_name(
                'AthenaHealth',
                settings['Sources'][athenahealth_name]['ContextName'],
            )
        except KeyError:
            logger.error(
                f'Athena Context details not found in settings (Athena ContextName: {athenahealth_name})'
            )
            return
        file = athenahealth_operations.get_data_api(
            timestamp,
            bucket,
            directory,
            token,
            context_id,
            client_canonical_name,
            last_run_time,
            all_data,
        )
        if file:
            pathlib.Path(file).unlink(missing_ok=True)
    else:
        logger.error(
            f'API authentication failed => file fetch skipped ({err} response)'
        )
    return


def get_main_upsert(athenahealth_name: str, test: bool, all_data: bool):
    '''
    Get from Athena, post to Canonical in Redshift
    '''
    context.request_context.set({'route': 'AthenaHealth_To_Redshift'})
    timestamp = datetime.datetime.now()
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if isinstance(
        last_run_time, int
    ):  # only problem is if all_data=False i.e. delta changes but las run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for AthenaHealth_To_Redshift missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    bucket, directory = (
        settings['S3']['bucket'],
        f'{settings["S3"]["key"]}/{athenahealth_name}/{datetime.date.today()}',
    )
    if not test:
        get_dataview(timestamp, bucket, directory, last_run_time, all_data)
    # get_api(timestamp, bucket, directory, athenahealth_name, test, last_run_time, all_data)
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    return
