import django.db.models


class audit_log(django.db.models.Model):
    # include meta parameter to exclude model from being migrated, useful if model is already a table
    # class Meta:
    #     managed = False
    audit_log_id = django.db.models.BigAutoField(primary_key=True)
    route = django.db.models.CharField(max_length=100)
    route_section = django.db.models.CharField(max_length=1000)
    additional_details = django.db.models.TextField(null=True, default=None)
    is_error = django.db.models.BooleanField(default=False)
    exception_message = django.db.models.TextField(null=True, default=None)
    create_date_time = django.db.models.DateTimeField(auto_now_add=True)


class update_manual_review_statuses(django.db.models.Model):
    # include meta parameter to exclude model from being migrated, useful if model is already a table
    # class Meta:
    #     managed = False
    update_manual_review_status_id = django.db.models.BigAutoField(
        primary_key=True
    )
    update_manual_review_status_name = django.db.models.CharField(
        max_length=100
    )
    update_manual_review_status_description = django.db.models.TextField()


class update_manual_review_data(django.db.models.Model):
    # include meta parameter to exclude model from being migrated, useful if model is already a table
    # class Meta:
    #     managed = False
    update_manual_review_data_id = django.db.models.BigAutoField(
        primary_key=True
    )
    canonical_object = django.db.models.CharField(max_length=100)
    salesforce_id = django.db.models.CharField(max_length=20)
    redshift_id = django.db.models.TextField()
    keys = django.db.models.TextField()
    redshift_data = django.db.models.TextField()
    salesforce_data = django.db.models.TextField()
    review_status = django.db.models.IntegerField()
    create_date_time = django.db.models.DateTimeField(auto_now_add=True)
    modified_date_time = django.db.models.DateTimeField(auto_now_add=True)


class update_manual_review_log(django.db.models.Model):
    # include meta parameter to exclude model from being migrated, useful if model is already a table
    # class Meta:
    #     managed = False
    update_manual_review_log_id = django.db.models.BigAutoField(
        primary_key=True
    )
    user = django.db.models.CharField(max_length=200)
    # Foreign Key references primary key of referenced model by default, "PROTECT" throws an exception if row in referenced table is deleted
    update_manual_review_data = django.db.models.ForeignKey(
        update_manual_review_data, on_delete=django.db.models.PROTECT
    )  # on_delete options: https://stackoverflow.com/questions/8543877/django-models-foreignkey-on-delete-attribute-full-meaning
    canonical_object = django.db.models.CharField(max_length=100)
    salesforce_id = django.db.models.CharField(max_length=20)
    keys = django.db.models.TextField()
    reviewed_data = django.db.models.TextField()
    create_date_time = django.db.models.DateTimeField(auto_now_add=True)
