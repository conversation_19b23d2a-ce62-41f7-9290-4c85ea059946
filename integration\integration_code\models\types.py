import typing
from datetime import date, datetime, timedelta

import numpy as np
from pandas import Period, Timestamp

Scalar = typing.Union[
    str,
    complex,
    bytes,
    int,
    float,
    bool,
    date,
    datetime,
    timedelta,
    Period,
    Timestamp,
    np.generic,
]


class LookupData(typing.TypedDict, total=False):
    Name: str


class ManualReviewIgnoredFields(typing.TypedDict, total=False):
    Id: str
    RedshiftId: str
    Gain_ID__c: str
    Root_Parent_Account__c: str
    PartnerAccountName: str
    MedicalLocationName: str
    Content: str


class BaseRollbackData(typing.TypedDict, total=False):
    id: str


class MedicalFacilityRollbackData(BaseRollbackData, total=False):
    Gain_ID__c: str
    External_Location_ID__c: str
    Name: str | None
    Phone: str | None
    Fax: str | None
    Email__c: str | None
    Follow_up_email_for_entire_account__c: str | None
    ShippingPostalCode: str | None
    BillingStreet: str | None
    BillingCity: str | None
    BillingState: str | None
    BillingPostalCode: str | None
    Website: str | None


class MedicalFacilityRecord(
    MedicalFacilityRollbackData, ManualReviewIgnoredFields, total=False
):
    RecordTypeId: str


class ChargesRollbackData(BaseRollbackData, total=False):
    Date_of_Service__c: Scalar | None
    Amount__c: Scalar | None
    CPT_Code__c: Scalar | None
    CPT_Modifier__c: Scalar | None
    Non_Gain_Adjustment__c: Scalar | None
    Non_Gain_Amount_Paid_to_Provider__c: Scalar | None
    Reimbursement_Rate__c: Scalar | None
    Funding__c: Scalar | None
    Charge_Id__c: Scalar | None
    Deductible__c: Scalar | None
    Coinsurance__c: Scalar | None
    Copayment__c: Scalar | None
    Gain_Adjustment__c: Scalar | None
    Gain_Pre_Negotiation_Amount_Paid__c: Scalar | None


class ChargesRecord(
    ChargesRollbackData, ManualReviewIgnoredFields, total=False
):
    pass


class FundingsRollbackData(BaseRollbackData, total=False):
    Name: str | None
    Date_of_Service__c: str | None
    Medical_Claim_Number__c: Scalar | None
    Medical_Case__c: str | None
    Non_Rollup_Charge_Amounts_Total__c: str | None
    Non_Rollup_Non_Gain_Adjustments_Total__c: str | None
    Non_Rollup_Non_Gain_Payments_Total__c: str | None
    Total_Deductible__c: str | None
    Total_Coinsurance__c: str | None
    Total_Copayment__c: str | None
    Non_Rollup_Balance_Total__c: str | None
    Non_Rollup_Amounts_to_Partner_Total__c: str | None
    Medical_Facility__c: str | None
    Medical_Facility__r: LookupData
    Medical_Location__c: str | None
    Medical_Location__r: LookupData
    Partner_Account__c: str | None
    Partner_Account__r: LookupData
    Funding_Stage__c: str | None
    Funding_Sub_Stage__c: str | None
    Plaintiff__c: str | None
    RecordTypeId: str | None
    RecordType: LookupData


FundingsRecordFunctional = typing.TypedDict(
    "FundingsRecordFunctional", {"Salesforce Invoice Amount": str}, total=False
)


class FundingsRecord(
    FundingsRollbackData,
    FundingsRecordFunctional,
    ManualReviewIgnoredFields,
    total=False,
):
    Total_Gain_Adjustment__c: str
    Total_Gain_Pre_Negotiation_Amount_Paid__c: str
    redshift_sf_id: str | None
    sf_id: str


class OpportunityRollbackData(BaseRollbackData, total=False):
    Name: str | None
    Date_of_Birth__c: str | None
    SSN__c: str | None
    Drivers_License__c: str | None
    Gender__c: str | None
    Company_Name__c: str | None
    Home_Phone__c: str | None
    Cell_Phone__c: str | None
    Other_Phone__c: str | None
    Plaintiff_Email__c: str | None
    Address__c: str | None
    Address_2__c: str | None
    City__c: str | None
    State__c: str | None
    Zip__c: str | None
    Case_Status__c: Scalar | str | None
    Date_of_Accident__c: str | None
    Description_of_Accident_Incident__c: Scalar | str | None
    What_type_of_case__c: Scalar | str | None
    AccountId: str | None
    Law_Firm_Account_Name__c: str | None
    Attorney__c: str | None
    StageName: str | None
    CloseDate: str | None
    Plaintiff_Account__c: str | None
    Plaintiff_Account__r: LookupData
    Insured_s_Name__c: str | None
    Insurance_Company__c: str | None
    Insurance_Limits__c: str | None
    Insurance_Agent__c: str | None
    Insurance_Co_Address__c: str | None
    Insurance_Co_Address_2__c: str | None
    Insurance_Co_City__c: str | None
    Insurance_Co_State__c: str | None
    Insurance_Co_Zipcode__c: str | None
    Insurance_Co_Phone__c: str | None
    Insurance_Co_Fax__c: str | None
    X2_Insured_s_Name__c: str | None
    Insurance_Company_2__c: str | None
    Insurance_Limits_2__c: str | None
    X2_Zipcode__c: str | None
    Surgery_Date__c: str | None
    Surgeon__c: str | None
    Type_of_Surgery__c: str | None
    Surgery_Comments__c: str | None
    Estimated_Surgical_Charges__c: str | None
    Medical_Facility_P__c: str | None
    Medical_Facility_P__r: LookupData
    Partner_Account__c: str | None
    Partner_Account__r: LookupData
    Paralegal_or_Case_Manager__c: str | None
    Grand_Total_Deductible__c: str | None
    Grand_Total_Coinsurance__c: str | None
    Grand_Total_Copayment__c: str | None
    RecordTypeId: str | None
    RecordType: LookupData
    ATI_Tail_Claim__c: str | None
    Cocounsel__c: str | None


class OpportunityRecord(
    OpportunityRollbackData,
    ManualReviewIgnoredFields,
    total=False,
):
    pass
