from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pandas as pd

from integration.integration_code import ati_local_operations, aws_operations


class TestNonExistentBillingsWithdrawal:
    """Test handling of non-existent billings in withdrawal files."""

    def test_ati_generate_billings_delete_data_with_existing_billings(self):
        """Test that existing billings are processed normally."""
        # Create test data with claim IDs
        df_input = pd.DataFrame(
            {'claimid': ['CLAIM001', 'CLAIM002', 'CLAIM003']}
        )

        # Mock the database response for existing billings
        mock_records = [
            {
                'gain_record': <PERSON><PERSON>(id='GAIN001'),
                'ati_record': <PERSON><PERSON>(id='CLAIM001'),
            },
            {
                'gain_record': <PERSON><PERSON>(id='GAIN002'),
                'ati_record': <PERSON><PERSON>(id='CLAIM002'),
            },
        ]

        with patch.object(
            aws_operations,
            'get_gainid_records_by_ids',
            return_value=mock_records,
        ):
            with patch.object(
                ati_local_operations,
                '_handle_non_existent_billings_for_manual_review',
            ) as mock_manual_review:
                billing_gainids, billing_gainids_map = (
                    ati_local_operations.ati_generate_billings_delete_data(
                        df_input
                    )
                )

        # Verify existing billings are returned
        assert len(billing_gainids) == 2
        assert 'GAIN001' in billing_gainids
        assert 'GAIN002' in billing_gainids
        assert billing_gainids_map == {'GAIN001': 'ATI', 'GAIN002': 'ATI'}

        # Verify manual review was called for non-existent billing
        mock_manual_review.assert_called_once()
        call_args = mock_manual_review.call_args[0]
        assert call_args[1] == ['CLAIM003']  # non_existent_claim_ids

    def test_ati_generate_billings_delete_data_all_existing(self):
        """Test when all billings exist in database."""
        df_input = pd.DataFrame({'claimid': ['CLAIM001', 'CLAIM002']})

        mock_records = [
            {
                'gain_record': Mock(id='GAIN001'),
                'ati_record': Mock(id='CLAIM001'),
            },
            {
                'gain_record': Mock(id='GAIN002'),
                'ati_record': Mock(id='CLAIM002'),
            },
        ]

        with patch.object(
            aws_operations,
            'get_gainid_records_by_ids',
            return_value=mock_records,
        ):
            with patch.object(
                ati_local_operations,
                '_handle_non_existent_billings_for_manual_review',
            ) as mock_manual_review:
                billing_gainids, billing_gainids_map = (
                    ati_local_operations.ati_generate_billings_delete_data(
                        df_input
                    )
                )

        # Verify all billings are returned
        assert len(billing_gainids) == 2
        assert 'GAIN001' in billing_gainids
        assert 'GAIN002' in billing_gainids
        assert billing_gainids_map == {'GAIN001': 'ATI', 'GAIN002': 'ATI'}

        # Verify manual review was not called
        mock_manual_review.assert_not_called()

    def test_ati_generate_billings_delete_data_all_non_existent(self):
        """Test when no billings exist in database."""
        df_input = pd.DataFrame({'claimid': ['CLAIM001', 'CLAIM002']})

        # Mock empty database response
        mock_records = []

        with patch.object(
            aws_operations,
            'get_gainid_records_by_ids',
            return_value=mock_records,
        ):
            with patch.object(
                ati_local_operations,
                '_handle_non_existent_billings_for_manual_review',
            ) as mock_manual_review:
                billing_gainids, billing_gainids_map = (
                    ati_local_operations.ati_generate_billings_delete_data(
                        df_input
                    )
                )

        # Verify no billings are returned for deletion
        assert len(billing_gainids) == 0
        assert billing_gainids_map == {}

        # Verify manual review was called for all billings
        mock_manual_review.assert_called_once()
        call_args = mock_manual_review.call_args[0]
        assert set(call_args[1]) == {
            'CLAIM001',
            'CLAIM002',
        }  # non_existent_claim_ids

    def test_ati_generate_billings_delete_data_without_manual_review_params(
        self,
    ):
        """Test behavior when non-existent billings are found (new S3 logging pattern)."""
        df_input = pd.DataFrame({'claimid': ['CLAIM001', 'CLAIM002']})

        mock_records = []

        with patch.object(
            aws_operations,
            'get_gainid_records_by_ids',
            return_value=mock_records,
        ):
            with patch(
                'integration.integration_code.ati_local_operations.logger'
            ) as mock_logger:
                billing_gainids, billing_gainids_map = (
                    ati_local_operations.ati_generate_billings_delete_data(
                        df_input
                    )
                )

        # Use the variables to avoid unused variable warnings
        assert isinstance(billing_gainids, list)
        assert isinstance(billing_gainids_map, dict)

        # Verify logger.warning was called for each record + flush (3 total calls)
        assert mock_logger.warning.call_count == 3  # 2 records + 1 flush

        # Check the calls: first two should be records, last should be flush
        warning_calls = mock_logger.warning.call_args_list

        # First record
        first_call_args, first_call_kwargs = warning_calls[0]
        assert 'Non-existent billing in withdrawal file' in first_call_args[0]
        assert 'record' in first_call_kwargs['extra']

        # Second record
        second_call_args, second_call_kwargs = warning_calls[1]
        assert 'Non-existent billing in withdrawal file' in second_call_args[0]
        assert 'record' in second_call_kwargs['extra']

        # Flush call
        flush_call_args, flush_call_kwargs = warning_calls[2]
        assert flush_call_args[0] == 'flush'
        assert flush_call_kwargs['extra']['flush'] is True

    @patch('integration.integration_code.ati_local_operations.logger')
    def test_handle_non_existent_billings_for_manual_review(
        self, mock_logger: MagicMock
    ):
        """Test the manual review handling function using new S3 logging pattern."""
        # Create test data
        df_input = pd.DataFrame(
            {
                'claimid': ['CLAIM001', 'CLAIM002', 'CLAIM003'],
                'amount': [100.0, 200.0, 300.0],
                'description': ['Test 1', 'Test 2', 'Test 3'],
            }
        )

        non_existent_claim_ids = ['CLAIM001', 'CLAIM003']

        ati_local_operations._handle_non_existent_billings_for_manual_review(
            df_input, non_existent_claim_ids
        )

        # Verify logger.warning was called for each non-existent billing
        assert mock_logger.warning.call_count == 3  # 2 records + 1 flush

        # Check the first two calls are for the records
        warning_calls = mock_logger.warning.call_args_list

        # First record call
        first_call_args, first_call_kwargs = warning_calls[0]
        assert 'Non-existent billing in withdrawal file' in first_call_args[0]
        assert 'record' in first_call_kwargs['extra']
        assert (
            first_call_kwargs['extra']['s3_filename']
            == 'non_existent_billings_manual_review'
        )
        # Check that subdirectory includes date (format: billings_manual_review/YYYY-MM-DD)
        assert first_call_kwargs['extra']['s3_subdirectory'].startswith(
            'billings_manual_review'
        )

        # Second record call
        second_call_args, second_call_kwargs = warning_calls[1]
        assert 'Non-existent billing in withdrawal file' in second_call_args[0]
        assert 'record' in second_call_kwargs['extra']

        # Flush call
        flush_call_args, flush_call_kwargs = warning_calls[2]
        assert flush_call_args[0] == 'flush'
        assert flush_call_kwargs['extra']['flush'] is True

    def test_empty_dataframe_handling(self):
        """Test handling of empty withdrawal dataframe."""
        df_input = None

        billing_gainids, billing_gainids_map = (
            ati_local_operations.ati_generate_billings_delete_data(df_input)
        )

        assert billing_gainids == []
        assert billing_gainids_map == {}

    def test_empty_non_existent_list_handling(self):
        """Test handling when non-existent list is empty."""
        df_input = pd.DataFrame({'claimid': ['CLAIM001', 'CLAIM002']})

        with patch(
            'integration.integration_code.ati_local_operations.logger'
        ) as mock_logger:
            ati_local_operations._handle_non_existent_billings_for_manual_review(
                df_input,
                [],  # empty non_existent_claim_ids
            )

        # Verify no logging operations were performed (empty list should return early)
        mock_logger.warning.assert_not_called()
