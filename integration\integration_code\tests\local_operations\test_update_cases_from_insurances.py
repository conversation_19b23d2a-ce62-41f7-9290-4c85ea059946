import typing
from unittest.mock import MagicMock

import psycopg
import pytest
from psycopg.rows import dict_row

from integration.integration_code import local_operations
from tests import test_helper


class TestUpdateCasesFromInsurances:
    # These IDs match the existing test data in the seeds
    CASE_ID = '04b1ace113fe41b1'
    CASE_ID_2 = '04b1ace113fe41b2'
    CASE_ID_3 = '04b1ace113fe41b3'

    INSURANCE_ID = 'insur1234567890'
    INSURANCE_ID_2 = 'insur2345678901'
    INSURANCE_ID_3 = 'insur3456789012'

    @pytest.mark.django_db
    def test_no_insurances(
        self,
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
    ):
        """Should return None when no insurances exist"""

        result = local_operations.update_cases_from_insurances(set())

        assert result is None
        assert test_helper.verify_if_log_exists(
            verify_errors, "No data fetched for cases update"
        )

    @pytest.mark.django_db
    def test_valid_insurance(self, database: psycopg.Connection[typing.Any]):
        """Should process valid insurance and return case ID"""

        cursor = database.cursor(row_factory=dict_row)

        result = local_operations.update_cases_from_insurances(
            {self.INSURANCE_ID}
        )

        assert result is not None
        assert self.CASE_ID in result

        cursor.execute(f"SELECT * FROM cases WHERE gainid = '{self.CASE_ID}'")
        updated_case = cursor.fetchone()
        assert updated_case is not None

        cursor.execute(
            "SELECT * FROM integrationtimestamps WHERE integrationroute = 'Redshift_Update'"
        )
        timestamp = cursor.fetchone()
        assert timestamp is not None

    @pytest.mark.django_db
    def test_multiple_insurances(
        self, database: psycopg.Connection[typing.Any]
    ):
        """Should process multiple insurances and return all case IDs"""

        cursor = database.cursor(row_factory=dict_row)
        cursor.execute(
            f"SELECT * FROM cases WHERE gainid IN ('{self.CASE_ID}', '{self.CASE_ID_2}', '{self.CASE_ID_3}')"
        )
        initial_cases = cursor.fetchall()

        result = local_operations.update_cases_from_insurances(
            {self.INSURANCE_ID, self.INSURANCE_ID_2, self.INSURANCE_ID_3}
        )
        assert result is not None
        assert set(result) == {
            self.CASE_ID,
            self.CASE_ID_2,
            self.CASE_ID_3,
        }

        cursor.execute(
            f"SELECT * FROM cases WHERE gainid IN ('{self.CASE_ID}', '{self.CASE_ID_2}', '{self.CASE_ID_3}')"
        )
        updated_cases = cursor.fetchall()
        assert len(updated_cases) == len(initial_cases)

    @pytest.mark.django_db
    def test_settlement_data(self, database: psycopg.Connection[typing.Any]):
        """Should correctly update settlement data for cases"""

        cursor = database.cursor(row_factory=dict_row)

        result = local_operations.update_cases_from_insurances(
            {self.INSURANCE_ID}
        )

        assert result is not None
        assert self.CASE_ID in result

        cursor.execute(f"SELECT * FROM cases WHERE gainid = '{self.CASE_ID}'")
        updated_case = cursor.fetchone()
        assert updated_case is not None

        cursor.execute(
            f"SELECT datesettled, grandtotalsettlementamount FROM cases WHERE gainid = '{self.CASE_ID}'"
        )
        settlement_data = cursor.fetchone()
        assert settlement_data is not None

        cursor.execute(
            f"SELECT datesettled, totalsettlementamount FROM insurances WHERE gainid = '{self.INSURANCE_ID}'"
        )
        insurance_data = cursor.fetchone()
        assert insurance_data is not None

        assert settlement_data['datesettled'] is not None
        assert insurance_data['datesettled'] is not None

        assert float(settlement_data['grandtotalsettlementamount']) == float(
            insurance_data['totalsettlementamount']
        )
