import urllib.request

import django.conf
import django.shortcuts
import rest_framework.decorators
import rest_framework.permissions
import rest_framework.request
import rest_framework.response

from integration.integration_code import context, logger_config

logger = logger_config.get_logger(__name__)


def home_page(request: rest_framework.request.HttpRequest):
    # return django.shortcuts.render(request,'gainhome.html')
    return django.shortcuts.render(request, 'home/index.html')


@rest_framework.decorators.api_view(['GET', 'POST'])
def health_check(request: rest_framework.request.HttpRequest):
    return rest_framework.response.Response("Successful", 200)


@rest_framework.decorators.api_view(['GET'])
# @rest_framework.decorators.permission_classes([rest_framework.permissions.IsAuthenticated])
def test_ip(request: rest_framework.request.HttpRequest):
    return rest_framework.response.Response(
        f'External IP for this device is: {urllib.request.urlopen("https://v4.ident.me/").read().decode("utf8")} (IPv4)',
        200,
    )


@rest_framework.decorators.api_view(['GET'])
# @rest_framework.decorators.permission_classes([rest_framework.permissions.IsAuthenticated])
def test_logging(request: rest_framework.request.HttpRequest):
    context.request_context.set({'route': 'Test Logging'})
    logger.info('Test logging')
    return rest_framework.response.Response("Successful", 200)


@rest_framework.decorators.api_view(['GET'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def check_app_config(request: rest_framework.request.Request):
    setting_val = request.query_params.get('config_param', None)
    if not setting_val:
        return rest_framework.response.Response(
            f'Pass HTTP parameter config_param as part of request', 400
        )
    app_config = django.conf.settings
    if setting_val.upper() == 'SETTINGS':
        var = app_config.SETTINGS
    if setting_val.upper() == 'CREDENTIALS':
        var = app_config.CREDENTIALS
    print(var)
    return rest_framework.response.Response(
        f'App Config {setting_val} printed', 200
    )
