ALTER TABLE integration.dev.cases
ADD COLUMN datetreatmentcompleted date ENCODE az64
;

ALTER TABLE integration.staging.cases
ADD COLUMN datetreatmentcompleted date ENCODE az64
;

ALTER TABLE integration.prod.cases
ADD COLUMN datetreatmentcompleted date ENCODE az64
;

ALTER TABLE integration.main.cases
ADD COLUMN datetreatmentcompleted date ENCODE az64
;

ALTER TABLE integration.dev.cases
ADD COLUMN treatmentcompleted boolean DEFAULT false ENCODE raw
;

ALTER TABLE integration.staging.cases
ADD COLUMN treatmentcompleted boolean DEFAULT false ENCODE raw
;

ALTER TABLE integration.prod.cases
ADD COLUMN treatmentcompleted boolean DEFAULT false ENCODE raw
;

ALTER TABLE integration.main.cases
ADD COLUMN treatmentcompleted boolean DEFAULT false ENCODE raw
;

INSERT INTO integration.dev.integrationtimestamps (integrationroute, lastrun)
VALUES ('Redshift_To_Salesforce_Update_Treatment_Complete', CURRENT_TIMESTAMP)
;

INSERT INTO integration.staging.integrationtimestamps (integrationroute, lastrun)
VALUES ('Redshift_To_Salesforce_Update_Treatment_Complete', CURRENT_TIMESTAMP)
;

INSERT INTO integration.prod.integrationtimestamps (integrationroute, lastrun)
VALUES ('Redshift_To_Salesforce_Update_Treatment_Complete', CURRENT_TIMESTAMP)
;

INSERT INTO integration.main.integrationtimestamps (integrationroute, lastrun)
VALUES ('Redshift_To_Salesforce_Update_Treatment_Complete', CURRENT_TIMESTAMP)
;

