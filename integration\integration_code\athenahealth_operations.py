import base64
import collections
import datetime
import glob
import json

import img2pdf
import requests
import snowflake.connector

from . import (
    athenahealth_local_operations,
    aws_operations,
    local_operations,
    logger_config,
)

logger = logger_config.get_logger(__name__)

api_url, auth_url = None, None
DEFAULT_TIMEOUT, MAX_RETRIES = 5, 5
with open('integration/integration_code/credentials.json', 'rb') as f:
    credentials = json.load(f)
    credentials = credentials['athenahealth']
with open(
    'integration/integration_code/athenahealth_documenttypes.json', 'rb'
) as f:
    athena_documenttypes = json.load(f)

dataview_session, api_session = None, None
sql_queries = {
    'cases': '''
        SELECT
            CONCAT(p.PatientId, '-',
            CASE
                WHEN cl.AccidentDate IS NOT NULL THEN CAST(DATE(cl.AccidentDate) AS VARCHAR)
                WHEN pi.AccidentDateDateTime IS NOT NULL THEN CAST(DATE(pi.AccidentDateDateTime) AS VARCHAR)
                WHEN pi.CaseInjuryDate IS NOT NULL THEN CAST(DATE(pi.CaseInjuryDate) AS VARCHAR)
                ELSE 'null'
            END
            ) AS SourceId,
            p.ContextName AS SourceName,
            p.PatientId AS PlaintiffId,
            CONCAT(p.Firstname, ' ', p.Lastname) AS PlaintiffName,
            p.DOB AS PlaintiffDateOfBirth,
            (
                CASE
                    WHEN cl.AccidentDate IS NOT NULL THEN DATE(cl.AccidentDate)
                    WHEN pi.AccidentDateDateTime IS NOT NULL THEN DATE(pi.AccidentDateDateTime)
                    ELSE DATE(pi.CaseInjuryDate)
            END
            ) AS FinalAccidentDate,
            --pi.CaseNumber,
            /*
            (
                CASE
                    WHEN cl.RelatedToAutoAccidentYN IS NOT NULL THEN cl.RelatedToAutoAccidentYN
                    ELSE pi.RelatedToAutoAccidentYN
                END
            ) AS RelatedToAutoAccidentYN,
            (
                CASE
                    WHEN cl.AnotherPartyResponsibleYN IS NOT NULL THEN cl.AnotherPartyResponsibleYN
                    ELSE pi.AnotherPartyResponsibleYN
                END
            ) AS AnotherPartyResponsibleYN,
            */
            (
                CASE
                    WHEN cl.AutoAccidentState IS NOT NULL THEN cl.AutoAccidentState
                    ELSE pi.AutoAccidentState
                END
            ) AS FinalAutoAccidentState,
            LISTAGG(DISTINCT COALESCE(
                pi.InjuredBodyPart,
                '|',
                pi.InjuryDescription
            ), ', ') AS AccidentDescription,
            --pi.InjuryDescription,
            T1.InsuranceCategory AS Type,
            --cl.ClaimPrimaryPatientInsId,
            --cl.ClaimSecondaryPatientInsId,
            --pi.PatientRelationship,
            --pi.InsuranceName
            MAX(
                CASE
                    WHEN pi.InsurancePackageId = 616479 THEN TRUE
                    ELSE FALSE
                END
            ) AS RelevantToGain,
            MAX(GREATEST(
                p.LastUpdated,
                c.LastUpdated,
                pi.LastUpdated,
                cl.LastUpdated,
                T1.LastUpdated
            )) AS SourceModifiedDateTime
        FROM
            Patient p
            LEFT JOIN Client c ON c.ClientId = p.PatientId
            LEFT JOIN PatientInsurance pi ON pi.PatientId = p.PatientId
            LEFT JOIN Claim cl ON cl.PatientId = p.PatientId
            LEFT JOIN (
                SELECT DISTINCT
                    InsurancePackageId,
                    CONCAT(InsurancePackageType, ' - ', IRCGroup) AS InsuranceCategory,
                    LastUpdated
                FROM Payer
            ) AS T1 ON T1.InsurancePackageId = pi.InsurancePackageId
        WHERE RelevantToGain = TRUE
        --WHERE pi.InsurancePackageId = 616479
        --WHERE p.PatientId IN (19, 126235555, 126125138, 126406763, 126137588, 126405018, 126143606, 126249578)
        WHERE p.PatientId IN (124696448, 125410304, 126361628)
        GROUP BY
            SourceId,
            SourceName,
            PlaintiffId,
            PlaintiffName,
            PlaintiffDateOfBirth,
            FinalAccidentDate,
            FinalAutoAccidentState,
            --AccidentDescription,
            Type
        --LIMIT 100
    ''',
    'plaintiffs': '''
        SELECT
            p.PatientId AS SourceId,
            p.ContextName AS SourceName,
            CONCAT(p.Firstname, ' ', p.Lastname) AS Name,
            p.DOB AS DateOfBirth,
            p.PatientSSN AS SSN,
            c.LicenseNumber,
            p.Sex AS Gender,
            p.MaritalStatus,
            p.PatientHomePhone AS HomePhone,
            p.MobilePhone,
            p.WorkPhone AS OtherPhone,
            p.Email AS PrimaryEmail,
            p.Address AS HomeAddressLine1,
            p.Address2 AS HomeAddressLine2,
            p.City AS HomeAddressCity,
            p.State AS HomeAddressState,
            p.Zip AS HomeAddressZip,
            p.CurrentDepartmentId as DepartmentId,
            CONCAT(p.PatientId, '-',
            CASE
                WHEN cl.AccidentDate IS NOT NULL THEN CAST(DATE(cl.AccidentDate) AS VARCHAR)
                WHEN pi.AccidentDateDateTime IS NOT NULL THEN CAST(DATE(pi.AccidentDateDateTime) AS VARCHAR)
                WHEN pi.CaseInjuryDate IS NOT NULL THEN CAST(DATE(pi.CaseInjuryDate) AS VARCHAR)
                ELSE 'null'
            END
            ) AS CaseId,
            MAX(
                CASE
                    WHEN pi.InsurancePackageId = 616479 THEN TRUE
                    ELSE FALSE
                END
            ) AS RelevantToGain,
            MAX(GREATEST(
                p.LastUpdated,
                c.LastUpdated,
                pi.LastUpdated,
                cl.LastUpdated,
                pa.LastUpdated
            )) AS SourceModifiedDateTime
        FROM
            Patient p
            LEFT JOIN Client c ON c.ClientId = p.PatientId
            LEFT JOIN PatientInsurance pi ON pi.PatientId = p.PatientId
            LEFT JOIN Claim cl ON cl.PatientId = p.PatientId
            LEFT JOIN Payer pa ON pa.InsurancePackageId = pi.InsurancePackageId
        WHERE RelevantToGain = TRUE
        --WHERE pi.InsurancePackageId = 616479
        --WHERE p.PatientId IN (19, 126235555, 126125138, 126406763, 126137588, 126405018, 126143606, 126249578)
        WHERE p.PatientId IN (124696448, 125410304, 126361628)
        --WHERE p.PatientId IN (125212350, 124577776, 126417736, 126336427, 126429062, 126353735, 126351264, 126444660, 126370967, 125382698, 126067314, 126415012, 124973453, 124627816, 124993096, 126451773, 126441509, 125149584, 124792112, 126196498, 126470524, 125572520, 126314878, 126162662, 126122683, 126117096, 126334385)
        GROUP BY
            SourceId,
            SourceName,
            Name,
            DateOfBirth,
            p.PatientSSN,
            c.LicenseNumber,
            Gender,
            MaritalStatus,
            p.PatientHomePhone,
            p.MobilePhone,
            p.WorkPhone,
            p.Email,
            HomeAddressLine1,
            HomeAddressLine2,
            HomeAddressCity,
            HomeAddressState,
            HomeAddressZip,
            DepartmentId,
            CaseId
    ''',
    'billings': '''
        SELECT
            cl.ClaimId AS SourceId,
            cl.ContextName AS SourceName,
            cl.PatientId AS PlaintiffId,
            t.Amount AS BilledAmount,
            cl.ClaimId AS MedicalClaimNumber,
            cl.ClaimServiceDate AS DateOfService,
            t.ProcedureCode AS CPTCode,
            'Medical Funding' AS Type,--cl.ClaimType AS Type,
            CONCAT(p.PatientId, '-',
            CASE
                WHEN cl.AccidentDate IS NOT NULL THEN CAST(DATE(cl.AccidentDate) AS VARCHAR)
                WHEN pi.AccidentDateDateTime IS NOT NULL THEN CAST(DATE(pi.AccidentDateDateTime) AS VARCHAR)
                WHEN pi.CaseInjuryDate IS NOT NULL THEN CAST(DATE(pi.CaseInjuryDate) AS VARCHAR)
                ELSE 'null'
            END
            ) AS CaseId,
            MAX(
                CASE
                    WHEN pi.InsurancePackageId = 616479 THEN TRUE
                    ELSE FALSE
                END
            ) AS RelevantToGain,
            MAX(GREATEST(
                p.LastUpdated,
                pi.LastUpdated,
                cl.LastUpdated,
                t.LastUpdated
            )) AS SourceModifiedDateTime
        FROM
            Claim cl
            JOIN Patient p ON p.PatientId = cl.PatientId
            JOIN PatientInsurance pi ON pi.PatientId = cl.PatientId
            JOIN "TRANSACTION" t ON t.ClaimId = cl.ClaimId
        WHERE t.TransactionType = 'CHARGE'
            AND (cl.ClaimPrimaryPatientInsId = pi.PatientInsuranceId
                OR cl.ClaimSecondaryPatientInsId = pi.PatientInsuranceId)
            AND RelevantToGain = TRUE
            --AND pi.InsurancePackageId = 616479
            --AND p.PatientId IN (19, 126235555, 126125138, 126406763, 126137588, 126405018, 126143606, 126249578)
            AND p.PatientId IN (124696448, 125410304, 126361628)
            --AND p.PatientId IN (124577776, 124696448, 124792112, 124667422, 125992869, 126349862, 126481723, 126477940, 126472736, 124556306, 124556328, 124556362, 124702225, 126112952, 126441509, 126417198, 126435997, 124977632, 126388031, 126125771, 126092434, 125119837, 124684513, 126462117, 124577776, 125998869, 126169272, 126169272, 126381755, 126417198)
        GROUP BY
            SourceId,
            SourceName,
            PlaintiffId,
            BilledAmount,
            MedicalClaimNumber,
            DateOfService,
            CPTCode,
            ChargeId,
            Type,
            CaseId
    ''',
}


# region DataView
def authenticate_dataview():
    global credentials, dataview_session
    resp = False
    try:
        conn = snowflake.connector.connect(
            user=credentials['dataview']['username'],
            password=credentials['dataview']['password'],
            account=credentials['dataview']['account'],
            warehouse=credentials['dataview']['warehouse'],
            database=credentials['dataview']['db'],
            schema=credentials['dataview']['schema'],
            login_timeout=60,  # default value, can be changed if necessary
        )
        dataview_session = conn  # if successful, store credentials globally
        resp = True
    except snowflake.connector.errors.DatabaseError:
        logger.error('Authentication error')
    except snowflake.connector.errors.ForbiddenError:
        logger.error('Failed to connect to DB')
    except KeyError:
        logger.error(
            'AthenaHealth DataView credential value does not exist in JSON'
        )
    except Exception as e:
        logger.error(e)
    return resp


def get_from_dataview(
    canonical_object: str,
    last_run_time: datetime.datetime | datetime.time | None,
    all_data: bool,
):
    global dataview_session, sql_queries
    sql = sql_queries[canonical_object]
    if not all_data:
        sql += f'''
            HAVING SourceModifiedDateTime > '{last_run_time}'
        '''
    try:
        cur = dataview_session.cursor()
        cur.execute(sql)
        df = cur.fetch_pandas_all()
        if 'DEPARTMENTID' in df:
            df['DEPARTMENTID'] = df['DEPARTMENTID'].astype('Int64')
        if df.empty:
            logger.error(f'No data fetched for {canonical_object}')
        return df
    except AttributeError:
        logger.error('DataView session NULL or not set')
    except snowflake.connector.errors.ProgrammingError:
        logger.error(f'Query error for {canonical_object}')
    except Exception as e:
        logger.error(e)
    return


def get_data_dataview(
    timestamp: str,
    bucket: str,
    directory: str,
    last_run_time: datetime.datetime | datetime.time | None,
    all_data: bool,
):
    all_dataview_data = {  # when trying to do delta ingestion, all_data being False will fetch only data after the timestamp
        'cases': get_from_dataview('cases', last_run_time, all_data),
        'plaintiffs': get_from_dataview('plaintiffs', last_run_time, all_data),
        'billings': get_from_dataview('billings', last_run_time, all_data),
    }
    all_s3_data = (
        athenahealth_local_operations.athenahealth_dataview_generate_s3_data(
            all_dataview_data, timestamp
        )
    )  # data generation
    files = athenahealth_local_operations.athenahealth_dataview_generate_csv(
        all_s3_data, timestamp
    )  # data storage
    for file in files.values():
        aws_operations.upload_s3(bucket, directory, file)
    # Redshift storage
    for mapping, file in files.items():
        aws_operations.s3_to_postgres(bucket, directory, file, mapping)
    return files


# endregion


# region API
def initialize_api(auth: str, api: str):
    global auth_url, api_url, api_session, DEFAULT_TIMEOUT, MAX_RETRIES
    auth_url, api_url = auth, api
    api_session = local_operations.setup_session(
        api_session, DEFAULT_TIMEOUT, MAX_RETRIES
    )  # set session parameters for API, for timeout and retries
    return


def authenticate_api(
    mode: str = 'key',
) -> tuple[bool, str | None, Exception | str | None]:
    global auth_url
    succ, auth_token, error = False, None, None
    if mode == 'key':  # authenticate using secret key
        try:
            k, v = (
                credentials['api']['production']['client'],
                credentials['api']['production']['secret'],
            )
        except KeyError:
            logger.error(
                'AthenaHealth API Credential value does not exist in JSON'
            )
            error = 'KeyError'
        except Exception as e:
            logger.error(e)
            error = e
        try:
            resp = api_session.post(
                url=f'{auth_url}',
                headers={
                    'Authorization': f'Basic {base64.b64encode(f"{k}:{v}".encode("ascii")).decode("ascii")}',
                    'Content_Type': 'application/x-www-form-urlencoded',
                },
                data={
                    'grant_type': 'client_credentials',
                    'scope': 'athena/service/Athenanet.MDP.*',
                },
            )
            succ = True if resp.status_code == 200 else False
            if succ:
                auth_token = resp.json()['access_token']
        except requests.exceptions.ConnectionError:
            logger.error('Connection error occurred')
            error = 'ConnectionError'
        except requests.exceptions.HTTPError as he:
            logger.error(f'Unsuccessful HTTP response: {he}')
            error = f'Unsuccessful HTTP response: {he.response.status_code}'
        except Exception as e:
            logger.error(e)
            error = e
    else:  # authenticate using JWT
        pass
    return (succ, auth_token, error)


'''
Documents - Chart export method (legacy)

def post_files_export(patientIds, token, context_id):
    documentIds = []
    for patientId in patientIds:
        post_resp = requests.post(
            url = f'{api_url}/{context_id}/chart/{patientId["patientId"]}/documentexport',
            #url = f'{api_url}/195933/chart/26575/documentexport', #test patient
            headers = {
                'Authorization': f'Bearer {token}',
                'Content_Type': 'application/x-www-form-urlencoded',
                'accept': 'application/json'
            },
            data = {
                'createfromdatedocumentclass': 'ADMIN',
                'createfromdaterangeend': '12/31/2999',
                'createfromdaterangestart': '01/01/2000',
                #'departmentid': patientId['departmentId'][:-2]
                'departmentid': '150' #test patient
            }
        )
        if post_resp.status_code == 200:
            record = {
                'documentId': post_resp.json()['documentid'],
                'patientId': patientId['patientid'],
                #'patientId': '26575', #test patient
                'patientName': patientId['patientName'],
                'departmentId': patientId['departmentid'][:-2]
                #'departmentId': '150' #test patient
            }
            documentIds.append(record)
            #break #for testing
    return documentIds

def get_files_pdf(documentIds, bucket, directory, timestamp, token, context_id):
    files, files_redshift_data, file_num = [], [], 0
    for doc in documentIds:
        get_resp, execution_time = None, 0
        while not get_resp or (get_resp.status_code != 200 and execution_time <= 10):
            time.sleep(.3)
            get_resp = requests.get(
                url = f'{api_url}/{context_id}/chart/{doc["patientId"]}/documentexport/{doc["documentId"]}',
                headers = {
                    'Authorization': f'Bearer {token}',
                    'Accept': '*/*'
                },
                params = {
                    'departmentid': doc['departmentId']
                }
            )
            execution_time += 1
        if get_resp.status_code == 200:
            file = get_resp.content
            file_name = f'{doc["patientName"].replace(" ", "_")}_{doc["patientId"]}_{timestamp}_{file_num}.pdf'
            with open(f'integration/integration_code/{file_name}', 'wb') as f:
                f.write(file)
                files.append(f'integration/integration_code/{file_name}')
            record = {
                's3URI': f's3://{bucket}/{directory}/{file_name}',
                'patientId': doc['patientId'],
                'documentId': doc['documentId'],
                'departmentId': doc['departmentId']
            }
            files_redshift_data.append(record)
            file_num += 1
    return (files, files_redshift_data)
'''


def get_file_data(
    patients: list[dict[str, str]],
    token: str,
    context_id: str,
    document_type: str,
    client_canonical_name: str,
    last_run_time: datetime.datetime | datetime.time | None,
    all_data: bool,
):
    global api_session
    documents = []
    for patient in patients:
        try:
            get_resp = api_session.get(
                # url = f'{api_url}/195933/patients/26575/documents/{document_type}', #test patient
                url=f'{api_url}/{context_id}/patients/{patient["patientId"]}/documents/{document_type}',
                headers={
                    'Authorization': f'Bearer {token}',
                    'Content_Type': 'application/x-www-form-urlencoded',
                    'accept': 'application/json',
                },
                params={
                    # 'departmentid': '150'  # test patient
                    'departmentid': patient['departmentId']
                },
                timeout=600,
            )
        except requests.exceptions.ConnectionError:
            logger.error('Connection error occurred')
            continue
        except requests.exceptions.HTTPError as he:
            logger.error(
                f'Unsuccessful HTTP response for {document_type} documents for patient {patient["patientId"]}: {he}'
            )
            continue
        except Exception as e:
            logger.error(e)
            continue
        resp = get_resp.json().get(f'{document_type}s', None)
        if resp:
            for doc in resp:
                if not all_data:  # i.e. for a delta-change ingestion
                    if (
                        doc['lastmodifieddatetime']
                        and datetime.datetime.fromisoformat(
                            doc['lastmodifieddatetime']
                        ).replace(tzinfo=None)
                        <= last_run_time
                    ):
                        continue  # if lastmodifieddatetime is not > last time ingestion was done, skip fetching the file
                if (
                    doc.get('documentsubclass', None) is None
                    or doc['documentsubclass']
                    in athena_documenttypes[client_canonical_name][
                        'DocumentSubtypes'
                    ][document_type]
                ):  # only pull wanted subtypes of documents
                    record = {
                        'documentId': doc[f'{document_type}id'],
                        # 'patientId': '26575',  # test patient
                        'patientId': patient['patientId'],
                        'patientName': patient['patientName'],
                        # 'departmentId': '150',  # test patient
                        'departmentId': patient['departmentId'],
                        'createdatetime': (
                            doc['createddatetime']
                            if doc['createddatetime'] is not None
                            else ''
                        ),
                        'lastmodifieddatetime': (
                            doc['lastmodifieddatetime']
                            if doc['lastmodifieddatetime'] is not None
                            else ''
                        ),
                    }
                    record['documentType'] = (
                        doc['documentsubclass']
                        if doc.get('documentsubclass', None) is not None
                        else (
                            doc['documentclass']
                            if doc.get('documentclass', None) is not None
                            else ''
                        )
                    )
                    documents.append(record)
    documents = sorted(
        documents, key=lambda d: d['patientId']
    )  # fetch documents by patient, hence sorting by patientId
    return documents


def get_files(
    documents: list[dict[str, str]],
    bucket: str,
    directory: str,
    timestamp: str,
    token: str,
    context_id: str,
    document_type: str,
    client_canonical_name: str,
):
    global api_session
    timestampString = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    files_redshift_data, files_pages_missing = [], collections.defaultdict(int)
    for doc in documents:
        try:
            get_resp_doc = api_session.get(
                # url = f'{api_url}/195933/patients/26575/documents/{document_type}/{doc["documentId"]}', # test patient
                url=f'{api_url}/{context_id}/patients/{doc["patientId"]}/documents/{document_type}/{doc["documentId"]}',
                headers={
                    'Authorization': f'Bearer {token}',
                    'Content_Type': 'application/x-www-form-urlencoded',
                    'accept': 'application/json',
                },
                params={'departmentid': doc['departmentId'][:-2]},
                timeout=600,
            )
        except requests.exceptions.ConnectionError:
            logger.error('Connection error occurred')
            continue
        except requests.exceptions.HTTPError as he:
            logger.error(
                f'Unsuccessful HTTP response for {document_type} documents for patient {doc["patientId"]}: {he}'
            )
            continue
        except Exception as e:
            logger.error(e)
            continue
        file_name = f'{doc["patientName"].replace(" ", "_")}_{doc["patientId"]}_{doc["documentId"]}-{timestampString}'
        '''
        # Only if dynamic file naming necessary (is not required presently)
        file_num = 0
        f = glob.glob(f'integration/integration_code/{file_name}_{file_num}*')
        while f:
            file_num += 1 # that name is the available one to assign to file
            f = glob.glob(f'integration/integration_code/{file_name}_{file_num}*') # keep iterating until name not found
        file_name = f'{file_name}_{file_num}'
        '''
        if (
            document_type
            in athena_documenttypes[client_canonical_name][
                'DocumentValueTypes'
            ]
        ):  # any file types that contain analytical quantitative data, gets stored as json files
            value_file = local_operations.write_json(
                get_resp_doc.json()[0],
                f'integration/integration_code/{file_name}_VALUES',
            )
            aws_operations.upload_s3(
                bucket,
                f'{directory}/files',
                value_file,
            )
            local_operations.clean_up_file(value_file)
            record = {
                's3URI': f's3://{bucket}/{directory}/files/{file_name}_VALUES',
                'patientId': doc['patientId'],
                'documentId': doc['documentId'],
                'departmentId': doc['departmentId'],
                'sourcecreatedatetime': doc['createdatetime'],
                'sourcelastmodifieddatetime': doc['lastmodifieddatetime'],
            }
            record['documentType'] = f'{document_type}_VALUES'
            files_redshift_data.append(record)
        doc_pages = []
        pages = get_resp_doc.json()[0].get('pages', None)
        if pages:
            create_doc_flag = True
            pages = sorted(pages, key=lambda d: d['pageordering'])
            for page in pages:
                doc_pages.append(page['pageid'])
            for doc_page in doc_pages:
                try:
                    get_resp_doc_page = api_session.get(
                        # url = f'{api_url}/195933/patients/26575/documents/{document_type}/{doc["documentId"]}/pages/{doc_page}',
                        url=f'{api_url}/{context_id}/patients/{doc["patientId"]}/documents/{document_type}/{doc["documentId"]}/pages/{doc_page}',
                        headers={
                            'Authorization': f'Bearer {token}',
                            'Content_Type': 'application/x-www-form-urlencoded',
                            'accept': 'application/json',
                        },
                        params={'departmentid': doc['departmentId'][:-2]},
                        timeout=600,
                    )
                except requests.exceptions.ConnectionError:
                    logger.error('Connection error occurred')
                    local_operations.clean_up_file(
                        f'integration/integration_code/{file_name}_page.png'
                    )  # if intermediate page retrieval fails, have to delete the remaining pages and declare document retrieval a failure
                    create_doc_flag = False  # do not create a document
                    break  # terminate; don't need to check remaining pages
                except requests.exceptions.HTTPError as he:
                    logger.error(
                        f'Unsuccessful HTTP response when trying to retrieve page {doc_page} of document with id {doc["documentId"]}, {he.response.status_code} response'
                    )
                    local_operations.clean_up_file(
                        f'integration/integration_code/{file_name}_page.png'
                    )  # if intermediate page retrieval fails, have to delete the remaining pages and declare document retrieval a failure
                    create_doc_flag = False  # do not create a document
                    break  # terminate; don't need to check remaining pages
                except Exception as e:
                    logger.error(e)
                    local_operations.clean_up_file(
                        f'integration/integration_code/{file_name}_page.png'
                    )  # if intermediate page retrieval fails, have to delete the remaining pages and declare document retrieval a failure
                    create_doc_flag = False  # do not create a document
                    break  # terminate; don't need to check remaining pages
                page_name = file_name + f'_page_{doc_page}.png'
                page = get_resp_doc_page.content
                with open(
                    f'integration/integration_code/{page_name}', 'wb'
                ) as f:
                    f.write(page)
            if create_doc_flag:
                file = f'integration/integration_code/{file_name}.pdf'
                with open(file, 'wb') as f:
                    fs = sorted(
                        glob.glob(
                            f'integration/integration_code/{file_name}*.png'
                        )
                    )  # get all images
                    f.write(
                        img2pdf.convert(
                            fs
                        )  # pyright: ignore[reportArgumentType]
                    )  # combine into 1 pdf
                    local_operations.clean_up_file(
                        f'integration/integration_code/{file_name}_page.png'
                    )
                aws_operations.upload_s3(
                    bucket,
                    f'{directory}/files',
                    file,
                )
                local_operations.clean_up_file(file)
                record = {
                    's3URI': f's3://{bucket}/{directory}/files/{file_name}',
                    'patientId': doc['patientId'],
                    'documentId': doc['documentId'],
                    'departmentId': doc['departmentId'],
                    'documentType': doc['documentType'],
                    'sourcecreatedatetime': doc['createdatetime'],
                    'sourcelastmodifieddatetime': doc['lastmodifieddatetime'],
                }
                files_redshift_data.append(record)
        else:
            logger.error(
                f'Pages missing in Athena API response for document with id {doc["documentId"]} of type {doc["documentType"]}'
            )
            files_pages_missing[
                doc['documentType']
            ] += 1  # keep track of files with missing pages by type
    if files_pages_missing:  # if files with pages missing, log as error
        logger.error(
            f'Documents with pages missing found\n{files_pages_missing}'
        )
    return files_redshift_data


def get_data_api(
    timestamp: str,
    bucket: str,
    directory: str,
    token: str,
    context_id: str,
    client_canonical_name: str,
    last_run_time: datetime.datetime | datetime.time | None,
    all_data: bool,
):
    files_data, csv_file, patients = [], None, []
    patient_data = aws_operations.query_postgres(
        ['plaintiffs'],
    )
    if 'plaintiffs' in patient_data:
        df = patient_data['plaintiffs']

        for patient in df.itertuples():
            record = {
                'patientId': patient.GainId,
                'patientName': patient.Name,
                'departmentId': patient.DepartmentId,
            }
            patients.append(record)
    '''
    # Documents - Chart export method
    documentIds = post_files_export(patientIds, token, context_id)
    timestampString = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    files, files_redshift_data = get_files_pdf(documentIds, bucket, directory, timestampString, token, context_id)
    '''
    # Documents - Image Array method
    for document_type in athena_documenttypes[client_canonical_name][
        'DocumentTypes'
    ]:  # document types to pull
        documents = get_file_data(
            patients,
            token,
            context_id,
            document_type,
            client_canonical_name,
            last_run_time,
            all_data,
        )
        if documents:  # only retrieve details if list is being returned
            documents_data = get_files(
                documents,
                bucket,
                directory,
                timestamp,
                token,
                context_id,
                document_type,
                client_canonical_name,
            )
            files_data.extend(
                documents_data
            )  # update all files data with files data fetched for the current type
    if files_data:
        csv_file = athenahealth_local_operations.athenahealth_api_generate_csv(
            files_data, client_canonical_name, timestamp
        )
        aws_operations.upload_s3(bucket, directory, csv_file)
        aws_operations.s3_to_postgres(bucket, directory, csv_file, 'files')
    else:
        logger.error('No data fetched for files')
    return csv_file


# endregion
