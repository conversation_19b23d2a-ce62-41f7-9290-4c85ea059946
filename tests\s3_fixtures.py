import csv
import json
import os
import typing
from io import StringIO

import boto3
import pytest
from moto import mock_aws
from moto.core.models import MockAWS
from mypy_boto3_s3 import S3Client


@pytest.fixture()
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "us-east-1"


@pytest.fixture()
def aws_mock(aws_credentials: None):
    mock = mock_aws()
    mock.start()

    yield mock

    mock.stop()


@pytest.fixture()
def s3(
    aws_mock: MockAWS,
) -> typing.Generator[S3Client, typing.Any, typing.Any]:
    """
    Return a mocked S3 client
    """
    yield boto3.client("s3", region_name="us-east-1")


@pytest.fixture()
def gain_bucket(s3: typing.Any):
    s3.create_bucket(Bucket="gain-servicing")


def load_json_file_to_s3(
    s3: typing.Any, file_name: str, source: str, to_folder: str
):
    with open(
        f'integration/integration_code/fixtures/{source}/{file_name}', 'r'
    ) as json_file:
        json_data = json.load(json_file)

    csv_buffer = StringIO()
    csv_writer = csv.DictWriter(
        csv_buffer, fieldnames=json_data[0].keys(), delimiter='|'
    )
    csv_writer.writeheader()
    csv_writer.writerows(json_data)

    call_types = ['upsert', 'delete', 'update_settled']
    for call_type in call_types:
        s3.put_object(
            Bucket='gain-servicing',
            Key=f'integration/{source}/{call_type}/raw_data/processed/{to_folder}/base_data.csv',
            Body=csv_buffer.getvalue(),
        )


def load_file_to_s3(
    s3: typing.Any,
    file_name: str,
    source: str,
    to_folder: str,
    *,
    key: str | None = None,
):
    from integration.integration_code.aws_operations import upload_s3

    upload_s3(
        'gain-servicing',
        (
            key
            if key
            else f'integration/{source}/upsert/raw_data/processed/{to_folder}'
        ),
        f'integration/integration_code/fixtures/{source}/{file_name}',
    )


@pytest.fixture()
def processed_data(
    gain_bucket: typing.Any, s3: typing.Any, request: typing.Any
):
    source = request.param.get('source')
    if source == 'ati':
        litigation_placement_canonicals = [
            'plaintiffs',
            'medicalfacilities',
            'lawfirms',
            'legalpersonnel',
            'cases',
        ]
        for canonical in litigation_placement_canonicals:
            load_json_file_to_s3(
                s3,
                'base_data.json',
                source,
                canonical,
            )
        load_json_file_to_s3(s3, 'charges.json', source, 'billings')
        load_json_file_to_s3(s3, 'charges.json', source, 'charges')
        load_json_file_to_s3(s3, 'transactions.json', source, 'transactions')
        load_file_to_s3(
            s3, '26265902_DN_20150828-2023-12-19_10-51-49.pdf', source, 'files'
        )
        load_json_file_to_s3(s3, 'notes.json', source, 'notes')

    elif source == 'jopari':
        load_file_to_s3(s3, 'base_data.json', source, 'plaintiffs')
        load_file_to_s3(
            s3, 'medicalfacilities.json', source, 'medicalfacilities'
        )
        load_file_to_s3(s3, 'categories_data.json', source, 'insurances')
        load_file_to_s3(s3, 'categories_data.json', source, 'charges')
        load_file_to_s3(s3, 'categories_data.json', source, 'cases')
        load_file_to_s3(s3, 'categories_data.json', source, 'billings')
        load_file_to_s3(
            s3, '1000001954_2024-10-02_16-15-57.pdf', source, 'files'
        )


@pytest.fixture()
def unproceeded_data_ati(
    gain_bucket: typing.Any, s3: typing.Any, request: typing.Any
):
    litigation_charges_canonicals = ['billings', 'charges']
    for canonical in litigation_charges_canonicals:
        load_file_to_s3(
            s3,
            'Litigation Placement Charges_GainServicing_20250709-2025-07-09_05-23-17.txt',
            'ati',
            canonical,
        )
    load_file_to_s3(
        s3,
        '26578243_DN_20150828-2023-12-19_10-51-49.pdf',
        'ati',
        'files',
    )
    load_file_to_s3(
        s3,
        'Litigation Patient Comments_GainServicing_20250709-2025-07-09_05-23-17.txt',
        'ati',
        'notes',
    )
    load_file_to_s3(
        s3,
        'Litigation Transaction Update_GainServicing_20250709-2025-07-09_05-23-17.txt',
        'ati',
        'transactions',
    )
    litigation_placement_canonicals = [
        'plaintiffs',
        'medicalfacilities',
        'lawfirms',
        'legalpersonnel',
        'cases',
    ]
    for canonical in litigation_placement_canonicals:
        load_file_to_s3(
            s3,
            'Litigation Update_GainServicing_20250702-2025-07-02_05-25-40.txt',
            'ati',
            canonical,
        )


@pytest.fixture()
def unproceeded_data_jopari(
    gain_bucket: typing.Any, s3: typing.Any, request: typing.Any
):
    base_json_canonicals = [
        'billings',
        'cases',
        'charges',
        'insurances',
        'medicalfacilities',
        'plaintiffs',
    ]
    for canonical in base_json_canonicals:
        load_file_to_s3(
            s3,
            'jopari_PROF_2025-6-26 15.0.10.json',
            'jopari',
            canonical,
        )
    load_file_to_s3(
        s3,
        '100219603785_20250625150003.pdf',
        'jopari',
        'files',
    )


@pytest.fixture()
def ref_files(gain_bucket: typing.Any, s3: typing.Any):
    load_file_to_s3(
        s3,
        '346807174_039 - Pidcock Chiropractic Paid in FULL 2023-09-30 1334.docx',
        'filevine',
        'files',
        key='integration/filevine/hostilo/2024-09-19/files',
    )
