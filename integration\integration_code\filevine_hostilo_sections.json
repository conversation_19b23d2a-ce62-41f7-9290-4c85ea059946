{"count": 34, "offset": 0, "limit": 50, "hasMore": false, "requestedFields": "*", "items": [{"sectionSelector": "audit", "isCollection": false, "name": "Audit", "customFields": [{"fieldSelector": "instructions", "name": "Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "auditDate", "name": "Treatment Audit Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "demandPrepAuditDate", "name": "Demand Prep Audit Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "demandOutAuditDate", "name": "Demand Out Audit Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settlementAuditDate", "name": "Settlement Audit Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes", "name": "Demand Prep Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "treatmentNotes", "name": "Treatment Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "demandOutNotes", "name": "Demand Out Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "settlementNotes", "name": "Settlement Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "didTheBillingAnalystStart", "name": "Did the Billing Analyst start balances within a week of DO?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "wasThe72HourCallMadeOnT", "name": "Was the 48 Hour Call made on time?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "wasTheIntakeCallAttempted", "name": "Was the Intake call attempted/completed on time?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "wereBillsAndRecordsFollowe", "name": "Were bills and records followed up on within a decent time?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "wereTheDecPagesReceivedIn", "name": "Were the Dec Pages received in a timely manner?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "hasTheAttorneyPromptlyFoll", "name": "Has the attorney promptly followed up on offers?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "wereReductionRequestsSentO", "name": "Were reduction requests sent on time?", "dropdownItems": ["Yes", "No", "N/A"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "wasTheSubroSettlementNotic", "name": "Was the Subro settlement notice sent in a timely manner?", "dropdownItems": ["Yes", "No", "N/A"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "probateOrBankruptcy", "name": "Probate or Bankruptcy?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "dateCaseSettled", "name": "Date Case Settled", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "dateCaseMovedToDSB", "name": "Date Case Moved to DSB", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "wereBalancesStartedBeforeT", "name": "Has the BA started balances?", "dropdownItems": ["Yes", "No", "N/A"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "isTheClientInBKY", "name": "Is the client in BKY?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "bKYSearchDate", "name": "BKY Search Date", "dropdownItems": [], "customFieldType": "Date"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/audit"}}, {"sectionSelector": "bankruptcy", "isCollection": false, "name": "Bankruptcy", "customFields": [{"fieldSelector": "Filed", "name": "Filed?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "employmentorderfiled", "name": "Employment Order Filed?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "employmentorderfileddate", "name": "Employment Order Filed Date?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "motiontoapprove", "name": "Motion to Approve?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "appprovedate", "name": "Approve Date?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "hearingdatereceived", "name": "Hearing Date Received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "hearingdate", "name": "Hearing Date?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "datefiledinbankruptcycourt", "name": "Date Filed In Bankruptcy Court?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "Time", "name": "Time?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "courthouse", "name": "Courthouse?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "district", "name": "District?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "chapter", "name": "Chapter?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "bankruptcyCaseNo", "name": "Bankruptcy Case No.", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "bankruptcyTemplates", "name": "Bankruptcy Templates", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "bankruptcyDocGenTemplates", "name": "Bankruptcy Doc Gen Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "caseName", "name": "Case Name", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "bankruptcyAttorney", "name": "Bankruptcy Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "judge_1", "name": "Judge", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "trustee", "name": "Trustee", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "contacts", "name": "Contacts", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "settlementHasBeenApprovedB", "name": "Trigger When Settlement is Approved by BKY Court (No Changes Needed to SS)", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "triggerWhenSettlementIsApp", "name": "Trigger When Settlement is MODIFIED by BKY Court (Changes ARE Needed on SS)", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "docs", "name": "Docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "totalCaseExpenses", "name": "Total Case Expenses", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "totalAttorneyFees", "name": "Total Attorney Fe<PERSON>", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "totalProposedAmountToClien", "name": "Total Proposed Amount to Client", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "settlementAmount", "name": "Total Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/bankruptcy"}}, {"sectionSelector": "calllog", "isCollection": true, "name": "Client Contact", "customFields": [{"fieldSelector": "user", "name": "Your Initials", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "contactNotes", "name": "Contact Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "callRecordings", "name": "Call Recordings", "dropdownItems": [], "customFieldType": "Doc"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/calllog"}}, {"sectionSelector": "<PERSON><PERSON><PERSON><PERSON>", "isCollection": false, "name": "Case Summary", "customFields": [{"fieldSelector": "courtinfo", "name": "Court Info", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "case", "name": "Case #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "keydates", "name": "Key Dates", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "judge", "name": "Judge", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "statuteoflimitations", "name": "Statute of Limitations:", "dropdownItems": [], "customFieldType": "Deadline"}, {"fieldSelector": "cocounsel", "name": "Co-Counsel", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "firmname", "name": "Firm Name (Co Counsel Payee for Fee)", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "paralegal", "name": "Litigation Paralegal", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "courthouse", "name": "Courthouse", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON>", "name": "Primary Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON>", "name": "Case Summary", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "settlementdate", "name": "Actual Settlement Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settlementamount", "name": "Liability Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "demandsent", "name": "Liability Demand Sent Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "casemanager", "name": "Case Manager", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "staffInformation", "name": "Staff Information", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "uMSettlementDate", "name": "UM Settlement Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMSettlementAmount", "name": "UM Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "caption<PERSON><PERSON><PERSON><PERSON>", "name": "Caption Plaintiff", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "captionDefendant", "name": "Caption Defendant", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "county", "name": "County", "dropdownItems": [], "customFieldType": "Dropdown"}, {"fieldSelector": "judicialDistrict", "name": "Judicial District", "dropdownItems": [], "customFieldType": "Dropdown"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Associate Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "liabilityDemandToAttorney", "name": "Liability Demand to Attorney", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMDemandToAttorney", "name": "<PERSON><PERSON> to Attorney", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMDemandSend", "name": "UM Demand Sent Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "second<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Second Liability Demand to Attorney", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "secondDemandSentDate", "name": "Second Liability Demand Sent Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "rejectionAndFiredInformatio", "name": "Rejection and Fired Information", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "rejectedOrFiredDate", "name": "Rejected or Fired Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "whoRequestedRejectionOrFir", "name": "Who Requested Rejection or Fire?", "dropdownItems": ["Attorney", "Client"], "customFieldType": "Dropdown"}, {"fieldSelector": "reasonForRejectionOrFire", "name": "Reason for Rejection or Fire?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "claimsParalegal", "name": "Claims Paralegal", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "disbursementCoordinator", "name": "Disbursement Coordinator", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "postSettlementCoordinator", "name": "Post Settlement Coordinator", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "caseCaption", "name": "Case Caption", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "defendants", "name": "Defendants", "dropdownItems": [], "customFieldType": "PersonList"}, {"fieldSelector": "defense<PERSON><PERSON><PERSON><PERSON>", "name": "Defense Attorneys", "dropdownItems": [], "customFieldType": "PersonList"}, {"fieldSelector": "resolutionType", "name": "Resolution Type", "dropdownItems": ["Rejected", "Referral-Rejected", "Fired", "Lost (Atty Shopping/Settled with <PERSON>)"], "customFieldType": "Dropdown"}, {"fieldSelector": "secondUMDemandToAttorney", "name": "Second <PERSON><PERSON> Demand To Attorney", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "secondUMDemandSentDate", "name": "Second UM Demand Sent Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdUMDemandToAttorney", "name": "Third UM Demand To Attorney", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdUMDemandSentDate", "name": "Third UM Demand Sent Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defenseAttorneyNotes", "name": "Defense Attorney Notes (Name + Who They Rep + Your Init.)", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "valuesTestMA", "name": "Values Test (MA)", "dropdownItems": [], "customFieldType": "ReportFusion"}, {"fieldSelector": "billingAnalyst", "name": "Billing Analyst", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "attorney<PERSON>ienInformation", "name": "Attorney <PERSON><PERSON>", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "wasAttorneyLienFiled", "name": "Was Attorney <PERSON><PERSON> Filed?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "hasCheckBeenReceived", "name": "Has Check Been Received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "lienAmount", "name": "Amount of Check Received", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "filedLienAmount", "name": "Filed Lien Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "caseExpensesIncludedInFile", "name": "Case Expenses Included in Filed Lien?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "amountOfCaseExpensesInclud", "name": "Amount of Case Expenses Included", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "lienDocuments", "name": "Lien Documents", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "line", "name": "Line", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "aRClaims", "name": "AR/Claims", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "potentialClientIntake", "name": "Potential Client Intake Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "pCIREF", "name": "PCI-REF Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "temporary", "name": "Temporary Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "intake", "name": "Intake Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "treatment", "name": "Treatment Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "demandPrepLiability", "name": "Demand Prep Liability Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "liabilityDemandOut", "name": "Liability Demand Out Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "demandPrepUM", "name": "Demand Prep UM Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMDemandOut", "name": "UM Demand Out Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settlement", "name": "Settlement Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "disbursement", "name": "Disbursement Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "litigation", "name": "Litigation Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "litigationUMOnly", "name": "Litigation UM Only Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "filedSuit", "name": "Filed Suit Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "magistrate", "name": "Magistrate Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settledResolvingLiens", "name": "Settled Resolving Liens (PRV) Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settledResolvingLiensHI", "name": "Settled Resolving Liens (HI) Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settledResolvingLiensMP", "name": "Settled Resolving <PERSON><PERSON> (MP) Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "bankruptcy", "name": "Bankruptcy Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "probate", "name": "Probate Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "rejection", "name": "Rejection Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "statuteOfLimitations_1", "name": "Statute of Limitations Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "referredOut", "name": "Referred Out Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "pendingClosure", "name": "Pending Closure Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "refile", "name": "Refile Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "outOfStateCase", "name": "Out of State Case Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "otherCasesWithSameClient", "name": "Former or Other Cases with Same Client", "dropdownItems": [], "customFieldType": "ProjectLinkList"}, {"fieldSelector": "fIRED", "name": "FIRED Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "potentialClientIntakeEnd", "name": "Potential Client Intake End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "pCIREFEnd", "name": "PCI-REF End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "temporaryEnd", "name": "Temporary End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "intakeEnd", "name": "Intake End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "treatmentEnd", "name": "Treatment End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "demandPrepLiabilityEnd", "name": "Demand Prep Liability End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "liabilityDemandOutEnd", "name": "Liability Demand Out End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "demandPrepUMEnd", "name": "Demand Prep UM End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMDemandOutEnd", "name": "UM Demand Out End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settlementEnd", "name": "Settlement End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "disbursementEnd", "name": "Disbursement End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "litigationEnd", "name": "Litigation End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "litigationUMOnlyEnd", "name": "Litigation UM Only End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "filedSuitEnd", "name": "Filed Suit End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "magistrateEnd", "name": "Magistrate End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settledResolvingLiensPRV", "name": "Settled Resolving Liens (PRV) End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settledResolvingLiensHIE", "name": "Settled Resolving Liens (HI) End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "settledResolvingLiensMPE", "name": "Settled Resolving <PERSON><PERSON> (MP) End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "bankruptcyEnd", "name": "Bankruptcy End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "probateEnd", "name": "Probate End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "rejectionEnd", "name": "Rejection End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "fIREDEnd", "name": "FIRED End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "statuteOfLimitationsEnd", "name": "Statute of Limitations End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "referredOutEnd", "name": "Referred Out End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "pendingClosureEnd", "name": "Pending Closure End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "refileEnd", "name": "Refile End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "outOfStateCaseEnd", "name": "Out of State Case End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "lastClientContact", "name": "Last Client Contact", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "priorPhaseID", "name": "Prior PhaseID", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "updateProjects", "name": "Update Projects", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "buttonForProjects", "name": "Button for Projects", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "2ndLiabilitySettlementAmoun", "name": "2nd Liability Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "2ndLiabilitySettlementDate", "name": "2nd Liability Settlement Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "2ndUMSettlementDate", "name": "2nd UM Settlement Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "otherSettlementDateNot1st", "name": "Other Settlement Date (Not 1st or 2nd)", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "2ndUMSettlementAmount", "name": "2nd UM Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "otherSettlementAmount", "name": "Other Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "totalSettlementAmount", "name": "Total Settlement Amount", "dropdownItems": [], "customFieldType": "CalculatedCurrency"}, {"fieldSelector": "hIDDENFIELDS", "name": "HIDDEN FIELDS", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "settlementPaymentItem", "name": "Settlement Payment Item", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "reasonForRejection", "name": "Reason for Rejection?", "dropdownItems": ["No Coverage", "No Contact", "Unreasonable Expectations", "Attorney Shopping", "Gap in Treatment or No Treatment", "At Fault", "Client won't accept offer", "<PERSON><PERSON>", "PD or WC Only", "Word v. Word", "Conflict of Interest", "Client settled with insurance company.", "Other"], "customFieldType": "Dropdown"}, {"fieldSelector": "swapAttorneysTestField", "name": "Swap attorneys? (Test field)", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "reassigningAttorney", "name": "Reassigning Attorney", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "submittedForRejectionLessT", "name": "Submitted for Rejection less than 30 days from signup?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "doNotUse", "name": "Do not use", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "iNIBenefitsCoordinator", "name": "INI Benefits Coordinator", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "dEMDOBenefitsCoordinator", "name": "DEM/DO Benefits Coordinator", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "sETBenefitsCoordinator", "name": "SET Benefits Coordinator", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "financeTeam", "name": "Finance Team", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "thirdLiabilitySentDate", "name": "Third Liability To Attorney", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdLiabilitySentDate_1", "name": "Third Liability Sent Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "3rdLiabilitySettlementAmoun", "name": "3rd Liability Settlement Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "3rdLiabilitySettlementAmoun_1", "name": "3rd Liability Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "attyFeeLDNew", "name": "Atty Fee LD New", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "attyFeeLDAddl", "name": "Atty Fee LD Addl", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "lDAttyFeeTotal", "name": "LD Atty Fee Total", "dropdownItems": [], "customFieldType": "CalculatedCurrency"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/casesummary"}}, {"sectionSelector": "checkRequest", "isCollection": true, "name": "Check Request", "customFields": [{"fieldSelector": "amount", "name": "Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "payableTo", "name": "Payable To", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "memo", "name": "Memo (prints on check to vendor)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "description", "name": "Description (appears on printed full page checks)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "invoice", "name": "Invoice", "dropdownItems": [], "customFieldType": "Doc"}, {"fieldSelector": "notes", "name": "Notes (internal notes, not pushed to QuickBooks)", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "lastUpdateFromQuickBooks", "name": "Last update from QuickBooks", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "sentToQuickBooks", "name": "Sent to Quick<PERSON><PERSON>s", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "paymentStatus", "name": "Payment Status", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "paymentInstructions", "name": "Payment Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "status", "name": "Status", "dropdownItems": ["Created", "Printed", "Void"], "customFieldType": "Dropdown"}, {"fieldSelector": "dateOfCheck", "name": "Date of Check", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "checkNumber", "name": "Check Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "emailAccounting", "name": "Email Accounting", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "emailAccountingButtonInfo", "name": "Email Accounting Button Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "accountingOnly", "name": "Accounting Only", "dropdownItems": [], "customFieldType": "Instructions"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/checkRequest"}}, {"sectionSelector": "cQCR_1", "isCollection": false, "name": "CQCR", "customFields": [{"fieldSelector": "startDate", "name": "CQC Review Start Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "hasCaseManagerIntakeCallB_1", "name": "Did the Case Manager call the client, or attempt to call the client within 24 hours per SCOPE?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideFirst24HourCall", "name": "CQCR Guide, First 24 Hour Call", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "wasTheAttorneySFirstCall", "name": "Was the Attorney's first call to client made within the first 72 hours of intake?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuide72HourCall", "name": "CQCR Guide: 72 hour call", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheCaseManagerRequested", "name": "Has the Case Manager requested initial bills and records per SCOPE requirements timeline?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideInitialBillsRecs", "name": "CQCR Guide: Initial Bills/Recs", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheClaimsManagerReceive", "name": "Was the AR requested?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideAR", "name": "CQCR Guide: AR", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheClaimsManagerSetUp", "name": "Has the Claims Manager set up all possible auto claims per SCOPE requirements timeline?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimingOfWhenThe", "name": "CQCR Guide: Timing of when the claim is set up", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheCMOrACMConfirmedRe", "name": "Has the CM or ACM confirmed receipt of the auto LOR and Dec Pages and received verbal limits?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideDecPagesVerbal", "name": "CQCR Guide: Dec Pages/Verbal", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheLORForHealthInsuran", "name": "Has the LOR for Health Insurance been sent?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineHILORs", "name": "CQCR Guide Timeline: HI LORs", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheMedPayDemandBeenSen", "name": "Has the MedPay Demand been sent? (if not applicable, leave blank)", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineMedpayDe", "name": "CQCR Guide Timeline: Medpay Demand", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheDemandBeenReviewedB", "name": "Has the Demand been reviewed by the Attorney and sent in the required timeframe per SCOPE documents?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineDemandRe", "name": "CQCR Guide Timeline: Demand Review by Attorney", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheCaseManagerOrAssig", "name": "Has the Demand Package(s) been sent to the applicable companies?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineDemandSe", "name": "CQCR Guide Timeline: Demand sent to Ins Carrier", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheAttorneyOrAssigned", "name": "Has the Attorney (or assigned person) confirmed that the Demand was Received?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineConfirmin", "name": "CQCR Guide Timeline: Confirming Demand Recieved", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheAttorneyBeenUpdating", "name": "Does the \"Negotiations Section\" contain negotiation information? (if applicable)", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineNegotiati", "name": "CQCR Guide Timeline: Negotiations", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheBenefitCoordinatorsS", "name": "Has the Billing Analyst started verifying provider balances?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineBalances", "name": "CQCR Guide Timeline: Balances", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "pt1HasTheAttorneyReceived", "name": "Pt1. Has the Attorney received Settlement Authority? Pt2. Is the settlement authority documented?", "dropdownItems": ["Yes (Pt1)", "No (Pt1)", "Yes (Pt2)", "No (Pt2)"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineSettlemen", "name": "CQCR Guide Timeline: Settlement Authority", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasTheSettlementAcceptance", "name": "Has the settlement acceptance letter been sent out?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineAcceptanc", "name": "CQCR Guide Timeline: Acceptance Letter", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "hasThePostSettlementCoordi", "name": "Has the Post Settlement Coordinator followed up with the adjuster as required?", "dropdownItems": ["Yes", "No"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "cQCRGuideTimelineAcceptanc_1", "name": "CQCR Guide Timeline: Acceptance Letter Confirmation", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "first14Days", "name": "First 14 Days Review", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "first30Days", "name": "First 30 Days Review", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "afterTreatmentPhaseReview", "name": "After Treatment Phase Review", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "duringDemandOutPhaseReview", "name": "During Demand Out Phase Review", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "duringSettlementPhaseReview", "name": "After Settlement Phase Review", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "cQCRGUIDEYesNoMeaning", "name": "CQCR GUIDE: Yes/ No Meaning", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "tESTDONTUSE", "name": "TEST DONT USE", "dropdownItems": [], "customFieldType": "ReportFusion"}, {"fieldSelector": "tESTDONTUSEFORVIDEO", "name": "TEST DONT USE FOR VIDEO", "dropdownItems": [], "customFieldType": "MultiReportFusion"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/cQCR_1"}}, {"sectionSelector": "damages", "isCollection": false, "name": "Damages", "customFields": [{"fieldSelector": "nonEconomicDamages", "name": "Non-Economic Damages", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "clientCanNoLongerDoList", "name": "Client can no longer do (list):", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "clientExperiencesPainWhen", "name": "Client Experiences Pain When:", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "healthHistoryOverallHealt", "name": "Health History - Overall Health (surgeries, diabetes, etc.)", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "economicDamages", "name": "Economic Damages", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "employedOnDateOfIncident", "name": "Employed on Date of Incident", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "employer", "name": "Employer", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "supervisor", "name": "Supervisor", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "jobTitle", "name": "Job Title", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "duties", "name": "Duties", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "lostWagesStart", "name": "Lost Wages Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "lostWagesEnd", "name": "Lost Wages End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "hourlyPayRateOrAnnualSala", "name": "Hourly Pay Rate or Annual Salary", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "workingHoursMissed", "name": "Working Hours Missed:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "uploadClientStatementQuesti", "name": "Upload Client Statement/Questionnaire", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "additionalEmployment", "name": "Additional Employment?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "employer2", "name": "Employer 2", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "employer2HourlyPayRateAnn", "name": "Employer 2 Hourly Pay Rate/Annual Salary", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "employer3", "name": "Employer 3", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "employer3HourlyPayRateAnn", "name": "Employer 3 Hourly Pay Rate/Annual Salary", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "taxReturns", "name": "Tax Returns", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "w21099", "name": "W2/1099", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "paystubs", "name": "Paystubs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "additionalEconomicDamages", "name": "Additional Economic Damages", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "describeDamagesAndAmounts", "name": "Describe Damages and Amounts", "dropdownItems": [], "customFieldType": "StringList"}, {"fieldSelector": "outOfPocketReceipts", "name": "Out of Pocket Receipts", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "additionalEconomicDamagesNo", "name": "additional economic damages notes", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "priors", "name": "Priors", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "detailPriorInjuriesLegalC", "name": "Detail prior injuries, legal claims, and/or insurance claims:", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "priorsInstructions", "name": "Priors instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "employmentLostWagesNotes", "name": "Employment - Lost Wages Notes", "dropdownItems": [], "customFieldType": "Text"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/damages"}}, {"sectionSelector": "parties", "isCollection": true, "name": "Defendants", "customFields": [{"fieldSelector": "name", "name": "Name", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Attorney for Defendant", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "servicedate", "name": "Service Date", "dropdownItems": [], "customFieldType": "Deadline"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "defendantLetterDocGenTempl", "name": "Defendant Letter Doc Gen Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/parties"}}, {"sectionSelector": "demand", "isCollection": false, "name": "Demand", "customFields": [{"fieldSelector": "adjuster", "name": "Adjuster", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "claimNumber", "name": "Claim Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "gradedBy", "name": "Graded By?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "dateGraded", "name": "Date Graded", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "medicalSpecials", "name": "Medical Specials", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "lostWages", "name": "Lost Wages", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "mileage", "name": "Mileage", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "caseExpenses", "name": "Case Expenses", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "totalSpecials", "name": "Total Specials", "dropdownItems": [], "customFieldType": "CalculatedCurrency"}, {"fieldSelector": "demandInformation", "name": "Demand Information", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "dUI", "name": "DUI?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "demandedLimits", "name": "Demanded Limits?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "clientStillTreating", "name": "Client Still Treating?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "liens", "name": "Liens:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "bankruptcy", "name": "Bankruptcy?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "healthInsurance", "name": "Health Insurance?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "insurance", "name": "Insurance", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "liabilityLimits", "name": "Liability Limits:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "uMUM", "name": "UM/UM:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "2ndUM", "name": "2nd UM:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "2ndLiability", "name": "2nd Liability:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "medpay", "name": "Medpay:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "medpayRequested", "name": "Med<PERSON><PERSON> Requested?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "medpayRecD", "name": "Medpay Rec'd?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "__DecPagesRecieved__", "name": "Dec Pages In File?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "propertyDamageHandled", "name": "Property Damage Handled?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "vehicleTotaled", "name": "Vehicle Totaled?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "fee", "name": "Fee %:", "dropdownItems": [], "customFieldType": "Percent"}, {"fieldSelector": "notesForAttorney", "name": "Notes For Attorney:", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "insuranceCompany", "name": "Insurance Company", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "injuries", "name": "Injuries:", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "demandDocs", "name": "Demand Docs", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "docAttachments", "name": "Doc Attachments", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "oCGAViolationS", "name": "OCGA Violation(s) - Write violations and descriptions as you want them to appear on the Demand.", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "demandAmount", "name": "<PERSON><PERSON> Amount", "dropdownItems": [], "customFieldType": "Integer"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/demand"}}, {"sectionSelector": "depos", "isCollection": true, "name": "De<PERSON>s", "customFields": [{"fieldSelector": "datescheduled", "name": "Date Scheduled", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "deponent", "name": "Deponent", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "prepdate", "name": "Prep Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "prepped", "name": "Prepped", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "generalnotes", "name": "General Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "redflags", "name": "Red Flags", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "documents", "name": "Documents", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "depositionDocumentGeneration", "name": "Deposition Document Generation", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "timeScheduled", "name": "Time Scheduled", "dropdownItems": [], "customFieldType": "String"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/depos"}}, {"sectionSelector": "disbursals", "isCollection": true, "name": "Disbursals", "customFields": [{"fieldSelector": "typeofcheckrequest", "name": "Type of Check Request", "dropdownItems": ["Due to <PERSON>rm (Attorney <PERSON>)", "Due to Firm (Addl Policy Attorney Fe<PERSON>)", "Due to Firm (Expenses)", "Due to Firm (Postage)", "Client Proceeds", "Due to Firm (Subro Reimbursement)", "Due to Firm (PRV Reimbursement)", "Atty Fee Reimbursement", "Co-Counsel", "Provider", "Miscellaneous", "Excess Lien"], "customFieldType": "Dropdown"}, {"fieldSelector": "checkrequestsforclientfirm", "name": "Check Requests for Client/Firm", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "amountdue", "name": "Amount Due", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "checkinstruction", "name": "Check Instruction", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "amountpaid", "name": "Amount <PERSON>", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "checkdate", "name": "Check Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "checkmemo", "name": "Memo (sends to Quickbooks)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "checkrequest", "name": "Payment Information", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "checkinfo", "name": "Check Info", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "status", "name": "Status of Payment", "dropdownItems": ["In Queue", "Moved to Accounting", "Paid", "Voided"], "customFieldType": "Dropdown"}, {"fieldSelector": "checkhistory", "name": "Check History", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "payee", "name": "Disbursal Check Payee", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "checknumber", "name": "Check No./Wire No.", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "documents", "name": "Documents", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "market", "name": "Market", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "sendToTrustCheckRequest", "name": "Create Trust Check Request", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "fundsReadyToWire", "name": "Funds Ready to Wire", "dropdownItems": [], "customFieldType": "ActionButton"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/disbursals"}}, {"sectionSelector": "expenses", "isCollection": true, "name": "Postage Only", "customFields": [{"fieldSelector": "checkmemo", "name": "Memo", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "docs", "name": "Docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "date", "name": "Date of Expense", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "checknumber", "name": "Check Number", "dropdownItems": [], "customFieldType": "Integer"}, {"fieldSelector": "amountdue", "name": "Amount Due", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "payee", "name": "Payee", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "status", "name": "Status", "dropdownItems": ["In Queue", "Moved to Accounting", "Paid", "Voided"], "customFieldType": "Dropdown"}, {"fieldSelector": "sendtofvcheckreq", "name": "Request Check", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "checkRequest", "name": "Check Request", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "qBInstructions", "name": "QB Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "amountpaid", "name": "Total Check Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "checkdate", "name": "Check Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "checkhistory", "name": "Check History", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "expenseType", "name": "Expense Type", "dropdownItems": ["Postage"], "customFieldType": "Dropdown"}, {"fieldSelector": "transactionamount", "name": "Transaction Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/expenses"}}, {"sectionSelector": "experts", "isCollection": true, "name": "Experts", "customFields": [{"fieldSelector": "expert", "name": "Expert", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "purpose", "name": "Purpose", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "field", "name": "Field", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "dateretained", "name": "Date Retained", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "retainer", "name": "Retainer", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "imedate", "name": "IME Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "documents", "name": "Documents", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "expertDocGenerationTemplate", "name": "Expert Doc Generation Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/experts"}}, {"sectionSelector": "settlementS", "isCollection": false, "name": "Final Costs & Fees", "customFields": [{"fieldSelector": "coCounselFees", "name": "Co-Counsel Fe<PERSON>?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "coCounsel1", "name": "Co-Counsel 1", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "coCounsel1PercentageOfFee", "name": "Co-Counsel 1 Percentage of Fee", "dropdownItems": [], "customFieldType": "Percent"}, {"fieldSelector": "coCounsel2", "name": "Co-Counsel 2", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "coCounsel2PercentageFee", "name": "Co-Counsel 2 Percentage Fee", "dropdownItems": [], "customFieldType": "Percent"}, {"fieldSelector": "quickBooksCaseCosts", "name": "QuickBooks Case Costs", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "viewDetailedTransactions", "name": "View Detailed Transactions", "dropdownItems": [], "customFieldType": "Url"}, {"fieldSelector": "instructionsQB", "name": "Instructions - QB", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "quickbookCaseCosts", "name": "Quickbook Case Costs", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "additionalFees", "name": "Additional Fees", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "attorneyFeeFORLD", "name": "Attorney <PERSON> (FOR LD)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "totalExpensesTESTING", "name": "Total Expenses (TESTING)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "totalAllExpenses", "name": "Total All Expenses", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "updateFromQuickBooks", "name": "Update from QuickBooks", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "lastUpdateFromQuickBooks", "name": "Last Update from QuickBooks", "dropdownItems": [], "customFieldType": "String"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/settlementS"}}, {"sectionSelector": "forms", "isCollection": false, "name": "Forms", "customFields": [{"fieldSelector": "settlementStatement", "name": "Settlement Statement", "dropdownItems": [], "customFieldType": "ReportFusion"}, {"fieldSelector": "generalLetters", "name": "General Letters", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "clientLetters", "name": "Client Letters", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "litigationTemplates", "name": "Litigation Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "documentGenerationTemplates", "name": "Document Generation Templates", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "documentGenerationInstructio", "name": "Document Generation Instructions", "dropdownItems": [], "customFieldType": "Instructions"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/forms"}}, {"sectionSelector": "insurance", "isCollection": true, "name": "Insurance", "customFields": [{"fieldSelector": "insurer", "name": "Insurer", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "adjuster", "name": "Adjuster", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "policynumber", "name": "Policy Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "claimnumber", "name": "Claim Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "policylimits", "name": "Policy  Limits", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "fileattachment", "name": "File Attachment", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "insurancetype", "name": "Insurance Type", "dropdownItems": ["Health", "Homeowner's", "Liability", "Liability - Passenger", "Liability Rideshare", "Life Insurance", "Med Pay", "Medicaid", "Medicare", "No Coverage", "No Coverage-Liability", "No Coverage-UM", "No-Fault", "Personal UM/UIM", "PIP", "Property Damage", "Resident Relative", "Self-Insured", "Short / Long Term Disability", "Third Party Administrator", "UM/UIM", "UM/UIM Rideshare", "Umbrella-Liability", "Umbrella-UM", "Uninsured Motorist", "Worker's Comp"], "customFieldType": "Dropdown"}, {"fieldSelector": "insured", "name": "Insured", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "insurancetemplates", "name": "Insurance Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "analystS", "name": "Analyst(s)", "dropdownItems": [], "customFieldType": "PersonList"}, {"fieldSelector": "liabilityAccepted", "name": "Liability Accepted?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "memberID", "name": "Member ID #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "groupNo", "name": "Group No:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "decPageReceived", "name": "Dec Page Received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "driver", "name": "Driver", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "insuranceDocGenTemplates", "name": "Insurance Doc Gen Templates", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "medpayExhausted", "name": "Medpay Exhausted?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "defenseCounsel", "name": "Defense Counsel", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "settled", "name": "Settled?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "settledAmount", "name": "Settled Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "settledBy", "name": "Settled By?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "probate", "name": "Probate?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "probate_1", "name": "Probate", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "bankruptcy", "name": "Bankruptcy?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "subrogationLien", "name": "Subrogation Lien?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "otherLiens", "name": "Other Liens?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "<PERSON><PERSON><PERSON>", "name": "Attorney <PERSON><PERSON>", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "attorneyFeeFlexible", "name": "Attorney <PERSON>e Flexible?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "amountToClient", "name": "Amount to Client", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "billsPaid", "name": "Bills Paid?", "dropdownItems": ["Yes", "No", "Liens Only", "Other (See Below)"], "customFieldType": "Dropdown"}, {"fieldSelector": "biggestPieceOfThePie", "name": "Biggest Piece of the Pie?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "releaseType", "name": "Release Type", "dropdownItems": ["Limited Liability", "Covenant Not to Execute", "General", "Alabama Pro Ami"], "customFieldType": "Dropdown"}, {"fieldSelector": "additionalReleaseDetails", "name": "Additional Release Details", "dropdownItems": ["No Further Details", "<PERSON>t LLR with <PERSON><PERSON>", "Need Release Signed Before Check Sent", "Multiple Claimants Involved", "Minor"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "wasThisCaseReferredInByA", "name": "Was This Case Referred In By an Attorney?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "<PERSON><PERSON><PERSON>", "name": "Referred By?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "referralFeePercentage", "name": "Referral Fee Percentage", "dropdownItems": [], "customFieldType": "Percent"}, {"fieldSelector": "settlementNotes", "name": "Settlement Notes", "dropdownItems": [], "customFieldType": "TextLarge"}, {"fieldSelector": "verifyAdjuster", "name": "Veri<PERSON> Adjuster", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "howWasCaseSettled", "name": "How Was <PERSON> Settled?", "dropdownItems": ["Email", "In Person", "Mediation", "PLDA", "Recorded Line", "Text"], "customFieldType": "Dropdown"}, {"fieldSelector": "settlementSummary", "name": "Settlement Summary", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "settlementSummaryTemplate", "name": "Settlement Summary Template", "dropdownItems": [], "customFieldType": "MultiReportFusion"}, {"fieldSelector": "dateSettled", "name": "Date Settled?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "verifyContact", "name": "Verify Contact", "dropdownItems": ["Single", "Married", "Minor", "Deceased"], "customFieldType": "Dropdown"}, {"fieldSelector": "policyTender", "name": "Policy Limit Tender?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "negotiations", "name": "Negotiations", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "negotiationInstructions", "name": "Negotiation Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "newNegotiation", "name": "New Negotiation:", "dropdownItems": [], "customFieldType": "StringList"}, {"fieldSelector": "isCaseInNegotiation", "name": "Is case in Negotiation?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "settlementInformation", "name": "Settlement Information", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "medicalAndExpensesSummary", "name": "Medical and Expenses Summary (if applicable with demand)", "dropdownItems": [], "customFieldType": "MultiReportFusion"}, {"fieldSelector": "triggerMedpayTaskflow", "name": "Trigger Medpay Taskflow", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "pIPExhausted", "name": "PIP Exhausted?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "adjusterInfo", "name": "Adjuster Info", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "pDAndBIAdjusterInformation", "name": "PD and BI Adjuster Information", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "settlementStatement", "name": "Settlement Statement", "dropdownItems": [], "customFieldType": "OutlawTemplate"}, {"fieldSelector": "isLiabilityAccepted", "name": "Is Liability Accepted?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "demands", "name": "Demands by Attorney", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "pushToDemandSection", "name": "Push to Demand Section", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "doesAttorneyWantToReviewS", "name": "Does Attorney want to review SS when ready?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "isLiabilityAcceptedLiabil", "name": "Is Liability Accepted? (Liability Passenger)", "dropdownItems": [], "customFieldType": "Boolean"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/insurance"}}, {"sectionSelector": "intake", "isCollection": false, "name": "Intake", "customFields": [{"fieldSelector": "accidentinformation", "name": "Accident Information", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "accidenttype", "name": "Case Type", "dropdownItems": ["Dog Bite", "Dual (Premises/WC)", "Motor Vehicle Accident", "Med Mal", "Nursing Home Negligence ( Med Mal )", "Premises Liability", "Product Liability", "Workers Comp", "Social Security", "Referred Out", "Worker Comp Referred Out", "Recycled Case Numbers", "Civil", "Bankruptcy", "Breach of Contract/ Negligence", "Wrongful Death"], "customFieldType": "Dropdown"}, {"fieldSelector": "timeandlocationofaccident", "name": "Time of Incident", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "wereauthoritiescalledtothescene", "name": "Police Called?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "authorities", "name": "Policy Agency", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "werecitationsissued", "name": "Were citations issued?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "citation", "name": "Citation", "dropdownItems": [], "customFieldType": "Doc"}, {"fieldSelector": "sfpremisepictures", "name": "S&F/Premises Pictures", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "uploadpremisepics", "name": "Upload Premises Pics", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "descriptionofaccident", "name": "Description of Incident", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "accidentdiagrams", "name": "Accident Diagram(s)", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "policereport", "name": "Police Report", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "policereportupload", "name": "Police Report Upload", "dropdownItems": [], "customFieldType": "Doc"}, {"fieldSelector": "propertydamage", "name": "Property Damage", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "clientsvehiclemakemodel", "name": "Clients Vehicle - Make & Model", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "damagetoclientsvehicle", "name": "Damage To Clients Vehicle", "dropdownItems": ["Front End", "Left", "Rear End", "Right", "Total Loss", "Multiple Damages"], "customFieldType": "Dropdown"}, {"fieldSelector": "defendantsvehiclemakemodel", "name": "Defendant's Vehicle - Make & Model", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "damagetodefendantsvehicle", "name": "Damage to Defendant's Vehicle", "dropdownItems": ["Front End", "Left", "Rear End", "Right", "Total Loss"], "customFieldType": "Dropdown"}, {"fieldSelector": "picturesofdamages", "name": "Pictures of Damages?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "bywhomandwhere", "name": "By Whom and Where", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "datetaken", "name": "Date Taken", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "photosofdamages", "name": "Photos of Damages", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "locationofclientsvehicle", "name": "Location of Client's Vehicle", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "vehicletowed", "name": "Vehicle Towed", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "towedby", "name": "Towed By", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "towedto", "name": "Towed To", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "estimatedone", "name": "Estimate Done", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "listcarriers", "name": "Who Performed Estimate?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "amount", "name": "Amount", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "datedone", "name": "Date Done", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "injuries", "name": "Injuries", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "injuriessustainedpaincomplaints", "name": "Injuries Sustained/ Pain Complaints", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "picturestakenofinjuries", "name": "Pictures taken of Injuries", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "picturestakenbywhomandwhere", "name": "Pictures taken by whom and where", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "datepicturesofinjuriestanken", "name": "Date Pictures of Injuries Taken", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "picturesofinjuries", "name": "Pictures of Injuries", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "priors", "name": "Priors", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "priorinjuries", "name": "Prior Injuries", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "economicdamages", "name": "Economic Damages", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "employmentstatus", "name": "Employment Status", "dropdownItems": ["Employed - Not Losing Wages", "Employed - Losing Wages", "Unemployed", "Seasonal", "Student", "Temporary", "Full Time", "Disabled", "Self Employed", "Part Time", "Retired", "Minor", "1099 Employee"], "customFieldType": "Dropdown"}, {"fieldSelector": "employer", "name": "Employer", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "lostwagesstart", "name": "Lost Wages Start", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "lostwagesend", "name": "Lost Wages End", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "jobtitle", "name": "Job Title", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "duties", "name": "Duties", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "clientshourlyratesalary", "name": "Client's Hourly Rate/ Salary", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "noneconomicdamages", "name": "Non Economic Damages", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "thingsclientcannolongerdoatall", "name": "Things Client can no longer do at all", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "canbutwithpainn<PERSON>swell", "name": "Can, but with pain/ not as well", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "lossoflifesenjoyment", "name": "Loss of life's enjoyment", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "healthhistoryoverallhealthsurgeriesbrokenbonesetc", "name": "Health History - Overall Health (surgeries, broken bones, etc.)", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "wrapup", "name": "Wrap-Up", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "subsequentaccidents", "name": "Subsequent Accidents", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "explanation", "name": "Explanation", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "personalobservationtake", "name": "Personal Observation / Take", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "reviewsocialmedialetterpolicy", "name": "Does Client have Social Media?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "additionalthoughtsconcernsraisedaboutsocialmedia", "name": "Additional Thoughts/Concerns Raised about Social Media", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "allsocialmediaplatformsanduserids", "name": "All Social Media Platforms and User ID's", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "workerscompcarrier", "name": "Workers Comp Carrier", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "workerscompadjuster", "name": "Workers Comp Adjuster", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "workerscompclaim", "name": "Workers Comp Claim #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "indemnityclaim", "name": "Indemnity Claim", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "clientjobdescriptiontitleduties", "name": "Client Job Description (title/duties)", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "lastdateworked", "name": "Last Date Worked", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "accidentreportfirstreportofinjury", "name": "Incident Report/First Report of Injury", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "accidentsreportfirstreportofinjuryupload", "name": "Incident Report/First Report of Injury Upload", "dropdownItems": [], "customFieldType": "Doc"}, {"fieldSelector": "towhomwasitgiven", "name": "Who was the report given to?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "responsegiven", "name": "Response Given", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "whatform4336etc", "name": "What kind of response? (Form 43, 36, etc.)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "receivingbenefits", "name": "Receiving Benefits", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "benefitsreceivingreceived", "name": "Benefits Receiving/Received", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON>", "name": "How much in benefits per week?", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "animalowner", "name": "Animal Owner", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "typeofanimal", "name": "Type of Animal", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "product", "name": "Product", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "manufacturer", "name": "Manufacturer", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "supervisor", "name": "Supervisor", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "previousjobs<PERSON>e", "name": "Previous Jobs Done", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "detailedjobdescriptionthisaccident", "name": "Detailed Job Description - This Accident", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "additionalinfo", "name": "Additional Info", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "whoispayingfortreatment", "name": "Who is Paying for treatment", "dropdownItems": ["LOP", "Client", "WC", "Insurance"], "customFieldType": "Dropdown"}, {"fieldSelector": "authorizedtreatingphysician", "name": "Authorized Treating Physician", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "additionaldocs", "name": "Additional Docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "witnesses", "name": "Witnesses", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "locationofaccident", "name": "Location of Incident", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "dateofintake", "name": "Intake Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "<PERSON><PERSON><PERSON>", "name": "Referred Out To", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "referralfee", "name": "Referral Fee", "dropdownItems": [], "customFieldType": "Percent"}, {"fieldSelector": "wereyoutransportedinanambulance", "name": "Were you transported in an ambulance?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "nameofambulance", "name": "Ambulance Company", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "didyougotothehospital", "name": "Did you go to the hospital?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "hospital", "name": "Hospital", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "resolution", "name": "Resolution", "dropdownItems": ["Accepted", "Declined", "Case Referred Out", "Incoming Referral"], "customFieldType": "Dropdown"}, {"fieldSelector": "datecontractsigned", "name": "Date Contract Signed", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "personperformingintake", "name": "Intake Coordinator", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "details", "name": "Referral Details", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "qualstat", "name": "Qualification Status", "dropdownItems": ["Qualified", "Unqualified", "Pending"], "customFieldType": "Dropdown"}, {"fieldSelector": "<PERSON><PERSON>ton", "name": "Enter witness information", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "insurancebutton", "name": "Insurance Button", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "incident", "name": "Date of Incident", "dropdownItems": [], "customFieldType": "IncidentDate"}, {"fieldSelector": "intake", "name": "Case Intake", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "familyinfoheader", "name": "Family Info", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON>", "name": "Spouse/Partner (if applicable)", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "children", "name": "Children", "dropdownItems": [], "customFieldType": "PersonList"}, {"fieldSelector": "maincontact", "name": "Main Contact (caretaker, guardian, or client)", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "malpracticenegligenceoccured", "name": "Describe what malpractice/negligence you believe occurred.", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "physicianhospitalresponsible", "name": "List all physicians, hospitals, or others you believe may be responsible for your injuries.", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "medicalmalpractice", "name": "Medical Malpractice", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "typeofmedmal", "name": "Type of Med Mal (Add them to Dropdown)", "dropdownItems": [], "customFieldType": "Dropdown"}, {"fieldSelector": "othermedmal", "name": "Other Med Mal?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "originalcondition", "name": "For what condition were you originally seeing doctor or hospital when malpractice occurred?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "permanentscarring", "name": "Has injury caused permanent injury or scarring?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "injurycorrected", "name": "Has injury been corrected?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "injurysurgicalimplant", "name": "Was injury caused by defective surgical implant?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "whatimplant", "name": "What implant and where was it implanted?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "nursinghomenegligence", "name": "Nursing Home Negligence", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "typeofnursinghomenegabuse", "name": "Type of Nursing Home Neg./Abuse (add dropdown items)", "dropdownItems": [], "customFieldType": "Dropdown"}, {"fieldSelector": "othernursinghomenegligence", "name": "Other Nursing Home Negligence", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "admittedtonursinghomedate", "name": "When was resident admitted to Nursing Home?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "nursinghomepreviousincident", "name": "Did resident have previous incidents at Nursing Home?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "reportnegtonursinghome", "name": "Did you report negligence / abuse to Nursing Home?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "personyoufilednegreportwith", "name": "Person you filed report with?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "didnursinghomenotifyyouofincident", "name": "Did Nursing Home notify you of incident?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "whenwereyounotified", "name": "When were you notified?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "residentgotohospital", "name": "Did resident go to the hospital following the incident?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "residenttakenbyambulance", "name": "Was resident taken to hospital by ambulance?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "residentothermedproviders", "name": "Has resident seen any other medical providers for your injuries sustained in the incident?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "othermedproviderfaultnursinghome", "name": "Has any other medical provider said the resident’s injury was the fault of the Nursing Home?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "governmentowned", "name": "Government, City or County Owned Vehicle?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "Antilitem", "name": "Anti-Litem", "dropdownItems": [], "customFieldType": "PersonList"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Driver of Vehicle Involved", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "roleinaccident", "name": "Role in Incident", "dropdownItems": ["Driver", "Passenger", "Pedestrian", "Other", "Victim"], "customFieldType": "Dropdown"}, {"fieldSelector": "reportnumber", "name": "Report Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "rentaloption", "name": "Rental Options", "dropdownItems": ["Own", "Liability", "None"], "customFieldType": "Dropdown"}, {"fieldSelector": "xrays", "name": "X-Rays?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "radiology<PERSON><PERSON><PERSON>", "name": "Radiology Provider", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "bankruptcy", "name": "Bankruptcy?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "bankruptcytab", "name": "Notify of Bankruptcy", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "moreinfo", "name": "Market", "dropdownItems": ["Aiken", "Alabama", "Albany", "Atlanta", "Augusta", "Beaufort", "Charleston", "Columbus", "Florida", "Macon", "Savannah"], "customFieldType": "Dropdown"}, {"fieldSelector": "intakeInstructions", "name": "Intake Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "<PERSON><PERSON><PERSON>", "name": "Referred By?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "referralSource", "name": "Referral Source", "dropdownItems": ["Attorney", "Billboard", "Client", "Facebook", "Family Member", "Friend", "Instagram", "LinkedIn", "Online Search", "Professional", "Radio Commercial", "Television Commercial", "Twitter", "Word of Mouth"], "customFieldType": "Dropdown"}, {"fieldSelector": "doesClientHaveHealthyInsur", "name": "Does Client Have Health Insurance?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "accidentIncidentReportInfor", "name": "Accident/Incident Report Information", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "signUpDocuments", "name": "Sign Up Documents", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "newClientVerificationCheckl", "name": "New Client Verification Checklist", "dropdownItems": [], "customFieldType": "DocGen"}, {"fieldSelector": "isClientTheOwnerOfTheVeh", "name": "Is client the owner of the vehicle?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "createNewMedsItemWithHosp", "name": "Create New Meds Item with Hospital Contact", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "createNewMedsProviderForR", "name": "Create New Meds Provider for Radiology Provider", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "haveYouTreatedWithYourPri", "name": "Have you treated with your primary care physician for this incident?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "primaryCarePhysician", "name": "Primary Care Physician?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "createNewMedsProviderForP", "name": "Create New Meds Provider for Primary Care Physician", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "areYouTreatingWithAChirop", "name": "Are you treating with a Chiropractor for this Incident?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "chiropractor", "name": "Chiropractor?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "createNewMedsProviderForC", "name": "Create New Meds Provider for Chiropractor", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "createNewMedsProviderForA", "name": "Create New Meds Provider for Ambulance", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "emergencyRoomPhysician", "name": "Emergency Room Physician?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "emergencyRoomPhysicianProvi", "name": "Emergency Room Physician Provider?", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "createNewMedsProviderForE", "name": "Create New Meds Provider for ER Physician", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "healthInsuranceComapny", "name": "Health Insurance Company", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "memberID", "name": "Member ID #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "groupNumber", "name": "Group Number:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "createNewInsuranceProvider", "name": "Create New Insurance Provider for Health Insurance", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "createNewMemberInHealth", "name": "Create New Member/Group # in Health Insurance Field", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "doesClientHave2ndHealthIn", "name": "Does Client Have 2nd Health Insurance", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "2ndHealthInsuranceCompany", "name": "2nd Health Insurance Company", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "2ndMemberID", "name": "2nd Member ID #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "2ndGroupNumber", "name": "2nd Group Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "healthInsuranceNotes", "name": "Health Insurance Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "residentRelative", "name": "Resident Relative ?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "residentRelativeContact", "name": "Resident Relative Contact", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "residentRelativeInsuranceCo", "name": "Resident Relative Insurance Company", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "createNewInsuranceProvider_1", "name": "Create New Insurance Provider for Resident Relative", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "reasonForNoPhotos", "name": "Reason for no photos?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "residentRelativeDetails", "name": "Resident Relative Details", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "reason", "name": "Reason", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "insuranceCoverageAdditional", "name": "Insurance Coverage: Additional Info", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "multipleDamagesDetails", "name": "Multiple Damages Details", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "residentRelativePolicyNumbe", "name": "Resident Relative Policy Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "residentRelativeInstructions", "name": "Resident Relative Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "relationOfClientToInsured", "name": "Relation of Client to Insured", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "clientsPreferredContactMeth", "name": "Clients Preferred Contact Method", "dropdownItems": ["Call", "Text", "Email", "Mail"], "customFieldType": "Dropdown"}, {"fieldSelector": "documentUploadScore", "name": "Document Upload Score", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "contractFeePercentageSign", "name": "Contract Fee Percentage @Sign-Up", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "auditNotes", "name": "Audit Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "24HourCall", "name": "24 Hour Call", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "typeOfReferral", "name": "Type of Referral", "dropdownItems": ["MVA", "Camp <PERSON>", "Premise", "WC", "Product Liability", "<PERSON><PERSON>", "Transvaginal Mesh", "Medical Malpractice", "Other"], "customFieldType": "Dropdown"}, {"fieldSelector": "milestonesEnabled", "name": "Milestones Enabled", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "mike<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "clientPreviouslyRepresented", "name": "Client Previously Represented?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "respondingOfficer", "name": "Responding Officer", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "premiseTaskflow", "name": "Premise Taskflow", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "wasClientWorkingAtTimeOf", "name": "Was Client Working at Time of Incident?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "doesClientHavePersonalUMC", "name": "Does <PERSON><PERSON> have Personal UM Coverage?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "policyNumber", "name": "Policy Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "insuranceCompany", "name": "Insurance Company", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "wasClientDrivingRidingFor", "name": "Was Client Driving/Riding for Rideshare?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "createNewInsuranceProvider_2", "name": "Create New Insurance Provider for UM/UIM", "dropdownItems": [], "customFieldType": "ActionButton"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/intake"}}, {"sectionSelector": "liens", "isCollection": true, "name": "<PERSON><PERSON>", "customFields": [{"fieldSelector": "amountdue", "name": "FINAL Amount After Redux/Dispute", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "lienholder", "name": "<PERSON><PERSON>", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "payee", "name": "Recovery Agency (Check Payee)", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "recoveryagent", "name": "Recovery Agent", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "letterofrepsent", "name": "Letter of Rep sent", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "datesent", "name": "Date Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "noticereceived", "name": "Notice Received", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "datereceived", "name": "Date Received", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Final <PERSON><PERSON> Received", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "dateofreceipt", "name": "Date of Receipt", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "documents", "name": "Documents", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "checkmemo", "name": "Memo", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "amountClaimed", "name": "Initial Amount Claimed (Before Redux/Dispute)", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "readyToPayLien", "name": "Ready to Pay Lien", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "lienLetterTemplates", "name": "Lien Letter Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "lienType", "name": "Lien Type", "dropdownItems": ["Subrogation", "Funding", "Child Support", "Worker's Compensation", "Attorney <PERSON><PERSON>", "Bankruptcy", "Occupational Accident"], "customFieldType": "Dropdown"}, {"fieldSelector": "insuredParty", "name": "Insured Party", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "lienFileNo", "name": "Lien File No:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "clickButtonIfThisIsCMS", "name": "Click Button ONLY if this is CMS", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "liensDocGenerationTemplates", "name": "Liens Doc Generation Templates", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "isThereAnExcess", "name": "Is there an excess?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "excessAmount", "name": "Excess Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "linkToExcessDisbursalReque", "name": "Link to Excess Disbursal Request", "dropdownItems": [], "customFieldType": "Url"}, {"fieldSelector": "createExcessDisbursalItem", "name": "Create Excess Disbursal Item", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "originalPaidLienAmount", "name": "Original Paid Lien Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "excludefromsettlement", "name": "Click Yes to Exclude from Settlement Payments (i.e. Not Valid)", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "subroClosed", "name": "Subro Closed", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "sendToTrustCheckRequest", "name": "Send to Trust Check Request?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "createTrustCheckRequestFor", "name": "Create Trust Check Request for Lien?", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "payoffReceived", "name": "Payoff Received", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "wCRecoveryAgent", "name": "WC Recovery Agent", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "wCInsuredParty", "name": "WC Insured Party", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "wCLienFileNo", "name": "WC Lien File No:", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "wCLORSent", "name": "WC LOR Sent", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "wCNoticeReceived", "name": "WC Notice Received", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "wCLORDateSent", "name": "WC LOR Date <PERSON>t", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "wCFinalReceived", "name": "WC Final Received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "ifGeorgiaWasMadeWholeSen", "name": "If Georgia, was Made Whole sent?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "vATricare", "name": "VA/Tricare", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "memberID", "name": "Member ID", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "lORTemplates", "name": "LOR Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "groupNo", "name": "Group No.", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "instructions", "name": "Instructions", "dropdownItems": [], "customFieldType": "Instructions"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/liens"}}, {"sectionSelector": "lIT", "isCollection": true, "name": "LIT", "customFields": [{"fieldSelector": "litigationTypes", "name": "Litigation Types", "dropdownItems": ["Counsel of Record/Defendant Info", "Court Info", "Defendant Discovery Request to Plantiff", "Plantiff Discovery Request to Defendant", "Service", "UM/UIM Discovery to Plantiff"], "customFieldType": "Dropdown"}, {"fieldSelector": "defendantName", "name": "Defendant Name", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defense<PERSON><PERSON><PERSON><PERSON>", "name": "Defense Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "type", "name": "Type", "dropdownItems": ["Liability", "UM/UIM", "Resident Relative"], "customFieldType": "Dropdown"}, {"fieldSelector": "counterClaim", "name": "Counter Claim?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "counterClaimDefendant", "name": "Counter Claim Defendant", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "counterClaimDefenseAttorney", "name": "Counter Claim Defense Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "counterClaimType", "name": "Counter Claim Type", "dropdownItems": ["Liability", "UM/UIM", "Resident Relative"], "customFieldType": "Dropdown"}, {"fieldSelector": "noticeOfSuitSent", "name": "Notice of Suit Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantNameForService", "name": "Defendant Name for Service", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "serviceComplete", "name": "Service Complete?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "dateServed", "name": "Date Served?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "answerDueDate", "name": "Answer Due Date?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantNameForPlDisc", "name": "Defendant Name for Pl. Disc.", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "plantiffDiscoveryServed", "name": "Plantiff Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "plantiffDiscoveryDue", "name": "Plantiff Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "plantiffDiscoveryResponseRe", "name": "Plantiff Discovery Response Received", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "plantiffDiscoveryExtended", "name": "Plantiff Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "newDueDate", "name": "New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantNameForDiscovery", "name": "Defendant Name for Discovery", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defendantDiscoveryServed", "name": "Defendant Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantDiscoveryDue", "name": "Defendant Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantDiscoveryResponseS", "name": "Defendant Discovery Response Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantDiscoveryExtended", "name": "Defendant Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "defendantDiscoveryNewDueDa", "name": "Defendant Discovery New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "nameForDiscovery", "name": "Name for Discovery", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "uMUIMDiscoveryServed", "name": "UM/UIM Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMUIMDiscoveryDue", "name": "UM/UIM Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMUIMDiscoveryResponseSent", "name": "UM/UIM Discovery Response Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMUIMDiscoveryExtended", "name": "UM/UIM Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "uMUIMDiscoveryNewDueDate", "name": "UM/UIM Discovery New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "litDocs", "name": "Lit Docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "litigationTemplates", "name": "Litigation Templates", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "statuteOfLimitationsWithRe", "name": "Statute of Limitations with <PERSON><PERSON><PERSON>:", "dropdownItems": [], "customFieldType": "Deadline"}, {"fieldSelector": "lossOfConsortium", "name": "Loss of Consortium", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "caseCaption", "name": "Case Caption", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "judge", "name": "Judge", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "courthouse", "name": "Courthouse", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "county", "name": "County", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "case", "name": "Case #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "judicialDistrict", "name": "Judicial District", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "complaintFiled", "name": "Comp<PERSON>t Filed", "dropdownItems": [], "customFieldType": "Date"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/lIT"}}, {"sectionSelector": "litigation", "isCollection": false, "name": "Litigation", "customFields": [{"fieldSelector": "sOL", "name": "SOL", "dropdownItems": [], "customFieldType": "Deadline"}, {"fieldSelector": "courtInfo", "name": "Court Info", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "caseCaption", "name": "Case Caption", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "judge", "name": "Judge", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "courthouse", "name": "Courthouse", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "county", "name": "County", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "case", "name": "Case #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "judicialDistrict", "name": "Judicial District", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "complaintFiled", "name": "Comp<PERSON>t Filed", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "statuteOfLimitationsWithRe", "name": "Statute of Limitations with <PERSON><PERSON><PERSON>:", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "counselOfRecord", "name": "Counsel of Record/Defendant Info", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "defendantName", "name": "Defendant Name", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defense<PERSON><PERSON><PERSON><PERSON>", "name": "Defense Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "type", "name": "Type", "dropdownItems": ["Liability", "UM/UIM", "Resident Relative"], "customFieldType": "Dropdown"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "addAnotherDefendant", "name": "Add Another Defendant", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "otherDefendantName", "name": "2nd Defendant Name", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defenseAttorney2", "name": "Defense Attorney 2", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "typeAdditional", "name": "Type (Additional)", "dropdownItems": ["Liability", "UM/UIM", "Resident Relative"], "customFieldType": "Dropdown"}, {"fieldSelector": "additionalNotes", "name": "Additional Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "service", "name": "Service", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "defendantNameForService", "name": "Defendant Name for Service", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "serviceComplete", "name": "Service Complete?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "serviceNotes", "name": "Service Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "addAnotherService", "name": "Add Another Service?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "secondDefendantName", "name": "Second Defendant Name for Service", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "secondServiceComplete", "name": "Second Service Complete?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "secondServiceNotes", "name": "Second Service Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "addThirdDefendant", "name": "Add Third Defendant", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "thirdDefendantName", "name": "Third Defendant Name", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defenseAttorney3", "name": "Defense Attorney 3", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "typeThird", "name": "Type (Third)", "dropdownItems": ["Liability", "UM/UIM"], "customFieldType": "Dropdown"}, {"fieldSelector": "thirdDefNotes", "name": "Third Def. Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "addFourthDefendant", "name": "Add Fourth Defendant", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "addThirdService", "name": "Add Third Service?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "fourthDefendantName", "name": "Fourth Defendant Name", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defenseAttorney4", "name": "Defense Attorney 4", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "typeFourth", "name": "Type (Fourth)", "dropdownItems": ["Liability", "UM/UIM"], "customFieldType": "Dropdown"}, {"fieldSelector": "fourthDefNotes", "name": "Fourth Def. Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "thirdDefendantNameForServi", "name": "Third Defendant Name for Service", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "thirdServiceComplete", "name": "Third Service Complete?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "thirdServiceNotes", "name": "Third Service Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "addFourthService", "name": "Add Fourth Service?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "fourthDefendantNameForServ", "name": "Fourth Defendant Name for Service", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "fourthServiceComplete", "name": "Fourth Service Complete", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "fourthServiceNotes", "name": "Fourth Service Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "discovery", "name": "Discovery", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "plantiffDiscoveryRequestTo", "name": "Plantiff Discovery Request to Defendant", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "served", "name": "Pl. Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "due", "name": "Pl Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "received", "name": "Pl. Discovery Response Received", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "extended", "name": "Pi. Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "newDueDate", "name": "New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantDiscoveryRequestTo", "name": "Defendant Discovery Request to Plaintiff", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "defDiscoveryServed", "name": "Def <PERSON> Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defDiscoveryDue", "name": "Def <PERSON> Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defDis<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Def Discovery Response Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defDiscExtended", "name": "Def Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "defDiscNewDueDate", "name": "Def Discovery New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "addAnotherDiscovery", "name": "Add Second Def. Discovery?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "2ndPlDiscoveryServed", "name": "Second Def. Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "2ndPlDiscoveryDue", "name": "Second Def. <PERSON> Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "litDocs", "name": "Lit Docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "2ndPlDiscoveryReceived", "name": "Second Def. Discovery Response Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "2ndDefDiscoveryServed", "name": "Third Def Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "2ndDefDiscoveryDue", "name": "Third Def Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "2ndDiscoveryReceived", "name": "Third Def Discovery Response Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "2ndDefDiscoveryExtended", "name": "Third Def Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "2ndPlDiscoveryExtended", "name": "Second Def. Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "2ndPlDiscoveryNewDueDa", "name": "Second Def. Discovery - New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "litigationDocs", "name": "Litigation Docs", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "triggerButtonIfLossOfCons", "name": "<PERSON>gger <PERSON> if Loss of Consortium", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "addThirdDiscovery", "name": "Add Third Def. Discovery?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "3rdDefDiscoveryNewDueDate", "name": "Third Def. Discovery New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes_1", "name": "Notes?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "dateServed", "name": "Date Served?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "answerDueDate", "name": "Answer Due Date?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "additionalNotes_1", "name": "Additional Notes?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "thirdDefNotes_1", "name": "Third Def. Notes?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "fourthDefNotes_1", "name": "Fourth Def. Notes?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "addSecondPlaintiffDiscovery", "name": "Add Second Plaintiff Discovery?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "secondPlDiscoveryServed", "name": "Second Pl. Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "secondPlDiscoveryDue", "name": "Second Pl. Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "secondPlDiscoverySent", "name": "Second Pl. Discovery Response Received", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "secondPlDiscoveryExtended", "name": "Second Pl. Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "secondPlDiscoveryDueDate", "name": "Second Pl. Discovery Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "addThirdPlaintiffDiscovery", "name": "Add Third Plaintiff Discovery?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "thirdPlDiscoveryServed", "name": "Third Pl. Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdPlDiscoveryDue", "name": "Third Pl. Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdPlDiscoverySent", "name": "Third Pl. Discovery Response Received", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdPlaintiffDiscoveryExte", "name": "Third Plaintiff Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "defendantNameForDiscovery", "name": "Defendant Name for Discovery", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "secondDefendantForDiscovery", "name": "Second Defendant for Discovery", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "thirdDefendantForDiscovery", "name": "Third Defendant for Discovery", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "thirdPlDiscoveryNewDueDa", "name": "Third Pl. Discovery New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "secondServiceDateServed", "name": "Second Service Date Served?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "secondServiceAnswerDueDate", "name": "Second Service Answer Due Date?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdServiceDateServed", "name": "Third Service Date Served?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "thirdServiceAnswerDueDate", "name": "Third Service Answer Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "fourthServiceDateServed", "name": "Fourth Service Date Served?", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "fourthServiceAnswerDueDate", "name": "Fourth Service Answer Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "defendantNameForPlDisc", "name": "Defendant Name for Pl. Disc.", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defendantNameFor2ndPlDis", "name": "Defendant Name for 2nd Pl. Disc.", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "defendantNameFor3rdPlDis", "name": "Defendant Name for 3rd Pl. Disc.", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "uMUIMDiscoveryToPlaintiff", "name": "UM/UIM Discovery to Plaintiff", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "nameForDiscovery", "name": "Name for Discovery", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "uMUIMDiscoveryServed", "name": "UM/UIM Discovery Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMUIMDiscoveryDue", "name": "UM/UIM Discovery Due", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMUIMDiscoveryResponseSent", "name": "UM/UIM Discovery Response Sent", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "uMUIMDiscoveryExtended", "name": "UM/UIM Discovery Extended?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "uMUIMDiscoveryNewDueDate", "name": "UM/UIM Discovery New Due Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "__CounterClaim__", "name": "Counter Claim?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "counterClaimDefendant", "name": "Counter Claim Defendant", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "counterClaimDefenseAttorney", "name": "Counter Claim Defense Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "counterClaimType", "name": "Counter Claim Type", "dropdownItems": ["Liability", "UM/UIM", "Resident Relative"], "customFieldType": "Dropdown"}, {"fieldSelector": "counterClaimNotes", "name": "Counter Claim Notes?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "counterClaimNotes_1", "name": "Counter Claim Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "noticeOfSuitSent", "name": "Notice of Suit Sent", "dropdownItems": [], "customFieldType": "Date"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/litigation"}}, {"sectionSelector": "marketing", "isCollection": false, "name": "Marketing", "customFields": [{"fieldSelector": "typeOfPhone", "name": "Type of Phone", "dropdownItems": ["Apple", "Samsung", "LG", "Non-smart"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "bank", "name": "Bank", "dropdownItems": ["Suntrust", "Bank of America", "Wells Fargo", "Regions", "South State Bank", "Other"], "customFieldType": "Dropdown"}, {"fieldSelector": "top3AppsUsed", "name": "Top 3 Apps Used?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "useBankMobileApp", "name": "Use Bank Mobile App?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "lastTimeYouWentOnATrip", "name": "Last time you went on a Trip?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "whereDidYouGo", "name": "Where did you go?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "hobbiesOrInterest", "name": "Hobbies or Interest?", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "bestWayToContactToYou", "name": "Best way to contact to you?", "dropdownItems": ["Call", "Text", "Email", "Mail"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "tVBrandOwned", "name": "TV Brand Owned?", "dropdownItems": ["Hisense", "Insignia", "JVC", "LG", "Other", "RCA", "Samsung", "<PERSON><PERSON><PERSON>", "Sony", "TCL", "Toshiba", "<PERSON><PERSON><PERSON>", "WestingHouse"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "smartTV", "name": "Smart TV?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "stream", "name": "Stream?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "cableProvider", "name": "Cable Provider?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "howManyVehiclesDoYouOwn", "name": "How Many Vehicles Do You Own?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "doYouAttendAnyChurchOrLo", "name": "Do You Attend Any Church or Local Events", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "howDidYouHearAboutUs", "name": "How Did You Hear About Us?", "dropdownItems": ["TV", "Radio", "<PERSON><PERSON><PERSON><PERSON>", "Social Media", "Friend or Family Member"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "ethnicity", "name": "Ethnicity", "dropdownItems": ["African American", "Asian", "Caucasian", "Latino"], "customFieldType": "Dropdown"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/marketing"}}, {"sectionSelector": "meds", "isCollection": true, "name": "Medicals and Damages", "customFields": [{"fieldSelector": "payee", "name": "Provider (Payee for Meds)", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "amountbilled", "name": "Amount Billed", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "docs", "name": "Docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "plaintiffstreatmentstatus", "name": "Treatment Status", "dropdownItems": ["Actively Treating", "Treatment Complete", "No Medical Treatment"], "customFieldType": "Dropdown"}, {"fieldSelector": "datetreatmentstarted", "name": "Treatment Start Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "datetreatmentcompleted", "name": "Treatment End Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "haveallbillsbeenordered", "name": "Have all bills been ordered?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "billsordereddate", "name": "Bills Ordered Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "haveallbillsbeenreceived", "name": "Have all bills been received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "billsreceiveddate", "name": "Bills Received Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "haveallrecordsbeenordered", "name": "Have all records been ordered?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "recordsordereddate", "name": "Records Ordered Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "haveallrecordsbeenreceived", "name": "Have all records been received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "recordsreceiveddate", "name": "Records Received Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "billinvoicereceived", "name": "Bill Invoice Received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "recordsinvoicereceived", "name": "Records Invoice Received?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "providersaccountnumber", "name": "Account Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "a", "name": "a", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "lienfiled", "name": "Lien Filed?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "add<PERSON>ien", "name": "<PERSON><PERSON>", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "providerBalancePaid", "name": "Provider Balance Verified/Reduced", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "invoice", "name": "Invoice #", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "pIPPayment", "name": "PIP Payment", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "primaryHealthInsPayment", "name": "Primary Health Ins. Payment", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "adjustment", "name": "Adjustment", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "secondaryHealthInsPayment", "name": "Secondary Health Ins. Payment", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "amountdue", "name": "Balance", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "checkmemo", "name": "Memo", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "lettersRequests", "name": "Letters & Requests", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "clientPayments", "name": "Client Payments", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "medicalDamagesDocGenerati", "name": "Medical & Damages Doc Generation Templates", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "excludefromsettlement", "name": "Click Yes to Exclude from Settlement Payments (i.e. Not Valid)", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "calculatedamountdue", "name": "Calculated Balance", "dropdownItems": [], "customFieldType": "CalculatedCurrency"}, {"fieldSelector": "sendToTrustCheckRequest", "name": "Send to Trust Check Request?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "createTrustCheckRequest", "name": "Create Trust Check Request", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "areRecordsBeingRequestedFr", "name": "Different address for Records?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "providerForRecordsRequest", "name": "Provider for Records Request", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "medicalRecordsRequest", "name": "Medical Records Request", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "instructions", "name": "Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "excess", "name": "Excess?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "providerExcess", "name": "Create Provider Excess TCR", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "excessAmount", "name": "Excess Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "amountToPayBeforeExcess", "name": "Amount to Pay before Excess", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/meds"}}, {"sectionSelector": "negotiations", "isCollection": true, "name": "Negotiations", "customFields": [{"fieldSelector": "offerdemandsettled", "name": "Offer/Demand/Settled", "dropdownItems": ["Offer", "Demand", "Settled", "Counter Offer", "Top Offer"], "customFieldType": "Dropdown"}, {"fieldSelector": "amount", "name": "Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "tofrom", "name": "To/From:", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "date", "name": "Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "docs", "name": "Docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "insuranceType", "name": "Insurance Type", "dropdownItems": ["Liability", "UM", "PD Punitives"], "customFieldType": "Dropdown"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/negotiations"}}, {"sectionSelector": "payments", "isCollection": true, "name": "Payments", "customFields": [{"fieldSelector": "paymentType", "name": "Payment Type", "dropdownItems": ["MedPay", "PIP", "Property Damage", "Settlement - Liability", "Settlement - UM", "PD Settlement", "Subro Payment", "Referral Fee", "Refund", "Attorney <PERSON><PERSON>", "Child Support", "Miscellaneous"], "customFieldType": "Dropdown"}, {"fieldSelector": "paymentDate", "name": "Payment Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "paymentAmount", "name": "Payment Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "documents", "name": "Check", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "payor", "name": "Payor", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "market", "name": "Market", "dropdownItems": ["SAV", "BEA", "MAC", "AUG", "AIK", "FLO", "ALA", "COL", "ALY", "ATL", "CHA"], "customFieldType": "Dropdown"}, {"fieldSelector": "settledAmount", "name": "Settled Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "deposited", "name": "Deposited?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "doNotDeposit", "name": "Do not deposit", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "reasonWeCannotDeposit", "name": "Reason we cannot deposit", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "releaseInfo", "name": "Release Info:", "dropdownItems": ["General", "LLR", "CNTE", "UM"], "customFieldType": "Dropdown"}, {"fieldSelector": "release", "name": "Release:", "dropdownItems": ["Release Attached", "Release Not Included", "Release Signed", "No Release Required (UM)"], "customFieldType": "Dropdown"}, {"fieldSelector": "needReleaseTriggerToSend", "name": "Need Release - Trigger to send PSC a task to obtain release", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Marital Status (Check)", "dropdownItems": ["Single", "Married", "Minor", "Deceased"], "customFieldType": "Dropdown"}, {"fieldSelector": "maritalStatusRelease", "name": "Marital Status (Release)", "dropdownItems": ["Single", "Married", "One Parent", "Both Parents"], "customFieldType": "Dropdown"}, {"fieldSelector": "whoSignedTheContract", "name": "Who signed the contract?", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "disbursementCreditCaseManag", "name": "Disbursement Credit/Case Manager", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "dateCheckCheckedIn", "name": "Date Check Checked In", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "dateCaseSettled", "name": "Date Case Settled", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "negotiations", "name": "Negotiations:", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "settledTab", "name": "Settled Tab:", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "documentedAuthority", "name": "Documented Authority", "dropdownItems": ["Yes", "No", "UM", "PD"], "customFieldType": "Dropdown"}, {"fieldSelector": "settlementNotes", "name": "Settlement Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "release_1", "name": "Release", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "checkInSheet", "name": "Check In Sheet", "dropdownItems": [], "customFieldType": "ReportFusion"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/payments"}}, {"sectionSelector": "pleadingsindex", "isCollection": true, "name": "Pleadings Index", "customFields": [{"fieldSelector": "pleadingtype", "name": "Pleading type", "dropdownItems": ["<PERSON><PERSON><PERSON><PERSON>", "Answer", "Notice", "Motion", "Response", "Other"], "customFieldType": "Dropdown"}, {"fieldSelector": "dateserved", "name": "Date Served", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "responder", "name": "<PERSON><PERSON><PERSON><PERSON>", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "responsedate", "name": "Response Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "replier", "name": "Replier", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "replydate", "name": "Reply Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "hearingdate", "name": "Hearing Date", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "ordertendered", "name": "Order Tendered", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "documents", "name": "Documents", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "pleadingname", "name": "Pleading Name", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "drafter", "name": "Drafter", "dropdownItems": [], "customFieldType": "PersonLink"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/pleadingsindex"}}, {"sectionSelector": "issues", "isCollection": true, "name": "Red Flags", "customFields": [{"fieldSelector": "issue", "name": "Issue", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "ourposition", "name": "Our Position", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "supportingwitns", "name": "Supporting Witns'?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "namesofwitns", "name": "Names of Wit<PERSON>'", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "deposofwi<PERSON><PERSON>", "name": "Depos of Wit<PERSON>'", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "opposition", "name": "OP Position", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "evsuppopviewnotes", "name": "Ev Supp OP View Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "opsupportingwitns", "name": "OP Supporting Witns'?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "namesofopwitns", "name": "Names of OP Witns'", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "depositionsofopwi<PERSON>", "name": "Depositions of OP Wits", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "generalissuenotes", "name": "General Issue Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "ourview", "name": "Our View", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "opposingpartyview", "name": "Opposing Party View", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "evidencesupporting", "name": "Evidence Supporting", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "uploadeddeposop", "name": "Uploaded Depos OP", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "uploadeddepos", "name": "Uploaded Depos", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "evsuppopview", "name": "Ev Supp OP View", "dropdownItems": [], "customFieldType": "DocList"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/issues"}}, {"sectionSelector": "reportFusion", "isCollection": false, "name": "Report Fusion", "customFields": [{"fieldSelector": "demandOutFullList", "name": "Demand Out - Full List", "dropdownItems": [], "customFieldType": "ReportFusion"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/reportFusion"}}, {"sectionSelector": "5YearAccounting", "isCollection": false, "name": "5 Year Send Out", "customFields": [{"fieldSelector": "sendToState", "name": "Send to State", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "dateSentToState", "name": "Date Sent to State", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notesRegardingStaleCheck", "name": "Notes Regarding Stale Check", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "sentAmount", "name": "<PERSON><PERSON>", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/5YearAccounting"}}, {"sectionSelector": "settled_1", "isCollection": false, "name": "Settled", "customFields": [{"fieldSelector": "liabilityAdjuster", "name": "Liability Adjuster", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "liabilitySettlementAmount", "name": "Liability Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "referring<PERSON><PERSON><PERSON><PERSON>", "name": "Referring Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "referralAgreement", "name": "Referral Agreement %", "dropdownItems": [], "customFieldType": "Percent"}, {"fieldSelector": "caseExpenseReferringAttorne", "name": "Case Expense Referring Attorney", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "defense<PERSON><PERSON><PERSON><PERSON>", "name": "Defense Attorney", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "maritalStatus", "name": "Marital Status", "dropdownItems": ["Single", "Married", "Divorced", "Separated", "Significant Other", "Widowed"], "customFieldType": "Dropdown"}, {"fieldSelector": "spouse", "name": "Spouse", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "isClientAMinor", "name": "Is <PERSON><PERSON> a Minor?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "areParentsListedInIntakeT", "name": "Are Parents Listed in Intake Tab?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "spouseListedOnPartyTab", "name": "Spouse Listed on Party Tab", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "isClientInBankruptcy", "name": "Is Client in Bankruptcy?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "sSDIDisability", "name": "SSDI(Disability)", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "probate", "name": "Probate?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "healthInsurance", "name": "Health Insurance?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "subrogationLien", "name": "Subrogation Lien?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "otherLiens", "name": "Other Liens", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "<PERSON><PERSON><PERSON>", "name": "Attorney <PERSON><PERSON>", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "attorneyFeeFlexible", "name": "Attorney <PERSON>e Flexible?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "biggestPieceOfPie", "name": "Biggest Piece of Pie?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "amountToClient", "name": "Amount to Client", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "billsPaid", "name": "Bills Paid?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "howWasCaseSettled", "name": "How Was <PERSON> Settled?", "dropdownItems": ["Recorded Line", "Phone", "Email", "Text", "In Person", "PLDA"], "customFieldType": "Dropdown"}, {"fieldSelector": "releaseType", "name": "Release Type", "dropdownItems": ["General Release", "Limited Release", "SC-Covenant Not To Execute", "LLR with Demand?", "Release Before Check?", "Multiple Claimants?"], "customFieldType": "MultiSelectList"}, {"fieldSelector": "notesOnSettlementBreakdown", "name": "Notes on Settlement Breakdown", "dropdownItems": [], "customFieldType": "TextLarge"}, {"fieldSelector": "uMSettlementInfo", "name": "UM Settlement Info", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "uMSettlementAmount", "name": "UM Settlement Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "uMAttorneyFee", "name": "UM Attorney <PERSON>", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "uMAmountToClient", "name": "UM Amount to Client", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "uMAdjuster", "name": "UM Adjuster", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "uMSettlementBreakdown", "name": "UM Settlement Breakdown", "dropdownItems": [], "customFieldType": "TextLarge"}, {"fieldSelector": "insuranceCompany", "name": "Insurance Company", "dropdownItems": [], "customFieldType": "PersonLink"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/settled_1"}}, {"sectionSelector": "staleChecks", "isCollection": false, "name": "Stale Checks", "customFields": [{"fieldSelector": "staleChecks", "name": "Stale Checks", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "typeOfFunds", "name": "Type of Funds", "dropdownItems": ["Remaining PI Funds", "Medpay Proceeds", "Refund of Overpayment", "Other"], "customFieldType": "Dropdown"}, {"fieldSelector": "amountOfFunds", "name": "Amount of Funds", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "dateOfCheck", "name": "Date of Check", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/staleChecks"}}, {"sectionSelector": "trustAccount", "isCollection": false, "name": "Trust Account", "customFields": [{"fieldSelector": "trustBalance", "name": "QuickBooks Trust Balance", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "transactionDetailReport", "name": "Transaction Detail Report", "dropdownItems": [], "customFieldType": "Url"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "updateNow", "name": "Update Now", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "checkCopiesAndDocs", "name": "Check copies and docs", "dropdownItems": [], "customFieldType": "DocList"}, {"fieldSelector": "datesNotes", "name": "Dates - Notes", "dropdownItems": [], "customFieldType": "StringList"}, {"fieldSelector": "checkCopiesInstructions", "name": "Check Copies Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "lastUpdateFromQuickBooks", "name": "Last Update from QuickBooks", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "balanceTriggerDONOTUSE", "name": "Balance Trigger", "dropdownItems": [], "customFieldType": "ActionButton"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/trustAccount"}}, {"sectionSelector": "trustCheckRequest", "isCollection": true, "name": "Trust Check Request", "customFields": [{"fieldSelector": "checkRequestHeader", "name": "Check Request Header", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "lineBreak", "name": "Line Break", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "amount", "name": "Amount", "dropdownItems": [], "customFieldType": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldSelector": "payableTo", "name": "Payable To", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "description", "name": "Description (appears on printed full page checks)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "memo", "name": "Memo (prints on check to vendor)", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "supportingAttachment", "name": "Supporting Attachment (pushed to QuickBooks)", "dropdownItems": [], "customFieldType": "Doc"}, {"fieldSelector": "notes", "name": "Notes (internal notes, not pushed to QuickBooks)", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "paymentStatus", "name": "Payment Status", "dropdownItems": [], "customFieldType": "Header"}, {"fieldSelector": "paymentInstructions", "name": "Payment Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "status", "name": "Status", "dropdownItems": ["Created", "Printed", "Void", "Deleted"], "customFieldType": "Dropdown"}, {"fieldSelector": "dateOfCheck", "name": "Date of Check", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "checkNumber", "name": "Check Number", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "lastUpdateFromQuickBooks", "name": "Last update from QuickBooks", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "emailAccountingButtonInstru", "name": "Email Accounting Button Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "emailAccounting", "name": "Email Accounting", "dropdownItems": [], "customFieldType": "ActionButton"}, {"fieldSelector": "saveInstructions", "name": "Save Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "createInstructions", "name": "Create Instructions", "dropdownItems": [], "customFieldType": "Instructions"}, {"fieldSelector": "readyToCreateTheCheckInQ", "name": "Ready to create the check in QuickBooks?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "templatesDoNotUse", "name": "Templates (Do not use)", "dropdownItems": [], "customFieldType": "MultiDocGen"}, {"fieldSelector": "excess", "name": "Excess?", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "explanationOfExcess", "name": "Explanation of Excess", "dropdownItems": [], "customFieldType": "String"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/trustCheckRequest"}}, {"sectionSelector": "witnesses", "isCollection": true, "name": "Witnesses", "customFields": [{"fieldSelector": "witness", "name": "Witness", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "purpose", "name": "Purpose", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "interviewed", "name": "Interviewed", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "dateinterviewed", "name": "Date Interviewed", "dropdownItems": [], "customFieldType": "Date"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "documents", "name": "Documents", "dropdownItems": [], "customFieldType": "DocList"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/witnesses"}}, {"sectionSelector": "writtendisc", "isCollection": true, "name": "Written Disc", "customFields": [{"fieldSelector": "inout", "name": "In/Out", "dropdownItems": ["Incoming", "Outgoing", "Non-party- Outgoing", "Non-party- Incoming", "Other"], "customFieldType": "Dropdown"}, {"fieldSelector": "type", "name": "Type", "dropdownItems": ["Interrogatories", "Requests to Admit", "Requests to Produce", "Subpoena", "Subpeona Duces Tecum", "Subpoena Site Inspection"], "customFieldType": "Dropdown"}, {"fieldSelector": "response", "name": "Response", "dropdownItems": [], "customFieldType": "Deadline"}, {"fieldSelector": "title", "name": "Title", "dropdownItems": [], "customFieldType": "String"}, {"fieldSelector": "drafter", "name": "Drafter", "dropdownItems": [], "customFieldType": "PersonLink"}, {"fieldSelector": "done", "name": "Done", "dropdownItems": [], "customFieldType": "Boolean"}, {"fieldSelector": "notes", "name": "Notes", "dropdownItems": [], "customFieldType": "Text"}, {"fieldSelector": "documents", "name": "Documents", "dropdownItems": [], "customFieldType": "DocList"}], "links": {"self": "ProjectTypes/Filevine.PublicApi.Models.Identifier/Sections/writtendisc"}}], "links": {"self": "/projecttypess/15130/sections?name=&offset=0&limit=50&requestedFields=*", "prev": null, "next": null}}