import json

import rest_framework.decorators
import rest_framework.permissions
import rest_framework.request
import rest_framework.response
import rest_framework.status

from .chatgpt_code import chatgpt_operations, portal_reduction_operations


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def openai(
    request: rest_framework.request.Request,
) -> rest_framework.response.Response:
    try:
        if not request.data:
            return rest_framework.response.Response(
                {'error': 'Conversation can\'t be null'},
                status=rest_framework.status.HTTP_400_BAD_REQUEST,
            )
        conversation: list[dict[str, str]] = request.data
        response = chatgpt_operations.chatGPT_conversation(conversation)
        return rest_framework.response.Response(
            json.dumps(response), status=rest_framework.status.HTTP_200_OK
        )
    except Exception as e:
        return rest_framework.response.Response(
            {'error': str(e)},
            status=rest_framework.status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def portal_reduction(
    request: rest_framework.request.Request,
) -> rest_framework.response.Response:
    try:
        if not request.data:
            return rest_framework.response.Response(
                {'error': 'Conversation can\'t be null'},
                status=rest_framework.status.HTTP_400_BAD_REQUEST,
            )

        try:
            llm = portal_reduction_operations.initialize_openai()
            chain = portal_reduction_operations.initialize_llm_chain(llm)
        except Exception as e:
            return rest_framework.response.Response(
                {'error': f'Failed to initialize LLM chain: {str(e)}'},
                status=rest_framework.status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        try:
            conversation: dict[str, str] = request.data
            response = portal_reduction_operations.generate_response(
                chain, conversation
            )
        except KeyError as e:
            return rest_framework.response.Response(
                {'error': f'Missing required data field: {str(e)}'},
                status=rest_framework.status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            if 'insufficient_quota' in str(e):
                return rest_framework.response.Response(
                    {
                        'error': 'Insufficient quota for OpenAI API. Please check your plan and billing details.'
                    },
                    status=rest_framework.status.HTTP_429_TOO_MANY_REQUESTS,
                )
            else:
                return rest_framework.response.Response(
                    {'error': f'Failed to generate response: {str(e)}'},
                    status=rest_framework.status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return rest_framework.response.Response(
            json.dumps(response), status=rest_framework.status.HTTP_200_OK
        )

    except Exception as e:
        return rest_framework.response.Response(
            {'error': str(e)},
            status=rest_framework.status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
