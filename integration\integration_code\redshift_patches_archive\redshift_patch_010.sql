ALTER TABLE integration.dev.cases
ADD COLUMN insurancevendorassigned boolean ENCODE raw
;

ALTER TABLE integration.staging.cases
ADD COLUMN insurancevendorassigned boolean ENCODE raw
;

ALTER TABLE integration.prod.cases
ADD COLUMN insurancevendorassigned boolean ENCODE raw
;

ALTER TABLE integration.main.cases
ADD COLUMN insurancevendorassigned boolean ENCODE raw
;

-- Since route is being renamed, we need to update the integrationtimestamps
-- table to reflect the new route name

UPDATE integration.dev.integrationtimestamps
SET integrationroute = 'Redshift_To_Salesforce_Post_Upsert_Update'
WHERE integrationroute = 'Redshift_To_Salesforce_Update_Treatment_Complete'
;

UPDATE integration.staging.integrationtimestamps
SET integrationroute = 'Redshift_To_Salesforce_Post_Upsert_Update'
WHERE integrationroute = 'Redshift_To_Salesforce_Update_Treatment_Complete'
;

UPDATE integration.prod.integrationtimestamps
SET integrationroute = 'Redshift_To_Salesforce_Post_Upsert_Update'
WHERE integrationroute = 'Redshift_To_Salesforce_Update_Treatment_Complete'
;

UPDATE integration.main.integrationtimestamps
SET integrationroute = 'Redshift_To_Salesforce_Post_Upsert_Update'
WHERE integrationroute = 'Redshift_To_Salesforce_Update_Treatment_Complete'
;
