from unittest.mock import MagicMock

import pytest


class TestSalesforceUpsertTask:
    def test_salesforce_upsert_task_with_default_args(
        self,
        mock_post_main_upsert: MagicMock,
        mock_post_upsert_update: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        from integration.integration_code.tasks import salesforce_upsert_task

        args_dict = {}
        result = salesforce_upsert_task(args_dict)
        expected_partial_config = {
            'plaintiffs': True,
            'lawfirms': True,
            'legalpersonnel': True,
            'cases': True,
            'billings': True,
            'charges': True,
            'files': True,
            'notes': True,
        }
        mock_post_main_upsert.assert_called_once_with(
            False, expected_partial_config, False
        )
        mock_post_upsert_update.assert_called_once_with(False, False)
        assert result == 'Salesforce Upsert Complete'

    def test_salesforce_upsert_task_with_custom_args(
        self,
        mock_post_main_upsert: MagicMock,
        mock_post_upsert_update: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        from integration.integration_code.tasks import salesforce_upsert_task

        custom_partial_config = {
            'plaintiffs': True,
            'cases': True,
            'billings': False,
        }
        args_dict = {
            'test': True,
            'partial_config': custom_partial_config,
            'all_data': True,
        }
        result = salesforce_upsert_task(args_dict)
        mock_post_main_upsert.assert_called_once_with(
            True, custom_partial_config, True
        )
        mock_post_upsert_update.assert_called_once_with(True, True)
        assert result == 'Salesforce Upsert Complete'

    def test_salesforce_upsert_task_with_partial_args(
        self,
        mock_post_main_upsert: MagicMock,
        mock_post_upsert_update: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        from integration.integration_code.tasks import salesforce_upsert_task

        args_dict = {'test': True, 'all_data': True}
        result = salesforce_upsert_task(args_dict)
        expected_partial_config = {
            'plaintiffs': True,
            'lawfirms': True,
            'legalpersonnel': True,
            'cases': True,
            'billings': True,
            'charges': True,
            'files': True,
            'notes': True,
        }
        mock_post_main_upsert.assert_called_once_with(
            True, expected_partial_config, True
        )
        mock_post_upsert_update.assert_called_once_with(True, True)
        assert result == 'Salesforce Upsert Complete'

    def test_salesforce_upsert_task_lock_not_acquired(
        self,
        mock_advisory_lock_failure: MagicMock,
    ):
        """
        Test that salesforce_upsert_task raises an exception when the advisory lock is not acquired.
        """
        from integration.integration_code.tasks import salesforce_upsert_task

        args_dict = {}

        with pytest.raises(Exception, match="LOCK_NOT_ACQUIRED"):
            salesforce_upsert_task(args_dict)
