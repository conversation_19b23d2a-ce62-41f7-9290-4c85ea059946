import datetime

import numpy as np

from . import helpers


def compute_age(age: str, age_default_val: float) -> float:
    if age is None or age == '':
        return age_default_val
    return float(age)


def compute_gender(gender: str, gender_default_val: int) -> int:
    gender_dict = {'Male': 1, 'Female': 0, 'M': 1, 'F': 0}
    gender_val = gender_dict.get(gender, gender_default_val)
    return gender_val


def compute_injury_type(injury_type: str) -> tuple[bool, bool, bool, bool]:
    injury_type_default_val = {
        'injury_type__c_catastrophic': False,
        'injury_type__c_serious': False,
        'injury_type__c_soft_tissue': False,
        'injury_type__c_fatal': False,
    }
    injury_type_dict = {
        'Catastrophic': {
            'injury_type__c_catastrophic': True,
            'injury_type__c_serious': False,
            'injury_type__c_soft_tissue': False,
            'injury_type__c_fatal': False,
        },
        'Serious': {
            'injury_type__c_catastrophic': False,
            'injury_type__c_serious': True,
            'injury_type__c_soft_tissue': False,
            'injury_type__c_fatal': False,
        },
        'Soft Tissue': {
            'injury_type__c_catastrophic': False,
            'injury_type__c_serious': False,
            'injury_type__c_soft_tissue': True,
            'injury_type__c_fatal': False,
        },
        'Fatal': {
            'injury_type__c_catastrophic': False,
            'injury_type__c_serious': False,
            'injury_type__c_soft_tissue': False,
            'injury_type__c_fatal': True,
        },
        'Serious/Catastrophic': {
            'injury_type__c_catastrophic': True,
            'injury_type__c_serious': True,
            'injury_type__c_soft_tissue': False,
            'injury_type__c_fatal': False,
        },
        'Soft Tissue/Serious': {
            'injury_type__c_catastrophic': False,
            'injury_type__c_serious': True,
            'injury_type__c_soft_tissue': True,
            'injury_type__c_fatal': False,
        },
    }
    injury_type_val = injury_type_dict.get(
        injury_type, injury_type_default_val
    )
    return (
        injury_type_val['injury_type__c_catastrophic'],
        injury_type_val['injury_type__c_serious'],
        injury_type_val['injury_type__c_soft_tissue'],
        injury_type_val['injury_type__c_fatal'],
    )


def compute_insurance_company_ii__c(insurance_company_ii__c: str) -> int:
    return 0 if insurance_company_ii__c == '' else 1


def compute_any_other_lawsuits_pending_at_this_time(
    any_other_lawsuits_pending_at_this_time: str,
    any_other_lawsuits_pending_at_this_time_default_val: int,
) -> int:
    any_other_lawsuits_pending_at_this_time_dict = {
        'Yes': 1,
        'No': 0,
        'Y': 1,
        'N': 0,
    }
    return any_other_lawsuits_pending_at_this_time_dict.get(
        any_other_lawsuits_pending_at_this_time,
        any_other_lawsuits_pending_at_this_time_default_val,
    )


def compute_what_type_of_case(
    what_type_of_case: str,
) -> tuple[bool, bool, bool]:
    what_type_of_case_clean = {
        'MVA': 'Motor Vehicle Accident',
        'Motor Vehicle Accident': 'Motor Vehicle Accident',
        'Slip and Fall': 'Slip and Fall',
        'S/F': 'Slip and Fall',
    }
    what_type_of_case = what_type_of_case_clean.get(what_type_of_case, 'Other')
    what_type_of_case_dict = {
        'Motor Vehicle Accident': {
            'what_type_of_case__c_motor_vehicle_accident': True,
            'what_type_of_case__c_s_f': False,
            'what_type_of_case__c_other': False,
        },
        'Slip and Fall': {
            'what_type_of_case__c_motor_vehicle_accident': False,
            'what_type_of_case__c_s_f': True,
            'what_type_of_case__c_other': False,
        },
        'Other': {
            'what_type_of_case__c_motor_vehicle_accident': False,
            'what_type_of_case__c_s_f': False,
            'what_type_of_case__c_other': True,
        },
    }
    what_type_of_case_val = what_type_of_case_dict[what_type_of_case]
    return (
        what_type_of_case_val['what_type_of_case__c_motor_vehicle_accident'],
        what_type_of_case_val['what_type_of_case__c_s_f'],
        what_type_of_case_val['what_type_of_case__c_other'],
    )


def compute_type_of_funding(type_of_funding: str) -> tuple[bool, bool]:
    type_of_funding = type_of_funding.lower()
    type_of_funding_default_val = {
        'type_of_funding__c_medical': False,
        'type_of_funding__c_plaintiff': False,
    }
    type_of_funding_dict = {
        'plaintiff': {
            'type_of_funding__c_medical': False,
            'type_of_funding__c_plaintiff': True,
        },
        'medical': {
            'type_of_funding__c_medical': True,
            'type_of_funding__c_plaintiff': False,
        },
        'plaintiff;medical': {
            'type_of_funding__c_medical': True,
            'type_of_funding__c_plaintiff': True,
        },
        'plaintiff;attorney': {
            'type_of_funding__c_medical': False,
            'type_of_funding__c_plaintiff': True,
        },
    }
    type_of_funding_val = type_of_funding_dict.get(
        type_of_funding,
        type_of_funding_default_val,
    )
    return (
        type_of_funding_val['type_of_funding__c_medical'],
        type_of_funding_val['type_of_funding__c_plaintiff'],
    )


def compute_prior_funding_company(prior_funding_company: str) -> int:
    return 0 if prior_funding_company == '' else 1


def compute_prior_funding(
    prior_funding: str, prior_funding_company: int
) -> float:
    if (
        prior_funding is None
        or prior_funding == ''
        or prior_funding_company == 0
    ):
        return 0.0
    prior_funding_clean = dict.fromkeys(
        ["no", "None", "none", "No", "n", "none found"], '0.0'
    )
    prior_funding = prior_funding_clean.get(prior_funding, prior_funding)
    return helpers.pull_number(
        prior_funding, r'\$?\d{1,9} ?k? ?K? ?m?(?:,\d{3})*(?:\,\d+)?(?:\.\d+)?'
    )


def compute_origination_ar_book(origination_ar_book: str) -> int:
    return 0 if origination_ar_book == '' else 1


def compute_treatment_gap(
    date_of_first_accident: str, date_of_accident: str, default_val: float
) -> int | float:
    """
    Create a new field called treatment_gap to store the gap in days
    between date_of_accident__c and date_of_first_treatment__c
    """
    if (
        date_of_first_accident is None
        or date_of_first_accident == ''
        or date_of_accident is None
        or date_of_accident == ''
    ):
        return default_val
    date_format = "%Y-%m-%d"
    treatment_gap = (
        datetime.datetime.strptime(date_of_first_accident, date_format)
        - datetime.datetime.strptime(date_of_accident, date_format)
    ).days
    return treatment_gap


def post_process_old_insurance(amount: str) -> float:
    value = float(amount)
    if value <= 500 and value > 0:
        return value * 1000
    return value


def post_process_new_insurance(amount: str) -> float:
    value = float(amount)
    if value == 0:
        return 25000
    if value <= 500 and value > 0:
        return value * 1000
    return value


def compute_insurance_limits(
    insurance_limits_1: str, insurance_limits_2: str
) -> float:
    """
    Create a new field called insurance_limits to store the maximum
    of old_insurance_limits__c and new_insurance_limits__c
    """
    insurance_limits = np.nan
    if insurance_limits_1 is None or insurance_limits_1 == '':
        raise ValueError(
            'Input parameter "insurance_limits_1" not filled in input data'
        )
    insurance_limits_1_val = helpers.pull_number(
        insurance_limits_1,
        r'\$?\d{1,9} ?k? ?K? ?m? ?M?(?:,\d{3})*(?:\,\d+)?(?:\.\d+)?',
        post_process_new_insurance,
    )
    if insurance_limits_1_val is np.nan:
        raise ValueError(
            'Input parameter "insurance_limits_1" is not a valid number'
        )
    if insurance_limits_2 is None or insurance_limits_2 == '':
        insurance_limits_2_val = 0.0
    else:
        insurance_limits_2_val = helpers.pull_number(
            insurance_limits_2,
            r'\$?\d{1,9} ?k? ?K? ?m? ?M?(?:,\d{3})*(?:\,\d+)?(?:\.\d+)?',
            post_process_old_insurance,
        )
    insurance_limits = insurance_limits_1_val + insurance_limits_2_val
    return insurance_limits
