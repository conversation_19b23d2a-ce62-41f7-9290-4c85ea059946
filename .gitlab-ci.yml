image: python:3.10

variables:
  PGDATA: /var/lib/pg/data
  POSTGRES_PASSWORD: postgres
  POSTGRES_HOST: postgres-db

test:
  stage: test
  services:
    - name: postgres:14
      alias: postgres-db
  script:
    - export CREDENTIALS_INTEGRATION_SALESFORCE_PASSWORD=$(echo "$CREDENTIALS_INTEGRATION_SALESFORCE_PASSWORD_BASE64" | base64 --decode)
    - export CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_PASSWORD=$(echo "$CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_PASSWORD_BASE64" | base64 --decode)
    - export CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_PASSWORD=$(echo "$CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_PASSWORD_BASE64" | base64 --decode)
    - pip3 install pdm==2.20.*
    - pdm install --no-isolation
    - pdm run black --check .
    - pdm run isort --check .
    - pdm run pyright --level error .
    - pdm run pytest --cov --junitxml=dist/junit.xml
    - pdm run coverage xml
    # start django server and if it fails stop the deploy
    - |
      if [[ "$CI_COMMIT_BRANCH" == "develop" ]]; then
        sh run_and_check.sh devSettings
      elif [[ "$CI_COMMIT_BRANCH" == "staging" ]]; then
        sh run_and_check.sh stagingSettings
      fi
  coverage: '/^TOTAL.*\s+(\d+\%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
      junit:
        - ./dist/junit.xml

.common_script: &common_script
  - sed -i 's/GainInternalWebApp.settings/GainInternalWebApp.'"$DJANGO_SETTINGS"'/g' entrypoint.sh
  - sed -i 's/GainInternalWebApp.settings/GainInternalWebApp.'"$DJANGO_SETTINGS"'/g' docker-compose.yml
  - git add .
  - pip3 install awsebcli==3.17.* awscli==1.17.*
  - aws configure set aws_access_key_id "$aws_access_key_id"
  - aws configure set aws_secret_access_key "$aws_secret_access_key"
  - aws configure set region us-east-2
  - eb init "$EB_APPLICATION_NAME" --region us-east-2 --platform docker
  - eb setenv
    ENCRYPTION_KEY=$ENCRYPTION_KEY
    CREDENTIALS_INTEGRATION_AWS_ACCESS_KEY_ID=$aws_access_key_id
    CREDENTIALS_INTEGRATION_AWS_SECRET_ACCESS_KEY=$aws_secret_access_key
    CREDENTIALS_INTEGRATION_AWS_REDSHIFT_USER=$CREDENTIALS_INTEGRATION_AWS_REDSHIFT_USER
    CREDENTIALS_INTEGRATION_AWS_REDSHIFT_PASSWORD=$CREDENTIALS_INTEGRATION_AWS_REDSHIFT_PASSWORD
    CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_KEY=$CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_KEY
    CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_SECRET=$CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_SECRET
    CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_KEY=$CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_KEY
    CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_SECRET=$CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_SECRET
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_CLIENT=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_CLIENT
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_SECRET=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_SECRET
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_CLIENT=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_CLIENT
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_SECRET=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_SECRET
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_CLIENT=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_CLIENT
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_SECRET=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_SECRET
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_USERNAME=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_USERNAME
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_PASSWORD=$(echo "$CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_PASSWORD_BASE64" | base64 --decode)
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_ACCOUNT=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_ACCOUNT
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_WAREHOUSE=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_WAREHOUSE
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_DB=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_DB
    CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_SCHEMA=$CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_SCHEMA
    CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_USERNAME=$CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_USERNAME
    CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_PASSWORD=$(echo "$CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_PASSWORD_BASE64" | base64 --decode)
    CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_SECURITY_TOKEN=$CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_SECURITY_TOKEN
    CREDENTIALS_INTEGRATION_SALESFORCE_USERNAME=$CREDENTIALS_INTEGRATION_SALESFORCE_USERNAME
    CREDENTIALS_INTEGRATION_SALESFORCE_PASSWORD=$(echo "$CREDENTIALS_INTEGRATION_SALESFORCE_PASSWORD_BASE64" | base64 --decode)
    CREDENTIALS_INTEGRATION_SALESFORCE_SECURITY_TOKEN=$CREDENTIALS_INTEGRATION_SALESFORCE_SECURITY_TOKEN
    CREDENTIALS_PARSING_AWS_ACCESS_KEY_ID=$aws_access_key_id
    CREDENTIALS_PARSING_AWS_SECRET_ACCESS_KEY=$aws_secret_access_key
    CREDENTIALS_PARSING_AWS_REGION_NAME=$CREDENTIALS_PARSING_AWS_REGION_NAME
    CREDENTIALS_CHATGPT_API_KEY=$CREDENTIALS_CHATGPT_API_KEY
  - eb deploy "$EB_ENVIRONMENT_NAME" --staged --timeout 20

develop-deploy:
  stage: deploy
  only:
    - develop
  variables:
    EB_APPLICATION_NAME: $EB_dev_application_name
    EB_ENVIRONMENT_NAME: $EB_dev_environment_name
    DJANGO_SETTINGS: devSettings
  script:
    - *common_script # Reuse the script with dynamically resolved variables
  environment: Development
  when: manual
  needs: ["test"]

staging-deploy:
  stage: deploy
  only:
    - staging
  variables:
    EB_APPLICATION_NAME: $EB_stag_application_name
    EB_ENVIRONMENT_NAME: $EB_stag_environment_name
    DJANGO_SETTINGS: stagingSettings
  script:
    - *common_script # Reuse the script with dynamically resolved variables
  environment: Staging
  when: manual
  needs: ["test"]

production-deploy:
  stage: deploy
  only:
    - production
  variables:
    EB_APPLICATION_NAME: $EB_prod_application_name
    EB_ENVIRONMENT_NAME: $EB_prod_environment_name
    DJANGO_SETTINGS: prodSettings
  script:
    - export CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_USERNAME=$CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_USERNAME_PROD
    - export CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_SECURITY_TOKEN=$CREDENTIALS_INTEGRATION_SALESFORCE_SECURITY_TOKEN_PROD
    - *common_script # Reuse the script with dynamically resolved variables
  environment: Production
  when: manual
  needs: ["test"]
