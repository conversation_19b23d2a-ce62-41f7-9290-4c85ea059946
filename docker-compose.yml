services:
  deployment:
    build:
      context: .
      dockerfile: ./Dockerfile
    ports:
      - "80:8000"
    environment:
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      CREDENTIALS_INTEGRATION_AWS_ACCESS_KEY_ID: ${CREDENTIALS_INTEGRATION_AWS_ACCESS_KEY_ID}
      CREDENTIALS_INTEGRATION_AWS_SECRET_ACCESS_KEY: ${CREDENTIALS_INTEGRATION_AWS_SECRET_ACCESS_KEY}
      CREDENTIALS_INTEGRATION_AWS_REDSHIFT_USER: ${CREDENTIALS_INTEGRATION_AWS_REDSHIFT_USER}
      CREDENTIALS_INTEGRATION_AWS_REDSHIFT_PASSWORD: ${CREDENTIALS_INTEGRATION_AWS_REDSHIFT_PASSWORD}
      CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_KEY: ${CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_KEY}
      CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_SECRET: ${CREDENTIALS_INTEGRATION_FILEVINE_TEST_API_SECRET}
      CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_KEY: ${CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_KEY}
      CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_SECRET: ${CREDENTIALS_INTEGRATION_FILEVINE_HOSTILO_API_SECRET}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_CLIENT: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_CLIENT}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_SECRET: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_TEST_SECRET}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_CLIENT: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_CLIENT}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_SECRET: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_PREVIEW_SECRET}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_CLIENT: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_CLIENT}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_SECRET: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_API_SECRET}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_USERNAME: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_USERNAME}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_PASSWORD: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_PASSWORD}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_ACCOUNT: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_ACCOUNT}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_WAREHOUSE: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_WAREHOUSE}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_DB: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_DB}
      CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_SCHEMA: ${CREDENTIALS_INTEGRATION_ATHENAHEALTH_DATAVIEW_SCHEMA}
      CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_USERNAME: ${CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_USERNAME}
      CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_PASSWORD: ${CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_PASSWORD}
      CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_SECURITY_TOKEN: ${CREDENTIALS_INTEGRATION_SALESFORCE_SANDBOX_SECURITY_TOKEN}
      CREDENTIALS_INTEGRATION_SALESFORCE_USERNAME: ${CREDENTIALS_INTEGRATION_SALESFORCE_USERNAME}
      CREDENTIALS_INTEGRATION_SALESFORCE_PASSWORD: ${CREDENTIALS_INTEGRATION_SALESFORCE_PASSWORD}
      CREDENTIALS_INTEGRATION_SALESFORCE_SECURITY_TOKEN: ${CREDENTIALS_INTEGRATION_SALESFORCE_SECURITY_TOKEN}
      CREDENTIALS_PARSING_AWS_ACCESS_KEY_ID: ${CREDENTIALS_PARSING_AWS_ACCESS_KEY_ID}
      CREDENTIALS_PARSING_AWS_SECRET_ACCESS_KEY: ${CREDENTIALS_PARSING_AWS_SECRET_ACCESS_KEY}
      CREDENTIALS_PARSING_AWS_REGION_NAME: ${CREDENTIALS_PARSING_AWS_REGION_NAME}
      CREDENTIALS_CHATGPT_API_KEY: ${CREDENTIALS_CHATGPT_API_KEY}
      COMPOSE_HTTP_TIMEOUT: 1200
