from unittest.mock import MagicMock


class TestSalesforceUpdateSettledTask:
    def test_salesforce_update_settled_task_with_default_args(
        self,
        mock_post_main_update_settled: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test Salesforce update settled task with default arguments."""
        from integration.integration_code.tasks import (
            salesforce_update_settled_task,
        )

        args_dict = {}
        result = salesforce_update_settled_task(args_dict)

        # Verify the function was called with default arguments
        expected_partial_config = {
            'plaintiffs': False,
            'lawfirms': False,
            'legalpersonnel': False,
            'cases': False,
            'billings': True,
            'charges': False,
            'files': False,
            'notes': False,
        }
        mock_post_main_update_settled.assert_called_once_with(
            False, expected_partial_config, False
        )
        assert result == 'Salesforce Update Settled Complete'

    def test_salesforce_update_settled_task_with_custom_args(
        self,
        mock_post_main_update_settled: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test Salesforce update settled task with custom arguments."""
        from integration.integration_code.tasks import (
            salesforce_update_settled_task,
        )

        custom_partial_config = {
            'billings': True,
            'charges': True,
        }

        args_dict = {
            'test': True,
            'partial_config': custom_partial_config,
            'all_data': True,
        }
        result = salesforce_update_settled_task(args_dict)

        # Verify the function was called with custom arguments
        mock_post_main_update_settled.assert_called_once_with(
            True, custom_partial_config, True
        )
        assert result == 'Salesforce Update Settled Complete'

    def test_salesforce_update_settled_task_with_partial_args(
        self,
        mock_post_main_update_settled: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test Salesforce update settled task with partial arguments."""
        from integration.integration_code.tasks import (
            salesforce_update_settled_task,
        )

        args_dict = {'test': True, 'all_data': True}
        result = salesforce_update_settled_task(args_dict)

        # Verify the function was called with default partial_config
        expected_partial_config = {
            'plaintiffs': False,
            'lawfirms': False,
            'legalpersonnel': False,
            'cases': False,
            'billings': True,
            'charges': False,
            'files': False,
            'notes': False,
        }
        mock_post_main_update_settled.assert_called_once_with(
            True, expected_partial_config, True
        )
        assert result == 'Salesforce Update Settled Complete'

    def test_salesforce_update_settled_task_lock_not_acquired(
        self,
        mock_advisory_lock_failure: MagicMock,
    ):
        """
        Test that salesforce_update_settled_task returns the correct message when the advisory lock is not acquired.
        """
        from integration.integration_code.tasks import (
            salesforce_update_settled_task,
        )

        args_dict = {}

        result = salesforce_update_settled_task(args_dict)
        assert result == "Salesforce Update Settled Failed: Lock not acquired"
