import csv
import os
import pathlib

import langchain.chains
import langchain.document_loaders
import langchain.prompts
import langchain.schema
import langchain_community.vectorstores.faiss
import langchain_openai

current_dir = pathlib.Path(__file__).parent
training_files_dir = pathlib.Path(current_dir, 'portal_reduction_data')
llm_chain_template_path = pathlib.Path(
    training_files_dir, 'reduction_llm_chain_template.txt'
)
recommended_reduction_notes_path = pathlib.Path(
    training_files_dir, 'recommended_reduction_notes.csv'
)


def initialize_openai(
    model_name: str = 'gpt-5',
    temperature: float = 0,
    max_tokens: int = 200,
    timeout: int = 10,
) -> langchain_openai.ChatOpenAI:
    '''
    Function to initialize the OpenAI API key and return a ChatOpenAI object.
    '''
    try:
        openai_api_key = os.getenv('CREDENTIALS_CHATGPT_API_KEY')
        if openai_api_key is None:
            raise Exception('OpenAI API key is not set')
        return langchain_openai.ChatOpenAI(
            api_key=openai_api_key,  # type: ignore  # Stored as a secret string in the environment
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            timeout=timeout,
        )
    except Exception as e:
        raise Exception(f'Failed to initialize OpenAI API: {str(e)}')


"""
Loader Part: Only Needed If `retrieve_info` Is Used
---------------------------------------------------
The following function is used to load documents, create embeddings,
and initialize a FAISS vector store. This part is only necessary if you plan to use
the `retrieve_info` function, which relies on the vector store to perform similarity searches.
"""


def initialize_embeddings(
    documents: list[langchain.schema.Document],
) -> langchain_community.vectorstores.faiss.FAISS:
    '''
    Function to initialize embeddings using OpenAI embeddings and FAISS vectorstore.
    '''
    try:
        embeddings = langchain_openai.OpenAIEmbeddings()
        return langchain_community.vectorstores.faiss.FAISS.from_documents(
            documents, embeddings
        )
    except Exception as e:
        raise Exception(f'Failed to initialize embeddings: {str(e)}')


def initialize_llm_chain(
    llm: langchain_openai.ChatOpenAI,
) -> langchain.chains.LLMChain:
    '''
    Function to initialize the LLMChain with a prompt template.
    '''
    global llm_chain_template_path

    try:
        if not llm_chain_template_path.exists():
            raise FileNotFoundError(
                f'File not found at path: {llm_chain_template_path}'
            )

        with open(llm_chain_template_path, 'r', encoding='utf-8') as file:
            template = file.read()
        prompt = langchain.prompts.PromptTemplate(
            template=template,
            input_variables=[
                'Payoff_Amount',
                'Offer_Amount',
                'pro_rata_amount',
                'CashOut',
                'Settlement_Amount',
                'Top_Offer',
                'Provider_Name',
                'funding_record_types',
                'facility_lien_amount',
                'professional_lien_amount',
                'State',
                'is_settlement_statement_available',
                'RMCounterofferAmount',
                'NegotiationHistory',
                'Plaintiff_Name',
                'LawFirm_Name',
                'previous_offer',
            ],
        )

        chain = langchain.chains.LLMChain(llm=llm, prompt=prompt)
        return chain
    except Exception as e:
        raise Exception(f'Failed to initialize LLM chain: {str(e)}')


def load_and_initialize_vector_store(
    vector_store_path: pathlib.Path,
    docs_file_path: pathlib.Path,
    embeddings: langchain_openai.OpenAIEmbeddings,
    force_recreate: bool = False,
) -> langchain_community.vectorstores.faiss.FAISS:
    '''
    Function returns previously created vector store if it exists.
    Otherwise, it loads documents from a CSV file, initializes embeddings,
    and creates a FAISS vector store, which is then saved to disk.
    '''
    try:
        if not vector_store_path.exists():
            print(
                f'Vector store not found at path: {vector_store_path.as_posix()}'
            )
            print('Creating vector store...')
        if vector_store_path.exists() and not force_recreate:
            return langchain_community.vectorstores.faiss.FAISS.load_local(
                vector_store_path.as_posix(), embeddings
            )
        if not docs_file_path.exists():
            raise FileNotFoundError(
                f'File not found at path: {docs_file_path.as_posix()}'
            )

        loader = langchain.document_loaders.CSVLoader(docs_file_path)
        if loader is None:
            raise ValueError('Failed to load documents')
        documents = loader.load()
        vectorized_db = initialize_embeddings(documents)
        vectorized_db.save_local(vector_store_path.as_posix())
        return vectorized_db

    except FileNotFoundError as e:
        raise FileNotFoundError(str(e))
    except ValueError as e:
        raise ValueError(str(e))
    except Exception as e:
        raise Exception(f'Error initializing vector store: {str(e)}')


"""
retrieve_info Function: Only Needed If `retrieve_info` Is Used
--------------------------------------------------------------
This function performs a similarity search on the vector store (db) to retrieve
relevant information based on a query. It requires the `db` initialized from the loader part.
"""


def retrieve_info(
    query: str, db: langchain_community.vectorstores.faiss.FAISS
) -> list[str]:
    '''
    Function to retrieve information from the vectorstore based on a query.
    '''
    try:
        most_similar_responses = db.similarity_search(query, k=20)
        response_list = [doc.page_content for doc in most_similar_responses]
        return response_list
    except Exception as e:
        raise Exception(
            f'Error retrieving information from vectorstore: {str(e)}'
        )


def generate_response(
    chain: langchain.chains.LLMChain, data: dict[str, str]
) -> str:
    '''
    Function to generate a response using the LLMChain.
    '''
    global recommended_reduction_notes_path

    recommended_reduction_notes = ''

    # Retrieve best practices using retrieve_info if vector store (db) is available.
    # Note: This requires the `retrieve_info` function and the vector store (`db`) to be initialized.
    # Uncomment the following lines if you need to use retrieve_info:
    # best_practice = retrieve_info(data['message'], db)  # Assuming `db` is initialized and passed to the function

    try:
        if not recommended_reduction_notes_path.exists():
            raise FileNotFoundError(
                f'File not found at path: {recommended_reduction_notes_path}'
            )

        with open(
            recommended_reduction_notes_path, mode='r', encoding='utf-8'
        ) as recommended_reduction_notes_file:
            reader = csv.reader(recommended_reduction_notes_file)
            for row in reader:
                # Since there's only one column, we concatenate the first element of each row
                recommended_reduction_notes += row[0] + '\n'

    except FileNotFoundError as e:
        raise FileNotFoundError(str(e))
    except Exception as e:
        raise Exception(f'Error reading reduction notes: {str(e)}')

    required_keys = [
        'Payoff_Amount',
        'Offer_Amount',
        'pro_rata_amount',
        'CashOut',
        'Settlement_Amount',
        'Top_Offer',
        'Provider_Name',
        'funding_record_types',
        'facility_lien_amount',
        'professional_lien_amount',
        'State',
        'is_settlement_statement_available',
        'RMCounterofferAmount',
        'NegotiationHistory',
        'Plaintiff_Name',
        'LawFirm_Name',
        'previous_offer',
    ]
    for key in required_keys:
        if key not in data:
            raise KeyError(f'Missing required data field: {key}')

    try:
        response = chain.run(
            Payoff_Amount=data['Payoff_Amount'],
            Offer_Amount=data['Offer_Amount'],
            pro_rata_amount=data['pro_rata_amount'],
            CashOut=data['CashOut'],
            Settlement_Amount=data['Settlement_Amount'],
            Top_Offer=data['Top_Offer'],
            Provider_Name=data['Provider_Name'],
            funding_record_types=data['funding_record_types'],
            facility_lien_amount=data['facility_lien_amount'],
            professional_lien_amount=data['professional_lien_amount'],
            State=data['State'],
            is_settlement_statement_available=data[
                'is_settlement_statement_available'
            ],
            RMCounterofferAmount=data['RMCounterofferAmount'],
            NegotiationHistory=data['NegotiationHistory'],
            Plaintiff_Name=data['Plaintiff_Name'],
            LawFirm_Name=data['LawFirm_Name'],
            previous_offer=data['previous_offer'],
        )
        return response
    except Exception as e:
        raise Exception(f'Error generating response: {str(e)}')
