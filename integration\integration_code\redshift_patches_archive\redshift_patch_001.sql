ALTER TABLE integration.dev.billings
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.dev.charges
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.dev.transactions
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.dev.cases
RENAME COLUMN casestatus TO status
;

ALTER TABLE integration.staging.billings
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.staging.charges
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.staging.transactions
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.staging.cases
RENAME COLUMN casestatus TO status
;

ALTER TABLE integration.prod.billings
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.prod.charges
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.prod.transactions
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.prod.cases
RENAME COLUMN casestatus TO status
;

ALTER TABLE integration.main.billings
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.main.charges
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.main.transactions
ADD COLUMN status character varying(100) ENCODE lzo
DEFAULT ''
;

ALTER TABLE integration.main.cases
RENAME COLUMN casestatus TO status
;

CREATE
OR REPLACE VIEW integration.dev.cases_with_settled_billings_status AS
SELECT
   DISTINCT cases.sourceid,
   cases.sourcename,
   billings.status
FROM
   integration.dev.cases cases
   JOIN integration.dev.billings billings ON billings.caseid:: text = cases.sourceid:: text
   AND billings.sourcename:: text = cases.sourcename:: text;

CREATE
OR REPLACE VIEW integration.staging.cases_with_settled_billings_status AS
SELECT
   DISTINCT cases.sourceid,
   cases.sourcename,
   billings.status
FROM
   integration.staging.cases cases
   JOIN integration.staging.billings billings ON billings.caseid:: text = cases.sourceid:: text
   AND billings.sourcename:: text = cases.sourcename:: text;

CREATE
OR REPLACE VIEW integration.prod.cases_with_settled_billings_status AS
SELECT
   DISTINCT cases.sourceid,
   cases.sourcename,
   billings.status
FROM
   integration.prod.cases cases
   JOIN integration.prod.billings billings ON billings.caseid:: text = cases.sourceid:: text
   AND billings.sourcename:: text = cases.sourcename:: text;
   
CREATE
OR REPLACE VIEW integration.main.cases_with_settled_billings_status AS
SELECT
   DISTINCT cases.sourceid,
   cases.sourcename,
   billings.status
FROM
   integration.main.cases cases
   JOIN integration.main.billings billings ON billings.caseid:: text = cases.sourceid:: text
   AND billings.sourcename:: text = cases.sourcename:: text;