import typing
from unittest.mock import MagicMock

import psycopg
import pytest


class TestPostMainUpdateSettled:
    @pytest.mark.django_db
    def test_should_post_main_update_settled(
        self,
        database: psycopg.Connection[typing.Any],
        mock_get_sf_action: MagicMock,
    ):
        from integration.integration_code import salesforce

        update_response = [
            {
                'success': True,
                'created': True,
                'id': '001Ec00000emK49IAE',
                'errors': [],
            }
        ]

        mock_update_hcp = MagicMock()
        mock_update_liability = MagicMock()
        mock_update_writeoff = MagicMock()

        mock_update_hcp.return_value = update_response
        mock_update_liability.return_value = update_response
        mock_update_writeoff.return_value = update_response

        mock_get_sf_action.side_effect = [
            mock_update_hcp,
            mock_update_liability,
            mock_update_writeoff,
        ]

        salesforce.post_main_update_settled(
            test=True,
            all_data=True,
            partial_config={
                'billings': True,
            },
        )

        data, *_ = mock_update_hcp.call_args[0]

        assert data[0] == {
            'Id': 'salesforce id',
            'Payoff_Status__c': 'Pending Payment from HCP',
        }

        data, *_ = mock_update_liability.call_args[0]

        assert data[0] == {
            'Id': 'salesforce id auto',
            'Payoff_Status__c': 'Liability Insurance Payment to Provider',
        }

        data, *_ = mock_update_writeoff.call_args[0]

        assert data[0] == {
            'Id': 'salesforce id',
            'Payoff_Status__c': 'Write Off by Provider',
        }

        assert mock_get_sf_action.call_count == 3

    @pytest.mark.django_db
    def test_liability_insurance_payment_filter(
        self,
        database: psycopg.Connection[typing.Any],
    ):
        """Test that only AutoCommercialInsurance + HCP records get Liability Insurance status"""
        import pandas as pd

        from integration.integration_code.local_operations import (
            get_redshift_to_salesforce_update_settled_billing_data,
        )

        test_data = pd.DataFrame(
            [
                {
                    'GainId': 'test1',
                    'PaidTo': 'HCP',
                    'PaidBy': 'AutoCommercialInsurance',
                    'Status': 'Settled',
                },
                {
                    'GainId': 'test2',
                    'PaidTo': 'HCP',
                    'PaidBy': 'Attorney',
                    'Status': 'Settled',
                },
                {
                    'GainId': 'test3',
                    'PaidTo': 'Gain',
                    'PaidBy': 'AutoCommercialInsurance',
                    'Status': 'Settled',
                },
            ]
        )

        result = get_redshift_to_salesforce_update_settled_billing_data(
            test_data, 'Liability Insurance Payment to Provider'
        )

        assert len(result) == 1
        assert result.iloc[0]['GainId'] == 'test1'
        assert (
            result.iloc[0]['PayoffStatus']
            == 'Liability Insurance Payment to Provider'
        )

    @pytest.mark.django_db
    def test_pending_payment_hcp_filter(
        self,
        database: psycopg.Connection[typing.Any],
    ):
        """Test that only Attorney + HCP records get Pending Payment from HCP status"""
        import pandas as pd

        from integration.integration_code.local_operations import (
            get_redshift_to_salesforce_update_settled_billing_data,
        )

        test_data = pd.DataFrame(
            [
                {
                    'GainId': 'test1',
                    'PaidTo': 'HCP',
                    'PaidBy': 'Attorney',
                    'Status': 'Settled',
                },
                {
                    'GainId': 'test2',
                    'PaidTo': 'HCP',
                    'PaidBy': 'AutoCommercialInsurance',
                    'Status': 'Settled',
                },
                {
                    'GainId': 'test3',
                    'PaidTo': 'Gain',
                    'PaidBy': 'Attorney',
                    'Status': 'Settled',
                },
            ]
        )

        result = get_redshift_to_salesforce_update_settled_billing_data(
            test_data, 'Pending Payment from HCP'
        )

        assert len(result) == 1
        assert result.iloc[0]['GainId'] == 'test1'
        assert result.iloc[0]['PayoffStatus'] == 'Pending Payment from HCP'
