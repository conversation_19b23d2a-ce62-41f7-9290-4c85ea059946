CREATE TABLE insurances (
    gainid VARCHAR(16) NOT NULL,
    companyname VA<PERSON>HAR(250),
    policyid VARCHAR(20),
    memberid VARCHAR(20),
    groupnumber VARCHAR(20),
    status VARCHAR(100),
    limits VARCHAR(100),
    phone VARCHAR(100),
    fax VARCHAR(100),
    email VARCHAR(100),
    followupemail VARCHAR(100),
    billingaddressline1 VARCHAR(250),
    billingaddressline2 VARCHAR(250),
    billingaddresscity VARCHAR(100),
    billingaddressstate VARCHAR(100),
    billingaddresszip VARCHAR(20),
    physicaladdressline1 VARCHAR(250),
    physicaladdressline2 VARCHAR(250),
    physicaladdresscity VARCHAR(100),
    physicaladdressstate VARCHAR(100),
    physicaladdresszip VARCHAR(20),
    liabilityaccepted BOOLEAN,
    declarationpagereceived BOOLEAN,
    medpayexhausted BOOLEAN,
    pipexhausted BOOLEAN,
    probate BOOLEAN,
    bankruptcy BOOLEAN,
    subrogationlien BOOLEAN,
    otherlien BOOLEAN,
    otherlienname VARCHAR(100),
    drivername VARCHAR(100),
    datesettled TIMESTAMP WITHOUT TIME ZONE,
    howcasewassettled VARCHAR(100),
    totalsettlementamount NUMERIC(10, 4),
    attorneyfee NUMERIC(10, 4),
    attorneyfeeflexible BOOLEAN,
    referralfeepercentage NUMERIC(10, 4),
    amounttoclient NUMERIC(10, 4),
    settlementnotes VARCHAR(1000),
    notes VARCHAR(1000),
    type VARCHAR(100),
    caseid VARCHAR(20) NOT NULL,
    relevanttogain BOOLEAN,
    todelete BOOLEAN DEFAULT false,
    todeletesystem VARCHAR(100),
    deletepreventoverride BOOLEAN DEFAULT false,
    deletepreventoverridereason VARCHAR(1000),
    billspaid BOOLEAN,
    PRIMARY KEY (gainid)
);