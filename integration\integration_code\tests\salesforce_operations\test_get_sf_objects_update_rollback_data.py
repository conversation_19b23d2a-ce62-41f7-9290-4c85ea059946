import pytest

import integration.integration_code.salesforce_operations as salesforce_operations


class TestGetSfObjectsUpdateRollbackData:

    @pytest.mark.django_db
    def test_should_get_sf_objects_update_rollback_data(self):
        """Test that rollback data can be retrieved for Salesforce objects."""
        salesforce_operations.authenticate_or_fail_salesforce(test=True)

        data = salesforce_operations.get_sf_objects_update_rollback_data(
            ['001Ec000009pwbtIAA'],
            'select id from account',
            'Account',
        )
        assert data is not None
        assert data.size == 1
