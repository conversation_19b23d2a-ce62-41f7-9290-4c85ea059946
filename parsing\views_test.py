import base64
import typing
from unittest.mock import MagicMock, patch

import django
import django.urls
import pytest


class TestParsingViews:

    def pdf_to_base64(self, pdf_path: str):
        with open(pdf_path, "rb") as pdf_file:
            base64_encoded = base64.b64encode(pdf_file.read()).decode("utf-8")
        return base64_encoded

    @pytest.fixture
    def mock_write_json(self):
        from parsing.parsing_code.hcfa import write_json

        with patch('parsing.parsing_code.hcfa.write_json') as mock_write_json:
            mock_write_json.side_effect = write_json

            yield mock_write_json

    @pytest.mark.django_db
    @patch('parsing.parsing_code.hcfa.aws_operations.textract')
    def test_parse_hcfa(
        self,
        mock_textract: MagicMock,
        mock_write_json: MagicMock,
        authorized_client: typing.Any,
        gain_bucket: typing.Any,
    ):
        mock_textract.return_value = (
            '08 26 1994',
            [
                '08 05 21',
                '99213',
                '221 00',
                '',
                'and',
                '',
                '',
                'and',
                '',
                '',
                'and',
                '',
                '',
                'and',
                '',
                '',
                'and',
                '',
                '111392 010005',
                '22100',
                '000',
            ],
        )

        url = django.urls.reverse('hcfa_parsing')
        claim_content = self.pdf_to_base64(
            'parsing/parsing_code/fixtures/base_test_claim.pdf'
        )
        response = authorized_client.post(
            url, {'name': 'test', 'data': claim_content}
        )

        mock_textract.assert_called_once()

        _, data = mock_write_json.call_args[0]

        resulta_data = {
            'dob': '1994-08-26',
            'error': 0,
            'date_1': '2021-08-05',
            'cpt_1': '99213',
            'amount_1': '221.00',
            'date_2': '',
            'cpt_2': '',
            'amount_2': '',
            'date_3': '',
            'cpt_3': '',
            'amount_3': '',
            'date_4': '',
            'cpt_4': '',
            'amount_4': '',
            'date_5': '',
            'cpt_5': '',
            'amount_5': '',
            'date_6': '',
            'cpt_6': '',
            'amount_6': '',
            'account': '************',
            'total': '221.00',
            'paid': '0.00',
        }
        assert data == resulta_data

        assert response.status_code == 200
        response_json = response.json()
        assert response_json == 'Parsed test sucessfully'

        response = authorized_client.get(url, {'name': 'test'})
        assert response.status_code == 200
        response_json = response.json()
        assert response_json == resulta_data
