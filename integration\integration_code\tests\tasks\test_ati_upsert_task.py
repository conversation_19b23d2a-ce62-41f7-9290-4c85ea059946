from unittest.mock import MagicMock


class TestAtiUpsertTask:
    def test_ati_upsert_task_with_default_args(
        self,
        mock_get_main_upsert: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI upsert task with default arguments."""
        from integration.integration_code.tasks import ati_upsert_task

        args_dict = {}
        result = ati_upsert_task(args_dict)
        mock_get_main_upsert.assert_called_once_with(
            True, True, True, True, False, False
        )
        assert result == 'ATI Upsert Complete'

    def test_ati_upsert_task_with_custom_args(
        self,
        mock_get_main_upsert: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI upsert task with custom arguments."""
        from integration.integration_code.tasks import ati_upsert_task

        args_dict = {
            'move_flag': False,
            'charges_update_flag': False,
            'billings_update_flag': True,
            'cases_update_flag': False,
            'test': True,
            'all_data': True,
        }
        result = ati_upsert_task(args_dict)
        mock_get_main_upsert.assert_called_once_with(
            <PERSON>als<PERSON>, <PERSON>als<PERSON>, True, <PERSON>als<PERSON>, True, True
        )
        assert result == 'ATI Upsert Complete'

    def test_ati_upsert_task_with_partial_args(
        self,
        mock_get_main_upsert: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI upsert task with partial arguments."""
        from integration.integration_code.tasks import ati_upsert_task

        args_dict = {'test': True, 'all_data': True}
        result = ati_upsert_task(args_dict)
        mock_get_main_upsert.assert_called_once_with(
            True, True, True, True, True, True
        )
        assert result == 'ATI Upsert Complete'

    def test_ati_upsert_task_lock_not_acquired(
        self,
        mock_advisory_lock_failure: MagicMock,
    ):
        """
        Test that ati_upsert_task returns the correct message when the advisory lock is not acquired.
        """
        from integration.integration_code.tasks import ati_upsert_task

        args_dict = {}

        result = ati_upsert_task(args_dict)
        assert result == "ATI Upsert Failed: Lock not acquired"
