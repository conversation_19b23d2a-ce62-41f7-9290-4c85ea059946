{"client_details": {"personId": {"native": 36376362, "partner": null}, "firstName": "<PERSON>'kina", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>", "primaryEmail": "harri<PERSON><EMAIL>", "pictureUrl": "/images/Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "pictureKey": "Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "ssn": "***********", "birthDate": "2003-05-21T00:00:00Z", "gender": "F", "maritalStatus": "S", "driversLicenseNumber": "*********", "personTypes": ["Client"], "uniqueId": "62002a74-20a3-47ab-81b5-34bbc22058ec", "searchNames": ["a'kina", "harris", "a'kina harris"], "phones": [{"phoneId": {"native": 69107297, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "links": {}}, {"phoneId": {"native": 69107298, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Home", "links": {}}, {"phoneId": {"native": 69107299, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Work", "links": {}}], "emails": [{"emailId": {"native": 17062075, "partner": null}, "address": "harri<PERSON><EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51507399, "partner": null}, "line1": "2409 Luxembourg Dr", "city": "Augusta", "state": "GA", "postalCode": "30906-4036", "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36376362", "projects": "/contacts/36376362/projects", "phones": "/contacts/36376362/phones", "emailAddresses": "/contacts/36376362/emailAddresses", "addresses": "/contacts/36376362/addresses"}}, "project": {"projectTypeCode": "PII", "rootDocFolderId": {"native": ********, "partner": null}, "phaseName": "Settled Resolving Liens(PRV)", "clientName": "<PERSON><PERSON><PERSON><PERSON>", "clientPictureURL": "/image-error.png", "clientPictureKey": "/images/Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "firstPrimaryName": "Follower", "firstPrimaryUsername": "follower", "isArchived": false, "lastActivity": "2024-04-24T15:10:27.6867147Z", "uniqueKey": "AkinaHarrisZ********", "projectOrClientName": "<PERSON><PERSON><PERSON><PERSON> - MVA - 2/15/2023", "hashtags": ["#ante-litem", "#AUG"], "orgId": 5676, "projectEmailAddress": "<EMAIL>", "createdDate": "0001-01-01T00:00:00Z", "projectUrl": "https://hostilolaw.filevineapp.com/#/project/********", "projectId": {"native": ********, "partner": null}, "projectTypeId": {"native": 15130, "partner": null}, "clientId": {"native": 36376362, "partner": null}, "projectName": "<PERSON><PERSON><PERSON><PERSON> - MVA - 2/15/2023", "phaseId": {"native": 124319, "partner": null}, "incidentDate": "2023-02-15T00:00:00Z", "links": {"self": "/projects/********", "projectType": "/projecttypes/15130", "rootFolder": "/folders/********", "client": "/contacts/36376362", "contacts": "/projects/********/contacts"}}, "contacts": [{"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36661955, "partner": null}, "role": "Intake: Policy Agency", "orgContact": {"personId": {"native": 36661955, "partner": null}, "firstName": "Richmond County Sheriff's Office", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "Richmond County Sheriff's Office", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default5ea14c45-5cf4-46c2-bf30-1a0bce169adf.png", "pictureKey": "Default5ea14c45-5cf4-46c2-bf30-1a0bce169adf.png", "notes": "AKA Full: RCSO\n\n* ALL open records request must be sent either FAX or sent via email *", "fromCompany": "", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "RCSO", "personTypes": [], "uniqueId": "2af822e0-424b-4290-a918-02661b034e2f", "searchNames": ["richmond", "county", "sheriff's", "office", "richmond county sheriff's office", "rcso"], "phones": [{"phoneId": {"native": 69405772, "partner": null}, "number": "(*************", "rawNumber": "7068211010", "label": "Main", "links": {}}, {"phoneId": {"native": 69405773, "partner": null}, "number": "(*************", "rawNumber": "7068211012", "label": "Fax", "links": {}}, {"phoneId": {"native": 69405774, "partner": null}, "number": "(*************", "rawNumber": "7068211431", "links": {}}], "emails": [{"emailId": {"native": 17179218, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51701010, "partner": null}, "line1": "400 Walton Way", "city": "Augusta", "state": "GA", "postalCode": "30901", "label": "Work", "fullAddress": "400 Walton Way, Augusta, GA 30901", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/36661955", "projects": "/contacts/36661955/projects", "phones": "/contacts/36661955/phones", "emailAddresses": "/contacts/36661955/emailAddresses", "addresses": "/contacts/36661955/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 34922247, "partner": null}, "role": "Intake: Towed By", "orgContact": {"personId": {"native": 34922247, "partner": null}, "firstName": "<PERSON>", "lastName": "One", "isSingleName": false, "fullName": "Wright One", "pictureUrl": "/images/Default159b6674-bdb6-40e4-9d73-6f69d391d847.png", "pictureKey": "Default159b6674-bdb6-40e4-9d73-6f69d391d847.png", "personTypes": [], "uniqueId": "3c90d84f-b201-4d76-a245-ba21238f55a6", "searchNames": ["wright", "one", "wright one"], "phones": [], "emails": [], "addresses": [{"addressId": {"native": 50610809, "partner": null}, "line1": "1848 <PERSON>", "line2": "", "city": "Augusta", "state": "GA", "postalCode": "30904", "fullAddress": "1848 <PERSON>, Augusta, GA 30904", "links": {}}], "hashtags": [], "links": {"self": "/contacts/34922247", "projects": "/contacts/34922247/projects", "phones": "/contacts/34922247/phones", "emailAddresses": "/contacts/34922247/emailAddresses", "addresses": "/contacts/34922247/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": ********, "partner": null}, "role": "Intake: Hospital", "orgContact": {"personId": {"native": ********, "partner": null}, "firstName": "Wellstar MCG Formerly AU Medical Center", "lastName": "", "isSingleName": true, "fullName": "Wellstar MCG Formerly AU Medical Center", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "notes": "Please send the <NAME_EMAIL> 8/10/2023\n\nAll balance verification are now done via fax and just make sure the client authorization is attached - fax to ************\n\nReductions 25% - <EMAIL>\n<EMAIL> \nHeather <PERSON> - <PERSON><EMAIL> - send redux request\n\nAKA Full: AUMC\nCan send bill request's to email: <EMAIL>\n\n11.8.23 SH - AUMC will no longer except request with Non-valid entity names include the following: Medical College of Georgia (MCG), Augusta\nUniversity (AU), and Georgia Regents University (GRU). \nThey will only accept  MCG Health, Inc., GRMC, Georgia Regents Medical Center, AU Medical Center. We can also accept requests addressed to GR Health or AU Health, which are the past and present names for the Health System", "fromCompany": "Augusta University Medical Center", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "Augusta University Medical Center AUMC", "personTypes": ["MedicalProvider"], "isMinor": false, "uniqueId": "9bdc1b1a-d5fc-46c1-9be1-991ed33ea9c8", "searchNames": ["wellstar", "mcg", "formerly", "au", "medical", "center", "wellstar mcg formerly au medical center", "augusta university medical center aumc"], "phones": [{"phoneId": {"native": 66262837, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 70954343, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 68559892, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 70954313, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 64899228, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 64938471, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Home", "links": {}}, {"phoneId": {"native": 65742062, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}, {"phoneId": {"native": 69154851, "partner": null}, "number": "************", "rawNumber": "7068136076", "links": {}}, {"phoneId": {"native": 69250564, "partner": null}, "number": "18003671500", "rawNumber": "18003671500", "links": {}}, {"phoneId": {"native": 69251247, "partner": null}, "number": "8887152667", "rawNumber": "8887152667", "links": {}}, {"phoneId": {"native": 69412425, "partner": null}, "number": "************", "rawNumber": "7067212948", "label": "Work", "links": {}}], "emails": [{"emailId": {"native": 17076627, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118339, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118340, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118341, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17347425, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 46172992, "partner": null}, "line1": "1120 15th Street", "line2": "BPM-120", "city": "Augusta", "state": "GA", "postalCode": "30912", "label": "Work", "fullAddress": "1120 15th Street, BPM-120, Augusta, GA 30912", "links": {}}, {"addressId": {"native": 52484901, "partner": null}, "line1": "4057 Rural Plains Circle, Suite 400", "line2": "", "city": "<PERSON>", "state": "TN", "postalCode": "37064", "label": "Other", "fullAddress": "4057 Rural Plains Circle, Suite 400, Franklin, TN 37064", "links": {}}, {"addressId": {"native": 49867867, "partner": null}, "line1": "1120 15th Street", "line2": "MOB-120", "city": "Augusta", "state": "GA", "postalCode": "30912", "label": "Work", "fullAddress": "1120 15th Street, MOB-120, Augusta, GA 30912", "links": {}}], "hashtags": [], "links": {"self": "/contacts/********", "projects": "/contacts/********/projects", "phones": "/contacts/********/phones", "emailAddresses": "/contacts/********/emailAddresses", "addresses": "/contacts/********/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27657947, "partner": null}, "role": "Intake: Intake Coordinator", "orgContact": {"personId": {"native": 27657947, "partner": null}, "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSingleName": false, "fullName": "<PERSON><PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "fromCompany": "<PERSON>, LLC", "gender": "M", "isTextingPermitted": false, "remarket": false, "personTypes": ["Firm"], "isMinor": false, "uniqueId": "c36b0368-96d4-426c-bfe5-de8b499d439a", "searchNames": ["terrell", "mc<PERSON><PERSON>", "terrell mc<PERSON>"], "phones": [{"phoneId": {"native": 69154324, "partner": null}, "number": "7069103771", "rawNumber": "7069103771", "links": {}}], "emails": [{"emailId": {"native": 17080471, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [], "hashtags": [], "department": "intake", "links": {"self": "/contacts/27657947", "projects": "/contacts/27657947/projects", "phones": "/contacts/27657947/phones", "emailAddresses": "/contacts/27657947/emailAddresses", "addresses": "/contacts/27657947/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36376364, "partner": null}, "role": "Intake: Driver of Vehicle Involved", "orgContact": {"personId": {"native": 36376364, "partner": null}, "firstName": "<PERSON><PERSON>", "middleName": "J", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON><PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "pictureKey": "Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "ssn": "***********", "birthDate": "1965-07-09T00:00:00Z", "gender": "F", "maritalStatus": "M", "driversLicenseNumber": "*********", "personTypes": ["Client"], "uniqueId": "5b5909f4-6d0d-43fb-9189-caceb79d71e0", "searchNames": ["sharmaine", "j", "harris", "shar<PERSON><PERSON> j harris", "sharmaine harris"], "phones": [{"phoneId": {"native": 69107301, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "links": {}}, {"phoneId": {"native": 69107302, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Home", "links": {}}], "emails": [{"emailId": {"native": 17062024, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51507400, "partner": null}, "line1": "2409 Luxembourg Dr", "city": "Augusta", "state": "GA", "postalCode": "30906-4036", "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36376364", "projects": "/contacts/36376364/projects", "phones": "/contacts/36376364/phones", "emailAddresses": "/contacts/36376364/emailAddresses", "addresses": "/contacts/36376364/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27736724, "partner": null}, "role": "Intake: Radiology Provider", "orgContact": {"personId": {"native": 27736724, "partner": null}, "firstName": "Augusta University Medical Associates", "lastName": "", "isSingleName": true, "fullName": "Augusta University Medical Associates", "primaryEmail": "<EMAIL>", "primaryEmailLabel": "Work", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "notes": "10/20/23 Spoke to <PERSON> who confirmed:\nFor Rad bills send <NAME_EMAIL>\nFor ER physician call <PERSON><PERSON><PERSON><PERSON> billing **********\n\nAKA Full: PPG\nGood Contact for <PERSON>: <PERSON><PERSON>iter - email: <EMAIL>  - WL\nBalances -************\n\nReductions 25% - \n (<PERSON>) b<PERSON><PERSON>@enablecomp.com\n(Jordan Mills) <EMAIL>\n (<PERSON>) <EMAIL>\n(Shamika Hall) <EMAIL>\n\n\n12.12.2022 - the contract with AU MEDICAL ASSO EMG MED has expired. This is now the PHY BILLING FOR AUMC.  request in CS", "fromCompany": "AU Medical Associates", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "WELLSTAR XRAY", "personTypes": ["MedicalProvider"], "isMinor": false, "uniqueId": "6e505ea3-da59-4079-88f6-080a134123d4", "searchNames": ["augusta", "university", "medical", "associates", "augusta university medical associates", "wellstar xray"], "phones": [{"phoneId": {"native": 62515702, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 62515703, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 69216978, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Main", "links": {}}, {"phoneId": {"native": 60145300, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Home", "links": {}}, {"phoneId": {"native": 61672659, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 60145301, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Main", "links": {}}, {"phoneId": {"native": 60145303, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 67905779, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}, {"phoneId": {"native": 69117528, "partner": null}, "number": "7067212948", "rawNumber": "7067212948", "links": {}}, {"phoneId": {"native": 69168276, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}, {"phoneId": {"native": 69756655, "partner": null}, "number": "************", "rawNumber": "8558711526", "links": {}}], "emails": [{"emailId": {"native": 17065650, "partner": null}, "address": "<EMAIL>", "label": "Work", "links": {}}, {"emailId": {"native": 15305923, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 16354127, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118344, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118342, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118343, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118345, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 46127753, "partner": null}, "line1": "1499 Walton Way", "line2": "Suite 1400", "city": "Augusta", "state": "GA", "postalCode": "30901", "label": "Work", "fullAddress": "1499 Walton Way, Suite 1400, Augusta, GA 30901", "links": {}}, {"addressId": {"native": 46127754, "partner": null}, "line1": "PO Box 96153", "city": "Oklahoma City", "state": "OK", "postalCode": "73143", "label": "Other", "fullAddress": "PO Box 96153, Oklahoma City, OK 73143", "links": {}}, {"addressId": {"native": 51514515, "partner": null}, "line1": "1120 15th Street", "line2": "MOB M120", "city": "Augusta", "state": "GA", "postalCode": "30912", "fullAddress": "1120 15th Street, MOB M120, Augusta, GA 30912", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27736724", "projects": "/contacts/27736724/projects", "phones": "/contacts/27736724/phones", "emailAddresses": "/contacts/27736724/emailAddresses", "addresses": "/contacts/27736724/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27735821, "partner": null}, "role": "Intake: Referred By?", "orgContact": {"personId": {"native": 27735821, "partner": null}, "firstName": "TV-Augusta", "lastName": "", "isSingleName": true, "fullName": "TV-Augusta", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "fromCompany": "TV-Augusta", "isTextingPermitted": false, "remarket": false, "personTypes": [], "isMinor": false, "uniqueId": "431acf1f-f3e9-4d5b-887f-938dc7d2e0b2", "searchNames": ["tv-augusta"], "phones": [], "emails": [], "addresses": [], "hashtags": [], "links": {"self": "/contacts/27735821", "projects": "/contacts/27735821/projects", "phones": "/contacts/27735821/phones", "emailAddresses": "/contacts/27735821/emailAddresses", "addresses": "/contacts/27735821/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27975633, "partner": null}, "role": "Intake: Emergency Room Physician Provider?", "orgContact": {"personId": {"native": 27975633, "partner": null}, "firstName": "AU Medical Associates", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "AU Medical Associates", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default89a77474-bfa5-4e2f-af46-bbb8ee372a66.png", "pictureKey": "Default89a77474-bfa5-4e2f-af46-bbb8ee372a66.png", "notes": "Reductions 25% to <EMAIL>", "abbreviatedName": "Radiology/Physicians", "personTypes": ["MedicalProvider"], "salutation": "", "uniqueId": "c1ba2f08-94fa-460f-9181-226ff8566c1a", "searchNames": ["au", "medical", "associates", "au medical associates", "radiology/physicians"], "phones": [{"phoneId": {"native": 69254525, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 61712384, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 69131488, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}], "emails": [{"emailId": {"native": 16932218, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17702979, "partner": null}, "address": "<PERSON><PERSON><PERSON><PERSON>@enablecomp.com", "links": {}}], "addresses": [{"addressId": {"native": 52899724, "partner": null}, "line1": "4057 Rural Plains Circle, Suit 400", "line2": "Attn: MVA Department", "city": "<PERSON>", "state": "TN", "postalCode": "37064", "label": "Billing", "fullAddress": "4057 Rural Plains Circle, Suit 400, Attn: MVA Department, Franklin, TN 37064", "links": {}}, {"addressId": {"native": 51042398, "partner": null}, "line1": "1499 Walton Way", "line2": "", "city": "Augusta", "state": "GA", "postalCode": "30901", "fullAddress": "1499 Walton Way, Augusta, GA 30901", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27975633", "projects": "/contacts/27975633/projects", "phones": "/contacts/27975633/phones", "emailAddresses": "/contacts/27975633/emailAddresses", "addresses": "/contacts/27975633/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 35732415, "partner": null}, "role": "Intake: Health Insurance Company", "orgContact": {"personId": {"native": 35732415, "partner": null}, "firstName": "United", "lastName": "Healthcare", "isSingleName": false, "fullName": "United Healthcare", "pictureUrl": "/images/Default642562b7-e50e-47d0-9ba3-06be27eeb0fb.png", "pictureKey": "Default642562b7-e50e-47d0-9ba3-06be27eeb0fb.png", "abbreviatedName": "Dual Complete", "personTypes": ["InsuranceCompany"], "uniqueId": "9694c6ce-7962-4528-81d8-3eeab93a74e2", "searchNames": ["united", "healthcare", "united healthcare", "dual complete"], "phones": [{"phoneId": {"native": 68439808, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Main", "links": {}}, {"phoneId": {"native": 68439809, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}], "emails": [], "addresses": [{"addressId": {"native": 51105555, "partner": null}, "line1": "P.O. Box 31350", "line2": "", "city": "Salt Lake City", "state": "UT", "postalCode": "84131-0350", "label": "Billing", "fullAddress": "P.O. Box 31350, Salt Lake City, UT 84131-0350", "links": {}}], "hashtags": [], "links": {"self": "/contacts/35732415", "projects": "/contacts/35732415/projects", "phones": "/contacts/35732415/phones", "emailAddresses": "/contacts/35732415/emailAddresses", "addresses": "/contacts/35732415/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27566162, "partner": null}, "role": "Case Summary: Primary Attorney", "orgContact": {"personId": {"native": 27566162, "partner": null}, "firstName": "<PERSON>", "lastName": "Butz", "isSingleName": false, "fullName": "<PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "fromCompany": "<PERSON>rm", "isTextingPermitted": false, "remarket": false, "personTypes": ["Attorney", "Firm"], "isMinor": false, "uniqueId": "508bdace-81f3-4842-8944-233dec30943d", "searchNames": ["ryan", "butz", "ryan butz"], "phones": [{"phoneId": {"native": 58457310, "partner": null}, "number": "************", "rawNumber": "4788450689", "label": "Work", "links": {}}, {"phoneId": {"native": 58457311, "partner": null}, "number": "************", "rawNumber": "4787870417", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 13563594, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 45341227, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27566162", "projects": "/contacts/27566162/projects", "phones": "/contacts/27566162/phones", "emailAddresses": "/contacts/27566162/emailAddresses", "addresses": "/contacts/27566162/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 32247168, "partner": null}, "role": "Case Summary: Case Manager", "orgContact": {"personId": {"native": 32247168, "partner": null}, "firstName": "<PERSON>", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Defaultac80c035-ca4b-4058-b2c7-5b1ed5d4bd34.png", "pictureKey": "Defaultac80c035-ca4b-4058-b2c7-5b1ed5d4bd34.png", "fromCompany": "<PERSON>rm", "personTypes": ["Firm"], "uniqueId": "6f7e63a6-79bc-492b-96fc-c82dd22d7483", "searchNames": ["kathy", "griffith", "kathy griffith"], "phones": [{"phoneId": {"native": 64307906, "partner": null}, "number": "(*************", "rawNumber": "7629942459", "label": "Work", "links": {}}, {"phoneId": {"native": 64311643, "partner": null}, "number": "(*************", "rawNumber": "7066618881", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 15127084, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 48425018, "partner": null}, "line1": "33 Park of Commerce Blvd.", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd., Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/32247168", "projects": "/contacts/32247168/projects", "phones": "/contacts/32247168/phones", "emailAddresses": "/contacts/32247168/emailAddresses", "addresses": "/contacts/32247168/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 28226442, "partner": null}, "role": "Case Summary: Claims Paralegal", "orgContact": {"personId": {"native": 28226442, "partner": null}, "firstName": "<PERSON>", "middleName": "", "lastName": "San<PERSON>", "isSingleName": false, "fullName": "<PERSON>", "pictureUrl": "/images/Defaultdfe81631-a5d3-41cc-832f-ddd2fb2f112c.png", "pictureKey": "Defaultdfe81631-a5d3-41cc-832f-ddd2fb2f112c.png", "abbreviatedName": "msa", "personTypes": ["Firm"], "salutation": "", "uniqueId": "85cb4486-d832-4b13-adae-61c036229a74", "searchNames": ["mel", "sanso", "mel sanso", "msa"], "phones": [{"phoneId": {"native": 65750741, "partner": null}, "number": "9123555515", "rawNumber": "9123555515", "links": {}}, {"phoneId": {"native": 65750742, "partner": null}, "number": "9123555519", "rawNumber": "9123555519", "label": "Fax", "links": {}}], "emails": [], "addresses": [{"addressId": {"native": 47033570, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/28226442", "projects": "/contacts/28226442/projects", "phones": "/contacts/28226442/phones", "emailAddresses": "/contacts/28226442/emailAddresses", "addresses": "/contacts/28226442/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 33808382, "partner": null}, "role": "Case Summary: Disbursement Coordinator", "orgContact": {"personId": {"native": 33808382, "partner": null}, "firstName": "Cierra", "lastName": "Lancaster", "isSingleName": false, "fullName": "<PERSON><PERSON><PERSON> Lancaster", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/4ed24ca1-f755-44c9-a90f-33ff9b92830b.png", "pictureKey": "4ed24ca1-f755-44c9-a90f-33ff9b92830b.png", "fromCompany": "<PERSON>rm", "personTypes": ["Firm"], "uniqueId": "4612d178-b6e4-48da-9e59-ad974766208e", "searchNames": ["cierra", "lancaster", "cierra lancaster"], "phones": [{"phoneId": {"native": 66483226, "partner": null}, "number": "************", "rawNumber": "9122285417", "label": "Work", "links": {}}, {"phoneId": {"native": 66483225, "partner": null}, "number": "************", "rawNumber": "9122144915", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 15978666, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 49888930, "partner": null}, "line1": "33 Park of Commerce Blvd.", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd., Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/33808382", "projects": "/contacts/33808382/projects", "phones": "/contacts/33808382/phones", "emailAddresses": "/contacts/33808382/emailAddresses", "addresses": "/contacts/33808382/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27574476, "partner": null}, "role": "Case Summary: Post Settlement Coordinator", "orgContact": {"personId": {"native": 27574476, "partner": null}, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "isSingleName": false, "fullName": "<PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "fromCompany": "<PERSON>rm", "isTextingPermitted": false, "remarket": false, "personTypes": ["Firm"], "isMinor": false, "uniqueId": "69aa402c-e6bd-4238-9103-654a5f812d58", "searchNames": ["mary", "mullice", "mary mullice"], "phones": [{"phoneId": {"native": 69392299, "partner": null}, "number": "************", "rawNumber": "9129887810", "label": "Work", "links": {}}, {"phoneId": {"native": 69392300, "partner": null}, "number": "************", "rawNumber": "9129887811", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 17170734, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51691051, "partner": null}, "line1": "33 Park of Commerce Blvd.", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd., Savannah, GA 31405", "links": {}}], "hashtags": [], "jobTitle": "", "links": {"self": "/contacts/27574476", "projects": "/contacts/27574476/projects", "phones": "/contacts/27574476/phones", "emailAddresses": "/contacts/27574476/emailAddresses", "addresses": "/contacts/27574476/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36420868, "partner": null}, "role": "Case Summary: Benefits Coordinator", "orgContact": {"personId": {"native": 36420868, "partner": null}, "firstName": "<PERSON><PERSON>", "lastName": "Austria", "isSingleName": false, "fullName": "Angelic Austria", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default21b083b5-983f-472a-a356-dd79271c2aff.png", "pictureKey": "Default21b083b5-983f-472a-a356-dd79271c2aff.png", "personTypes": ["Firm"], "uniqueId": "02665485-3f5b-4f8b-acd6-5167f5ff7a3c", "searchNames": ["angelic", "austria", "angelic austria"], "phones": [{"phoneId": {"native": 69167807, "partner": null}, "number": "************", "rawNumber": "9123416094", "links": {}}, {"phoneId": {"native": 69154319, "partner": null}, "number": "************", "rawNumber": "9125994361", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 17080469, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51537250, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36420868", "projects": "/contacts/36420868/projects", "phones": "/contacts/36420868/phones", "emailAddresses": "/contacts/36420868/emailAddresses", "addresses": "/contacts/36420868/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": ********, "partner": null}, "role": "Case Summary: Bill<PERSON> Analyst", "orgContact": {"personId": {"native": ********, "partner": null}, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "isSingleName": false, "fullName": "<PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default0c4c65d1-0e48-475b-acb6-0fb590f892ef.png", "pictureKey": "Default0c4c65d1-0e48-475b-acb6-0fb590f892ef.png", "fromCompany": "Michael <PERSON>, LLC", "personTypes": ["Firm"], "uniqueId": "1711ddc4-7ca1-4c68-a5ef-699b73a23dc0", "searchNames": ["joanna", "yerro", "joanna <PERSON>"], "phones": [{"phoneId": {"native": 69153501, "partner": null}, "number": "(*************", "rawNumber": "7062257239", "label": "Work", "links": {}}, {"phoneId": {"native": 69154317, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": ********, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": ********, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "hashtags": [], "jobTitle": "Billing Analyst", "department": "Accounting", "links": {"self": "/contacts/********", "projects": "/contacts/********/projects", "phones": "/contacts/********/phones", "emailAddresses": "/contacts/********/emailAddresses", "addresses": "/contacts/********/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": ********, "partner": null}, "role": "Case Summary: INI Benefits Coordinator", "orgContact": {"personId": {"native": ********, "partner": null}, "firstName": "AJ", "lastName": "Subalisdid", "isSingleName": false, "fullName": "AJ Subalisdid", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Defaultfe0e669a-e899-4d05-8407-dde87bc2674d.png", "pictureKey": "Defaultfe0e669a-e899-4d05-8407-dde87bc2674d.png", "personTypes": ["Firm"], "uniqueId": "8243b525-43b9-47e6-8db9-d1d066008806", "searchNames": ["aj", "subalisdid", "aj sub<PERSON><PERSON>"], "phones": [{"phoneId": {"native": ********, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}, {"phoneId": {"native": 69154321, "partner": null}, "number": "************", "rawNumber": "9125994362", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 17080470, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51537251, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/********", "projects": "/contacts/********/projects", "phones": "/contacts/********/phones", "emailAddresses": "/contacts/********/emailAddresses", "addresses": "/contacts/********/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36433873, "partner": null}, "role": "Case Summary: DEM/DO Benefits Coordinator", "orgContact": {"personId": {"native": 36433873, "partner": null}, "firstName": "<PERSON><PERSON>", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "<PERSON><PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default0bdd8e20-081b-49f2-8229-e4cc453f3a06.png", "pictureKey": "Default0bdd8e20-081b-49f2-8229-e4cc453f3a06.png", "fromCompany": "", "personTypes": ["Firm"], "uniqueId": "d37b4f63-0cc4-431d-8713-b062a2f28f13", "searchNames": ["yaj", "camille", "cañada", "yaj camille cañada"], "phones": [{"phoneId": {"native": 69168260, "partner": null}, "number": "************", "rawNumber": "9122285602", "links": {}}, {"phoneId": {"native": 69168261, "partner": null}, "number": "************", "rawNumber": "9126160117", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 17087644, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51546486, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/36433873", "projects": "/contacts/36433873/projects", "phones": "/contacts/36433873/phones", "emailAddresses": "/contacts/36433873/emailAddresses", "addresses": "/contacts/36433873/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": ********, "partner": null}, "role": "Case Summary: SET Benefits Coordinator", "orgContact": {"personId": {"native": ********, "partner": null}, "firstName": "AJ", "lastName": "Subalisdid", "isSingleName": false, "fullName": "AJ Subalisdid", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Defaultfe0e669a-e899-4d05-8407-dde87bc2674d.png", "pictureKey": "Defaultfe0e669a-e899-4d05-8407-dde87bc2674d.png", "personTypes": ["Firm"], "uniqueId": "8243b525-43b9-47e6-8db9-d1d066008806", "searchNames": ["aj", "subalisdid", "aj sub<PERSON><PERSON>"], "phones": [{"phoneId": {"native": ********, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}, {"phoneId": {"native": 69154321, "partner": null}, "number": "************", "rawNumber": "9125994362", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 17080470, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51537251, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/********", "projects": "/contacts/********/projects", "phones": "/contacts/********/phones", "emailAddresses": "/contacts/********/emailAddresses", "addresses": "/contacts/********/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": ********, "partner": null}, "role": "Medicals and Damages: Provider (Payee for Meds)", "orgContact": {"personId": {"native": ********, "partner": null}, "firstName": "Wellstar MCG Formerly AU Medical Center", "lastName": "", "isSingleName": true, "fullName": "Wellstar MCG Formerly AU Medical Center", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "notes": "Please send the <NAME_EMAIL> 8/10/2023\n\nAll balance verification are now done via fax and just make sure the client authorization is attached - fax to ************\n\nReductions 25% - <EMAIL>\n<EMAIL> \nHeather <PERSON> - <PERSON><EMAIL> - send redux request\n\nAKA Full: AUMC\nCan send bill request's to email: <EMAIL>\n\n11.8.23 SH - AUMC will no longer except request with Non-valid entity names include the following: Medical College of Georgia (MCG), Augusta\nUniversity (AU), and Georgia Regents University (GRU). \nThey will only accept  MCG Health, Inc., GRMC, Georgia Regents Medical Center, AU Medical Center. We can also accept requests addressed to GR Health or AU Health, which are the past and present names for the Health System", "fromCompany": "Augusta University Medical Center", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "Augusta University Medical Center AUMC", "personTypes": ["MedicalProvider"], "isMinor": false, "uniqueId": "9bdc1b1a-d5fc-46c1-9be1-991ed33ea9c8", "searchNames": ["wellstar", "mcg", "formerly", "au", "medical", "center", "wellstar mcg formerly au medical center", "augusta university medical center aumc"], "phones": [{"phoneId": {"native": 66262837, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 70954343, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 68559892, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 70954313, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 64899228, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 64938471, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Home", "links": {}}, {"phoneId": {"native": 65742062, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}, {"phoneId": {"native": 69154851, "partner": null}, "number": "************", "rawNumber": "7068136076", "links": {}}, {"phoneId": {"native": 69250564, "partner": null}, "number": "18003671500", "rawNumber": "18003671500", "links": {}}, {"phoneId": {"native": 69251247, "partner": null}, "number": "8887152667", "rawNumber": "8887152667", "links": {}}, {"phoneId": {"native": 69412425, "partner": null}, "number": "************", "rawNumber": "7067212948", "label": "Work", "links": {}}], "emails": [{"emailId": {"native": 17076627, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118339, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118340, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17118341, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17347425, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 46172992, "partner": null}, "line1": "1120 15th Street", "line2": "BPM-120", "city": "Augusta", "state": "GA", "postalCode": "30912", "label": "Work", "fullAddress": "1120 15th Street, BPM-120, Augusta, GA 30912", "links": {}}, {"addressId": {"native": 52484901, "partner": null}, "line1": "4057 Rural Plains Circle, Suite 400", "line2": "", "city": "<PERSON>", "state": "TN", "postalCode": "37064", "label": "Other", "fullAddress": "4057 Rural Plains Circle, Suite 400, Franklin, TN 37064", "links": {}}, {"addressId": {"native": 49867867, "partner": null}, "line1": "1120 15th Street", "line2": "MOB-120", "city": "Augusta", "state": "GA", "postalCode": "30912", "label": "Work", "fullAddress": "1120 15th Street, MOB-120, Augusta, GA 30912", "links": {}}], "hashtags": [], "links": {"self": "/contacts/********", "projects": "/contacts/********/projects", "phones": "/contacts/********/phones", "emailAddresses": "/contacts/********/emailAddresses", "addresses": "/contacts/********/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27975633, "partner": null}, "role": "Medicals and Damages: Provider (Payee for Meds)", "orgContact": {"personId": {"native": 27975633, "partner": null}, "firstName": "AU Medical Associates", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "AU Medical Associates", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default89a77474-bfa5-4e2f-af46-bbb8ee372a66.png", "pictureKey": "Default89a77474-bfa5-4e2f-af46-bbb8ee372a66.png", "notes": "Reductions 25% to <EMAIL>", "abbreviatedName": "Radiology/Physicians", "personTypes": ["MedicalProvider"], "salutation": "", "uniqueId": "c1ba2f08-94fa-460f-9181-226ff8566c1a", "searchNames": ["au", "medical", "associates", "au medical associates", "radiology/physicians"], "phones": [{"phoneId": {"native": 69254525, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 61712384, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 69131488, "partner": null}, "number": "************", "rawNumber": "**********", "links": {}}], "emails": [{"emailId": {"native": 16932218, "partner": null}, "address": "<EMAIL>", "links": {}}, {"emailId": {"native": 17702979, "partner": null}, "address": "<PERSON><PERSON><PERSON><PERSON>@enablecomp.com", "links": {}}], "addresses": [{"addressId": {"native": 52899724, "partner": null}, "line1": "4057 Rural Plains Circle, Suit 400", "line2": "Attn: MVA Department", "city": "<PERSON>", "state": "TN", "postalCode": "37064", "label": "Billing", "fullAddress": "4057 Rural Plains Circle, Suit 400, Attn: MVA Department, Franklin, TN 37064", "links": {}}, {"addressId": {"native": 51042398, "partner": null}, "line1": "1499 Walton Way", "line2": "", "city": "Augusta", "state": "GA", "postalCode": "30901", "fullAddress": "1499 Walton Way, Augusta, GA 30901", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27975633", "projects": "/contacts/27975633/projects", "phones": "/contacts/27975633/phones", "emailAddresses": "/contacts/27975633/emailAddresses", "addresses": "/contacts/27975633/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27735903, "partner": null}, "role": "Insurance: Insurer", "orgContact": {"personId": {"native": 27735903, "partner": null}, "firstName": "UMR", "lastName": "", "isSingleName": true, "fullName": "UMR", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "pictureKey": "Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "notes": "AKA Full: LORs", "fromCompany": "UMR", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "Subrogation", "personTypes": [], "isMinor": false, "uniqueId": "fa29c248-285f-4665-beb6-75797bfc6d7a", "searchNames": ["umr", "subrogation"], "phones": [{"phoneId": {"native": 57574425, "partner": null}, "number": "8882648721", "rawNumber": "8882648721", "label": "Main", "links": {}}, {"phoneId": {"native": 57574426, "partner": null}, "number": "8772913251", "rawNumber": "8772913251", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 16008710, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 44852481, "partner": null}, "line1": "L-3994", "city": "Columbus", "state": "OH", "postalCode": "43260", "label": "Work", "fullAddress": "L-3994, Columbus, OH 43260", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27735903", "projects": "/contacts/27735903/projects", "phones": "/contacts/27735903/phones", "emailAddresses": "/contacts/27735903/emailAddresses", "addresses": "/contacts/27735903/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27735903, "partner": null}, "role": "Liens: <PERSON><PERSON>", "orgContact": {"personId": {"native": 27735903, "partner": null}, "firstName": "UMR", "lastName": "", "isSingleName": true, "fullName": "UMR", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "pictureKey": "Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "notes": "AKA Full: LORs", "fromCompany": "UMR", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "Subrogation", "personTypes": [], "isMinor": false, "uniqueId": "fa29c248-285f-4665-beb6-75797bfc6d7a", "searchNames": ["umr", "subrogation"], "phones": [{"phoneId": {"native": 57574425, "partner": null}, "number": "8882648721", "rawNumber": "8882648721", "label": "Main", "links": {}}, {"phoneId": {"native": 57574426, "partner": null}, "number": "8772913251", "rawNumber": "8772913251", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 16008710, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 44852481, "partner": null}, "line1": "L-3994", "city": "Columbus", "state": "OH", "postalCode": "43260", "label": "Work", "fullAddress": "L-3994, Columbus, OH 43260", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27735903", "projects": "/contacts/27735903/projects", "phones": "/contacts/27735903/phones", "emailAddresses": "/contacts/27735903/emailAddresses", "addresses": "/contacts/27735903/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 33509652, "partner": null}, "role": "Liens: Recovery Agency (Check Payee)", "orgContact": {"personId": {"native": 33509652, "partner": null}, "firstName": "Optum", "isSingleName": false, "fullName": "Optum", "pictureUrl": "/images/Default5c545382-5de8-4113-b203-75e4522abc41.png", "pictureKey": "Default5c545382-5de8-4113-b203-75e4522abc41.png", "abbreviatedName": "PAYMENT", "personTypes": [], "uniqueId": "a80ae7cb-8971-47d9-aa04-0969556a64f5", "searchNames": ["optum", "payment"], "phones": [], "emails": [], "addresses": [{"addressId": {"native": 49712900, "partner": null}, "line1": "PO Box 182643", "line2": "", "city": "Columbus", "state": "OH", "postalCode": "43218", "fullAddress": "PO Box 182643, Columbus, OH 43218", "links": {}}], "hashtags": [], "links": {"self": "/contacts/33509652", "projects": "/contacts/33509652/projects", "phones": "/contacts/33509652/phones", "emailAddresses": "/contacts/33509652/emailAddresses", "addresses": "/contacts/33509652/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27740160, "partner": null}, "role": "Medicals and Damages: Provider (Payee for Meds)", "orgContact": {"personId": {"native": 27740160, "partner": null}, "firstName": "<PERSON><PERSON><PERSON> Chiropractic", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "<PERSON><PERSON><PERSON> Chiropractic", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "notes": "Reductions: 50% <NAME_EMAIL> (Check balance prior to sending request)\nTID:  58-2362299", "fromCompany": "", "isTextingPermitted": false, "remarket": false, "personTypes": ["MedicalProvider"], "isMinor": false, "uniqueId": "1507c144-0e50-481a-826d-889e52eb652f", "searchNames": ["pidcock", "chiropractic", "pidcock chiropractic"], "phones": [{"phoneId": {"native": 58451775, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 58451774, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Main", "links": {}}], "emails": [{"emailId": {"native": 13562147, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 45338858, "partner": null}, "line1": "3028 Peach Orchard Rd", "city": "Augusta", "state": "GA", "postalCode": "30906", "label": "Work", "fullAddress": "3028 Peach Orchard Rd, Augusta, GA 30906", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/27740160", "projects": "/contacts/27740160/projects", "phones": "/contacts/27740160/phones", "emailAddresses": "/contacts/27740160/emailAddresses", "addresses": "/contacts/27740160/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27569065, "partner": null}, "role": "Insurance: Insurer", "orgContact": {"personId": {"native": 27569065, "partner": null}, "firstName": "Richmond County Risk Management", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "Richmond County Risk Management", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Defaultcfc7a48f-afea-48c2-9ef5-cef47ff4bcd7.png", "pictureKey": "Defaultcfc7a48f-afea-48c2-9ef5-cef47ff4bcd7.png", "notes": "8.6.21 contact added <PERSON><PERSON><PERSON> to <PERSON> who said fax LOR and AR to risk management. They will investigate or set up claim and contact us. MS", "fromCompany": "", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "ARC Risk Management", "personTypes": ["InvolvedParty"], "isMinor": false, "uniqueId": "64d859e3-f5b4-41ae-a57c-658a2df8d1f8", "searchNames": ["richmond", "county", "risk", "management", "richmond county risk management", "arc risk management"], "phones": [{"phoneId": {"native": 64119232, "partner": null}, "number": "7068212301", "rawNumber": "7068212301", "links": {}}, {"phoneId": {"native": 64119233, "partner": null}, "number": "7068212502", "rawNumber": "7068212502", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 17103258, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 48314409, "partner": null}, "line1": "535 Telfair Street", "line2": "Suite 920", "city": "Augusta", "state": "GA", "postalCode": "30901", "fullAddress": "535 Telfair Street, Suite 920, Augusta, GA 30901", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/27569065", "projects": "/contacts/27569065/projects", "phones": "/contacts/27569065/phones", "emailAddresses": "/contacts/27569065/emailAddresses", "addresses": "/contacts/27569065/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36108232, "partner": null}, "role": "Insurance: Insured", "orgContact": {"personId": {"native": 36108232, "partner": null}, "firstName": "Augusta-Richmond County Board of Commissioners", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "Augusta-Richmond County Board of Commissioners", "pictureUrl": "/images/Defaulte217896a-7fda-46f1-8da6-c45fc82ec77a.png", "pictureKey": "Defaulte217896a-7fda-46f1-8da6-c45fc82ec77a.png", "fromCompany": "", "personTypes": [], "uniqueId": "eea3135d-d91e-4087-92bc-24a3b5fcfe61", "searchNames": ["augusta-richmond", "county", "board", "of", "commissioners", "augusta-richmond county board of commissioners"], "phones": [], "emails": [], "addresses": [{"addressId": {"native": 51342380, "partner": null}, "line1": "535 Telfair Street", "line2": "", "city": "Augusta", "state": "GA", "postalCode": "30901", "fullAddress": "535 Telfair Street, Augusta, GA 30901", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/36108232", "projects": "/contacts/36108232/projects", "phones": "/contacts/36108232/phones", "emailAddresses": "/contacts/36108232/emailAddresses", "addresses": "/contacts/36108232/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36379380, "partner": null}, "role": "Insurance: Driver", "orgContact": {"personId": {"native": 36379380, "partner": null}, "firstName": "<PERSON>", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON>", "pictureUrl": "/images/Default08bc9f57-4a00-4c20-8d81-6887401d5423.png", "pictureKey": "Default08bc9f57-4a00-4c20-8d81-6887401d5423.png", "personTypes": ["Defendant"], "uniqueId": "00a34b26-7626-4954-ba19-d37c5f081657", "searchNames": ["eric", "butler", "eric butler"], "phones": [{"phoneId": {"native": 69110749, "partner": null}, "number": "************", "rawNumber": "7062675070", "links": {}}], "emails": [], "addresses": [{"addressId": {"native": 51509581, "partner": null}, "line1": "1805 <PERSON><PERSON>", "line2": "", "city": "Hephzibah", "state": "GA", "postalCode": "30815", "fullAddress": "1805 <PERSON><PERSON>, Hephzibah, GA 30815", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36379380", "projects": "/contacts/36379380/projects", "phones": "/contacts/36379380/phones", "emailAddresses": "/contacts/36379380/emailAddresses", "addresses": "/contacts/36379380/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27566162, "partner": null}, "role": "Insurance: Settled By?", "orgContact": {"personId": {"native": 27566162, "partner": null}, "firstName": "<PERSON>", "lastName": "Butz", "isSingleName": false, "fullName": "<PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "fromCompany": "<PERSON>rm", "isTextingPermitted": false, "remarket": false, "personTypes": ["Attorney", "Firm"], "isMinor": false, "uniqueId": "508bdace-81f3-4842-8944-233dec30943d", "searchNames": ["ryan", "butz", "ryan butz"], "phones": [{"phoneId": {"native": 58457310, "partner": null}, "number": "************", "rawNumber": "4788450689", "label": "Work", "links": {}}, {"phoneId": {"native": 58457311, "partner": null}, "number": "************", "rawNumber": "4787870417", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 13563594, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 45341227, "partner": null}, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "postalCode": "31405", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27566162", "projects": "/contacts/27566162/projects", "phones": "/contacts/27566162/phones", "emailAddresses": "/contacts/27566162/emailAddresses", "addresses": "/contacts/27566162/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27729313, "partner": null}, "role": "Insurance: Insurer", "orgContact": {"personId": {"native": 27729313, "partner": null}, "firstName": "Omni Indemnity Company", "lastName": "", "isSingleName": true, "fullName": "Omni Indemnity Company", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Defaulted2ee915-b8f2-4d91-b572-94640492f349.png", "pictureKey": "Defaulted2ee915-b8f2-4d91-b572-94640492f349.png", "notes": "AKA Full: Good to go Auto Insurance \n5.5.23 address changed  MS", "fromCompany": "Omni Indemnity Company", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "Good 2 go Auto Insurance", "personTypes": ["InsuranceCompany"], "isMinor": false, "uniqueId": "abb749c4-3df4-417a-aa5e-b2b200145780", "searchNames": ["omni", "indemnity", "company", "omni indemnity company", "good 2 go auto insurance"], "phones": [{"phoneId": {"native": 57564703, "partner": null}, "number": "8007276664", "rawNumber": "8007276664", "label": "Main", "links": {}}, {"phoneId": {"native": 57564704, "partner": null}, "number": "8006801904", "rawNumber": "8006801904", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 13351039, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 44846470, "partner": null}, "line1": "P.O. Box 1930", "city": "Blue Bell", "state": "PA", "postalCode": "19422", "label": "Work", "fullAddress": "<PERSON>.<PERSON><PERSON> 1930, <PERSON> Bell, PA 19422", "links": {}}], "hashtags": [], "links": {"self": "/contacts/27729313", "projects": "/contacts/27729313/projects", "phones": "/contacts/27729313/phones", "emailAddresses": "/contacts/27729313/emailAddresses", "addresses": "/contacts/27729313/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 35656649, "partner": null}, "role": "Insurance: Adjuster", "orgContact": {"personId": {"native": 35656649, "partner": null}, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "isSingleName": false, "fullName": "<PERSON>", "pictureUrl": "/images/Default1431c6b4-7587-4669-b75d-b4b8ce8401ac.png", "pictureKey": "Default1431c6b4-7587-4669-b75d-b4b8ce8401ac.png", "fromCompany": "Omni/Good2GO", "personTypes": ["Adjuster"], "uniqueId": "0cd69d81-59d1-476e-b3f7-6b114ea61777", "searchNames": ["amy", "walski", "amy walski"], "phones": [{"phoneId": {"native": 68361465, "partner": null}, "number": "************ ext 8088", "rawNumber": "80072766648088", "links": {}}, {"phoneId": {"native": 68361466, "partner": null}, "number": "************", "rawNumber": "8006801904", "label": "Fax", "links": {}}], "emails": [], "addresses": [{"addressId": {"native": 51059685, "partner": null}, "line1": "P.O. Box 1930", "line2": "", "city": "Blue Bell", "state": "PA", "postalCode": "19422", "fullAddress": "<PERSON>.<PERSON><PERSON> 1930, <PERSON> Bell, PA 19422", "links": {}}], "hashtags": [], "links": {"self": "/contacts/35656649", "projects": "/contacts/35656649/projects", "phones": "/contacts/35656649/phones", "emailAddresses": "/contacts/35656649/emailAddresses", "addresses": "/contacts/35656649/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36376364, "partner": null}, "role": "Insurance: Insured", "orgContact": {"personId": {"native": 36376364, "partner": null}, "firstName": "<PERSON><PERSON>", "middleName": "J", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON><PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "pictureKey": "Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "ssn": "***********", "birthDate": "1965-07-09T00:00:00Z", "gender": "F", "maritalStatus": "M", "driversLicenseNumber": "*********", "personTypes": ["Client"], "uniqueId": "5b5909f4-6d0d-43fb-9189-caceb79d71e0", "searchNames": ["sharmaine", "j", "harris", "shar<PERSON><PERSON> j harris", "sharmaine harris"], "phones": [{"phoneId": {"native": 69107301, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "links": {}}, {"phoneId": {"native": 69107302, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Home", "links": {}}], "emails": [{"emailId": {"native": 17062024, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51507400, "partner": null}, "line1": "2409 Luxembourg Dr", "city": "Augusta", "state": "GA", "postalCode": "30906-4036", "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36376364", "projects": "/contacts/36376364/projects", "phones": "/contacts/36376364/phones", "emailAddresses": "/contacts/36376364/emailAddresses", "addresses": "/contacts/36376364/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36376364, "partner": null}, "role": "Insurance: Driver", "orgContact": {"personId": {"native": 36376364, "partner": null}, "firstName": "<PERSON><PERSON>", "middleName": "J", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON><PERSON>", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "pictureKey": "Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "ssn": "***********", "birthDate": "1965-07-09T00:00:00Z", "gender": "F", "maritalStatus": "M", "driversLicenseNumber": "*********", "personTypes": ["Client"], "uniqueId": "5b5909f4-6d0d-43fb-9189-caceb79d71e0", "searchNames": ["sharmaine", "j", "harris", "shar<PERSON><PERSON> j harris", "sharmaine harris"], "phones": [{"phoneId": {"native": 69107301, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "links": {}}, {"phoneId": {"native": 69107302, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Home", "links": {}}], "emails": [{"emailId": {"native": 17062024, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51507400, "partner": null}, "line1": "2409 Luxembourg Dr", "city": "Augusta", "state": "GA", "postalCode": "30906-4036", "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36376364", "projects": "/contacts/36376364/projects", "phones": "/contacts/36376364/phones", "emailAddresses": "/contacts/36376364/emailAddresses", "addresses": "/contacts/36376364/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36108232, "partner": null}, "role": "Payments: Payor", "orgContact": {"personId": {"native": 36108232, "partner": null}, "firstName": "Augusta-Richmond County Board of Commissioners", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "Augusta-Richmond County Board of Commissioners", "pictureUrl": "/images/Defaulte217896a-7fda-46f1-8da6-c45fc82ec77a.png", "pictureKey": "Defaulte217896a-7fda-46f1-8da6-c45fc82ec77a.png", "fromCompany": "", "personTypes": [], "uniqueId": "eea3135d-d91e-4087-92bc-24a3b5fcfe61", "searchNames": ["augusta-richmond", "county", "board", "of", "commissioners", "augusta-richmond county board of commissioners"], "phones": [], "emails": [], "addresses": [{"addressId": {"native": 51342380, "partner": null}, "line1": "535 Telfair Street", "line2": "", "city": "Augusta", "state": "GA", "postalCode": "30901", "fullAddress": "535 Telfair Street, Augusta, GA 30901", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/36108232", "projects": "/contacts/36108232/projects", "phones": "/contacts/36108232/phones", "emailAddresses": "/contacts/36108232/emailAddresses", "addresses": "/contacts/36108232/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 28417269, "partner": null}, "role": "Disbursals: Disbursal Check Payee", "orgContact": {"personId": {"native": 28417269, "partner": null}, "firstName": "Michael <PERSON>, LLC", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "Michael <PERSON>, LLC", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "notes": "AKA Full: MGH", "fromCompany": "", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "MGH", "personTypes": ["Firm"], "salutation": "", "barNumber": "386421", "isMinor": false, "uniqueId": "2c790389-6450-4568-8e06-93c21d0804a8", "searchNames": ["<PERSON><PERSON><PERSON>", "g.", "hostilo,", "llc", "mi<PERSON><PERSON> g. <PERSON>, llc", "mgh"], "phones": [{"phoneId": {"native": 58412305, "partner": null}, "number": "9122343142", "rawNumber": "9122343142", "label": "Main", "links": {}}, {"phoneId": {"native": 58412306, "partner": null}, "number": "9122343972", "rawNumber": "9122343972", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 13543549, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 45317063, "partner": null}, "line1": "33 Park of Commerce Blvd", "city": "Savannah", "state": "GA", "postalCode": "31405", "label": "Work", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/28417269", "projects": "/contacts/28417269/projects", "phones": "/contacts/28417269/phones", "emailAddresses": "/contacts/28417269/emailAddresses", "addresses": "/contacts/28417269/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 28417269, "partner": null}, "role": "Trust Check Request: Payable To", "orgContact": {"personId": {"native": 28417269, "partner": null}, "firstName": "Michael <PERSON>, LLC", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "Michael <PERSON>, LLC", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "notes": "AKA Full: MGH", "fromCompany": "", "isTextingPermitted": false, "remarket": false, "abbreviatedName": "MGH", "personTypes": ["Firm"], "salutation": "", "barNumber": "386421", "isMinor": false, "uniqueId": "2c790389-6450-4568-8e06-93c21d0804a8", "searchNames": ["<PERSON><PERSON><PERSON>", "g.", "hostilo,", "llc", "mi<PERSON><PERSON> g. <PERSON>, llc", "mgh"], "phones": [{"phoneId": {"native": 58412305, "partner": null}, "number": "9122343142", "rawNumber": "9122343142", "label": "Main", "links": {}}, {"phoneId": {"native": 58412306, "partner": null}, "number": "9122343972", "rawNumber": "9122343972", "label": "Fax", "links": {}}], "emails": [{"emailId": {"native": 13543549, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 45317063, "partner": null}, "line1": "33 Park of Commerce Blvd", "city": "Savannah", "state": "GA", "postalCode": "31405", "label": "Work", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/28417269", "projects": "/contacts/28417269/projects", "phones": "/contacts/28417269/phones", "emailAddresses": "/contacts/28417269/emailAddresses", "addresses": "/contacts/28417269/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36376362, "partner": null}, "role": "Disbursals: Disbursal Check Payee", "orgContact": {"personId": {"native": 36376362, "partner": null}, "firstName": "<PERSON>'kina", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>", "primaryEmail": "harri<PERSON><EMAIL>", "pictureUrl": "/images/Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "pictureKey": "Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "ssn": "***********", "birthDate": "2003-05-21T00:00:00Z", "gender": "F", "maritalStatus": "S", "driversLicenseNumber": "*********", "personTypes": ["Client"], "uniqueId": "62002a74-20a3-47ab-81b5-34bbc22058ec", "searchNames": ["a'kina", "harris", "a'kina harris"], "phones": [{"phoneId": {"native": 69107297, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "links": {}}, {"phoneId": {"native": 69107298, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Home", "links": {}}, {"phoneId": {"native": 69107299, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Work", "links": {}}], "emails": [{"emailId": {"native": 17062075, "partner": null}, "address": "harri<PERSON><EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51507399, "partner": null}, "line1": "2409 Luxembourg Dr", "city": "Augusta", "state": "GA", "postalCode": "30906-4036", "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36376362", "projects": "/contacts/36376362/projects", "phones": "/contacts/36376362/phones", "emailAddresses": "/contacts/36376362/emailAddresses", "addresses": "/contacts/36376362/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 36376362, "partner": null}, "role": "Trust Check Request: Payable To", "orgContact": {"personId": {"native": 36376362, "partner": null}, "firstName": "<PERSON>'kina", "lastName": "<PERSON>", "isSingleName": false, "fullName": "<PERSON><PERSON><PERSON><PERSON>", "primaryEmail": "harri<PERSON><EMAIL>", "pictureUrl": "/images/Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "pictureKey": "Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "ssn": "***********", "birthDate": "2003-05-21T00:00:00Z", "gender": "F", "maritalStatus": "S", "driversLicenseNumber": "*********", "personTypes": ["Client"], "uniqueId": "62002a74-20a3-47ab-81b5-34bbc22058ec", "searchNames": ["a'kina", "harris", "a'kina harris"], "phones": [{"phoneId": {"native": 69107297, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "links": {}}, {"phoneId": {"native": 69107298, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Home", "links": {}}, {"phoneId": {"native": 69107299, "partner": null}, "number": "(*************", "rawNumber": "**********", "label": "Work", "links": {}}], "emails": [{"emailId": {"native": 17062075, "partner": null}, "address": "harri<PERSON><EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 51507399, "partner": null}, "line1": "2409 Luxembourg Dr", "city": "Augusta", "state": "GA", "postalCode": "30906-4036", "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036", "links": {}}], "hashtags": [], "links": {"self": "/contacts/36376362", "projects": "/contacts/36376362/projects", "phones": "/contacts/36376362/phones", "emailAddresses": "/contacts/36376362/emailAddresses", "addresses": "/contacts/36376362/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 27740160, "partner": null}, "role": "Trust Check Request: Payable To", "orgContact": {"personId": {"native": 27740160, "partner": null}, "firstName": "<PERSON><PERSON><PERSON> Chiropractic", "middleName": "", "lastName": "", "isSingleName": true, "fullName": "<PERSON><PERSON><PERSON> Chiropractic", "primaryEmail": "<EMAIL>", "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "notes": "Reductions: 50% <NAME_EMAIL> (Check balance prior to sending request)\nTID:  58-2362299", "fromCompany": "", "isTextingPermitted": false, "remarket": false, "personTypes": ["MedicalProvider"], "isMinor": false, "uniqueId": "1507c144-0e50-481a-826d-889e52eb652f", "searchNames": ["pidcock", "chiropractic", "pidcock chiropractic"], "phones": [{"phoneId": {"native": 58451775, "partner": null}, "number": "**********", "rawNumber": "**********", "label": "Fax", "links": {}}, {"phoneId": {"native": 58451774, "partner": null}, "number": "************", "rawNumber": "**********", "label": "Main", "links": {}}], "emails": [{"emailId": {"native": 13562147, "partner": null}, "address": "<EMAIL>", "links": {}}], "addresses": [{"addressId": {"native": 45338858, "partner": null}, "line1": "3028 Peach Orchard Rd", "city": "Augusta", "state": "GA", "postalCode": "30906", "label": "Work", "fullAddress": "3028 Peach Orchard Rd, Augusta, GA 30906", "links": {}}], "prefix": "", "hashtags": [], "suffix": "", "jobTitle": "", "department": "", "links": {"self": "/contacts/27740160", "projects": "/contacts/27740160/projects", "phones": "/contacts/27740160/phones", "emailAddresses": "/contacts/27740160/emailAddresses", "addresses": "/contacts/27740160/addresses"}}, "links": {}}, {"projectId": {"native": ********, "partner": null}, "orgContactId": {"native": 33509652, "partner": null}, "role": "Trust Check Request: Payable To", "orgContact": {"personId": {"native": 33509652, "partner": null}, "firstName": "Optum", "isSingleName": false, "fullName": "Optum", "pictureUrl": "/images/Default5c545382-5de8-4113-b203-75e4522abc41.png", "pictureKey": "Default5c545382-5de8-4113-b203-75e4522abc41.png", "abbreviatedName": "PAYMENT", "personTypes": [], "uniqueId": "a80ae7cb-8971-47d9-aa04-0969556a64f5", "searchNames": ["optum", "payment"], "phones": [], "emails": [], "addresses": [{"addressId": {"native": 49712900, "partner": null}, "line1": "PO Box 182643", "line2": "", "city": "Columbus", "state": "OH", "postalCode": "43218", "fullAddress": "PO Box 182643, Columbus, OH 43218", "links": {}}], "hashtags": [], "links": {"self": "/contacts/33509652", "projects": "/contacts/33509652/projects", "phones": "/contacts/33509652/phones", "emailAddresses": "/contacts/33509652/emailAddresses", "addresses": "/contacts/33509652/addresses"}}, "links": {}}], "team": [{"userId": {"native": 1, "partner": null}, "username": "filevine", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "System", "email": "<EMAIL>", "fullname": "Filevine System", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/SystemUser.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 29358, "partner": null}, "username": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/9af834f3-010f-44c2-9535-14929b23b3e5.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 193569, "partner": null}, "name": "Finance Manager", "links": {}}, {"orgRoleId": {"native": 193570, "partner": null}, "name": "Senior Finance Manager", "links": {}}], "links": {}}, {"userId": {"native": 30330, "partner": null}, "username": "filevine407", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Integration", "email": "<EMAIL>", "fullname": "Filevine Integration", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/2c5524b7-5421-4cd3-92d9-e74e1daa39dc.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 35071, "partner": null}, "username": "rex<PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/e8c97a8d-877e-4e40-9b9f-a7c6b0c84e38.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 35974, "partner": null}, "username": "s<PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/a88d32ef-62c4-4455-9863-54beb36cfbfe.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40190, "partner": null}, "username": "awelch", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/60b9f5c3-2075-4d1a-9e64-f6d6ed650074.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40196, "partner": null}, "username": "ballday", "firstName": "<PERSON><PERSON> ", "lastName": "Allday ", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/8e64222e-8cb9-40bb-9073-0d78dbf1d882.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40201, "partner": null}, "username": "bnicotri", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/8b4a9788-e12f-47d4-8146-d4f751569387.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 193549, "partner": null}, "name": "Litigation Admin One", "links": {}}, {"orgRoleId": {"native": 201508, "partner": null}, "name": "SC Probate Paralegal", "links": {}}], "links": {}}, {"userId": {"native": 40243, "partner": null}, "username": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON> ", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/ec5862f1-e2c2-4987-b801-bff05229864a.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40245, "partner": null}, "username": "egregory", "firstName": "<PERSON> ", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default77288b17-954e-4c44-bb24-0d11efecf193.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 192993, "partner": null}, "name": "Benefits Coordinator", "links": {}}], "links": {}}, {"userId": {"native": 40275, "partner": null}, "username": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON> ", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/42edd5b3-2012-426a-a05f-337013febe46.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40287, "partner": null}, "username": "karen43", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default8bc2480d-102b-439b-ab4f-ab8a0cd129d7.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 192455, "partner": null}, "name": "Bankruptcy/Probate Attorney", "links": {}}], "links": {}}, {"userId": {"native": 40295, "partner": null}, "username": "<PERSON><PERSON><PERSON>s", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default460b33c8-59aa-423d-9932-cf1cd92198e5.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 194866, "partner": null}, "name": "Property Damage Coordinator", "links": {}}], "links": {}}, {"userId": {"native": 40296, "partner": null}, "username": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/91f675f1-eb03-467d-b81f-168dfb3e0345.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 199813, "partner": null}, "name": "Director of Case Management", "links": {}}], "links": {}}, {"userId": {"native": 40299, "partner": null}, "username": "kbowers", "firstName": "<PERSON><PERSON>", "lastName": "Bowers", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/edfcc365-e6ed-46de-895a-71d8e202d54e.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 193870, "partner": null}, "name": "Medpay Coordinator", "links": {}}], "links": {}}, {"userId": {"native": 40302, "partner": null}, "username": "l<PERSON>nham", "firstName": "LaRon ", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default2d780f86-9cd6-41e4-9d14-28f48190adc1.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40306, "partner": null}, "username": "lbutler", "firstName": "<PERSON><PERSON> ", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/9d96aa0e-2b4a-44cb-a773-07e097663fd4.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40314, "partner": null}, "username": "lkaplan", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default374780e2-f89b-479c-bd95-bc9b956b81f4.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40317, "partner": null}, "username": "mary81", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default40fcd1fb-d6bc-4da0-ab21-fc81c9f7c135.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 193568, "partner": null}, "name": "Post Settlement Coordinator", "links": {}}, {"orgRoleId": {"native": 199762, "partner": null}, "name": "Client Care Specialist", "links": {}}], "links": {}}, {"userId": {"native": 40320, "partner": null}, "username": "melsanso", "firstName": "<PERSON>", "lastName": "San<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default769849cd-2c7f-4171-a32e-569793cd609a.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 199855, "partner": null}, "name": "Senior Claims Specialist", "links": {}}], "links": {}}, {"userId": {"native": 40325, "partner": null}, "username": "michael136", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Defaultec2d92e6-c025-4f5a-aa86-a7c0a812ca1d.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40328, "partner": null}, "username": "mwilliams2", "firstName": "<PERSON>", "lastName": "<PERSON> ", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default0412bd7b-6521-4964-8a4e-e9fc45c8b272.png", "teamRoles": [{"roleId": {"native": 11, "partner": null}, "name": "Claims Manager", "links": {}}, {"roleId": {"native": 22, "partner": null}, "name": "Intake Specialist", "links": {}}], "teamOrgRoles": [{"orgRoleId": {"native": 118697, "partner": null}, "name": "Claims Manager", "links": {}}, {"orgRoleId": {"native": 118702, "partner": null}, "name": "Intake Specialist", "links": {}}], "links": {}}, {"userId": {"native": 40343, "partner": null}, "username": "rbutz", "firstName": "<PERSON>", "lastName": "Butz", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Defaulte43dc522-e2ae-4e99-9a2f-7350d6dd0591.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 193052, "partner": null}, "name": "Primary Attorney", "links": {}}], "links": {}}, {"userId": {"native": 40344, "partner": null}, "username": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default639e6dd6-9411-4eb5-9930-4c586a8e5475.png", "teamRoles": [{"roleId": {"native": 5, "partner": null}, "name": "Administrative Assistant", "links": {}}], "teamOrgRoles": [{"orgRoleId": {"native": 118692, "partner": null}, "name": "Administrative Assistant", "links": {}}], "links": {}}, {"userId": {"native": 40345, "partner": null}, "username": "rebecca<PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Defaultd7e8b5ef-d878-4ce1-b0de-e166bdce89a6.png", "teamRoles": [{"roleId": {"native": 20, "partner": null}, "name": "Clerical", "links": {}}], "teamOrgRoles": [{"orgRoleId": {"native": 118699, "partner": null}, "name": "Clerical", "links": {}}, {"orgRoleId": {"native": 194526, "partner": null}, "name": "Legal Assistant (ACM)", "links": {}}], "links": {}}, {"userId": {"native": 40353, "partner": null}, "username": "slord", "firstName": "<PERSON>", "lastName": "Lord ", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/81d7e643-b63e-48e5-8288-f21133f33b17.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40365, "partner": null}, "username": "tsyms", "firstName": "<PERSON>", "lastName": "Syms", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/b73f6e1b-b085-4900-8f00-5b39470ba442.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 193550, "partner": null}, "name": "Litigation Admin Two", "links": {}}], "links": {}}, {"userId": {"native": 40374, "partner": null}, "username": "tlee", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/de208608-f8b8-48be-a5fb-86f743d47a9b.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40376, "partner": null}, "username": "tgay", "firstName": "Troy", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default6b904d79-af93-4d22-8067-6c8d91368a0b.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40379, "partner": null}, "username": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/9f2e953a-3b6b-45a0-9d2c-0347b780835c.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 40384, "partner": null}, "username": "m<PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default20740721-47e7-4468-ae00-58ddc3efb675.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 41764, "partner": null}, "username": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON><PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default82911a86-4a80-4665-b04d-bdd4029403f8.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 42490, "partner": null}, "username": "p<PERSON><PERSON><PERSON><PERSON>", "firstName": "PRHostilo", "lastName": "Integration", "email": "<EMAIL>", "fullname": "PRHostilo Integration", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Defaulta0a7d4bc-a0c6-4d4c-b7d8-b7b5a7dfe42b.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 43906, "partner": null}, "username": "bhelms", "firstName": "<PERSON>y", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/2c84883c-1936-4b63-bc00-36cff13c519e.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 43908, "partner": null}, "username": "bdavis1", "firstName": "Brittany", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default7cee3d44-6119-4c37-93ef-ccc845de6d1c.png", "teamRoles": [{"roleId": {"native": 18, "partner": null}, "name": "Accounting", "links": {}}, {"roleId": {"native": 18, "partner": null}, "name": "Accounting", "links": {}}, {"roleId": {"native": 16, "partner": null}, "name": "File Clerk", "links": {}}], "teamOrgRoles": [{"orgRoleId": {"native": 118691, "partner": null}, "name": "Accounting", "links": {}}, {"orgRoleId": {"native": 118691, "partner": null}, "name": "Accounting", "links": {}}, {"orgRoleId": {"native": 118701, "partner": null}, "name": "File Clerk", "links": {}}], "links": {}}, {"userId": {"native": 43910, "partner": null}, "username": "g<PERSON>on", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/5d094596-2459-4e0b-833a-459515dd0a1e.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 43918, "partner": null}, "username": "<PERSON><PERSON><PERSON>", "firstName": "Jlovett", "email": "<EMAIL>", "fullname": "Jlovett", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default5cc20e34-42dd-4b59-b35b-b27cc72d8999.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 43930, "partner": null}, "username": "ksemco", "firstName": "<PERSON><PERSON>", "lastName": "Semco", "email": "<EMAIL>", "fullname": "<PERSON><PERSON> Semco", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/fb80e581-97c1-4245-ba1a-5f10096582ed.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 194712, "partner": null}, "name": "Case Quality Supervisor", "links": {}}], "links": {}}, {"userId": {"native": 43954, "partner": null}, "username": "tmcgahee", "firstName": "Tmcgahee", "email": "<EMAIL>", "fullname": "Tmcgahee", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default76f92312-c1a8-45d9-b277-96190a931cab.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 44533, "partner": null}, "username": "swmiller", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON> <PERSON>", "email": "<PERSON><PERSON>@hostilolaw.com", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/44a2ff6f-63c3-4b5c-b001-2dd14d960686.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 45237, "partner": null}, "username": "cpsharp", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Defaultc7934701-9b20-42a5-abc9-a1252bb13f32.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 199866, "partner": null}, "name": "Senior Litigation Paralegal", "links": {}}], "links": {}}, {"userId": {"native": 46356, "partner": null}, "username": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "d<PERSON><PERSON><PERSON>@hostilolaw.com", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/a8d6bb50-7206-4945-b412-4f7316265abb.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 51212, "partner": null}, "username": "follower", "firstName": "Follower", "lastName": "", "email": "<EMAIL>", "fullname": "Follower", "isPrimary": true, "isAdmin": true, "level": "Follower", "isFirstPrimary": true, "isOnlyPrimary": true, "pictureUrl": "/images/d89614e5-fed9-4c21-831c-1f44fef02b85.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 194885, "partner": null}, "name": "Primary", "links": {}}], "links": {}}, {"userId": {"native": 51228, "partner": null}, "username": "fvcompliance", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Compliance", "email": "<EMAIL>", "fullname": "Filevine Compliance", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/ffa84468-67b6-4af0-8a5d-e9e32fbdd513.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 192965, "partner": null}, "name": "CQC - Assessor", "links": {}}], "links": {}}, {"userId": {"native": 53817, "partner": null}, "username": "aknight", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default93cf32a6-5389-46ce-a5fa-8d7f70d50bc1.png", "teamRoles": [], "teamOrgRoles": [{"orgRoleId": {"native": 201282, "partner": null}, "name": "Client Care Analyst", "links": {}}], "links": {}}, {"userId": {"native": 54264, "partner": null}, "username": "eparnell", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/f231a47a-b891-4f8e-89de-f833909c1253.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 54267, "partner": null}, "username": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default01efadb6-482f-494b-9dba-3785507b286a.png", "teamRoles": [{"roleId": {"native": 25, "partner": null}, "name": "Case Manager", "links": {}}], "teamOrgRoles": [{"orgRoleId": {"native": 118696, "partner": null}, "name": "Case Manager", "links": {}}], "links": {}}, {"userId": {"native": 54635, "partner": null}, "username": "leaddocket135", "firstName": "LeadDocket", "lastName": "Integration", "email": "<EMAIL>", "fullname": "LeadDocket Integration", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Defaultbccb7467-6642-4aaf-9254-2550c6cc921d.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 55605, "partner": null}, "username": "jmason1", "firstName": "Jan<PERSON><PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "fullname": "<PERSON><PERSON><PERSON>", "isPrimary": false, "isAdmin": false, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Defaulte20b63d8-9107-499e-b66a-339042bc9af8.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}, {"userId": {"native": 56039, "partner": null}, "username": "tbates", "firstName": "Tbates", "email": "<EMAIL>", "fullname": "Tbates", "isPrimary": false, "isAdmin": true, "level": "Collaborator", "isFirstPrimary": false, "isOnlyPrimary": false, "pictureUrl": "/images/Default1fefc5ef-17a0-4fed-a829-026637702da8.png", "teamRoles": [], "teamOrgRoles": [], "links": {}}], "insurance": [{"itemId": {"native": "f5d58571-d2ea-4d6f-8726-39b5fc3286a7", "partner": null}, "dataObject": {"insurer": {"id": 27729313, "orgID": 5676, "firstName": "Omni Indemnity Company", "middleName": null, "lastName": "", "fullname": "Omni Indemnity Company", "isSingleName": true, "fromCompany": "Omni Indemnity Company", "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "Omni Indemnity Company", "nickname": "Good 2 go Auto Insurance", "initials": "O", "initialsFirstLast": "O", "personTypes": [{"id": 6803, "name": "Insurance Company", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "*************-41ca-85e9-bdb0fc4ffac0"}], "tags": [], "isArchived": false, "createdDate": "2021-01-08T15:22:17.08Z", "modifiedDate": "2023-05-05T12:23:19.423Z", "phones": [{"id": 57564703, "number": "8007276664", "rawNumber": "8007276664", "label": "Main", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2875, "name": "Main", "icon": "call", "isDeprecated": false, "globalSourceGuid": "2acd8e38-0470-4986-af57-3dbebb19e4d8"}, "notes": null}, {"id": 57564704, "number": "8006801904", "rawNumber": "8006801904", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 13351039, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 44846470, "line1": "P.O. Box 1930", "line2": null, "city": "Blue Bell", "state": "PA", "zip": "19422", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business", "fullAddress": "<PERSON>.<PERSON><PERSON> 1930, <PERSON> Bell, PA 19422"}], "pictureUrl": "/images/Defaulted2ee915-b8f2-4d91-b572-94640492f349.png", "pictureKey": "Defaulted2ee915-b8f2-4d91-b572-94640492f349.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "abb749c4-3df4-417a-aa5e-b2b200145780", "abbreviatedName": "Good 2 go Auto Insurance", "ssn": null, "notes": "AKA Full: Good to go Auto Insurance \n5.5.23 address changed  MS", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": true, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": false, "searchNames": ["omni", "indemnity", "company", "omni indemnity company", "good 2 go auto insurance"]}, "adjuster": {"id": 35656649, "orgID": 5676, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON><PERSON>", "fullname": "<PERSON>", "isSingleName": false, "fromCompany": "Omni/Good2GO", "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "<PERSON>", "nickname": null, "initials": "AW", "initialsFirstLast": "AW", "personTypes": [{"id": 6793, "name": "Adjuster", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "a628a714-9847-44b8-a0ab-0e38354eb883"}], "tags": [], "isArchived": false, "createdDate": "2022-09-28T15:23:07.72Z", "modifiedDate": "2022-12-06T19:19:23.947Z", "phones": [{"id": 68361465, "number": "************ ext 8088", "rawNumber": "80072766648088", "label": null, "phoneLabel": null, "notes": "ext 8088"}, {"id": 68361466, "number": "************", "rawNumber": "8006801904", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [], "addresses": [{"id": 51059685, "line1": "P.O. Box 1930", "line2": "", "city": "Blue Bell", "state": "PA", "zip": "19422", "label": null, "addressLabel": null, "notes": null, "fullAddress": "<PERSON>.<PERSON><PERSON> 1930, <PERSON> Bell, PA 19422"}], "pictureUrl": "/images/Default1431c6b4-7587-4669-b75d-b4b8ce8401ac.png", "pictureKey": "Default1431c6b4-7587-4669-b75d-b4b8ce8401ac.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "0cd69d81-59d1-476e-b3f7-6b114ea61777", "abbreviatedName": null, "ssn": null, "notes": null, "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": null, "isTypeClient": null, "isTypeAdjuster": true, "isTypeDefendant": null, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": null, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["amy", "walski", "amy walski"]}, "policynumber": "8706135", "claimnumber": "2023-52588", "insurancetype": "UM/UIM", "insured": {"id": 36376364, "orgID": 5676, "firstName": "<PERSON><PERSON>", "middleName": "J", "lastName": "<PERSON>", "fullname": "<PERSON><PERSON>", "isSingleName": false, "fromCompany": null, "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "<PERSON><PERSON>", "nickname": null, "initials": "SJH", "initialsFirstLast": "SH", "personTypes": [{"id": 6792, "name": "Client", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": true, "globalSourceGuid": "0f385512-2ebd-460e-927e-78df024c2da8"}], "tags": [], "isArchived": false, "createdDate": "2023-02-17T21:52:51.447Z", "modifiedDate": "2023-02-20T15:39:47.75Z", "phones": [{"id": 69107301, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "phoneLabel": {"isSmsable": true, "isFaxable": false, "id": 2871, "name": "Personal Mobile", "icon": "call", "isDeprecated": false, "globalSourceGuid": "9e240ef0-b821-4d9c-a5ab-f0c2ac30b604"}, "notes": null}, {"id": 69107302, "number": "(*************", "rawNumber": "**********", "label": "Home", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2870, "name": "Home", "icon": "call", "isDeprecated": false, "globalSourceGuid": "6300aacf-7780-4d45-81fd-934b9280e975"}, "notes": null}], "emails": [{"id": 17062024, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 51507400, "line1": "2409 Luxembourg Dr", "line2": null, "city": "Augusta", "state": "GA", "zip": "30906-4036", "label": null, "addressLabel": null, "notes": null, "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036"}], "pictureUrl": "/images/Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "pictureKey": "Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "birthDate": "1965-07-09T00:00:00Z", "deathDate": null, "isDeceased": null, "uniqueID": "5b5909f4-6d0d-43fb-9189-caceb79d71e0", "abbreviatedName": null, "ssn": "***********", "notes": null, "specialty": null, "gender": "F", "language": null, "maritalStatus": "M", "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": "*********", "isTypeClient": true, "isTypeAdjuster": null, "isTypeDefendant": null, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": null, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["sharmaine", "j", "harris", "shar<PERSON><PERSON> j harris", "sharmaine harris"]}, "insurancetemplates": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "052 - LOR UM CARRIER Omni Indemnity Company 2023-03-14 0840.docx", "uploadDate": "2023-03-14T12:41:17.683Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "driver": {"id": 36376364, "orgID": 5676, "firstName": "<PERSON><PERSON>", "middleName": "J", "lastName": "<PERSON>", "fullname": "<PERSON><PERSON>", "isSingleName": false, "fromCompany": null, "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "<PERSON><PERSON>", "nickname": null, "initials": "SJH", "initialsFirstLast": "SH", "personTypes": [{"id": 6792, "name": "Client", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": true, "globalSourceGuid": "0f385512-2ebd-460e-927e-78df024c2da8"}], "tags": [], "isArchived": false, "createdDate": "2023-02-17T21:52:51.447Z", "modifiedDate": "2023-02-20T15:39:47.75Z", "phones": [{"id": 69107301, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "phoneLabel": {"isSmsable": true, "isFaxable": false, "id": 2871, "name": "Personal Mobile", "icon": "call", "isDeprecated": false, "globalSourceGuid": "9e240ef0-b821-4d9c-a5ab-f0c2ac30b604"}, "notes": null}, {"id": 69107302, "number": "(*************", "rawNumber": "**********", "label": "Home", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2870, "name": "Home", "icon": "call", "isDeprecated": false, "globalSourceGuid": "6300aacf-7780-4d45-81fd-934b9280e975"}, "notes": null}], "emails": [{"id": 17062024, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 51507400, "line1": "2409 Luxembourg Dr", "line2": null, "city": "Augusta", "state": "GA", "zip": "30906-4036", "label": null, "addressLabel": null, "notes": null, "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036"}], "pictureUrl": "/images/Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "pictureKey": "Default5c9d82ab-1561-4b2e-9e7b-220fdcb5ff71.png", "birthDate": "1965-07-09T00:00:00Z", "deathDate": null, "isDeceased": null, "uniqueID": "5b5909f4-6d0d-43fb-9189-caceb79d71e0", "abbreviatedName": null, "ssn": "***********", "notes": null, "specialty": null, "gender": "F", "language": null, "maritalStatus": "M", "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": "*********", "isTypeClient": true, "isTypeAdjuster": null, "isTypeDefendant": null, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": null, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["sharmaine", "j", "harris", "shar<PERSON><PERSON> j harris", "sharmaine harris"]}}, "links": {}}, {"itemId": {"native": "133acd68-2a4a-46ef-9faf-a30546510a05", "partner": null}, "dataObject": {"insurer": {"id": 27569065, "orgID": 5676, "firstName": "Richmond County Risk Management", "middleName": "", "lastName": "", "fullname": "Richmond County Risk Management", "isSingleName": true, "fromCompany": "", "orgMetaVersionID": 0, "jobTitle": "", "department": "", "prefix": "", "suffix": "", "fullnameExtended": "Richmond County Risk Management", "nickname": "ARC Risk Management", "initials": "R", "initialsFirstLast": "R", "personTypes": [{"id": 6800, "name": "Involved Party", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "21abb957-30a7-4694-87b5-7cf868e056f3"}], "tags": [], "isArchived": false, "createdDate": "2021-01-08T07:14:50.977Z", "modifiedDate": "2023-08-21T18:12:29.377Z", "phones": [{"id": 64119232, "number": "7068212301", "rawNumber": "7068212301", "label": null, "phoneLabel": null, "notes": null}, {"id": 64119233, "number": "7068212502", "rawNumber": "7068212502", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 17103258, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "Send LORs to her"}], "addresses": [{"id": 48314409, "line1": "535 Telfair Street", "line2": "Suite 920", "city": "Augusta", "state": "GA", "zip": "30901", "label": null, "addressLabel": null, "notes": null, "fullAddress": "535 Telfair Street, Suite 920, Augusta, GA 30901"}], "pictureUrl": "/images/Defaultcfc7a48f-afea-48c2-9ef5-cef47ff4bcd7.png", "pictureKey": "Defaultcfc7a48f-afea-48c2-9ef5-cef47ff4bcd7.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "64d859e3-f5b4-41ae-a57c-658a2df8d1f8", "abbreviatedName": "ARC Risk Management", "ssn": null, "notes": "8.6.21 contact added <PERSON><PERSON><PERSON> to <PERSON> who said fax LOR and AR to risk management. They will investigate or set up claim and contact us. MS", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": true, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": false, "searchNames": ["richmond", "county", "risk", "management", "richmond county risk management", "arc risk management"]}, "policynumber": "self insured", "claimnumber": "RM50138", "fileattachment": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "LOR- Needs to go to Risk Management.pdf", "uploadDate": "2023-04-10T15:14:09.603Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "insurancetype": "Liability", "insured": {"id": 36108232, "orgID": 5676, "firstName": "Augusta-Richmond County Board of Commissioners", "middleName": "", "lastName": "", "fullname": "Augusta-Richmond County Board of Commissioners", "isSingleName": true, "fromCompany": "", "orgMetaVersionID": 0, "jobTitle": "", "department": "", "prefix": "", "suffix": "", "fullnameExtended": "Augusta-Richmond County Board of Commissioners", "nickname": null, "initials": "A", "initialsFirstLast": "A", "personTypes": [], "tags": [], "isArchived": false, "createdDate": "2022-12-02T21:43:16.31Z", "modifiedDate": "2022-12-02T21:47:04.84Z", "phones": [], "emails": [], "addresses": [{"id": 51342380, "line1": "535 Telfair Street", "line2": "", "city": "Augusta", "state": "GA", "zip": "30901", "label": null, "addressLabel": null, "notes": null, "fullAddress": "535 Telfair Street, Augusta, GA 30901"}], "pictureUrl": "/images/Defaulte217896a-7fda-46f1-8da6-c45fc82ec77a.png", "pictureKey": "Defaulte217896a-7fda-46f1-8da6-c45fc82ec77a.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "eea3135d-d91e-4087-92bc-24a3b5fcfe61", "abbreviatedName": null, "ssn": null, "notes": null, "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": null, "isTypeClient": null, "isTypeAdjuster": null, "isTypeDefendant": null, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": null, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["augusta-richmond", "county", "board", "of", "commissioners", "augusta-richmond county board of commissioners"]}, "insurancetemplates": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "033 - <PERSON>ETTER TO INSURANCE. SETTLEMENT ACCEPTANCE 2023-07-10 1525.docx", "uploadDate": "2023-07-10T19:26:13.233Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}, {"id": *********, "orgID": 5676, "projectID": ********, "filename": "54 - LOR LIABILITY CARRIER 2023-03-14 0838.docx", "uploadDate": "2023-03-14T12:45:45.823Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "driver": {"id": 36379380, "orgID": 5676, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON>", "fullname": "<PERSON>", "isSingleName": false, "fromCompany": null, "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "<PERSON>", "nickname": null, "initials": "EB", "initialsFirstLast": "EB", "personTypes": [{"id": 6795, "name": "Defendant", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "9bce0c6f-e2e9-4658-ada8-9d48ed2a7f5a"}], "tags": [], "isArchived": false, "createdDate": "2023-03-03T18:58:58.18Z", "modifiedDate": "2023-03-03T18:58:58.26Z", "phones": [{"id": 69110749, "number": "************", "rawNumber": "7062675070", "label": null, "phoneLabel": null, "notes": null}], "emails": [], "addresses": [{"id": 51509581, "line1": "1805 <PERSON><PERSON>", "line2": "", "city": "Hephzibah", "state": "GA", "zip": "30815", "label": null, "addressLabel": null, "notes": null, "fullAddress": "1805 <PERSON><PERSON>, Hephzibah, GA 30815"}], "pictureUrl": "/images/Default08bc9f57-4a00-4c20-8d81-6887401d5423.png", "pictureKey": "Default08bc9f57-4a00-4c20-8d81-6887401d5423.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "00a34b26-7626-4954-ba19-d37c5f081657", "abbreviatedName": null, "ssn": null, "notes": null, "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": null, "isTypeClient": null, "isTypeAdjuster": null, "isTypeDefendant": true, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": null, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["eric", "butler", "eric butler"]}, "settled": true, "settledAmount": 14400.0, "settledBy": {"id": 27566162, "orgID": 5676, "firstName": "<PERSON>", "middleName": null, "lastName": "Butz", "fullname": "<PERSON>", "isSingleName": false, "fromCompany": "<PERSON>rm", "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "<PERSON>", "nickname": null, "initials": "RB", "initialsFirstLast": "RB", "personTypes": [{"id": 6794, "name": "Attorney", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "ef3dc776-4400-44b6-8a7e-8a0b10a80520"}, {"id": 6797, "name": "Firm", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "0df1ec4b-26b1-4eef-b380-16c57a34aa16"}], "tags": [], "isArchived": false, "createdDate": "2021-01-08T07:14:18.857Z", "modifiedDate": "2023-10-17T14:36:20.723Z", "phones": [{"id": 58457310, "number": "************", "rawNumber": "4788450689", "label": "Work", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2873, "name": "Work", "icon": "call", "isDeprecated": false, "globalSourceGuid": "780172ec-8ad4-4ecb-9e48-cd9e251ac42f"}, "notes": null}, {"id": 58457311, "number": "************", "rawNumber": "4787870417", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 13563594, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 45341227, "line1": "33 Park of Commerce Blvd", "line2": "", "city": "Savannah", "state": "GA", "zip": "31405", "label": null, "addressLabel": null, "notes": null, "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405"}], "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "508bdace-81f3-4842-8944-233dec30943d", "abbreviatedName": null, "ssn": null, "notes": null, "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": true, "isTypeFirm": true, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": false, "searchNames": ["ryan", "butz", "ryan butz"]}, "probate": false, "bankruptcy": false, "subrogationLien": true, "otherLiens": "<PERSON><PERSON><PERSON>, AUMC", "attorneyFee": 5040.0, "attorneyFeeFlexible": false, "amountToClient": 5200.0, "billsPaid": "Yes", "biggestPieceOfThePie": true, "releaseType": "General", "additionalReleaseDetails": ["No Further Details"], "wasThisCaseReferredInByA": false, "settlementNotes": "All bills\n$14,400.00\n$5,040.00 AF 35%\n$80.69 CE\n$2,808.50 <PERSON><PERSON><PERSON> reduced\n$663.20 AUMC reduced\n$429.00 AU Med\n$146.50 Subro\n=$5,232.11", "verifyAdjuster": false, "howWasCaseSettled": "Recorded Line", "settlementSummaryTemplate": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "Settlement Statement -(1) 2023-08-24 1423.xlsx", "uploadDate": "2023-08-30T15:53:36.487Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "dateSettled": "2023-07-10T00:00:00Z", "verifyContact": "Single", "policyTender": false, "newNegotiation": ["$10,755.00 IO", "$30,000.00 Counter", "$14,400.00 Top", "$14,400.00 Settled"], "isCaseInNegotiation": true}, "links": {}}, {"itemId": {"native": "4bc76e31-b99c-405d-bc5e-c5ed0426390a", "partner": null}, "dataObject": {"insurer": {"id": 27735903, "orgID": 5676, "firstName": "UMR", "middleName": null, "lastName": "", "fullname": "UMR", "isSingleName": true, "fromCompany": "UMR", "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "UMR", "nickname": "Subrogation", "initials": "U", "initialsFirstLast": "U", "personTypes": [], "tags": [], "isArchived": false, "createdDate": "2021-01-08T15:24:59.49Z", "modifiedDate": "2023-07-19T13:47:17.833Z", "phones": [{"id": 57574425, "number": "8882648721", "rawNumber": "8882648721", "label": "Main", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2875, "name": "Main", "icon": "call", "isDeprecated": false, "globalSourceGuid": "2acd8e38-0470-4986-af57-3dbebb19e4d8"}, "notes": null}, {"id": 57574426, "number": "8772913251", "rawNumber": "8772913251", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 16008710, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 44852481, "line1": "L-3994", "line2": null, "city": "Columbus", "state": "OH", "zip": "43260", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business", "fullAddress": "L-3994, Columbus, OH 43260"}], "pictureUrl": "/images/Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "pictureKey": "Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "fa29c248-285f-4665-beb6-75797bfc6d7a", "abbreviatedName": "Subrogation", "ssn": null, "notes": "AKA Full: LORs", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": false, "searchNames": ["umr", "subrogation"]}, "insurancetype": "Health", "insurancetemplates": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "133 SUBROLOR-HI UMR - #SUBROLOR 2023-02-21 1047.docx", "uploadDate": "2023-02-21T15:47:03.963Z", "templateID": *********, "templateName": "133 SUBROLOR-HI {{insurance.insurer.name}} - #SUBROLOR.docx", "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "memberID": "22658342", "groupNo": "76-410516"}, "links": {}}], "liens": [{"itemId": {"native": "def01060-3b13-45ff-99c3-0837ce13ccc8", "partner": null}, "dataObject": {"amountdue": 283, "lienholder": {"id": 27735903, "orgID": 5676, "firstName": "UMR", "middleName": null, "lastName": "", "fullname": "UMR", "isSingleName": true, "fromCompany": "UMR", "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "UMR", "nickname": "Subrogation", "initials": "U", "initialsFirstLast": "U", "personTypes": [], "tags": [], "isArchived": false, "createdDate": "2021-01-08T15:24:59.49Z", "modifiedDate": "2023-07-19T13:47:17.833Z", "phones": [{"id": 57574425, "number": "8882648721", "rawNumber": "8882648721", "label": "Main", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2875, "name": "Main", "icon": "call", "isDeprecated": false, "globalSourceGuid": "2acd8e38-0470-4986-af57-3dbebb19e4d8"}, "notes": null}, {"id": 57574426, "number": "8772913251", "rawNumber": "8772913251", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 16008710, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 44852481, "line1": "L-3994", "line2": null, "city": "Columbus", "state": "OH", "zip": "43260", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business", "fullAddress": "L-3994, Columbus, OH 43260"}], "pictureUrl": "/images/Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "pictureKey": "Default00fe61ea-7f4f-43c4-a7fe-70442927b81d.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "fa29c248-285f-4665-beb6-75797bfc6d7a", "abbreviatedName": "Subrogation", "ssn": null, "notes": "AKA Full: LORs", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": false, "searchNames": ["umr", "subrogation"]}, "payee": {"id": 33509652, "orgID": 5676, "firstName": "Optum", "middleName": null, "lastName": null, "fullname": "Optum", "isSingleName": false, "fromCompany": null, "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "Optum", "nickname": "PAYMENT", "initials": "O", "initialsFirstLast": "O", "personTypes": [], "tags": [], "isArchived": false, "createdDate": "2022-01-14T16:00:23.953Z", "modifiedDate": "2023-04-25T21:19:42.437Z", "phones": [], "emails": [], "addresses": [{"id": 49712900, "line1": "PO Box 182643", "line2": "", "city": "Columbus", "state": "OH", "zip": "43218", "label": null, "addressLabel": null, "notes": null, "fullAddress": "PO Box 182643, Columbus, OH 43218"}], "pictureUrl": "/images/Default5c545382-5de8-4113-b203-75e4522abc41.png", "pictureKey": "Default5c545382-5de8-4113-b203-75e4522abc41.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "a80ae7cb-8971-47d9-aa04-0969556a64f5", "abbreviatedName": "PAYMENT", "ssn": null, "notes": null, "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": null, "isTypeClient": null, "isTypeAdjuster": null, "isTypeDefendant": null, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": null, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["optum", "payment"]}, "letterofrepsent": true, "datesent": "2023-02-23T00:00:00Z", "noticereceived": true, "datereceived": "2023-03-28T00:00:00Z", "notes": "8.22.23 ks Rec'd final\n06/21/2023 - submitted for claims on portal \n02/23/2023 - FAXED LOR", "documents": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "Optum Final.pdf", "uploadDate": "2023-08-19T14:00:38.367Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}, {"id": *********, "orgID": 5676, "projectID": ********, "filename": "OPTUM - LIEN NOTICE.pdf", "uploadDate": "2023-04-19T13:45:34.72Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}, {"id": 340767327, "orgID": 5676, "projectID": ********, "filename": "OPTUM - FILE NOTICE.pdf", "uploadDate": "2023-03-28T22:12:50.197Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "checkmemo": "110160115 - FINAL", "amountClaimed": 283, "lienLetterTemplates": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "039 - LIENS Payment Letter Optum 2023-09-20 0916.docx", "uploadDate": "2023-09-20T13:17:01.427Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}, {"id": *********, "orgID": 5676, "projectID": ********, "filename": "135 SUBRO Settlement Notice Optum 2023-07-11 1559.docx", "uploadDate": "2023-07-11T19:59:48.377Z", "templateID": 176927578, "templateName": "135 SUBRO Settlement Notice {{liens.payee.name}}.docx", "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "lienType": "Subrogation", "sendToTrustCheckRequest": true}, "links": {}}], "disbursals": [{"itemId": {"native": "1d042974-9c30-4765-8afb-4b6766c9d59f", "partner": null}, "dataObject": {"typeofcheckrequest": "Client Proceeds", "amountdue": 6298.43, "checkmemo": "PI Settlement", "payee": {"id": 36376362, "orgID": 5676, "firstName": "<PERSON>'kina", "middleName": null, "lastName": "<PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "isSingleName": false, "fromCompany": null, "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "<PERSON><PERSON><PERSON><PERSON>", "nickname": null, "initials": "AH", "initialsFirstLast": "AH", "personTypes": [{"id": 6792, "name": "Client", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": true, "globalSourceGuid": "0f385512-2ebd-460e-927e-78df024c2da8"}], "tags": [], "isArchived": false, "createdDate": "2023-02-17T21:52:35.357Z", "modifiedDate": "2023-03-02T16:49:29.54Z", "phones": [{"id": 69107297, "number": "(*************", "rawNumber": "**********", "label": "Personal Mobile", "phoneLabel": {"isSmsable": true, "isFaxable": false, "id": 2871, "name": "Personal Mobile", "icon": "call", "isDeprecated": false, "globalSourceGuid": "9e240ef0-b821-4d9c-a5ab-f0c2ac30b604"}, "notes": null}, {"id": 69107298, "number": "(*************", "rawNumber": "**********", "label": "Home", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2870, "name": "Home", "icon": "call", "isDeprecated": false, "globalSourceGuid": "6300aacf-7780-4d45-81fd-934b9280e975"}, "notes": null}, {"id": 69107299, "number": "(*************", "rawNumber": "**********", "label": "Work", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2873, "name": "Work", "icon": "call", "isDeprecated": false, "globalSourceGuid": "780172ec-8ad4-4ecb-9e48-cd9e251ac42f"}, "notes": null}], "emails": [{"id": 17062075, "address": "harri<PERSON><EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 51507399, "line1": "2409 Luxembourg Dr", "line2": null, "city": "Augusta", "state": "GA", "zip": "30906-4036", "label": null, "addressLabel": null, "notes": null, "fullAddress": "2409 Luxembourg Dr, Augusta, GA 30906-4036"}], "pictureUrl": "/images/Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "pictureKey": "Defaulta8901f57-20d8-4ec3-9586-25bb44c70374.png", "birthDate": "2003-05-21T00:00:00Z", "deathDate": null, "isDeceased": null, "uniqueID": "62002a74-20a3-47ab-81b5-34bbc22058ec", "abbreviatedName": null, "ssn": "***********", "notes": null, "specialty": null, "gender": "F", "language": null, "maritalStatus": "S", "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": "*********", "isTypeClient": true, "isTypeAdjuster": null, "isTypeDefendant": null, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": null, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["a'kina", "harris", "a'kina harris"]}, "market": "AUG"}, "links": {}}, {"itemId": {"native": "1909becd-85f7-4ee0-a42b-b1b30a3867aa", "partner": null}, "dataObject": {"typeofcheckrequest": "Due to Firm (Postage)", "amountdue": 16.2, "checkmemo": "Postage", "payee": {"id": 28417269, "orgID": 5676, "firstName": "Michael <PERSON>, LLC", "middleName": "", "lastName": "", "fullname": "Michael <PERSON>, LLC", "isSingleName": true, "fromCompany": "", "orgMetaVersionID": 0, "jobTitle": "", "department": "", "prefix": "", "suffix": "", "fullnameExtended": "Michael <PERSON>, LLC", "nickname": "MGH", "initials": "M", "initialsFirstLast": "M", "personTypes": [], "tags": [], "isArchived": false, "createdDate": "2021-01-28T17:27:09.327Z", "modifiedDate": "2023-12-29T15:36:33.047Z", "phones": [{"id": 58412305, "number": "9122343142", "rawNumber": "9122343142", "label": "Main", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2875, "name": "Main", "icon": "call", "isDeprecated": false, "globalSourceGuid": "2acd8e38-0470-4986-af57-3dbebb19e4d8"}, "notes": null}, {"id": 58412306, "number": "9122343972", "rawNumber": "9122343972", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 13543549, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 45317063, "line1": "33 Park of Commerce Blvd", "line2": null, "city": "Savannah", "state": "GA", "zip": "31405", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405"}], "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "2c790389-6450-4568-8e06-93c21d0804a8", "abbreviatedName": "MGH", "ssn": null, "notes": "AKA Full: MGH", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": "", "barNumber": "386421", "fiduciary": null, "isMinor": false, "searchNames": ["<PERSON><PERSON><PERSON>", "g.", "hostilo,", "llc", "mi<PERSON><PERSON> g. <PERSON>, llc", "mgh"]}, "market": "AUG"}, "links": {}}, {"itemId": {"native": "ea8e815a-b60b-4c83-b202-23453dc13db8", "partner": null}, "dataObject": {"typeofcheckrequest": "Due to Firm (Expenses)", "amountdue": 106.37, "checkmemo": "Case Exp", "payee": {"id": 28417269, "orgID": 5676, "firstName": "Michael <PERSON>, LLC", "middleName": "", "lastName": "", "fullname": "Michael <PERSON>, LLC", "isSingleName": true, "fromCompany": "", "orgMetaVersionID": 0, "jobTitle": "", "department": "", "prefix": "", "suffix": "", "fullnameExtended": "Michael <PERSON>, LLC", "nickname": "MGH", "initials": "M", "initialsFirstLast": "M", "personTypes": [], "tags": [], "isArchived": false, "createdDate": "2021-01-28T17:27:09.327Z", "modifiedDate": "2023-12-29T15:36:33.047Z", "phones": [{"id": 58412305, "number": "9122343142", "rawNumber": "9122343142", "label": "Main", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2875, "name": "Main", "icon": "call", "isDeprecated": false, "globalSourceGuid": "2acd8e38-0470-4986-af57-3dbebb19e4d8"}, "notes": null}, {"id": 58412306, "number": "9122343972", "rawNumber": "9122343972", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 13543549, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 45317063, "line1": "33 Park of Commerce Blvd", "line2": null, "city": "Savannah", "state": "GA", "zip": "31405", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405"}], "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "2c790389-6450-4568-8e06-93c21d0804a8", "abbreviatedName": "MGH", "ssn": null, "notes": "AKA Full: MGH", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": "", "barNumber": "386421", "fiduciary": null, "isMinor": false, "searchNames": ["<PERSON><PERSON><PERSON>", "g.", "hostilo,", "llc", "mi<PERSON><PERSON> g. <PERSON>, llc", "mgh"]}, "market": "AUG"}, "links": {}}, {"itemId": {"native": "b1fa82bb-32a4-476c-9e00-1e6cd0b2403d", "partner": null}, "dataObject": {"typeofcheckrequest": "Due to <PERSON>rm (Attorney <PERSON>)", "amountdue": 2040, "checkmemo": "Atty <PERSON>", "payee": {"id": 28417269, "orgID": 5676, "firstName": "Michael <PERSON>, LLC", "middleName": "", "lastName": "", "fullname": "Michael <PERSON>, LLC", "isSingleName": true, "fromCompany": "", "orgMetaVersionID": 0, "jobTitle": "", "department": "", "prefix": "", "suffix": "", "fullnameExtended": "Michael <PERSON>, LLC", "nickname": "MGH", "initials": "M", "initialsFirstLast": "M", "personTypes": [], "tags": [], "isArchived": false, "createdDate": "2021-01-28T17:27:09.327Z", "modifiedDate": "2023-12-29T15:36:33.047Z", "phones": [{"id": 58412305, "number": "9122343142", "rawNumber": "9122343142", "label": "Main", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2875, "name": "Main", "icon": "call", "isDeprecated": false, "globalSourceGuid": "2acd8e38-0470-4986-af57-3dbebb19e4d8"}, "notes": null}, {"id": 58412306, "number": "9122343972", "rawNumber": "9122343972", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 13543549, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 45317063, "line1": "33 Park of Commerce Blvd", "line2": null, "city": "Savannah", "state": "GA", "zip": "31405", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business", "fullAddress": "33 Park of Commerce Blvd, Savannah, GA 31405"}], "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "2c790389-6450-4568-8e06-93c21d0804a8", "abbreviatedName": "MGH", "ssn": null, "notes": "AKA Full: MGH", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": false, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": "", "barNumber": "386421", "fiduciary": null, "isMinor": false, "searchNames": ["<PERSON><PERSON><PERSON>", "g.", "hostilo,", "llc", "mi<PERSON><PERSON> g. <PERSON>, llc", "mgh"]}, "market": "AUG"}, "links": {}}], "meds": [{"itemId": {"native": "ae52014e-b48d-4dad-bb7d-a498bf67706f", "partner": null}, "dataObject": {"payee": {"id": 27740160, "orgID": 5676, "firstName": "<PERSON><PERSON><PERSON> Chiropractic", "middleName": "", "lastName": "", "fullname": "<PERSON><PERSON><PERSON> Chiropractic", "isSingleName": true, "fromCompany": "", "orgMetaVersionID": 0, "jobTitle": "", "department": "", "prefix": "", "suffix": "", "fullnameExtended": "<PERSON><PERSON><PERSON> Chiropractic", "nickname": null, "initials": "P", "initialsFirstLast": "P", "personTypes": [{"id": 6799, "name": "Medical Provider", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "90980cd5-ee0a-4baf-84f9-0966e9b078dc"}], "tags": [], "isArchived": false, "createdDate": "2021-01-08T15:26:33.763Z", "modifiedDate": "2023-08-18T13:20:32.88Z", "phones": [{"id": 58451774, "number": "************", "rawNumber": "**********", "label": "Main", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2875, "name": "Main", "icon": "call", "isDeprecated": false, "globalSourceGuid": "2acd8e38-0470-4986-af57-3dbebb19e4d8"}, "notes": null}, {"id": 58451775, "number": "**********", "rawNumber": "**********", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": null}], "emails": [{"id": 13562147, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 45338858, "line1": "3028 Peach Orchard Rd", "line2": null, "city": "Augusta", "state": "GA", "zip": "30906", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business", "fullAddress": "3028 Peach Orchard Rd, Augusta, GA 30906"}], "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "1507c144-0e50-481a-826d-889e52eb652f", "abbreviatedName": null, "ssn": null, "notes": "Reductions: 50% <NAME_EMAIL> (Check balance prior to sending request)\nTID:  58-2362299", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": true, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": false, "searchNames": ["pidcock", "chiropractic", "pidcock chiropractic"]}, "amountbilled": 5910, "notes": "7.20 cl -the balance is $2,227. HI paid $267.81 and adjusted $3,415.19\n7.18 cl - emailed <PERSON> for the balance\n6/1/2023js Per itemization UMR paid 146.50", "plaintiffstreatmentstatus": "Treatment Complete", "datetreatmentstarted": "2023-02-27T00:00:00Z", "datetreatmentcompleted": "2023-04-27T00:00:00Z", "haveallbillsbeenordered": true, "billsordereddate": "2023-05-11T00:00:00Z", "haveallbillsbeenreceived": true, "billsreceiveddate": "2023-05-11T00:00:00Z", "haveallrecordsbeenordered": true, "recordsordereddate": "2023-05-11T00:00:00Z", "haveallrecordsbeenreceived": true, "recordsreceiveddate": "2023-05-11T00:00:00Z", "providerBalancePaid": true, "primaryHealthInsPayment": 267.81, "adjustment": 3415.19, "amountdue": 2227, "checkmemo": "Full and Final", "lettersRequests": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "55 Pidcock Chiropractic Records & Bill Request Akina 2023-05-11 0905.docx", "uploadDate": "2023-05-11T13:05:45.457Z", "templateID": 241406397, "templateName": "55 {{meds.payee.name}} Records & Bill Request {{client.firstname}}.docx", "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "calculatedamountdue": {"value": 2227, "error": null}, "sendToTrustCheckRequest": true}, "links": {}}, {"itemId": {"native": "b0b1ac88-b737-4d91-8330-c137a18c848e", "partner": null}, "dataObject": {"payee": {"id": 27975633, "orgID": 5676, "firstName": "AU Medical Associates", "middleName": "", "lastName": "", "fullname": "AU Medical Associates", "isSingleName": true, "fromCompany": null, "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "AU Medical Associates", "nickname": "Radiology", "initials": "A", "initialsFirstLast": "A", "personTypes": [{"id": 6799, "name": "Medical Provider", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "90980cd5-ee0a-4baf-84f9-0966e9b078dc"}], "tags": [], "isArchived": false, "createdDate": "2021-01-14T19:08:58.477Z", "modifiedDate": "2023-11-13T15:25:17.833Z", "phones": [{"id": 69254525, "number": "************", "rawNumber": "**********", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": "for balance verification"}, {"id": 61712384, "number": "**********", "rawNumber": "**********", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": "Fax for Billing Dept to get balances"}, {"id": 69131488, "number": "************", "rawNumber": "**********", "label": null, "phoneLabel": null, "notes": "billing"}], "emails": [{"id": 16932218, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "Billing Requests"}], "addresses": [{"id": 51042398, "line1": "1499 Walton Way", "line2": "", "city": "Augusta", "state": "GA", "zip": "30901", "label": null, "addressLabel": null, "notes": null, "fullAddress": "1499 Walton Way, Augusta, GA 30901"}], "pictureUrl": "/images/Default89a77474-bfa5-4e2f-af46-bbb8ee372a66.png", "pictureKey": "Default89a77474-bfa5-4e2f-af46-bbb8ee372a66.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "c1ba2f08-94fa-460f-9181-226ff8566c1a", "abbreviatedName": "Radiology", "ssn": null, "notes": "Reductions 25% to <EMAIL>", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": null, "remarket": null, "driverLicenseNumber": null, "isTypeClient": null, "isTypeAdjuster": null, "isTypeDefendant": null, "isTypePlaintiff": null, "isTypeAttorney": null, "isTypeFirm": null, "isTypeExpert": null, "isTypeMedicalProvider": true, "isTypeInvolvedParty": null, "isTypeJudge": null, "isTypeCourt": null, "isTypeInsuranceCompany": null, "salutation": "", "barNumber": null, "fiduciary": null, "isMinor": null, "searchNames": ["au", "medical", "associates", "au medical associates", "radiology"]}, "amountbilled": 429, "notes": "01/12/24 I called and spoke to <PERSON>, he said the claim was just billed today after expediting it. hl\n\n12/29/23 I called and spoke to <PERSON>, he said the claim is still pending with Richmond County Risk Management. I told him that we spoke last month and told me that they will file it to UMR, he said he will talk to his manager to expedite the claim submission to UMR. hl\n\n12/05/23 I called and spoke to <PERSON>, she said that the balance is still pending with insurance. I asked if it was submitted to the HI, she said yes and they submitted it on 11/14/23 to UMR. hl\n\n11/14/23 I called and spoke to <PERSON>, he said that the claim was not submitted to HI. He took the details again and he said he will try to submit it. I will CB to check the status. hl\n\n11/02/23 I called and spoke to <PERSON>, she said that it was filed to Richmond County Risk Management. She said that they will try to file the claim to the HI of the client. TAT is 30-45 days. I will CB to check the status. hl\n\n11/01/23 I tried to call but no answer. hl\n10/31/23 I tried to call twice but no answer. hl\n09/27/23 I called and spoke to <PERSON>, he said that the balance is still pending with HI. hl\n\n09/13/23 I spoke to <PERSON>, the balance is still pending with the HI. The claim was submitted on 05/10/23. She said she will follow-up with the billing team to check with the HI. Follow-up after 2 weeks. HL\n\n8.18tb  Pi stated ins is still pending\n8.3tb <PERSON> stated there is a 0 balance pending ins pmt\nREQ-********\n5.11.2023 resubmitted w hosp b&r", "plaintiffstreatmentstatus": "Treatment Complete", "datetreatmentstarted": "2023-02-15T00:00:00Z", "datetreatmentcompleted": "2023-02-15T00:00:00Z", "haveallbillsbeenordered": true, "billsordereddate": "2023-02-20T00:00:00Z", "haveallbillsbeenreceived": true, "billsreceiveddate": "2023-05-19T00:00:00Z", "providersaccountnumber": "12052957V72507157", "providerBalancePaid": false, "lettersRequests": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "55 AU Medical Associates Itemized Bill Request Akina 2023-05-11 1107.docx", "uploadDate": "2023-05-11T15:07:15.383Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "calculatedamountdue": {"value": 429, "error": null}}, "links": {}}, {"itemId": {"native": "3c8e355e-ea60-477c-add7-04a35fd3eb0f", "partner": null}, "dataObject": {"payee": {"id": ********, "orgID": 5676, "firstName": "Wellstar MCG Formerly AU Medical Center", "middleName": null, "lastName": "", "fullname": "Wellstar MCG Formerly AU Medical Center", "isSingleName": true, "fromCompany": "Augusta University Medical Center", "orgMetaVersionID": 0, "jobTitle": null, "department": null, "prefix": null, "suffix": null, "fullnameExtended": "Wellstar MCG Formerly AU Medical Center", "nickname": "Augusta University Medical Center AUMC", "initials": "W", "initialsFirstLast": "W", "personTypes": [{"id": 6799, "name": "Medical Provider", "badgeColorClass": null, "isDeprecated": false, "includeBirthdateField": false, "globalSourceGuid": "90980cd5-ee0a-4baf-84f9-0966e9b078dc"}], "tags": [], "isArchived": false, "createdDate": "2021-01-08T15:22:39.073Z", "modifiedDate": "2024-01-27T00:32:50.257Z", "phones": [{"id": 66262837, "number": "************", "rawNumber": "**********", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": "FAX LOR FOR <PERSON><PERSON><PERSON><PERSON> VERIFICATION"}, {"id": 64899228, "number": "************", "rawNumber": "**********", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": "Atty fax"}, {"id": 68559892, "number": "************", "rawNumber": "**********", "label": "Fax", "phoneLabel": {"isSmsable": false, "isFaxable": true, "id": 2872, "name": "Fax", "icon": "call", "isDeprecated": false, "globalSourceGuid": "5989c347-f236-4c32-b43c-3c1c62abe786"}, "notes": ""}, {"id": 64938471, "number": "************", "rawNumber": "**********", "label": "Home", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2870, "name": "Home", "icon": "call", "isDeprecated": false, "globalSourceGuid": "6300aacf-7780-4d45-81fd-934b9280e975"}, "notes": "please request a regular rep Billing (no attorney req prompt. Get reg rep)"}, {"id": 65742062, "number": "************", "rawNumber": "**********", "label": null, "phoneLabel": null, "notes": "Wellstar billing make sure to send first fax before call"}, {"id": 69154851, "number": "************", "rawNumber": "7068136076", "label": null, "phoneLabel": null, "notes": "<PERSON> phone"}, {"id": 69250564, "number": "18003671500", "rawNumber": "18003671500", "label": null, "phoneLabel": null, "notes": "CIOX"}, {"id": 69251247, "number": "8887152667", "rawNumber": "8887152667", "label": null, "phoneLabel": null, "notes": "Enable Comp handles MVA/WC cases billing"}, {"id": 69412425, "number": "************", "rawNumber": "7067212948", "label": "Work", "phoneLabel": {"isSmsable": false, "isFaxable": false, "id": 2873, "name": "Work", "icon": "call", "isDeprecated": false, "globalSourceGuid": "780172ec-8ad4-4ecb-9e48-cd9e251ac42f"}, "notes": "HIM Services"}], "emails": [{"id": 17076627, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "9.5.23 SH Facility Bill XRAY Bill"}, {"id": 16568271, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "Supervisor 8/10/2023"}, {"id": 17076626, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "Supervisor"}, {"id": 17118339, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "9.5.23 SH Facility Bill XRAY Bill"}, {"id": 17118340, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "9.5.23 SH Facility Bill XRAY Bill"}, {"id": 17118341, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": "9.5.23 SH Facility Bill XRAY Bill"}, {"id": 17347425, "address": "<EMAIL>", "emailLabel": null, "label": null, "notes": null}], "addresses": [{"id": 49867867, "line1": "1120 15th Street", "line2": "MOB-120", "city": "Augusta", "state": "GA", "zip": "30912", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "ADDRESS FOR MEDICAL RECORDS DEPARTMENT", "fullAddress": "1120 15th Street, MOB-120, Augusta, GA 30912"}, {"id": 46172992, "line1": "1120 15th Street", "line2": "BPM-120", "city": "Augusta", "state": "GA", "zip": "30912", "label": "Work", "addressLabel": {"id": 1260, "name": "Work", "icon": "mail_outline", "isDeprecated": false, "globalSourceGuid": "718c4239-8b78-42b1-b627-13bdda0062b8"}, "notes": "Business (ADDRESS FOR BILLING DEPARTMENT)", "fullAddress": "1120 15th Street, BPM-120, Augusta, GA 30912"}], "pictureUrl": "/images/avatar-user-placeholder.png", "pictureKey": "avatar-user-placeholder.png", "birthDate": null, "deathDate": null, "isDeceased": null, "uniqueID": "9bdc1b1a-d5fc-46c1-9be1-991ed33ea9c8", "abbreviatedName": "Augusta University Medical Center AUMC", "ssn": null, "notes": "Please send the <NAME_EMAIL> 8/10/2023\n\nAll balance verification are now done via fax and just make sure the client authorization is attached - fax to ************\n\nReductions 25% - <EMAIL>\n<EMAIL> \nHeather <PERSON> - <PERSON><EMAIL> - send redux request\n\nAKA Full: AUMC\nCan send bill request's to email: <EMAIL>\n\n11.8.23 SH - AUMC will no longer except request with Non-valid entity names include the following: Medical College of Georgia (MCG), Augusta\nUniversity (AU), and Georgia Regents University (GRU). \nThey will only accept  MCG Health, Inc., GRMC, Georgia Regents Medical Center, AU Medical Center. We can also accept requests addressed to GR Health or AU Health, which are the past and present names for the Health System", "specialty": null, "gender": null, "language": null, "maritalStatus": null, "isTextingPermitted": false, "remarket": false, "driverLicenseNumber": null, "isTypeClient": false, "isTypeAdjuster": false, "isTypeDefendant": false, "isTypePlaintiff": false, "isTypeAttorney": false, "isTypeFirm": false, "isTypeExpert": false, "isTypeMedicalProvider": true, "isTypeInvolvedParty": false, "isTypeJudge": false, "isTypeCourt": false, "isTypeInsuranceCompany": false, "salutation": null, "barNumber": null, "fiduciary": null, "isMinor": false, "searchNames": ["wellstar", "mcg", "formerly", "au", "medical", "center", "wellstar mcg formerly au medical center", "augusta university medical center aumc"]}, "amountbilled": 829, "notes": "7.24 cl - per <PERSON><PERSON><PERSON> <PERSON> the balance is $0 her HI paid this bill. \n7.18 cl - reduction sent to <PERSON>", "plaintiffstreatmentstatus": "Treatment Complete", "datetreatmentstarted": "2023-02-15T00:00:00Z", "datetreatmentcompleted": "2023-02-15T00:00:00Z", "haveallbillsbeenordered": true, "billsordereddate": "2023-02-20T00:00:00Z", "haveallbillsbeenreceived": true, "billsreceiveddate": "2023-04-10T00:00:00Z", "haveallrecordsbeenordered": true, "recordsordereddate": "2023-02-20T00:00:00Z", "haveallrecordsbeenreceived": true, "recordsreceiveddate": "2023-04-10T00:00:00Z", "billinvoicereceived": true, "recordsinvoicereceived": true, "lienfiled": true, "providerBalancePaid": true, "primaryHealthInsPayment": 829, "adjustment": 0, "amountdue": 0, "lettersRequests": [{"id": *********, "orgID": 5676, "projectID": ********, "filename": "081 Augusta University Medical Center Detailed Reduction Request 2023-07-18 0910.docx", "uploadDate": "2023-07-18T13:12:00.907Z", "templateID": null, "templateName": null, "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}, {"id": 340394574, "orgID": 5676, "projectID": ********, "filename": "55 Augusta University Medical Center Hospital Records Request A‘kina 2023-02-20 1311.docx", "uploadDate": "2023-02-20T18:11:21.06Z", "templateID": 241406308, "templateName": "55 {{meds.payee.name}} Hospital Records Request {{client.firstname}}.docx", "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}, {"id": 340394571, "orgID": 5676, "projectID": ********, "filename": "55 Augusta University Medical Center Hospital Bill Request A‘kina 2023-02-20 1311.docx", "uploadDate": "2023-02-20T18:11:06.84Z", "templateID": 241407156, "templateName": "55 {{meds.payee.name}} Hospital Bill Request {{client.firstname}}.docx", "reportFusionID": null, "reportFusionName": null, "contractID": null, "canEditInPlace": true, "lockedByUserID": null, "lockedTime": null, "lockExpirationTime": null, "lockedByUserFullName": null, "isPending": false, "contract": null}], "calculatedamountdue": {"value": 0, "error": null}}, "links": {}}], "documents": [{"documentId": {"native": 340402407, "partner": null}, "filename": "#SubroLOR UMR.pdf", "folderName": "LOR", "size": 658802, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 65144, "partner": null}, "uploadDate": "2023-02-21T15:48:50.997Z", "hashtags": [], "uploaderFullname": "<PERSON><PERSON><PERSON>", "links": {"project": "/projects/********", "self": "/documents/340402407", "folder": "/folders/********", "hashtags": "/documents/340402407/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "033 - <PERSON>ETTER TO INSURANCE. SETTLEMENT ACCEPTANCE 2023-07-10 1525.docx", "folderName": "Auto Insurance", "size": 40281, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 59733, "partner": null}, "uploadDate": "2023-07-10T19:26:13.233Z", "hashtags": [], "uploaderFullname": "<PERSON><PERSON><PERSON> Lancaster", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "039 - LIENS Payment Letter Optum 2023-09-20 0916.docx", "folderName": "Bankruptcy & Liens", "size": 134729, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40379, "partner": null}, "uploadDate": "2023-09-20T13:17:01.427Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "039 - <PERSON><PERSON><PERSON> Chiropractic Paid in FULL 2023-09-30 1334.docx", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 134970, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40344, "partner": null}, "uploadDate": "2023-09-30T17:35:42.913Z", "hashtags": [], "uploaderFullname": "<PERSON><PERSON><PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "052 - LOR UM CARRIER Omni Indemnity Company 2023-03-14 0840.docx", "folderName": "Auto Insurance", "size": 52845, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40320, "partner": null}, "uploadDate": "2023-03-14T12:41:17.683Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "081 Augusta University Medical Center Detailed Reduction Request 2023-07-18 0910.docx", "folderName": "Settlement", "size": 35234, "folderId": {"native": 98404315, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 59733, "partner": null}, "uploadDate": "2023-07-18T13:12:00.907Z", "hashtags": [], "uploaderFullname": "<PERSON><PERSON><PERSON> Lancaster", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/98404315", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": 341413612, "partner": null}, "filename": "1. AU Medical Asso.pdf", "folderName": "Medical Records & Bills", "size": 1578678, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40353, "partner": null}, "uploadDate": "2023-05-19T13:17:19.04Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341413612", "folder": "/folders/********", "hashtags": "/documents/341413612/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "133 SUBROLOR-HI UMR - #SUBROLOR 2023-02-21 1047.docx", "folderName": "Auto Insurance", "size": 43938, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 65144, "partner": null}, "uploadDate": "2023-02-21T15:47:03.963Z", "hashtags": [], "uploaderFullname": "<PERSON><PERSON><PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "133 SUBROLOR-HI UMR - #SUBROLOR 2023-02-21 1047.docx", "folderName": "LOR", "size": 50787, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 65144, "partner": null}, "uploadDate": "2023-02-21T15:48:29.44Z", "hashtags": [], "uploaderFullname": "<PERSON><PERSON><PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "135 SUBRO Settlement Notice Optum 2023-07-11 1559.docx", "folderName": "Bankruptcy & Liens", "size": 34676, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 71778, "partner": null}, "uploadDate": "2023-07-11T19:59:48.377Z", "hashtags": [], "uploaderFullname": "Angelic Austria", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "2. AUMC Bill.pdf", "folderName": "Medical Records & Bills", "size": 37097, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-04-10T19:24:47.86Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": 340387503, "partner": null}, "filename": "2022 LeadDocket Contract File-Signed.pdf", "size": 571854, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 70296, "partner": null}, "uploadDate": "2023-02-17T21:52:44.65Z", "hashtags": [], "uploaderFullname": "LD Integration", "links": {"project": "/projects/********", "self": "/documents/340387503", "folder": "/folders/", "hashtags": "/documents/340387503/hashtags"}}, {"documentId": {"native": 340591942, "partner": null}, "filename": "2023-52588.pdf", "folderName": "Auto Insurance", "size": 499758, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40320, "partner": null}, "uploadDate": "2023-03-14T12:42:03.587Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340591942", "folder": "/folders/********", "hashtags": "/documents/340591942/hashtags"}}, {"documentId": {"native": 340591965, "partner": null}, "filename": "2023-52588.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 502803, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40320, "partner": null}, "uploadDate": "2023-03-14T12:44:40.703Z", "hashtags": ["#attachment", "#email"], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340591965", "folder": "/folders/********", "hashtags": "/documents/340591965/hashtags"}}, {"documentId": {"native": 340552453, "partner": null}, "filename": "3. AUMC Records.pdf", "folderName": "Medical Records & Bills", "size": 449787, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-04-10T19:24:03.433Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340552453", "folder": "/folders/********", "hashtags": "/documents/340552453/hashtags"}}, {"documentId": {"native": 341314731, "partner": null}, "filename": "4. <PERSON><PERSON><PERSON> b&rs.pdf", "folderName": "Medical Records & Bills", "size": 15049560, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-05-11T15:04:38.367Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341314731", "folder": "/folders/********", "hashtags": "/documents/341314731/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "54 - LOR LIABILITY CARRIER 2023-03-14 0838.docx", "folderName": "Auto Insurance", "size": 52365, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40320, "partner": null}, "uploadDate": "2023-03-14T12:45:45.823Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/********", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "55 AU Medical Associates Itemized Bill Request Akina 2023-05-11 1107.docx", "folderName": "Requests", "size": 37649, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-05-11T15:07:15.383Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/98404312", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": 340394571, "partner": null}, "filename": "55 Augusta University Medical Center Hospital Bill Request A‘kina 2023-02-20 1311.docx", "folderName": "Requests", "size": 37724, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-02-20T18:11:06.84Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340394571", "folder": "/folders/98404312", "hashtags": "/documents/340394571/hashtags"}}, {"documentId": {"native": 340394574, "partner": null}, "filename": "55 Augusta University Medical Center Hospital Records Request A‘kina 2023-02-20 1311.docx", "folderName": "Requests", "size": 38424, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-02-20T18:11:21.06Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340394574", "folder": "/folders/98404312", "hashtags": "/documents/340394574/hashtags"}}, {"documentId": {"native": *********, "partner": null}, "filename": "55 Pidcock Chiropractic Records & Bill Request Akina 2023-05-11 0905.docx", "folderName": "Requests", "size": 37664, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-05-11T13:05:45.457Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/*********", "folder": "/folders/98404312", "hashtags": "/documents/*********/hashtags"}}, {"documentId": {"native": 341509228, "partner": null}, "filename": "A Harris 50k Ante Litem Auth.mp3", "folderName": "Settlement", "size": 777069, "folderId": {"native": 98404315, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40343, "partner": null}, "uploadDate": "2023-05-31T20:23:47.683Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341509228", "folder": "/folders/98404315", "hashtags": "/documents/341509228/hashtags"}}, {"documentId": {"native": 343423168, "partner": null}, "filename": "A Harris 7500 bottom number.mp3", "folderName": "Settlement", "size": 985581, "folderId": {"native": 98404315, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40343, "partner": null}, "uploadDate": "2023-06-13T16:38:15.753Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/343423168", "folder": "/folders/98404315", "hashtags": "/documents/343423168/hashtags"}}, {"documentId": {"native": 340390938, "partner": null}, "filename": "Affidaivt.pdf", "folderName": "Client Info & Authorizations", "size": 219406, "folderId": {"native": 98404300, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40328, "partner": null}, "uploadDate": "2023-02-20T14:33:46.58Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340390938", "folder": "/folders/98404300", "hashtags": "/documents/340390938/hashtags"}}, {"documentId": {"native": 341448954, "partner": null}, "filename": "<PERSON>kina Haris school enrollment form.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 82150, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-05-23T19:49:33.573Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341448954", "folder": "/folders/********", "hashtags": "/documents/341448954/hashtags"}}, {"documentId": {"native": 343933147, "partner": null}, "filename": "<PERSON><PERSON><PERSON> Harris 135 SUBRO Settlement Notice Optum 2023-07-11 1559.pdf", "folderName": "Subrogation", "size": 130048, "folderId": {"native": 98404316, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 71778, "partner": null}, "uploadDate": "2023-07-11T20:13:17.867Z", "hashtags": [], "uploaderFullname": "Angelic Austria", "links": {"project": "/projects/********", "self": "/documents/343933147", "folder": "/folders/98404316", "hashtags": "/documents/343933147/hashtags"}}, {"documentId": {"native": 343908092, "partner": null}, "filename": "<PERSON><PERSON> SET auth at top all bills.mp3", "folderName": "Settlement", "size": 1101933, "folderId": {"native": 98404315, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40343, "partner": null}, "uploadDate": "2023-07-10T16:39:43.17Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/343908092", "folder": "/folders/98404315", "hashtags": "/documents/343908092/hashtags"}}, {"documentId": {"native": 341509328, "partner": null}, "filename": "Ante Litem Richmond County Board of Commissioners - Akina Harris.docx", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 69398, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40343, "partner": null}, "uploadDate": "2023-05-31T20:30:32.473Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341509328", "folder": "/folders/********", "hashtags": "/documents/341509328/hashtags"}}, {"documentId": {"native": 340524054, "partner": null}, "filename": "AR Sharmaine Harris.pdf", "folderName": "AR, Citation Info & Footage", "size": 318480, "folderId": {"native": 98404301, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40328, "partner": null}, "uploadDate": "2023-03-07T21:59:52.297Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340524054", "folder": "/folders/98404301", "hashtags": "/documents/340524054/hashtags"}}, {"documentId": {"native": 341314844, "partner": null}, "filename": "AU Med Assoc EMG MR.pdf", "folderName": "Requests", "size": 481979, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-05-11T15:08:29.47Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341314844", "folder": "/folders/98404312", "hashtags": "/documents/341314844/hashtags"}}, {"documentId": {"native": 340394623, "partner": null}, "filename": "AU Med Assoc Er physicians bill.pdf", "folderName": "Requests", "size": 565633, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-02-20T18:12:49.703Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340394623", "folder": "/folders/98404312", "hashtags": "/documents/340394623/hashtags"}}, {"documentId": {"native": 340394644, "partner": null}, "filename": "AUMC bill reqs.pdf", "folderName": "Requests", "size": 565919, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-02-20T18:13:49.733Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340394644", "folder": "/folders/98404312", "hashtags": "/documents/340394644/hashtags"}}, {"documentId": {"native": 340394632, "partner": null}, "filename": "AUMC records reqs.pdf", "folderName": "Requests", "size": 544108, "folderId": {"native": 98404312, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-02-20T18:13:20.18Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340394632", "folder": "/folders/98404312", "hashtags": "/documents/340394632/hashtags"}}, {"documentId": {"native": 340885815, "partner": null}, "filename": "Ciox 1.pdf", "folderName": "Invoices", "size": 66616, "folderId": {"native": 98404298, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-04-10T19:25:11.77Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340885815", "folder": "/folders/98404298", "hashtags": "/documents/340885815/hashtags"}}, {"documentId": {"native": 340885812, "partner": null}, "filename": "Ciox.pdf", "folderName": "Invoices", "size": 69425, "folderId": {"native": 98404298, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-04-10T19:25:07.44Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340885812", "folder": "/folders/98404298", "hashtags": "/documents/340885812/hashtags"}}, {"documentId": {"native": 340431256, "partner": null}, "filename": "Combined Doc_A‘kina <PERSON> - MVA - 2_15_2023_2_23.pdf", "folderName": "LOR", "size": 30057657, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40241, "partner": null}, "uploadDate": "2023-02-23T14:30:16.77Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340431256", "folder": "/folders/********", "hashtags": "/documents/340431256/hashtags"}}, {"documentId": {"native": 340394720, "partner": null}, "filename": "Conf_Conf_Fax_AUMC records reqs_2023-02-20-1015-PST.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 721497, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-02-20T18:18:44.897Z", "hashtags": ["#faxconfirmation"], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340394720", "folder": "/folders/********", "hashtags": "/documents/340394720/hashtags"}}, {"documentId": {"native": 343912656, "partner": null}, "filename": "Conf_Fax_033 - <PERSON>ETTER TO INSURANCE. SETTLEMENT ACCEPTANCE 2023-07-10 1525_2023-07-10-1227-PDT.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 247479, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 59733, "partner": null}, "uploadDate": "2023-07-10T19:28:03.657Z", "hashtags": ["#faxconfirmation"], "uploaderFullname": "<PERSON><PERSON><PERSON> Lancaster", "links": {"project": "/projects/********", "self": "/documents/343912656", "folder": "/folders/********", "hashtags": "/documents/343912656/hashtags"}}, {"documentId": {"native": 343933195, "partner": null}, "filename": "Conf_Fax_<PERSON><PERSON><PERSON> Harris 135 SUBRO Settlement Notice Optum 2023-07-11 1559_2023-07-11-1313-PDT.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 215531, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 71778, "partner": null}, "uploadDate": "2023-07-11T20:14:21.507Z", "hashtags": ["#faxconfirmation"], "uploaderFullname": "Angelic Austria", "links": {"project": "/projects/********", "self": "/documents/343933195", "folder": "/folders/********", "hashtags": "/documents/343933195/hashtags"}}, {"documentId": {"native": 340394677, "partner": null}, "filename": "Conf_Fax_AUMC bill reqs_2023-02-20-1014-PST.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 656780, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-02-20T18:15:45.193Z", "hashtags": ["#faxconfirmation"], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340394677", "folder": "/folders/********", "hashtags": "/documents/340394677/hashtags"}}, {"documentId": {"native": 340431372, "partner": null}, "filename": "Conf_Fax_Combined Doc_A‘kina <PERSON> - MVA - 2_15_2023_2_23_2023-02-23-0630-PST.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 30179727, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40241, "partner": null}, "uploadDate": "2023-02-23T14:37:21.833Z", "hashtags": ["#faxconfirmation"], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340431372", "folder": "/folders/********", "hashtags": "/documents/340431372/hashtags"}}, {"documentId": {"native": 340390940, "partner": null}, "filename": "Contract.pdf", "folderName": "Client Info & Authorizations", "size": 313757, "folderId": {"native": 98404300, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 40328, "partner": null}, "uploadDate": "2023-02-20T14:33:46.593Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/340390940", "folder": "/folders/98404300", "hashtags": "/documents/340390940/hashtags"}}, {"documentId": {"native": 340387508, "partner": null}, "filename": "CRN info 2(1).jpeg", "folderName": "AR, Citation Info & Footage", "size": 1910996, "folderId": {"native": 98404301, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 70296, "partner": null}, "uploadDate": "2023-02-17T21:52:50.79Z", "hashtags": [], "uploaderFullname": "LD Integration", "links": {"project": "/projects/********", "self": "/documents/340387508", "folder": "/folders/98404301", "hashtags": "/documents/340387508/hashtags"}}, {"documentId": {"native": 340387509, "partner": null}, "filename": "CRN info.jpeg", "folderName": "AR, Citation Info & Footage", "size": 1833602, "folderId": {"native": 98404301, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 70296, "partner": null}, "uploadDate": "2023-02-17T21:52:52.093Z", "hashtags": [], "uploaderFullname": "LD Integration", "links": {"project": "/projects/********", "self": "/documents/340387509", "folder": "/folders/98404301", "hashtags": "/documents/340387509/hashtags"}}, {"documentId": {"native": 340387505, "partner": null}, "filename": "DL.jpeg", "folderName": "Client Info & Authorizations", "size": 1772232, "folderId": {"native": 98404300, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 70296, "partner": null}, "uploadDate": "2023-02-17T21:52:46.69Z", "hashtags": [], "uploaderFullname": "LD Integration", "links": {"project": "/projects/********", "self": "/documents/340387505", "folder": "/folders/98404300", "hashtags": "/documents/340387505/hashtags"}}, {"documentId": {"native": 344222643, "partner": null}, "filename": "DOC072723-07272023193058.pdf", "folderName": "Settlement", "size": 78497, "folderId": {"native": 98404315, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 59102, "partner": null}, "uploadDate": "2023-07-28T11:32:04.85Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/344222643", "folder": "/folders/98404315", "hashtags": "/documents/344222643/hashtags"}}, {"documentId": {"native": 344182051, "partner": null}, "filename": "Duplicate.pdf", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 536734, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 59102, "partner": null}, "uploadDate": "2023-07-27T12:31:01.39Z", "hashtags": ["#attachment", "#email"], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/344182051", "folder": "/folders/********", "hashtags": "/documents/344182051/hashtags"}}, {"documentId": {"native": 340387506, "partner": null}, "filename": "ER discharge.jpeg", "folderName": "Medical Records & Bills", "size": 1858363, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 70296, "partner": null}, "uploadDate": "2023-02-17T21:52:48.09Z", "hashtags": [], "uploaderFullname": "LD Integration", "links": {"project": "/projects/********", "self": "/documents/340387506", "folder": "/folders/********", "hashtags": "/documents/340387506/hashtags"}}, {"documentId": {"native": 341466330, "partner": null}, "filename": "EXHIBIT A.docx", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 12152, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-05-25T15:19:58.52Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341466330", "folder": "/folders/********", "hashtags": "/documents/341466330/hashtags"}}, {"documentId": {"native": 341466351, "partner": null}, "filename": "EXHIBIT B.docx", "folderName": "A<PERSON><PERSON><PERSON> - MVA - 2_15_2023", "size": 12176, "folderId": {"native": ********, "partner": null}, "projectId": {"native": ********, "partner": null}, "uploaderId": {"native": 54267, "partner": null}, "uploadDate": "2023-05-25T15:21:02.44Z", "hashtags": [], "uploaderFullname": "<PERSON>", "links": {"project": "/projects/********", "self": "/documents/341466351", "folder": "/folders/********", "hashtags": "/documents/341466351/hashtags"}}]}