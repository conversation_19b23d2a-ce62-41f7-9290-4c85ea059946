from . import aws_operations


def get_unprocessed_files_from_s3(
    directory: str,
    canonical_object: str,
    bucket: str,
    all_data: bool,
):
    unprocessed_files = []
    dir_chunks = directory.split('/')
    dir_chunks.append('raw_data')
    if all_data:
        dir_chunks_temp = dir_chunks.copy()
        dir_chunks_temp.append('processed')
        dir_chunks_temp.append(canonical_object)
        dir_temp = '/'.join(dir_chunks_temp)
        unprocessed_files.extend(aws_operations.get_s3(bucket, dir_temp))
    dir_chunks.append('unprocessed')
    dir_chunks.append(canonical_object)
    directory = '/'.join(dir_chunks)
    unprocessed_files.extend(aws_operations.get_s3(bucket, directory))
    return unprocessed_files
