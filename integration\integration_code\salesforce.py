# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
import datetime
import typing

import django.conf
import pandas as pd

import integration.exceptions

from . import (
    aws_operations,
    context,
    data_review_logger,
    local_operations,
    logger_config,
    salesforce_local_operations,
    salesforce_operations,
)

logger = logger_config.get_logger(__name__)

app_config = django.conf.settings
settings = app_config.SETTINGS


def process_lawfirms_pipeline(
    bucket: str,
    directory: str,
    timestamp: datetime.datetime,
    last_run_time: int | datetime.datetime | None,
) -> None:
    '''
    Process lawfirms pipeline: Redshift LawFirms => Salesforce Accounts
    '''
    logger.info('Starting RS LawFirms -> SF Accounts Upsert')
    sf_lawfirms_data = salesforce_operations.get_sf_lawfirms_data()
    # Handle the case where last_run_time is -1 (int) by converting to None for query_postgres
    query_timestamp = (
        last_run_time
        if isinstance(last_run_time, (datetime.datetime, type(None)))
        else None
    )
    redshift_lawfirms_data = aws_operations.query_postgres(
        ['lawfirms'], timestamp=query_timestamp
    )
    (
        redshift_lawfirms_data_mapping_ids,
        redshift_lawfirm_external_id_mapping,
    ) = aws_operations.query_postgres_sf_map(
        ['lawfirms'], get_external_id=True
    )
    if (
        redshift_lawfirms_data is not None
        and 'lawfirms' in redshift_lawfirms_data
        and len(redshift_lawfirms_data['lawfirms']) > 0
    ):
        lawfirm_ext_ids = list(
            redshift_lawfirm_external_id_mapping['lawfirms'].values()
        )
        sf_lawfirm_ext_id_to_primary_id_mapping = (
            salesforce_operations.get_externalidcontainer_primary_gain_id_map(
                lawfirm_ext_ids
            )
        )
        if sf_lawfirm_ext_id_to_primary_id_mapping.empty:
            logger.error(
                'No External ID mapping found in Salesforce for lawfirms'
            )
        else:
            for (
                gainid,
                ext_id,
            ) in redshift_lawfirm_external_id_mapping['lawfirms'].items():
                primary_gainids = sf_lawfirm_ext_id_to_primary_id_mapping[
                    sf_lawfirm_ext_id_to_primary_id_mapping['Id'] == ext_id
                ]['Primary_Gain_ID__c'].values
                if len(primary_gainids) == 1:
                    primary_gainid = primary_gainids[0]
                else:
                    primary_gainid = None
                if primary_gainid is not None:
                    primary_sf_id = redshift_lawfirms_data_mapping_ids[
                        'lawfirms'
                    ].get(primary_gainid, None)
                    if primary_sf_id is not None:
                        redshift_lawfirms_data_mapping_ids['lawfirms'][
                            gainid
                        ] = primary_sf_id
        redshift_data_to_sf_lawfirms = salesforce_local_operations.redshift_data_to_salesforce_data_lawfirm_accounts(
            bucket,
            directory,
            redshift_lawfirms_data['lawfirms'],
            redshift_lawfirms_data_mapping_ids['lawfirms'],
            sf_lawfirms_data,
            timestamp,
        )
        salesforce_operations.upsert_sf_lawfirms(
            bucket,
            directory,
            redshift_data_to_sf_lawfirms,
            timestamp,
        )


def post_main_upsert(
    test: bool, partial_config: dict[str, bool], all_data: bool
) -> None:
    '''
    Get Canonical from Redshift, upsert to Salesforce
    '''
    timestamp = datetime.datetime.now()
    context.request_context.set({'route': 'Redshift_To_Salesforce_Upsert'})
    logger.info('Starting RS -> SF Upsert arm')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if isinstance(
        last_run_time, int
    ):  # only problem is if all_data=False i.e. delta changes but las run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for route missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    bucket, directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["Salesforce"]["S3"]["sub_directory"]}/upsert/{datetime.date.today()}',
    )
    auth_resp = salesforce_operations.authenticate_salesforce(test)
    if not auth_resp:
        logger.info('Salesforce authentication failed => Upsert skipped')
        return
    logger.info('SF authentication successful')
    redshift_sources = aws_operations.get_postgres_sources()
    sf_accounts_data = salesforce_operations.get_sf_accounts_data(
        redshift_sources
    )
    sf_accounts_ids = list(sf_accounts_data['root_name_id_map'].values())
    sf_lawfirms_data = []
    if partial_config[
        'plaintiffs'
    ]:  # Redshift Plaintiffs => Salesforce Accounts
        logger.info('Starting RS -> SF Upsert arm')
        sf_plaintiffaccounts_data = (
            salesforce_operations.get_sf_plaintiffaccount_data()
        )
        redshift_plaintiffs_data = aws_operations.query_postgres(
            ['plaintiffs'], timestamp=last_run_time
        )
        redshift_plaintiffs_data_mapping_ids, _ = (
            aws_operations.query_postgres_sf_map(['plaintiffs'])
        )
        if (
            redshift_plaintiffs_data is not None
            and 'plaintiffs' in redshift_plaintiffs_data
            and len(redshift_plaintiffs_data['plaintiffs']) > 0
        ):
            redshift_data_to_sf_plaintiffaccounts = salesforce_local_operations.redshift_data_to_salesforce_data_plaintiffaccounts(
                bucket,
                directory,
                redshift_plaintiffs_data['plaintiffs'],
                redshift_plaintiffs_data_mapping_ids['plaintiffs'],
                sf_plaintiffaccounts_data,
                timestamp,
            )
            salesforce_operations.upsert_sf_plaintiffaccounts(
                bucket,
                directory,
                redshift_data_to_sf_plaintiffaccounts,
                timestamp,
            )
    if partial_config.get(
        'medicalfacilities'
    ):  # Redshift Plaintiffs => Salesforce Accounts
        logger.info(
            'Starting RS Plaintiffs -> SF Medical Facility Accounts Upsert'
        )
        sf_medicalfacilities_accounts_data = (
            salesforce_operations.get_sf_medicalfacilities_data()
        )
        redshift_medicalfacilities_data = aws_operations.query_postgres(
            ['medicalfacilities'], timestamp=last_run_time
        )
        redshift_medicalfacilities_data_mapping_ids, _ = (
            aws_operations.query_postgres_sf_map(['medicalfacilities'])
        )
        if (
            redshift_medicalfacilities_data is not None
            and 'medicalfacilities' in redshift_medicalfacilities_data
            and len(redshift_medicalfacilities_data['medicalfacilities']) > 0
        ):
            redshift_data_to_sf_medicalfacilities_accounts = salesforce_local_operations.redshift_data_to_salesforce_data_medicalfacilities_accounts(
                bucket,
                directory,
                redshift_medicalfacilities_data['medicalfacilities'],
                redshift_medicalfacilities_data_mapping_ids[
                    'medicalfacilities'
                ],
                sf_medicalfacilities_accounts_data,
                sf_accounts_data['root_name_id_map'],
                timestamp,
            )
            salesforce_operations.upsert_sf_medicalfacilities(
                bucket,
                directory,
                redshift_data_to_sf_medicalfacilities_accounts,
                timestamp,
            )
    # Need to re-fetch the Salesforce Accounts data as it may have changed
    sf_accounts_data = salesforce_operations.get_sf_accounts_data(
        redshift_sources
    )
    sf_accounts_ids = list(sf_accounts_data['root_name_id_map'].values())
    if partial_config.get(
        'lawfirms'
    ):  # Redshift LawFirms => Salesforce Accounts
        process_lawfirms_pipeline(bucket, directory, timestamp, last_run_time)
    if partial_config.get(
        'legalpersonnel'
    ):  # Redshift LegalPersonnel => Salesforce Contacts
        # this is for LegalPersonnel which is not necessary for Athena system, the functions below will need to be modified when needed in the future
        logger.info('Starting RS LegalPersonnel -> SF Contacts Upsert')
        redshift_legalpersonnel_data = aws_operations.query_postgres(
            ['legalpersonnel'], timestamp=last_run_time
        )
        (
            redshift_legalpersonnel_data_mapping_ids,
            redshift_legalpersonnel_external_id_mapping,
        ) = aws_operations.query_postgres_sf_map(
            ['legalpersonnel'], get_external_id=True
        )
        (
            redshift_lawfirms_data_mapping_ids,
            redshift_lawfirm_external_id_mapping,
        ) = aws_operations.query_postgres_sf_map(
            ['lawfirms'], get_external_id=True
        )
        lawfirm_ext_ids = list(
            redshift_lawfirm_external_id_mapping['lawfirms'].values()
        )
        sf_lawfirm_ext_id_to_primary_id_mapping = (
            salesforce_operations.get_externalidcontainer_primary_gain_id_map(
                lawfirm_ext_ids
            )
        )
        if sf_lawfirm_ext_id_to_primary_id_mapping.empty:
            logger.error(
                'No External ID mapping found in Salesforce for lawfirms'
            )
        else:
            for (
                gainid,
                ext_id,
            ) in redshift_lawfirm_external_id_mapping['lawfirms'].items():
                primary_gainids = sf_lawfirm_ext_id_to_primary_id_mapping[
                    sf_lawfirm_ext_id_to_primary_id_mapping['Id'] == ext_id
                ]['Primary_Gain_ID__c'].values
                if len(primary_gainids) == 1:
                    primary_gainid = primary_gainids[0]
                else:
                    primary_gainid = None
                if primary_gainid is not None:
                    primary_sf_id = redshift_lawfirms_data_mapping_ids[
                        'lawfirms'
                    ].get(primary_gainid, None)
                    if primary_sf_id is not None:
                        redshift_lawfirms_data_mapping_ids['lawfirms'][
                            gainid
                        ] = primary_sf_id
        sf_lawfirms_data = salesforce_operations.get_sf_lawfirms_data()
        lawfirm_sf_ids = set(
            redshift_lawfirms_data_mapping_ids['lawfirms'].values()
        )
        for k in sf_lawfirms_data:
            for lawfirm_group in sf_lawfirms_data[k].values():
                lawfirm_sf_ids |= lawfirm_group
        sf_contacts_data = salesforce_operations.get_sf_contact_data(
            lawfirm_sf_ids  # pyright: ignore[reportArgumentType]
        )
        if (
            'legalpersonnel' in redshift_legalpersonnel_data
            and len(redshift_legalpersonnel_data['legalpersonnel']) > 0
        ):
            legalpersonnel_ext_ids = list(
                redshift_legalpersonnel_external_id_mapping[
                    'legalpersonnel'
                ].values()
            )
            sf_contact_ext_id_to_primary_id_mapping = salesforce_operations.get_externalidcontainer_primary_gain_id_map(
                legalpersonnel_ext_ids
            )
            if sf_contact_ext_id_to_primary_id_mapping.empty:
                logger.info(
                    'No External ID mapping found in Salesforce for legalpersonnel'
                )
            else:
                for (
                    gainid,
                    ext_id,
                ) in redshift_legalpersonnel_external_id_mapping[
                    'legalpersonnel'
                ].items():
                    primary_gainid = None
                    primary_gainids = sf_contact_ext_id_to_primary_id_mapping[
                        sf_contact_ext_id_to_primary_id_mapping['Id'] == ext_id
                    ]['Primary_Gain_ID__c'].values
                    if len(primary_gainids) == 1:
                        primary_gainid = primary_gainids[0]
                    if primary_gainid is not None:
                        primary_sf_id = (
                            redshift_legalpersonnel_data_mapping_ids[
                                'legalpersonnel'
                            ].get(
                                primary_gainid,
                                None,
                            )
                        )
                        if primary_sf_id is not None:
                            redshift_legalpersonnel_data_mapping_ids[
                                'legalpersonnel'
                            ][gainid] = primary_sf_id
            redshift_data_to_sf_contacts = salesforce_local_operations.redshift_data_to_salesforce_data_contacts(
                bucket,
                directory,
                redshift_legalpersonnel_data['legalpersonnel'],
                redshift_legalpersonnel_data_mapping_ids['legalpersonnel'],
                redshift_lawfirms_data_mapping_ids['lawfirms'],
                sf_contacts_data,
                timestamp,
            )
            salesforce_operations.upsert_sf_contacts(
                bucket,
                directory,
                redshift_data_to_sf_contacts,
                timestamp,
            )

    if partial_config.get(
        'cases'
    ):  # Redshift Cases => Salesforce Opportunities
        logger.info('Starting RS Cases -> SF Opportunities Upsert')
        sf_opportunity_data = salesforce_operations.get_sf_opportunity_data(
            sf_accounts_ids
        )
        redshift_cases_data = aws_operations.query_postgres(
            ['cases'], timestamp=last_run_time
        )
        redshift_cases_data_mapping_ids, _ = (
            aws_operations.query_postgres_sf_map(['cases'])
        )
        redshift_lawfirms_data_mapping_ids, _ = (
            aws_operations.query_postgres_sf_map(['lawfirms'])
        )
        redshift_legalpersonnel_data_mapping_ids, _ = (
            aws_operations.query_postgres_sf_map(['legalpersonnel'])
        )
        if (
            'cases' in redshift_cases_data
            and len(redshift_cases_data['cases']) > 0
        ):
            plaintiff_ids = local_operations.get_related_plaintiff_ids(
                'Cases', redshift_cases_data['cases']
            )
            # redshift_data = aws_operations.query_redshift(['plaintiffs', 'surgery'], id_subset=plaintiff_ids)
            redshift_data = aws_operations.query_postgres(
                ['plaintiffs'], id_subset=plaintiff_ids
            )
            redshift_data_ids, _ = aws_operations.query_postgres_sf_map(
                ['plaintiffs'], id_subset=plaintiff_ids
            )
            redshift_data_to_sf_opportunities = salesforce_local_operations.redshift_data_to_salesforce_data_opportunities(
                bucket,
                directory,
                pd.DataFrame(redshift_data.get('plaintiffs', [])),
                redshift_data_ids.get('plaintiffs', {}),
                redshift_cases_data['cases'],
                redshift_cases_data_mapping_ids['cases'],
                redshift_lawfirms_data_mapping_ids['lawfirms'],
                redshift_legalpersonnel_data_mapping_ids['legalpersonnel'],
                pd.DataFrame(redshift_data.get('surgery', [])),
                redshift_data_ids.get('surgery', {}),
                sf_opportunity_data,
                sf_accounts_data['name_id_map'],
                sf_accounts_data['name_type_map'],
                timestamp,
            )
            salesforce_operations.upsert_sf_opportunities(
                bucket,
                directory,
                redshift_data_to_sf_opportunities,
                timestamp,
            )

            updated_cases_gain_ids = (
                redshift_data_to_sf_opportunities['gainid_update']
                + redshift_data_to_sf_opportunities['gainid_insert']
            )

            billings_gains_ids_to_update = (
                aws_operations.get_billing_gainids_from_case_gainids(
                    updated_cases_gain_ids
                )
            )

            curr_timestamp = datetime.datetime.now()

            sf_objects_update_sf_ids = {
                gain_id: curr_timestamp
                for gain_id in billings_gains_ids_to_update
            }

            aws_operations.update_postgres_sf_modifieddatetime(
                'billings',
                sf_objects_update_sf_ids,
                curr_timestamp,
            )

    if partial_config.get(
        'billings'
    ):  # Redshift Billings => Salesforce Fundings
        sf_medicalfacilities_data = (
            salesforce_operations.get_sf_medicalfacilities_map()
        )
        logger.info('Starting RS Billings -> SF Fundings Upsert')
        sf_fundings_data = salesforce_operations.get_sf_funding_data(
            sf_accounts_ids
        )

        redshift_billings_data = aws_operations.query_postgres(
            ['billings'], timestamp=last_run_time
        )
        redshift_billings_data_mapping_ids, _ = (
            aws_operations.query_postgres_sf_map(['billings'])
        )
        if (
            'billings' in redshift_billings_data
            and len(redshift_billings_data['billings']) > 0
        ):
            case_ids = local_operations.get_related_case_ids(
                'Billings', redshift_billings_data['billings']
            )
            redshift_data = aws_operations.query_postgres(
                ['cases'], id_subset=case_ids
            )
            redshift_data_ids, _ = aws_operations.query_postgres_sf_map(
                ['cases'], id_subset=case_ids
            )
            redshift_data_to_sf_fundings = salesforce_local_operations.redshift_data_to_salesforce_data_fundings(
                bucket,
                directory,
                pd.DataFrame(redshift_data.get('cases', [])),
                redshift_data_ids.get('cases', {}),
                pd.DataFrame(redshift_billings_data.get('billings', [])),
                redshift_billings_data_mapping_ids['billings'],
                sf_fundings_data,
                sf_medicalfacilities_data['name_id_map'],
                sf_medicalfacilities_data['locationid_id_map'],
                sf_medicalfacilities_data['id_parentid_map'],
                timestamp,
            )
            salesforce_operations.upsert_sf_fundings(
                bucket, directory, redshift_data_to_sf_fundings, timestamp
            )
    if partial_config.get('charges'):  # Redshift Charges => Salesforce Charges
        logger.info('Starting RS Charges -> SF Charges Upsert')
        sf_charges_data = salesforce_operations.get_sf_charge_data(
            sf_accounts_ids
        )
        redshift_charges_data = aws_operations.query_postgres(
            ['charges'], timestamp=last_run_time
        )
        redshift_charges_data_mapping_ids, _ = (
            aws_operations.query_postgres_sf_map(['charges'])
        )
        if (
            'charges' in redshift_charges_data
            and len(redshift_charges_data['charges']) > 0
        ):
            billing_ids = local_operations.get_related_billing_ids(
                'Charges', redshift_charges_data['charges']
            )
            redshift_data = aws_operations.query_postgres(
                ['billings'], id_subset=billing_ids
            )
            redshift_data_ids, _ = aws_operations.query_postgres_sf_map(
                ['billings'], id_subset=billing_ids
            )
            redshift_data_to_sf_charges = salesforce_local_operations.redshift_data_to_salesforce_data_charges(
                bucket,
                directory,
                pd.DataFrame(redshift_data.get('billings', [])),
                redshift_data_ids.get('billings', {}),
                pd.DataFrame(redshift_charges_data.get('charges', [])),
                redshift_charges_data_mapping_ids['charges'],
                sf_charges_data,
                timestamp,
            )
            salesforce_operations.upsert_sf_charges(
                bucket, directory, redshift_data_to_sf_charges, timestamp
            )
    if partial_config.get(
        'files'
    ):  # Redshift Files => Salesforce ContentVersion
        logger.info('Starting RS Files -> SF ContentVersion Upsert')
        redshift_files_data = aws_operations.query_postgres(
            ['files'], timestamp=last_run_time
        )
        if (
            'files' in redshift_files_data
            and len(redshift_files_data['files']) > 0
        ):
            case_ids = local_operations.get_related_case_ids(
                'Files', redshift_files_data['files']
            )
            redshift_data = aws_operations.query_postgres(
                ['cases'], id_subset=case_ids
            )
            redshift_data_to_sf_files = salesforce_local_operations.redshift_data_to_salesforce_data_files(
                bucket,
                directory,
                redshift_data_cases=pd.DataFrame(
                    redshift_data.get('cases', [])
                ),
                redshift_data_files=pd.DataFrame(
                    redshift_files_data.get('files', [])
                ),
                name_id_map=sf_accounts_data['name_id_map'],
                name_type_map=sf_accounts_data['name_type_map'],
                curr_timestamp=timestamp,
            )
            salesforce_operations.upsert_sf_files(
                bucket, directory, redshift_data_to_sf_files, timestamp
            )
    if partial_config.get('notes'):  # Redshift Notes => Salesforce ContentNote
        logger.info('Starting RS Notes -> SF ContentNote Upsert')
        redshift_notes_data = aws_operations.query_postgres(
            ['notes'], timestamp=last_run_time
        )
        if (
            'notes' in redshift_notes_data
            and len(redshift_notes_data['notes']) > 0
        ):
            redshift_data_to_sf_notes = salesforce_local_operations.redshift_data_to_salesforce_data_notes(
                bucket,
                directory,
                redshift_data_notes=pd.DataFrame(
                    redshift_notes_data.get('notes', [])
                ),
                curr_timestamp=timestamp,
            )
            salesforce_operations.upsert_sf_notes(
                bucket, directory, redshift_data_to_sf_notes, timestamp
            )
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    logger.info('Finishing RS -> SF Upsert arm')


def post_main_delete(
    test: bool, partial_config: dict[str, bool], all_data: bool
) -> None:
    '''
    Get Canonical from Redshift, delete from Salesforce
    '''
    context.request_context.set({'route': 'Redshift_To_Salesforce_Delete'})
    timestamp = datetime.datetime.now()
    logger.info('Starting RS -> SF Delete arm')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if isinstance(
        last_run_time, int
    ):  # only problem is if all_data=False i.e. delta changes but las run time is not found i.e. value of -1
        logger.info(
            'Last run timestamp for route missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    bucket, directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["Salesforce"]["S3"]["sub_directory"]}/delete/{datetime.date.today()}',
    )
    auth_resp = salesforce_operations.authenticate_salesforce(test)
    if not auth_resp:
        logger.error('Salesforce authentication failed => Delete skipped')
        return
    logger.info('SF authentication successful')
    if partial_config[
        'plaintiffs'
    ]:  # Redshift Plaintiffs => Salesforce Accounts
        logger.info('Starting RS Plaintiffs -> SF Plaintiff Accounts Delete')
        redshift_plaintiffs_data = aws_operations.query_postgres(
            ['plaintiffs'], timestamp=last_run_time, for_delete=True
        )
        redshift_plaintiffs_data_ids = (
            local_operations.get_canonical_ids_from_df(
                redshift_plaintiffs_data['plaintiffs']
            )
        )
        redshift_plaintiffs_data_mappings, _ = (
            aws_operations.query_postgres_sf_map(
                ['plaintiffs'],
                id_subset=redshift_plaintiffs_data_ids,
            )
        )
        if (
            'plaintiffs' in redshift_plaintiffs_data
            and len(redshift_plaintiffs_data['plaintiffs']) > 0
        ):
            salesforce_operations.delete_sf_objects(
                'plaintiffs',
                'Account',
                bucket,
                directory,
                redshift_plaintiffs_data_mappings,
                timestamp,
            )
    if partial_config[
        'legalpersonnel'
    ]:  # Redshift LegalPersonnel => Salesforce Contacts
        # this is for LegalPersonnel which is not necessary for Athena system, the functions below will need to be modified when needed in the future
        logger.info('Starting RS LegalPersonnel -> SF Contacts Delete')
        redshift_legalpersonnel_data = aws_operations.query_postgres(
            ['legalpersonnel'],
            timestamp=last_run_time,
            for_delete=True,
        )
        redshift_legalpersonnel_data_ids = (
            local_operations.get_canonical_ids_from_df(
                redshift_legalpersonnel_data['legalpersonnel']
            )
        )
        redshift_legalpersonnel_data_mappings, _ = (
            aws_operations.query_postgres_sf_map(
                ['legalpersonnel'],
                id_subset=redshift_legalpersonnel_data_ids,
            )
        )
        if (
            'legalpersonnel' in redshift_legalpersonnel_data
            and len(redshift_legalpersonnel_data['legalpersonnel']) > 0
        ):
            salesforce_operations.delete_sf_objects(
                'legalpersonnel',
                'Contact',
                bucket,
                directory,
                redshift_legalpersonnel_data_mappings,
                timestamp,
            )
    if partial_config.get(
        'cases'
    ):  # Redshift Cases => Salesforce Opportunities
        logger.info('Starting RS Cases -> SF Opportunities Delete')
        redshift_cases_data = aws_operations.query_postgres(
            ['cases'], timestamp=last_run_time, for_delete=True
        )
        redshift_cases_data_ids = local_operations.get_canonical_ids_from_df(
            redshift_cases_data['cases']
        )
        redshift_cases_data_mappings, _ = aws_operations.query_postgres_sf_map(
            ['cases'], id_subset=redshift_cases_data_ids
        )
        if (
            'cases' in redshift_cases_data
            and len(redshift_cases_data['cases']) > 0
        ):
            salesforce_operations.delete_sf_objects(
                'cases',
                'Opportunity',
                bucket,
                directory,
                redshift_cases_data_mappings,
                timestamp,
            )
    if partial_config.get(
        'billings'
    ):  # Redshift Billings => Salesforce Fundings
        logger.info('Starting RS Billings -> SF Fundings Delete')
        redshift_billings_data = aws_operations.query_postgres(
            ['billings'], timestamp=last_run_time, for_delete=True
        )
        redshift_billings_data_ids = (
            local_operations.get_canonical_ids_from_df(
                redshift_billings_data['billings']
            )
        )
        redshift_billings_data_mappings, _ = (
            aws_operations.query_postgres_sf_map(
                ['billings'], id_subset=redshift_billings_data_ids
            )
        )
        if (
            'billings' in redshift_billings_data
            and len(redshift_billings_data['billings']) > 0
        ):
            salesforce_operations.delete_sf_objects(
                'billings',
                'Funding__c',
                bucket,
                directory,
                redshift_billings_data_mappings,
                timestamp,
            )
    if partial_config.get('charges'):  # Redshift Charges => Salesforce Charges
        logger.info('Starting RS Charges -> SF Charges Delete')
        redshift_charges_data = aws_operations.query_postgres(
            ['charges'], timestamp=last_run_time, for_delete=True
        )
        redshift_charges_data_ids = local_operations.get_canonical_ids_from_df(
            redshift_charges_data['charges']
        )
        redshift_charges_data_mappings, _ = (
            aws_operations.query_postgres_sf_map(
                ['charges'], id_subset=redshift_charges_data_ids
            )
        )
        if (
            'charges' in redshift_charges_data
            and len(redshift_charges_data['charges']) > 0
        ):
            salesforce_operations.delete_sf_objects(
                'charges',
                'Charge__c',
                bucket,
                directory,
                redshift_charges_data_mappings,
                timestamp,
            )

    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    logger.info('Finishing RS -> SF Delete arm')
    return


def update_settled_by_status(
    update_settled_flag: str,
    update_settled_status: str,
    last_run_time: datetime.datetime | None,
    bucket: str,
    directory: str,
    timestamp: datetime.datetime,
):
    redshift_settled_billing_data = aws_operations.query_postgres(
        ['billings'],
        timestamp=last_run_time,
        update_settled_flag=update_settled_flag,
    )
    df_redshift_billing_data = local_operations.get_redshift_to_salesforce_update_settled_billing_data(
        redshift_settled_billing_data['billings'],
        update_settled_status,
    )
    redshift_pending_payment_from_hcp_billing_data_ids = (
        df_redshift_billing_data['GainId'].tolist()
    )
    redshift_pending_payment_from_hcp_billings_data_mappings, _ = (
        aws_operations.query_postgres_sf_map(
            ['billings'],
            id_subset=redshift_pending_payment_from_hcp_billing_data_ids,
        )
    )
    df_redshift_billing_data['SalesforceId'] = None
    if (
        'billings' in redshift_pending_payment_from_hcp_billings_data_mappings
        and len(
            redshift_pending_payment_from_hcp_billings_data_mappings[
                'billings'
            ]
        )
        > 0
    ):
        df_redshift_billing_data['SalesforceId'] = df_redshift_billing_data[
            'GainId'
        ].map(
            redshift_pending_payment_from_hcp_billings_data_mappings[
                'billings'
            ]
        )
    df_redshift_billing_data.dropna(subset=['SalesforceId'], inplace=True)
    if not df_redshift_billing_data.empty:
        salesforce_operations.update_settled_sf_objects(
            'billings',
            'Funding__c',
            bucket,
            directory,
            df_redshift_billing_data,
            timestamp,
        )


def post_main_update_settled(
    test: bool, partial_config: dict[str, bool], all_data: bool
) -> None:
    '''
    Get Canonical from Redshift, settle from Salesforce
    '''
    context.request_context.set(
        {'route': 'Redshift_To_Salesforce_Update_Settled'}
    )
    timestamp = datetime.datetime.now()
    logger.info('Starting RS -> SF Update Settled arm')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if isinstance(
        last_run_time, int
    ):  # only problem is if all_data=False i.e. delta changes but las run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for route missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    bucket, directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["Salesforce"]["S3"]["sub_directory"]}/update_settled/{datetime.date.today()}',
    )
    auth_resp = salesforce_operations.authenticate_salesforce(test)
    if not auth_resp:
        logger.error(
            'Salesforce authentication failed => Update Settled skipped'
        )
        return
    logger.info('SF authentication successful')
    if partial_config.get(
        'billings'
    ):  # Redshift Billings => Salesforce Fundings
        logger.info('Starting RS Billings -> SF Fundings Update Settled')
        update_settled_by_status(
            'Settled',
            'Pending Payment from HCP',
            last_run_time,
            bucket,
            directory,
            timestamp,
        )

        update_settled_by_status(
            'Settled',
            'Liability Insurance Payment to Provider',
            last_run_time,
            bucket,
            directory,
            timestamp,
        )

        update_settled_by_status(
            'WriteOff',
            'WriteOff',
            last_run_time,
            bucket,
            directory,
            timestamp,
        )

    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    logger.info('Finishing RS -> SF Update Settled arm')
    return


def get_salesforce_delta_review_update(
    *,
    canonical_object: str | None = None,
    page_index: typing.Optional[int] = None,
    page_size: typing.Optional[int] = None,
) -> list[dict[str, typing.Any]]:
    data = data_review_logger.get_unreviewed_manual_review_data(
        canonical_object,
        page_index=page_index,
        page_size=page_size,
    )
    front_end_format = []
    for item in data:
        keys_list = item.keys.split("|")
        count = 0
        for key in keys_list:
            keys_list[count] = key.strip()
            count += 1
        redshift_data_list = item.redshift_data.split("|")
        count = 0
        for data in redshift_data_list:
            redshift_data_list[count] = data.strip()
            count += 1
        salesforce_data_list = item.salesforce_data.split("|")
        count = 0
        for data in salesforce_data_list:
            salesforce_data_list[count] = data.strip()
            count += 1
        front_end_item = {
            'manual_review_data_id': item.update_manual_review_data_id,
            'canonical_object': item.canonical_object,
            'salesforce_id': item.salesforce_id,
            'redshift_id': item.redshift_id,
            'name': salesforce_data_list[0],
            'keys': keys_list,
            'redshift_data': redshift_data_list,
            'salesforce_data': salesforce_data_list,
        }
        front_end_format.append(front_end_item)
    return front_end_format


def post_salesforce_delta_upsert(
    test: bool,
    user: typing.Any,
    manual_review_ids: list[str],
    canonical_object: str,
    redshift_reviewed_records: list[dict[str, typing.Any]],
    redshift_gainids: list[str],
) -> None:
    '''
    Get incremental change from Redshift, post to Salesforce
    Also being used to handle incremental changes during manual review process
    '''
    curr_timestamp = datetime.datetime.now()
    bucket, directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["ATI"]["S3"]["sub_directory"]}/{datetime.date.today()}',
    )
    auth_resp = salesforce_operations.authenticate_salesforce(test)
    if not auth_resp:
        raise integration.exceptions.IntegrationInternalBadRequestException(
            logger_config.AuditLog(
                is_error=True,
                exception_message='Salesforce authentication failed => data loading skipped',
            )
        )

    invalid_fields = ['PartnerAccountName', 'MedicalLocationName']
    for item in redshift_reviewed_records:
        for key in item:
            if key in invalid_fields:
                del item[key]
            if item[key] == 'True':
                item[key] = True
            elif item[key] == 'False':
                item[key] = False

    canonical_function_map = {
        'Plaintiffs': salesforce_operations.update_sf_plaintiffaccounts,
        'Cases': salesforce_operations.update_sf_opportunities,
        'Billings': salesforce_operations.update_sf_fundings,
        'LegalPersonnel': salesforce_operations.update_sf_contacts,
        'LawFirms': salesforce_operations.update_sf_lawfirms,
        'Charges': salesforce_operations.update_sf_charges,
        'MedicalFacilities': salesforce_operations.update_sf_medicalfacilities,
        'Files': salesforce_operations.update_sf_files,
    }
    if canonical_object in canonical_function_map:
        data_review_logger.update_manual_review_log(
            user,
            manual_review_ids,
            canonical_object,
            redshift_reviewed_records,
        )
        update_sf_function = canonical_function_map[canonical_object]

        if update_sf_function:
            update_sf_function(
                bucket,
                directory,
                redshift_reviewed_records,
                redshift_gainids,
                curr_timestamp,
                True,
                manual_review_ids,
            )
    elif canonical_object == 'Surgery':
        pass  # TBD later
    else:
        raise integration.exceptions.IntegrationInternalBadRequestException(
            logger_config.AuditLog(
                is_error=True,
                exception_message='Canonical object not found => data loading skipped',
            )
        )


def remove_salesforce_notfounds(
    test: bool,
    object_name: str,
    ids_map: dict[str, str],
    manual_ids: list[str],
    sf_ids: list[str],
) -> None:
    salesforce_operations.authenticate_or_fail_salesforce(test)

    batch_size = 500
    batches = []

    for i in range(0, len(sf_ids), batch_size):
        batches.append(sf_ids[i : i + batch_size])
    for i, bulk in enumerate(batches):
        found_records = salesforce_operations.verify_records_exists(
            object_name, bulk
        )
        found_records = [record['Id'] for record in found_records]
        not_found = [
            searched_id
            for searched_id in bulk
            if searched_id not in found_records
        ]

        manual_ids = []
        for searched_id in not_found:
            manual_ids += ids_map[searched_id]

        data_review_logger.update_manual_review_data_mark_completed(
            manual_ids, not_found
        )
        print(f'Removed {not_found}, {manual_ids}')


def post_upsert_update(test: bool, all_data: bool) -> None:
    '''
    Get Canonical from Redshift, delete from Salesforce
    '''
    timestamp = datetime.datetime.now()
    context.request_context.set(
        {'route': 'Redshift_To_Salesforce_Post_Upsert_Update'}
    )
    logger.info('Starting RS -> SF Update Post Upsert Update arm')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if isinstance(
        last_run_time, int
    ):  # only problem is if all_data=False i.e. delta changes but las run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for route missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    curr_timestamp = datetime.datetime.now()
    bucket, directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["Salesforce"]["S3"]["sub_directory"]}/{datetime.date.today()}',
    )
    auth_resp = salesforce_operations.authenticate_salesforce(test)
    if not auth_resp:
        logger.error('Salesforce authentication failed => Update skipped')
        return
    logger.info('SF authentication successful')
    logger.info('Starting RS -> SF Post Upsert Update')
    redshift_sources = aws_operations.get_postgres_sources()
    sf_accounts_data = salesforce_operations.get_sf_accounts_data(
        redshift_sources
    )
    sf_accounts_ids = list(sf_accounts_data['root_name_id_map'].values())
    redshift_cases_data = aws_operations.query_postgres(
        ['cases'], timestamp=last_run_time
    )
    redshift_cases_data_ids = local_operations.get_canonical_ids_from_df(
        redshift_cases_data['cases']
    )
    redshift_cases_data_mappings, _ = aws_operations.query_postgres_sf_map(
        ['cases'], id_subset=redshift_cases_data_ids
    )
    if (
        'cases' in redshift_cases_data
        and len(redshift_cases_data['cases']) > 0
    ):
        salesforce_account_opportunity_relation_df = (
            salesforce_operations.get_sf_account_opportunity_relation(
                redshift_cases_data_mappings,
                sf_accounts_ids,
            )
        )
        updated_salesforce_account_opportunity_relation_df = salesforce_local_operations.redshift_data_to_salesforce_data_accountopportunityrelation(
            redshift_cases_data,
            redshift_cases_data_mappings,
            salesforce_account_opportunity_relation_df,
        )
        salesforce_operations.update_sf_account_opportunity_relation(
            bucket,
            directory,
            updated_salesforce_account_opportunity_relation_df,
            curr_timestamp,
            salesforce_account_opportunity_relation_df,
        )
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    logger.info('Finishing RS -> SF Post Upsert Update arm')
    return


def get_object_fields(test: bool, object_name: str) -> dict[str, str]:
    salesforce_operations.authenticate_or_fail_salesforce(test)

    return salesforce_operations.get_object_fields(object_name)
