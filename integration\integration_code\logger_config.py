import dataclasses
import datetime
import logging
import os
import typing

import django.conf
import django.db
from opensearchpy import OpenSearch

from .. import models
from . import context

logging.getLogger("opensearch").disabled = True

app_config = django.conf.settings
settings = app_config.SETTINGS


@dataclasses.dataclass
class AuditLog:
    route_section: str = 'Function not passed'
    additional_details: typing.Optional[str] = None
    is_error: int = 0
    exception_message: typing.Optional[str] = None


def reset_db_connection():
    django.db.close_old_connections()


def audit_log(
    route_section: str = 'Function not passed',
    additional_details: typing.Optional[str] = None,
    is_error: int = 0,
    exception_message: typing.Optional[str | Exception] = None,
):
    request_context = context.request_context.get()
    # Close the current database connection to force a new one on the next query
    reset_db_connection()
    route = request_context.get('route')
    assert route is not None, 'Route not passed'
    with django.db.connection.cursor() as _:
        audit_log_entry = models.audit_log(
            route=route,
            route_section=route_section,
            additional_details=additional_details,
            is_error=is_error,
            exception_message=exception_message,
        )
        audit_log_entry.save()


class AuditDBHandler(logging.Handler):
    def emit(self, record: logging.LogRecord):
        try:
            route_section = (
                getattr(record, "route_section", None)
                or record.module + ' | ' + record.funcName
            )
            additional_details = getattr(record, "additional_details", None)
            is_error = 1 if record.levelname == 'ERROR' else 0
            exception_message = record.message

            audit_log(
                route_section=route_section,
                additional_details=additional_details,
                is_error=is_error,
                exception_message=exception_message,
            )

        except Exception:
            self.handleError(record)


class OpenSearchHandler(logging.Handler):
    """
    A logging handler that sends log records to OpenSearch.
    """

    def __init__(
        self,
        hosts: typing.Optional[typing.List[str]] = None,
        index_name: str = "application-logs",
        use_ssl: bool = True,
        verify_certs: bool = True,
        **kwargs: typing.Any,
    ):
        super().__init__()

        # Configure OpenSearch client
        client_config = {
            'hosts': hosts,
            'use_ssl': use_ssl,
            'verify_certs': verify_certs,
            'ssl_show_warn': False,
        }

        # Add any additional configuration
        client_config.update(kwargs)

        try:
            self.client = OpenSearch(**client_config)
            self.index_name = index_name
        except Exception as e:
            # If OpenSearch is not available, disable this handler
            self.client = None
            print(f"Warning: OpenSearch connection failed: {e}")

    def emit(self, record: logging.LogRecord):
        """
        Emit a log record to OpenSearch.
        """
        if self.client is None:
            return

        if record.name == 'opensearch':
            return

        try:
            # Get request context if available
            request_context = context.request_context.get()

            # Create the log document
            log_doc = {
                '@timestamp': datetime.datetime.now(
                    datetime.timezone.utc
                ).isoformat(),
                'level': record.levelname,
                'logger': record.name,
                'message': record.getMessage(),
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno,
                'pathname': record.pathname,
                'thread': record.thread,
                'thread_name': record.threadName,
                'process': record.process,
            }

            # Add request context if available
            if request_context:
                log_doc['route'] = request_context.get('route')
                log_doc['request_id'] = request_context.get('request_id')

            # Add custom attributes from the log record
            route_section = getattr(record, "route_section", None)
            if route_section:
                log_doc['route_section'] = route_section

            additional_details = getattr(record, "additional_details", None)
            if additional_details:
                log_doc['additional_details'] = additional_details

            # Add exception information if present
            if record.exc_info:
                log_doc['exception'] = {
                    'type': (
                        record.exc_info[0].__name__
                        if record.exc_info[0]
                        else None
                    ),
                    'message': (
                        str(record.exc_info[1]) if record.exc_info[1] else None
                    ),
                    'traceback': (
                        self.format(record) if self.formatter else None
                    ),
                }

            # Add stack trace if present
            if record.stack_info:
                log_doc['stack_info'] = record.stack_info

            # Generate date-based index name: IndexName-yyyy.MM.dd
            current_date = datetime.datetime.now(datetime.timezone.utc)
            date_suffix = current_date.strftime("%Y.%m.%d")
            dynamic_index_name = f"{self.index_name}-{date_suffix}"

            # Index the document
            self.client.index(
                index=dynamic_index_name,
                body=log_doc,
                refresh=False,  # Don't force refresh for performance
            )

        except Exception:
            self.handleError(record)


log_level = os.getenv("LOG_LEVEL", "INFO").upper()
log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
date_format = "%Y-%m-%d %H:%M:%S"

formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

# App Logger
app_logger = logging.getLogger()
app_logger.setLevel(log_level)


# Console Handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
console_handler.setLevel(log_level)
app_logger.addHandler(console_handler)

# DB Handler
audit_db_handler = AuditDBHandler()
audit_db_handler.setLevel(log_level)
app_logger.addHandler(audit_db_handler)

# Opensearch Handler
opensearch_enabled = settings['Opensearch']['Enabled'].lower() == 'true'
if opensearch_enabled:
    opensearch_hosts = settings['Opensearch']['Hosts'].split(",")
    opensearch_index = settings['Opensearch']['IndexName']

    opensearch_handler = OpenSearchHandler(
        hosts=opensearch_hosts,
        index_name=opensearch_index,
        use_ssl=True,
        verify_certs=True,
    )
    opensearch_handler.setLevel(log_level)
    opensearch_handler.setFormatter(formatter)
    app_logger.addHandler(opensearch_handler)


def get_logger(name: str) -> logging.Logger:
    return logging.getLogger(name)
