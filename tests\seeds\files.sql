INSERT INTO files (
    plaintiffid, 
    url, 
    type, 
    caseid, 
    relevanttogain, 
    sourcecreatedatetime, 
    sourcemodifieddatetime, 
    modifieddatetime, 
    todelete, 
    todeletesystem, 
    deletepreventoverride, 
    deletepreventoverridereason, 
    createdatetime, 
    gainid
) VALUES (
    '0bf956f1b7214de7', 
    's3://gain-servicing/integration/filevine/hostilo/2024-09-19/files/346807174_039 - <PERSON><PERSON><PERSON> Chiropractic Paid in FULL 2023-09-30 1334.docx', 
    'Other', 
    '04b1ace113fe41b1', 
    'true', 
    NULL, 
    NULL, 
    '2024-09-19 12:58:41.561013', 
    'false', 
    '', 
    'false', 
    '', 
    NULL, 
    '5a1195e3b8fc4a7c'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
) VALUES (
    '5a1195e3b8fc4a7c',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '5a1195e3b8fc4a7c',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'files'
);

INSERT INTO files (
    plaintiffid, 
    url,
    type,
    caseid,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    '0bf956f1b7214de7', 
    's3://gain-servicing/integration/filevine/hostilo/2024-09-19/files/346807174_039 - Pidcock Chiropractic Paid in FULL 2023-09-30 1334.docx', 
    'Other', 
    '04b1ace113fe41b3',
    false,
    true,
    '5a1195e3b8fc4a7d'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '5a1195e3b8fc4a7d',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '5a1195e3b8fc4a7d',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_files_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'files'
);