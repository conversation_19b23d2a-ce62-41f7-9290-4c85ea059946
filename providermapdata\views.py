# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning
from datetime import datetime

from django.shortcuts import render
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .data_update.combine_allproviders import combine_all_providers


@api_view(['GET'])
def providerMapData_view(request):
    return render(request, 'providermap/providermap.html')
    content = "This is the home page for providers map data processing!"
    return Response(content)


@api_view(['GET', 'PUT', 'POST'])
@permission_classes([IsAuthenticated])
def providerMapData_update(request):
    print("Loading...")
    # render(request, 'providermap_refreshing.html')
    combine_all_providers(request)
    content = "The providers map data has been updated on {}".format(
        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    # return Response(content)
    return render(
        request,
        'providermap/providermap.html',
        {
            'RefreshingTime': content,
            'DataShape': "(100, 20)",
            'OverallShape': "(200, 40)",
        },
    )


@api_view(['GET', 'PUT', 'POST'])
@permission_classes([IsAuthenticated])
def providerMapData_process(request):
    combine_all_providers(request)
    content = "The providers map data has been updated on {}!".format(
        datetime.now()
    )
    return render(
        request,
        'providermap/providermap_refreshing.html',
        {
            'RefreshingTime': content,
            'DataShape': "(100, 20)",
            'OverallShape': "(200, 40)",
        },
    )
