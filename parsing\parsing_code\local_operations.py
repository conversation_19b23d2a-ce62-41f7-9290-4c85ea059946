# pyright: reportCallIssue=warning,reportArgumentType=warning
import datetime
import glob
import pathlib


def aws_name(file: str):
    aws_file = file
    if '/' in file:
        aws_file = file.split('/')[-1]
    return aws_file


def clean_up(file: str):
    files = glob.glob(f'*{file[:-4]}*')
    for file in files:
        pathlib.Path(file).unlink(missing_ok=True)
    return


def date_clean(date: str):
    if date == '':
        return date
    date = list(filter(str.isdigit, date))
    if len(date) < 6:
        return ''
    date = (''.join(date).strip().split())[0]
    if len(date) != 8:
        if len(date) > 6:
            date = date[-6:]
        if len(date) == 6:
            yy = int(date[-2:])
            if yy > (datetime.date.today().year) % 100:
                date = date[:4] + '19' + date[-2:]
            else:
                date = date[:4] + '20' + date[-2:]
    date = f'{date[:2]}-{date[2:4]}-{date[4:]}'
    return date


def cpt_clean(cpt: str):
    cpt = ''.join(filter(str.isalnum, cpt))
    if len(cpt) < 5:
        return ''
    if len(cpt) > 5:
        cpt = cpt[-5:]
    return cpt


def amount_clean(amount: str):
    if 'O)' in amount:
        amount = amount.replace('O)', '9')
    amount = ''.join(filter(str.isdigit, amount))
    if amount == '0' or amount == '00':
        return '0.00'
    if len(amount) < 2:
        return ''
    elif len(amount) == 2:
        return f'0.{amount}'
    else:
        return f'{amount[:-2]}.{amount[-2:]}'


def date_transform(dos: str):
    i, dates_of_service = 0, {}
    while len(dos) > 3:
        service = {}
        serviced = dos[:3]
        service['date'] = date_clean(serviced[0])
        service['cpt'] = cpt_clean(serviced[1])
        service['amount'] = amount_clean(serviced[2])
        dates_of_service[f'dos {i+1}'] = service
        dos = dos[3:]
        i += 1
    return dates_of_service, dos


def account_clean(account: str):
    account = account.replace(' ', '')
    if account.startswith('26'):
        account = account[2:]
    # if len(account) > 12:
    #    account = account[:6] + '-' + account[6:]
    return account


def totals_transform(dos: str):
    totals = {}
    if dos[1].endswith('2'):
        dos[1] = dos[1][:-1]
    if dos[1].endswith('29'):
        dos[1] = dos[1][:-2]
    if dos[2].endswith('30'):
        dos[2] = dos[2][:-2]
    totals['account'], totals['total'], totals['paid'] = (
        account_clean(dos[0]),
        amount_clean(dos[1]),
        amount_clean(dos[2]),
    )
    return totals


def date_validate(date: str):
    f = 0
    mon, day, cen = date[:2], date[3:5], date[-4:-2]
    # print(f'mon {mon}; day {day}; cen {cen}')
    if int(mon) > 12:
        f = 1
    if int(day) > 31:
        f = 1
    if not (cen == '19' or cen == '20'):
        f = 1
    return f


def fix_patient(patient: dict[str, list[dict[str, str]]]):
    '''
    This function is to fix the patient object based on assumptions made; do not use this function if assumptions are going wrong by chance
    '''
    # DOS fix; assumption is that if all filled DOS are the same, then missing DOS will be that date too
    dos_dates = set(
        filter(None, [patient['dos'][i]['date'] for i in patient['dos']])
    )
    if len(dos_dates) == 1:
        for i in patient['dos']:
            temp = patient['dos'][i]
            if temp['cpt'] != '' or temp['amount'] != '':
                if temp['date'] == '':
                    temp['date'] = dos_dates.pop()
                    dos_dates.add(temp['date'])
    return patient


def validate_patient(patient: dict[str, list[dict[str, str]]]):
    try:
        f = 0
        amount = 0
        if len(patient['dob']) != 10:
            f = 1
            return f
        if len(patient['dob']) == 10:
            f = date_validate(patient['dob'])
            if f == 1:
                return f
        for i in patient['dos']:
            temp = patient['dos'][i]
            if temp['date'] != '' or temp['cpt'] != '' or temp['amount'] != '':
                if (
                    temp['date'] == ''
                    or temp['cpt'] == ''
                    or temp['amount'] == ''
                ):
                    f = 1
                    return f
            if len(temp['date']) > 0 and len(temp['date']) != 10:
                f = 1
                return f
            if len(temp['date']) == 10:
                f = date_validate(temp['date'])
                if f == 1:
                    return f
            if temp['amount'] != '':
                amount += float(temp['amount'])
        if amount != float(patient['totals']['total']):
            f = 1
            return f
    except:
        f = 1
    return f


def date_to_sf(date: str):
    parts = date.split('-')
    parts.insert(0, parts.pop())
    sf_date = '-'.join(parts)
    return sf_date


def date_sf_transform(patient: dict[str, str]):
    patient['dob'] = date_to_sf(patient['dob'])
    for i in range(6):
        patient[f'date_{i+1}'] = date_to_sf(patient[f'date_{i+1}'])
    return patient


def portal_transform(patient: dict[str, str]):
    i = 0
    for dos in patient['dos']:
        patient[f'date_{i+1}'] = patient['dos'][dos]['date']
        patient[f'cpt_{i+1}'] = patient['dos'][dos]['cpt']
        patient[f'amount_{i+1}'] = patient['dos'][dos]['amount']
        i += 1
    del patient['dos']
    patient['account'] = patient['totals']['account']
    patient['total'] = patient['totals']['total']
    patient['paid'] = patient['totals']['paid']
    del patient['totals']
    patient = date_sf_transform(patient)
    return patient
