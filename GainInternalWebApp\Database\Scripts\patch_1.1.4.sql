DROP TABLE IF EXISTS integration.dev.billings_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.cases_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.lawfirms_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.plaintiffs_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.transactions_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.files_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.charges_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.insurances_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.medicalfacilities_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.intakes_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.liens_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.legalpersonnel_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.surgery_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.dev.disbursals_insertdata_map CASCADE;

-- /integration/ati/withdraw path views
DROP VIEW IF EXISTS integration.dev.cases_with_todelete_billings_status;
CREATE
OR REPLACE VIEW integration.dev.cases_with_todelete_billings_status AS
SELECT
   DISTINCT cases.gainid,
   billings.todelete
FROM
   integration.dev.cases cases
   JOIN integration.dev.billings billings ON billings.caseid:: text = cases.gainid:: text;

DROP VIEW IF EXISTS integration.dev.plaintiffs_with_todelete_cases_status;
CREATE
OR REPLACE VIEW integration.dev.plaintiffs_with_todelete_cases_status AS
SELECT
   DISTINCT plaintiffs.gainid,
   cases.todelete
FROM
   integration.dev.plaintiffs plaintiffs
   JOIN integration.dev.cases cases ON cases.plaintiffid:: text = plaintiffs.gainid:: text;


DROP VIEW IF EXISTS integration.dev.billings_with_todelete_charges_status;
CREATE
OR REPLACE VIEW integration.dev.billings_with_todelete_charges_status AS
SELECT
   DISTINCT billings.gainid,
   charges.todelete
FROM
   integration.dev.billings billings
   JOIN integration.dev.charges charges ON charges.billingid:: text = billings.gainid:: text;


-- /integration/ati/update_settled path views
DROP VIEW IF EXISTS integration.dev.cases_with_settled_billings_status;
CREATE
OR REPLACE VIEW integration.dev.cases_with_settled_billings_status AS
SELECT
   DISTINCT cases.gainid,
   billings.status
FROM
   integration.dev.cases cases
   JOIN integration.dev.billings billings ON billings.caseid:: text = cases.gainid:: text
WHERE
   billings.todelete = false;





DROP TABLE IF EXISTS integration.staging.billings_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.cases_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.lawfirms_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.plaintiffs_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.transactions_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.files_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.charges_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.insurances_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.medicalfacilities_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.intakes_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.liens_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.legalpersonnel_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.surgery_insertdata_map CASCADE;
DROP TABLE IF EXISTS integration.staging.disbursals_insertdata_map CASCADE;

-- /integration/ati/withdraw path views
DROP VIEW IF EXISTS integration.staging.cases_with_todelete_billings_status;
CREATE
OR REPLACE VIEW integration.staging.cases_with_todelete_billings_status AS
SELECT
   DISTINCT cases.gainid,
   billings.todelete
FROM
   integration.staging.cases cases
   JOIN integration.staging.billings billings ON billings.caseid:: text = cases.gainid:: text;

DROP VIEW IF EXISTS integration.staging.plaintiffs_with_todelete_cases_status;
CREATE
OR REPLACE VIEW integration.staging.plaintiffs_with_todelete_cases_status AS
SELECT
   DISTINCT plaintiffs.gainid,
   cases.todelete
FROM
   integration.staging.plaintiffs plaintiffs
   JOIN integration.staging.cases cases ON cases.plaintiffid:: text = plaintiffs.gainid:: text;


DROP VIEW IF EXISTS integration.staging.billings_with_todelete_charges_status;
CREATE
OR REPLACE VIEW integration.staging.billings_with_todelete_charges_status AS
SELECT
   DISTINCT billings.gainid,
   charges.todelete
FROM
   integration.staging.billings billings
   JOIN integration.staging.charges charges ON charges.billingid:: text = billings.gainid:: text;


-- /integration/ati/update_settled path views
DROP VIEW IF EXISTS integration.staging.cases_with_settled_billings_status;
CREATE
OR REPLACE VIEW integration.staging.cases_with_settled_billings_status AS
SELECT
   DISTINCT cases.gainid,
   billings.status
FROM
   integration.staging.cases cases
   JOIN integration.staging.billings billings ON billings.caseid:: text = cases.gainid:: text
WHERE
   billings.todelete = false;