CREATE TABLE plaintiffs (
    name character varying(250),
    dateofbirth date,
    ssn character varying(10),
    taxid character varying(10),
    driverlicense character varying(20),
    gender character varying(1),
    maritalstatus character varying(100),
    company character varying(100),
    homephone character varying(100),
    cellphone character varying(100),
    businessphone character varying(100),
    otherphone character varying(100),
    primaryemail character varying(100),
    secondaryemail character varying(100),
    primaryaddressline1 character varying(250),
    primaryaddressline2 character varying(250),
    primaryaddresscity character varying(100),
    primaryaddressstate character varying(100),
    primaryaddresszip character varying(20),
    otheraddressline1 character varying(250),
    otheraddressline2 character varying(250),
    otheraddresscity character varying(100),
    otheraddressstate character varying(100),
    otheraddresszip character varying(20),
    departmentid character varying(5),
    relevanttogain boolean,
    sourcecreatedatetime timestamp without time zone,
    sourcemodifieddatetime timestamp without time zone,
    modifieddatetime timestamp without time zone,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    createdatetime timestamp without time zone,
    gainid character varying(16) NOT NULL DEFAULT '':: character varying,
    PRIMARY KEY (gainid)
);