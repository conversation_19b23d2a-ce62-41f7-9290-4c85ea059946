import typing

import psycopg
import pytest


class TestGetMainDelete:
    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "processed_data", [{"source": "ati"}], indirect=True
    )
    def test_should_get_main_delete(
        self,
        processed_data: dict[str, typing.Any],
        database: psycopg.Connection[typing.Any],
    ):
        """Test that the main delete function correctly processes ATI data."""
        import integration.integration_code.ati as ati

        ati.get_main_delete(
            move_flag=True,
            test=True,
            all_data=True,
        )
