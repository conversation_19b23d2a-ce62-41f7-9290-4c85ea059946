import typing

import rest_framework.decorators
import rest_framework.permissions
import rest_framework.request
import rest_framework.response

# Create your views here.


def lower_case_dict_keys(
    input_dict: dict[str, typing.Any],
) -> dict[str, typing.Any]:
    '''Convert all keys in a dictionary to lowercase.'''
    return {key.lower(): value for key, value in input_dict.items()}


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def case_value_single(
    request: rest_framework.request.Request,
) -> rest_framework.response.Response:
    '''POST request to predict the case value for a single case.'''
    from .predictive_analytics_code import case_value_prediction

    input_data: dict = request.data  # type: ignore
    # Generate a dummy dictionary with one key to
    # match the input format for case_value_prediction main function
    input_dict = {'dummy': lower_case_dict_keys(input_data)}
    try:
        case_values_predicted = case_value_prediction.case_value(input_dict)
        single_prediction = case_values_predicted['dummy']
        return rest_framework.response.Response(single_prediction, 200)
    except Exception as e:
        return rest_framework.response.Response(str(e), 500)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def case_value_multiple(
    request: rest_framework.request.Request,
) -> rest_framework.response.Response:
    '''POST request to predict the case value for multiple cases.'''
    from .predictive_analytics_code import case_value_prediction

    input_data: dict[str, dict] = request.data  # type: ignore
    input_dict = {k: lower_case_dict_keys(v) for k, v in input_data.items()}
    try:
        case_values_predicted = case_value_prediction.case_value(input_dict)
        return rest_framework.response.Response(case_values_predicted, 200)
    except Exception as e:
        return rest_framework.response.Response(str(e), 500)
