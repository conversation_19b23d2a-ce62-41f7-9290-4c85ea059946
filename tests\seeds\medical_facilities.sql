

INSERT INTO medicalfacilities (
    gainid,
    name,
    phone,
    fax,
    email,
    followupemail,
    billingaddressline1,
    billingaddressline2,
    billingaddresscity,
    billingaddressstate,
    billingaddresszip,
    physicaladdressline1,
    physicaladdressline2,
    physicaladdresscity,
    physicaladdressstate,
    physicaladdresszip,
    website,
    contracttype,
    specialties,
    description,
    portalaccount,
    notes,
    parentid,
    relevanttogain,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    modifieddatetime
) VALUES (
    '0b89f4beff68d371',
    'ATI Physical Therapy',
    '************',
    '',
    '<EMAIL>',
    '',
    '790 Remington Blvd',
    '',
    'Bolingbrook',
    'Illinois',
    '60440',
    '790 Remington Blvd',
    '',
    'Bolingbrook',
    'Illinois',
    '60440',
    '',
    '',
    '',
    '',
    NULL,
    '',
    NULL,
    true,
    false,
    '',
    false,
    '',
    '2024-09-10 15:04:14.752991'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
) VALUES (
    '0b89f4beff68d371',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '0b89f4beff68d371',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'medicalfacilities'
);

INSERT INTO medicalfacilities (
    name,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    'Medical manual review',
    false,
    true,
    '0b89f4beff68d372'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '0b89f4beff68d372',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '0b89f4beff68d372',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_medical_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'medicalfacilities'
);


-- medical facilities to UpdateCharges
INSERT INTO medicalfacilities (
    gainid,
    name,
    phone,
    fax,
    email,
    followupemail,
    billingaddressline1,
    billingaddressline2,
    billingaddresscity,
    billingaddressstate,
    billingaddresszip,
    physicaladdressline1,
    physicaladdressline2,
    physicaladdresscity,
    physicaladdressstate,
    physicaladdresszip,
    website,
    contracttype,
    specialties,
    description,
    portalaccount,
    notes,
    parentid,
    relevanttogain,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    modifieddatetime
) VALUES (
    '0b82f4geff68d292', --gainid
    'ATI Physical Therapy', --name
    '************', --phone
    '', --fax
    '<EMAIL>', --email
    '', --followupemail
    '790 Remington Blvd', --billingaddressline1
    '', --billingaddressline2
    'Bolingbrook', --billingaddresscity
    'Illinois', --billingaddressstate
    '60440', --billingaddresszip
    '790 Remington Blvd', --physicaladdressline1
    '', --physicaladdressline2
    'Bolingbrook', --physicaladdresscity
    'Illinois', --physicaladdressstate
    '60440', --physicaladdresszip
    '', --website
    '', --contracttype
    '', --specialties
    '', --description
    NULL, --portalaccount
    '', --notes
    NULL, --parentid
    true, --relevanttogain
    false, --todelete
    '', --todeletesystem
    false, --deletepreventoverride
    '', --deletepreventoverridereason
    '2024-09-10 15:04:14.752991' --modifieddatetim
),

(
    '0f9e8d7c6b5a4321', --gainid
    'ATI Physical Therapy 2', --name
    '************', --phone
    '', --fax
    '<EMAIL>', --email
    '', --followupemail
    '790 Remington Blvd', --billingaddressline1
    '', --billingaddressline2
    'Bolingbrook', --billingaddresscity
    'Illinois', --billingaddressstate
    '60440', --billingaddresszip
    '790 Remington Blvd', --physicaladdressline1
    '', --physicaladdressline2
    'Bolingbrook', --physicaladdresscity
    'Illinois', --physicaladdressstate
    '60440', --physicaladdresszip
    '', --website
    '', --contracttype
    '', --specialties
    '', --description
    NULL, --portalaccount
    '', --notes
    NULL, --parentid
    true, --relevanttogain
    false, --todelete
    '', --todeletesystem
    false, --deletepreventoverride
    '', --deletepreventoverridereason
    '2024-09-10 15:04:14.752991' --modifieddatetim
),

(
    '6f7a8b9c0d1e2345', --gainid
    'ATI Physical Therapy 3', --name
    '************', --phone
    '', --fax
    '<EMAIL>', --email
    '', --followupemail
    '790 Remington Blvd', --billingaddressline1
    '', --billingaddressline2
    'Bolingbrook', --billingaddresscity
    'Illinois', --billingaddressstate
    '60440', --billingaddresszip
    '790 Remington Blvd', --physicaladdressline1
    '', --physicaladdressline2
    'Bolingbrook', --physicaladdresscity
    'Illinois', --physicaladdressstate
    '60440', --physicaladdresszip
    '', --website
    '', --contracttype
    '', --specialties
    '', --description
    NULL, --portalaccount
    '', --notes
    NULL, --parentid
    true, --relevanttogain
    false, --todelete
    '', --todeletesystem
    false, --deletepreventoverride
    '', --deletepreventoverridereason
    '2024-09-10 15:04:14.752991' --modifieddatetim
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
) VALUES (
    '0b82f4geff68d292',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '0b82f4geff68d292',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'medicalfacilities'
),
(
    '0f9e8d7c6b5a4321',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '0f9e8d7c6b5a4321',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'medicalfacilities'
),
(
    '6f7a8b9c0d1e2345',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '6f7a8b9c0d1e2345',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'medicalfacilities'
);

INSERT INTO medicalfacilities (
    gainid,
    name,
    phone,
    fax,
    email,
    followupemail,
    billingaddressline1,
    billingaddressline2,
    billingaddresscity,
    billingaddressstate,
    billingaddresszip,
    physicaladdressline1,
    physicaladdressline2,
    physicaladdresscity,
    physicaladdressstate,
    physicaladdresszip,
    website,
    contracttype,
    specialties,
    description,
    portalaccount,
    notes,
    parentid,
    relevanttogain,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    modifieddatetime
) VALUES (
    'testmedfac00001', -- gainid for testbill4case001
    'Test Medical Facility 1',
    '************',
    '',
    '<EMAIL>',
    '',
    '123 Test Blvd',
    '',
    'Testville',
    'California',
    '90210',
    '123 Test Blvd',
    '',
    'Testville',
    'California',
    '90210',
    '',
    '',
    '',
    '',
    NULL,
    '',
    NULL,
    true,
    false,
    '',
    false,
    '',
    '2024-09-10 15:04:14.752991'
),
(
    'testmedfac00002', -- gainid for testbill4case002
    'Test Medical Facility 2',
    '555-987-6543',
    '',
    '<EMAIL>',
    '',
    '456 Test Ave',
    '',
    'Testopolis',
    'New York',
    '10001',
    '456 Test Ave',
    '',
    'Testopolis',
    'New York',
    '10001',
    '',
    '',
    '',
    '',
    NULL,
    '',
    NULL,
    true,
    false,
    '',
    false,
    '',
    '2024-09-10 15:04:14.752991'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
) VALUES (
    'testmedfac00001',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'testmedfac00001',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'medicalfacilities'
),
(
    'testmedfac00002',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'testmedfac00002',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'medicalfacilities'
);
