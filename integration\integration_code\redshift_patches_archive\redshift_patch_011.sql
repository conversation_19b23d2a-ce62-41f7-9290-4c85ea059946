
/*

Add new tables to the integration database for the canonical model

*/

-- Medical Facilities

CREATE TABLE IF NOT EXISTS integration.dev.MedicalFacilities (
    GainId VARCHAR(16) PRIMARY KEY,
    Name VARCHAR(250) NOT NULL,
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    Website VARCHAR(100),
    ContractType VARCHAR(100),
    Specialties VARCHAR(1000),
    Description VARCHAR(1000),
    PortalAccount BOOLEAN,
    Notes VARCHAR(1000),
    ParentId VARCHAR(100) REFERENCES integration.dev.MedicalFacilities(GainId) DEFAULT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.staging.MedicalFacilities (
    GainId VARCHAR(16) PRIMARY KEY,
    Name VARCHAR(250) NOT NULL,
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    Website VARCHAR(100),
    ContractType VARCHAR(100),
    Specialties VARCHAR(1000),
    Description VARCHAR(1000),
    PortalAccount BOOLEAN,
    Notes VARCHAR(1000),
    ParentId VARCHAR(100) REFERENCES integration.staging.MedicalFacilities(GainId) DEFAULT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.prod.MedicalFacilities (
    GainId VARCHAR(16) PRIMARY KEY,
    Name VARCHAR(250) NOT NULL,
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    Website VARCHAR(100),
    ContractType VARCHAR(100),
    Specialties VARCHAR(1000),
    Description VARCHAR(1000),
    PortalAccount BOOLEAN,
    Notes VARCHAR(1000),
    ParentId VARCHAR(100) REFERENCES integration.prod.MedicalFacilities(GainId) DEFAULT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.main.MedicalFacilities (
    GainId VARCHAR(16) PRIMARY KEY,
    Name VARCHAR(250) NOT NULL,
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    Website VARCHAR(100),
    ContractType VARCHAR(100),
    Specialties VARCHAR(1000),
    Description VARCHAR(1000),
    PortalAccount BOOLEAN,
    Notes VARCHAR(1000),
    ParentId VARCHAR(100) REFERENCES integration.main.MedicalFacilities(GainId) DEFAULT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);


-- Intakes

CREATE TABLE IF NOT EXISTS integration.dev.Intakes (
    GainId VARCHAR(16) PRIMARY KEY,
    AccidentDescription VARCHAR(1000),
    AccidentDateTime TIMESTAMP,
    WereSFPremisePicsTaken BOOLEAN,
    WasPoliceCalled BOOLEAN,
    WasPoliceReportGenerated BOOLEAN,
    WereCitationsIssued BOOLEAN,
    ClientsVehicleMakeModel VARCHAR(100),
    ClientsVehicleDamage VARCHAR(1000),
    DefendantsVehicleMakeModel VARCHAR(100),
    DefendantsVehicleDamage VARCHAR(1000),
    WereAutoDamagePicsTaken BOOLEAN,
    WasVehicleTowed BOOLEAN,
    VehicleDamageEstimate DECIMAL(10,4),
    InjuriesDescription VARCHAR(1000),
    WereInjuryPicsTaken BOOLEAN,
    PriorInjuries VARCHAR(1000),
    ClientJobTitle VARCHAR(100),
    ClientEmploymentStatus VARCHAR(100),
    ClientLostWagesStart TIMESTAMP,
    ClientJobDuties VARCHAR(1000),
    ClientLostWagesEnd TIMESTAMP,
    ClientsHourlySalaryRate DECIMAL(10,4),
    ClientBankruptcy BOOLEAN,
    ThingsClientCanNoLongerDo VARCHAR(1000),
    ThingsClientCanDoWithPain VARCHAR(1000),
    ClientsLossOfEnjoyment VARCHAR(1000),
    ClientHealthHistory VARCHAR(1000),
    ClientSubsequentAccidents BOOLEAN,
    ClientSubsequentAccidentsExplanation VARCHAR(1000),
    ClientObservations VARCHAR(1000),
    ClientOnSocialMedia BOOLEAN,
    ClientSocialMediaNotes VARCHAR(1000),
    DoesClientHaveHealthInsurance BOOLEAN,
    DoesClientHaveSecondHealthInsurance BOOLEAN,
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.dev.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.staging.Intakes (
    GainId VARCHAR(16) PRIMARY KEY,
    AccidentDescription VARCHAR(1000),
    AccidentDateTime TIMESTAMP,
    WereSFPremisePicsTaken BOOLEAN,
    WasPoliceCalled BOOLEAN,
    WasPoliceReportGenerated BOOLEAN,
    WereCitationsIssued BOOLEAN,
    ClientsVehicleMakeModel VARCHAR(100),
    ClientsVehicleDamage VARCHAR(1000),
    DefendantsVehicleMakeModel VARCHAR(100),
    DefendantsVehicleDamage VARCHAR(1000),
    WereAutoDamagePicsTaken BOOLEAN,
    WasVehicleTowed BOOLEAN,
    VehicleDamageEstimate DECIMAL(10,4),
    InjuriesDescription VARCHAR(1000),
    WereInjuryPicsTaken BOOLEAN,
    PriorInjuries VARCHAR(1000),
    ClientJobTitle VARCHAR(100),
    ClientEmploymentStatus VARCHAR(100),
    ClientLostWagesStart TIMESTAMP,
    ClientJobDuties VARCHAR(1000),
    ClientLostWagesEnd TIMESTAMP,
    ClientsHourlySalaryRate DECIMAL(10,4),
    ClientBankruptcy BOOLEAN,
    ThingsClientCanNoLongerDo VARCHAR(1000),
    ThingsClientCanDoWithPain VARCHAR(1000),
    ClientsLossOfEnjoyment VARCHAR(1000),
    ClientHealthHistory VARCHAR(1000),
    ClientSubsequentAccidents BOOLEAN,
    ClientSubsequentAccidentsExplanation VARCHAR(1000),
    ClientObservations VARCHAR(1000),
    ClientOnSocialMedia BOOLEAN,
    ClientSocialMediaNotes VARCHAR(1000),
    DoesClientHaveHealthInsurance BOOLEAN,
    DoesClientHaveSecondHealthInsurance BOOLEAN,
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.staging.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.prod.Intakes (
    GainId VARCHAR(16) PRIMARY KEY,
    AccidentDescription VARCHAR(1000),
    AccidentDateTime TIMESTAMP,
    WereSFPremisePicsTaken BOOLEAN,
    WasPoliceCalled BOOLEAN,
    WasPoliceReportGenerated BOOLEAN,
    WereCitationsIssued BOOLEAN,
    ClientsVehicleMakeModel VARCHAR(100),
    ClientsVehicleDamage VARCHAR(1000),
    DefendantsVehicleMakeModel VARCHAR(100),
    DefendantsVehicleDamage VARCHAR(1000),
    WereAutoDamagePicsTaken BOOLEAN,
    WasVehicleTowed BOOLEAN,
    VehicleDamageEstimate DECIMAL(10,4),
    InjuriesDescription VARCHAR(1000),
    WereInjuryPicsTaken BOOLEAN,
    PriorInjuries VARCHAR(1000),
    ClientJobTitle VARCHAR(100),
    ClientEmploymentStatus VARCHAR(100),
    ClientLostWagesStart TIMESTAMP,
    ClientJobDuties VARCHAR(1000),
    ClientLostWagesEnd TIMESTAMP,
    ClientsHourlySalaryRate DECIMAL(10,4),
    ClientBankruptcy BOOLEAN,
    ThingsClientCanNoLongerDo VARCHAR(1000),
    ThingsClientCanDoWithPain VARCHAR(1000),
    ClientsLossOfEnjoyment VARCHAR(1000),
    ClientHealthHistory VARCHAR(1000),
    ClientSubsequentAccidents BOOLEAN,
    ClientSubsequentAccidentsExplanation VARCHAR(1000),
    ClientObservations VARCHAR(1000),
    ClientOnSocialMedia BOOLEAN,
    ClientSocialMediaNotes VARCHAR(1000),
    DoesClientHaveHealthInsurance BOOLEAN,
    DoesClientHaveSecondHealthInsurance BOOLEAN,
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.prod.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.main.Intakes (
    GainId VARCHAR(16) PRIMARY KEY,
    AccidentDescription VARCHAR(1000),
    AccidentDateTime TIMESTAMP,
    WereSFPremisePicsTaken BOOLEAN,
    WasPoliceCalled BOOLEAN,
    WasPoliceReportGenerated BOOLEAN,
    WereCitationsIssued BOOLEAN,
    ClientsVehicleMakeModel VARCHAR(100),
    ClientsVehicleDamage VARCHAR(1000),
    DefendantsVehicleMakeModel VARCHAR(100),
    DefendantsVehicleDamage VARCHAR(1000),
    WereAutoDamagePicsTaken BOOLEAN,
    WasVehicleTowed BOOLEAN,
    VehicleDamageEstimate DECIMAL(10,4),
    InjuriesDescription VARCHAR(1000),
    WereInjuryPicsTaken BOOLEAN,
    PriorInjuries VARCHAR(1000),
    ClientJobTitle VARCHAR(100),
    ClientEmploymentStatus VARCHAR(100),
    ClientLostWagesStart TIMESTAMP,
    ClientJobDuties VARCHAR(1000),
    ClientLostWagesEnd TIMESTAMP,
    ClientsHourlySalaryRate DECIMAL(10,4),
    ClientBankruptcy BOOLEAN,
    ThingsClientCanNoLongerDo VARCHAR(1000),
    ThingsClientCanDoWithPain VARCHAR(1000),
    ClientsLossOfEnjoyment VARCHAR(1000),
    ClientHealthHistory VARCHAR(1000),
    ClientSubsequentAccidents BOOLEAN,
    ClientSubsequentAccidentsExplanation VARCHAR(1000),
    ClientObservations VARCHAR(1000),
    ClientOnSocialMedia BOOLEAN,
    ClientSocialMediaNotes VARCHAR(1000),
    DoesClientHaveHealthInsurance BOOLEAN,
    DoesClientHaveSecondHealthInsurance BOOLEAN,
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.main.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);


-- Insurances

CREATE TABLE IF NOT EXISTS integration.dev.Insurances (
    GainId VARCHAR(16) PRIMARY KEY,
    CompanyName VARCHAR(250),
    PolicyId VARCHAR(20),
    MemberId VARCHAR(20),
    GroupNumber VARCHAR(20),
    Status VARCHAR(100),
    Limits VARCHAR(100),
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    LiabilityAccepted BOOLEAN,
    DeclarationPageReceived BOOLEAN,
    MedpayExhausted BOOLEAN,
    PIPExhausted BOOLEAN,
    Probate BOOLEAN,
    Bankruptcy BOOLEAN,
    SubrogationLien BOOLEAN,
    OtherLien BOOLEAN,
    OtherLienName VARCHAR(100),
    DriverName VARCHAR(100),
    DateSettled TIMESTAMP,
    HowCaseWasSettled VARCHAR(100),
    TotalSettlementAmount DECIMAL(10,4),
    BillsPaid DECIMAL(10,4),
    AttorneyFee DECIMAL(10,4),
    AttorneyFeeFlexible BOOLEAN,
    ReferralFeePercentage DECIMAL(10,4),
    AmountToClient DECIMAL(10,4),
    SettlementNotes VARCHAR(1000),
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.dev.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.staging.Insurances (
    GainId VARCHAR(16) PRIMARY KEY,
    CompanyName VARCHAR(250),
    PolicyId VARCHAR(20),
    MemberId VARCHAR(20),
    GroupNumber VARCHAR(20),
    Status VARCHAR(100),
    Limits VARCHAR(100),
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    LiabilityAccepted BOOLEAN,
    DeclarationPageReceived BOOLEAN,
    MedpayExhausted BOOLEAN,
    PIPExhausted BOOLEAN,
    Probate BOOLEAN,
    Bankruptcy BOOLEAN,
    SubrogationLien BOOLEAN,
    OtherLien BOOLEAN,
    OtherLienName VARCHAR(100),
    DriverName VARCHAR(100),
    DateSettled TIMESTAMP,
    HowCaseWasSettled VARCHAR(100),
    TotalSettlementAmount DECIMAL(10,4),
    BillsPaid DECIMAL(10,4),
    AttorneyFee DECIMAL(10,4),
    AttorneyFeeFlexible BOOLEAN,
    ReferralFeePercentage DECIMAL(10,4),
    AmountToClient DECIMAL(10,4),
    SettlementNotes VARCHAR(1000),
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.staging.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.prod.Insurances (
    GainId VARCHAR(16) PRIMARY KEY,
    CompanyName VARCHAR(250),
    PolicyId VARCHAR(20),
    MemberId VARCHAR(20),
    GroupNumber VARCHAR(20),
    Status VARCHAR(100),
    Limits VARCHAR(100),
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    LiabilityAccepted BOOLEAN,
    DeclarationPageReceived BOOLEAN,
    MedpayExhausted BOOLEAN,
    PIPExhausted BOOLEAN,
    Probate BOOLEAN,
    Bankruptcy BOOLEAN,
    SubrogationLien BOOLEAN,
    OtherLien BOOLEAN,
    OtherLienName VARCHAR(100),
    DriverName VARCHAR(100),
    DateSettled TIMESTAMP,
    HowCaseWasSettled VARCHAR(100),
    TotalSettlementAmount DECIMAL(10,4),
    BillsPaid DECIMAL(10,4),
    AttorneyFee DECIMAL(10,4),
    AttorneyFeeFlexible BOOLEAN,
    ReferralFeePercentage DECIMAL(10,4),
    AmountToClient DECIMAL(10,4),
    SettlementNotes VARCHAR(1000),
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.prod.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.main.Insurances (
    GainId VARCHAR(16) PRIMARY KEY,
    CompanyName VARCHAR(250),
    PolicyId VARCHAR(20),
    MemberId VARCHAR(20),
    GroupNumber VARCHAR(20),
    Status VARCHAR(100),
    Limits VARCHAR(100),
    Phone VARCHAR(100),
    Fax VARCHAR(100),
    Email VARCHAR(100),
    FollowUpEmail VARCHAR(100),
    BillingAddressLine1 VARCHAR(250),
    BillingAddressLine2 VARCHAR(250),
    BillingAddressCity VARCHAR(100),
    BillingAddressState VARCHAR(100),
    BillingAddressZip VARCHAR(20),
    PhysicalAddressLine1 VARCHAR(250),
    PhysicalAddressLine2 VARCHAR(250),
    PhysicalAddressCity VARCHAR(100),
    PhysicalAddressState VARCHAR(100),
    PhysicalAddressZip VARCHAR(20),
    LiabilityAccepted BOOLEAN,
    DeclarationPageReceived BOOLEAN,
    MedpayExhausted BOOLEAN,
    PIPExhausted BOOLEAN,
    Probate BOOLEAN,
    Bankruptcy BOOLEAN,
    SubrogationLien BOOLEAN,
    OtherLien BOOLEAN,
    OtherLienName VARCHAR(100),
    DriverName VARCHAR(100),
    DateSettled TIMESTAMP,
    HowCaseWasSettled VARCHAR(100),
    TotalSettlementAmount DECIMAL(10,4),
    BillsPaid DECIMAL(10,4),
    AttorneyFee DECIMAL(10,4),
    AttorneyFeeFlexible BOOLEAN,
    ReferralFeePercentage DECIMAL(10,4),
    AmountToClient DECIMAL(10,4),
    SettlementNotes VARCHAR(1000),
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.main.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);


-- Liens

CREATE TABLE IF NOT EXISTS integration.dev.Liens (
    GainId VARCHAR(16) PRIMARY KEY,
    AmountDue DECIMAL(10,4),
    LienHolderName VARCHAR(100),
    LienFileNumber VARCHAR(20),
    OriginalPaidAmount DECIMAL(10,4),
    IsThereExcess BOOLEAN,
    ExcessAmount DECIMAL(10,4),
    ExcludeFromSettlement BOOLEAN,
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.dev.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    SourceCreateDateTime TIMESTAMP,
    SourceModifiedDateTime TIMESTAMP,
    CreateDateTime TIMESTAMP,
    ModifiedDateTime TIMESTAMP,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.staging.Liens (
    GainId VARCHAR(16) PRIMARY KEY,
    AmountDue DECIMAL(10,4),
    LienHolderName VARCHAR(100),
    LienFileNumber VARCHAR(20),
    OriginalPaidAmount DECIMAL(10,4),
    IsThereExcess BOOLEAN,
    ExcessAmount DECIMAL(10,4),
    ExcludeFromSettlement BOOLEAN,
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.staging.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    SourceCreateDateTime TIMESTAMP,
    SourceModifiedDateTime TIMESTAMP,
    CreateDateTime TIMESTAMP,
    ModifiedDateTime TIMESTAMP,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.prod.Liens (
    GainId VARCHAR(16) PRIMARY KEY,
    AmountDue DECIMAL(10,4),
    LienHolderName VARCHAR(100),
    LienFileNumber VARCHAR(20),
    OriginalPaidAmount DECIMAL(10,4),
    IsThereExcess BOOLEAN,
    ExcessAmount DECIMAL(10,4),
    ExcludeFromSettlement BOOLEAN,
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.prod.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    SourceCreateDateTime TIMESTAMP,
    SourceModifiedDateTime TIMESTAMP,
    CreateDateTime TIMESTAMP,
    ModifiedDateTime TIMESTAMP,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.main.Liens (
    GainId VARCHAR(16) PRIMARY KEY,
    AmountDue DECIMAL(10,4),
    LienHolderName VARCHAR(100),
    LienFileNumber VARCHAR(20),
    OriginalPaidAmount DECIMAL(10,4),
    IsThereExcess BOOLEAN,
    ExcessAmount DECIMAL(10,4),
    ExcludeFromSettlement BOOLEAN,
    Notes VARCHAR(1000),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.main.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    SourceCreateDateTime TIMESTAMP,
    SourceModifiedDateTime TIMESTAMP,
    CreateDateTime TIMESTAMP,
    ModifiedDateTime TIMESTAMP,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);


-- Disbursals

CREATE TABLE IF NOT EXISTS integration.dev.Disbursals (
    GainId VARCHAR(16) PRIMARY KEY,
    Status VARCHAR(100),
    AmountDue DECIMAL(10,4),
    CheckNumber VARCHAR(20),
    CheckDate TIMESTAMP,
    AmountPaid DECIMAL(10,4),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.dev.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.staging.Disbursals (
    GainId VARCHAR(16) PRIMARY KEY,
    Status VARCHAR(100),
    AmountDue DECIMAL(10,4),
    CheckNumber VARCHAR(20),
    CheckDate TIMESTAMP,
    AmountPaid DECIMAL(10,4),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.staging.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.prod.Disbursals (
    GainId VARCHAR(16) PRIMARY KEY,
    Status VARCHAR(100),
    AmountDue DECIMAL(10,4),
    CheckNumber VARCHAR(20),
    CheckDate TIMESTAMP,
    AmountPaid DECIMAL(10,4),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.prod.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);

CREATE TABLE IF NOT EXISTS integration.main.Disbursals (
    GainId VARCHAR(16) PRIMARY KEY,
    Status VARCHAR(100),
    AmountDue DECIMAL(10,4),
    CheckNumber VARCHAR(20),
    CheckDate TIMESTAMP,
    AmountPaid DECIMAL(10,4),
    Type VARCHAR(100),
    CaseId VARCHAR(20) REFERENCES integration.main.Cases(GainId) NOT NULL,
    RelevantToGain BOOLEAN,
    ToDelete BOOLEAN DEFAULT FALSE,
    ToDeleteSystem VARCHAR(100),
    DeletePreventOverride BOOLEAN DEFAULT FALSE,
    DeletePreventOverrideReason VARCHAR(1000)
);


/*

Modify existing tables in the integration database for the canonical model

*/

-- Plaintiffs

-- For integration.dev.Plaintiffs
ALTER TABLE integration.dev.Plaintiffs
    RENAME COLUMN HomeAddressLine1 TO PrimaryAddressLine1;
ALTER TABLE integration.dev.Plaintiffs
    RENAME COLUMN HomeAddressLine2 TO PrimaryAddressLine2;
ALTER TABLE integration.dev.Plaintiffs
    RENAME COLUMN HomeAddressCity TO PrimaryAddressCity;
ALTER TABLE integration.dev.Plaintiffs
    RENAME COLUMN HomeAddressState TO PrimaryAddressState;
ALTER TABLE integration.dev.Plaintiffs
    RENAME COLUMN HomeAddressZip TO PrimaryAddressZip;

-- For integration.staging.Plaintiffs
ALTER TABLE integration.staging.Plaintiffs
    RENAME COLUMN HomeAddressLine1 TO PrimaryAddressLine1;
ALTER TABLE integration.staging.Plaintiffs
    RENAME COLUMN HomeAddressLine2 TO PrimaryAddressLine2;
ALTER TABLE integration.staging.Plaintiffs
    RENAME COLUMN HomeAddressCity TO PrimaryAddressCity;
ALTER TABLE integration.staging.Plaintiffs
    RENAME COLUMN HomeAddressState TO PrimaryAddressState;
ALTER TABLE integration.staging.Plaintiffs
    RENAME COLUMN HomeAddressZip TO PrimaryAddressZip;

-- For integration.prod.Plaintiffs
ALTER TABLE integration.prod.Plaintiffs
    RENAME COLUMN HomeAddressLine1 TO PrimaryAddressLine1;
ALTER TABLE integration.prod.Plaintiffs
    RENAME COLUMN HomeAddressLine2 TO PrimaryAddressLine2;
ALTER TABLE integration.prod.Plaintiffs
    RENAME COLUMN HomeAddressCity TO PrimaryAddressCity;
ALTER TABLE integration.prod.Plaintiffs
    RENAME COLUMN HomeAddressState TO PrimaryAddressState;
ALTER TABLE integration.prod.Plaintiffs
    RENAME COLUMN HomeAddressZip TO PrimaryAddressZip;

-- For integration.main.Plaintiffs
ALTER TABLE integration.main.Plaintiffs
    RENAME COLUMN HomeAddressLine1 TO PrimaryAddressLine1;
ALTER TABLE integration.main.Plaintiffs
    RENAME COLUMN HomeAddressLine2 TO PrimaryAddressLine2;
ALTER TABLE integration.main.Plaintiffs
    RENAME COLUMN HomeAddressCity TO PrimaryAddressCity;
ALTER TABLE integration.main.Plaintiffs
    RENAME COLUMN HomeAddressState TO PrimaryAddressState;
ALTER TABLE integration.main.Plaintiffs
    RENAME COLUMN HomeAddressZip TO PrimaryAddressZip;


-- LawFirms

ALTER TABLE integration.dev.LawFirms
    ADD COLUMN Notes VARCHAR(1000)
;

ALTER TABLE integration.staging.LawFirms
    ADD COLUMN Notes VARCHAR(1000)
;

ALTER TABLE integration.prod.LawFirms
    ADD COLUMN Notes VARCHAR(1000)
;

ALTER TABLE integration.main.LawFirms
    ADD COLUMN Notes VARCHAR(1000)
;


-- Cases

-- For integration.dev.Cases
ALTER TABLE integration.dev.Cases
    DROP COLUMN InsuranceYN;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsured1Name;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1PolicyId;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1Limits;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1AddressLine1;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1AddressLine2;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1City;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1State;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1Zip;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1Phone;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance1Fax;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsured2Name;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2PolicyId;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2Limits;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2AddressLine1;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2AddressLine2;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2City;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2State;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2Zip;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2Phone;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsurance2Fax;
ALTER TABLE integration.dev.Cases
    DROP COLUMN DefendantInsuranceAgent;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuredName;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsurancePolicyId;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsurance;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuranceLimits;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine2;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuranceCity;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuranceState;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuranceZip;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsurancePhone;
ALTER TABLE integration.dev.Cases
    DROP COLUMN PlaintiffInsuranceFax;
ALTER TABLE integration.dev.Cases
    ADD COLUMN GrandTotalSettlementAmount DECIMAL(10, 4);
ALTER TABLE integration.dev.Cases
    ADD COLUMN Notes VARCHAR(1000);

-- For integration.staging.Cases
ALTER TABLE integration.staging.Cases
    DROP COLUMN InsuranceYN;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsured1Name;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1PolicyId;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1Limits;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1AddressLine1;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1AddressLine2;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1City;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1State;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1Zip;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1Phone;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance1Fax;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsured2Name;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2PolicyId;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2Limits;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2AddressLine1;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2AddressLine2;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2City;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2State;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2Zip;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2Phone;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsurance2Fax;
ALTER TABLE integration.staging.Cases
    DROP COLUMN DefendantInsuranceAgent;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuredName;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsurancePolicyId;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsurance;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuranceLimits;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine2;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuranceCity;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuranceState;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuranceZip;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsurancePhone;
ALTER TABLE integration.staging.Cases
    DROP COLUMN PlaintiffInsuranceFax;
ALTER TABLE integration.staging.Cases
    ADD COLUMN GrandTotalSettlementAmount DECIMAL(10, 4);
ALTER TABLE integration.staging.Cases
    ADD COLUMN Notes VARCHAR(1000);

-- For integration.prod.Cases
ALTER TABLE integration.prod.Cases
    DROP COLUMN InsuranceYN;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsured1Name;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1PolicyId;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1Limits;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1AddressLine1;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1AddressLine2;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1City;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1State;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1Zip;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1Phone;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance1Fax;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsured2Name;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2PolicyId;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2Limits;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2AddressLine1;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2AddressLine2;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2City;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2State;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2Zip;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2Phone;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsurance2Fax;
ALTER TABLE integration.prod.Cases
    DROP COLUMN DefendantInsuranceAgent;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuredName;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsurancePolicyId;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsurance;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuranceLimits;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine2;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuranceCity;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuranceState;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuranceZip;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsurancePhone;
ALTER TABLE integration.prod.Cases
    DROP COLUMN PlaintiffInsuranceFax;
ALTER TABLE integration.prod.Cases
    ADD COLUMN GrandTotalSettlementAmount DECIMAL(10, 4);
ALTER TABLE integration.prod.Cases
    ADD COLUMN Notes VARCHAR(1000);

-- For integration.main.Cases
ALTER TABLE integration.main.Cases
    DROP COLUMN InsuranceYN;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsured1Name;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1PolicyId;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1Limits;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1AddressLine1;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1AddressLine2;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1City;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1State;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1Zip;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1Phone;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance1Fax;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsured2Name;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2PolicyId;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2Limits;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2AddressLine1;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2AddressLine2;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2City;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2State;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2Zip;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2Phone;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsurance2Fax;
ALTER TABLE integration.main.Cases
    DROP COLUMN DefendantInsuranceAgent;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuredName;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsurancePolicyId;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsurance;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuranceLimits;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuranceAddressLine2;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuranceCity;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuranceState;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuranceZip;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsurancePhone;
ALTER TABLE integration.main.Cases
    DROP COLUMN PlaintiffInsuranceFax;
ALTER TABLE integration.main.Cases
    ADD COLUMN GrandTotalSettlementAmount DECIMAL(10, 4);
ALTER TABLE integration.main.Cases
    ADD COLUMN Notes VARCHAR(1000);


-- Billings

-- For integration.dev.Billings
ALTER TABLE integration.dev.Billings
    ADD COLUMN NOTES VARCHAR(1000);
ALTER TABLE integration.dev.Billings
    ADD CONSTRAINT fk_MedicalFacilityId
    FOREIGN KEY (MedicalFacilityId)
    REFERENCES integration.dev.MedicalFacilities(GainId);

-- For integration.staging.Billings
ALTER TABLE integration.staging.Billings
    ADD COLUMN NOTES VARCHAR(1000);
ALTER TABLE integration.staging.Billings
    ADD CONSTRAINT fk_MedicalFacilityId
    FOREIGN KEY (MedicalFacilityId)
    REFERENCES integration.staging.MedicalFacilities(GainId);

-- For integration.prod.Billings
ALTER TABLE integration.prod.Billings
    ADD COLUMN NOTES VARCHAR(1000);
ALTER TABLE integration.prod.Billings
    ADD CONSTRAINT fk_MedicalFacilityId
    FOREIGN KEY (MedicalFacilityId)
    REFERENCES integration.prod.MedicalFacilities(GainId);

-- For integration.main.Billings
ALTER TABLE integration.main.Billings
    ADD COLUMN NOTES VARCHAR(1000);
ALTER TABLE integration.main.Billings
    ADD CONSTRAINT fk_MedicalFacilityId
    FOREIGN KEY (MedicalFacilityId)
    REFERENCES integration.main.MedicalFacilities(GainId);


