CREATE TABLE intakes (
    gainid VARCHAR(16) NOT NULL,
    accidentdescription VARCHAR(1000),
    accidentdatetime TIMESTAMP WITHOUT TIME ZONE,
    weresfpremisepicstaken BOOLEAN,
    waspolicecalled BOOLEAN,
    waspolicereportgenerated BOOLEAN,
    werecitationsissued BOOLEAN,
    clientsvehiclemakemodel VARCHAR(100),
    clientsvehicledamage VARCHAR(1000),
    defendantsvehiclemakemodel VARCHAR(100),
    defendantsvehicledamage VARCHAR(1000),
    wereautodamagepicstaken BOOLEAN,
    wasvehicletowed BOOLEAN,
    vehicledamageestimate NUMERIC(10, 4),
    injuriesdescription VARCHAR(1000),
    wereinjurypicstaken BOOLEAN,
    priorinjuries VARCHAR(1000),
    clientjobtitle VARCHAR(100),
    clientemploymentstatus VARCHAR(100),
    clientlostwagesstart TIMESTAMP WITHOUT TIME ZONE,
    clientjobduties VARCHAR(1000),
    clientlostwagesend TIMESTAMP WITHOUT TIME ZONE,
    clientshourlysalaryrate NUMERIC(10, 4),
    clientbankruptcy BOOLEAN,
    thingsclientcannolongerdo VARCHAR(1000),
    thingsclientcandowithpain VARCHAR(1000),
    clientslossofenjoyment VARCHAR(1000),
    clienthealthhistory VARCHAR(1000),
    clientsubsequentaccidents BOOLEAN,
    clientsubsequentaccidentsexplanation VARCHAR(1000),
    clientobservations VARCHAR(1000),
    clientonsocialmedia BOOLEAN,
    clientsocialmedianotes VARCHAR(1000),
    doesclienthavehealthinsurance BOOLEAN,
    doesclienthavesecondhealthinsurance BOOLEAN,
    type VARCHAR(100),
    caseid VARCHAR(20) NOT NULL,
    relevanttogain BOOLEAN,
    todelete BOOLEAN DEFAULT false,
    todeletesystem VARCHAR(100),
    deletepreventoverride BOOLEAN DEFAULT false,
    deletepreventoverridereason VARCHAR(1000),
    PRIMARY KEY (gainid)
);