import json
import pathlib

import django.conf
import pandas as pd

from . import (
    aws_operations,
    jopari_local_operations,
    local_operations,
    logger_config,
    shared_operations,
)

logger = logger_config.get_logger(__name__)

app_config = django.conf.settings
credentials = app_config.CREDENTIALS

sftp_session = None


def authenticate_jopari_server():
    global credentials, sftp_session
    resp = False
    try:
        conn = None  # No Authentication yet, TBD if necessary later
        sftp_session = conn  # if successful, store credentials globally
        resp = True
    except Exception as e:
        logger.error(e)
    return resp


def get_from_jopari(
    timestamp: str,
    canonical_object: str,
    bucket: str,
    directory: str,
    call_type: str,
    all_data: bool,
    local_files: bool = False,
) -> tuple[pd.DataFrame | None, list[str], list[str]]:
    df, unprocessed_files, processed_files = pd.DataFrame(), [], []
    timestamp_string = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    is_files = True if canonical_object == 'files' else False
    data_file_path = f'integration/integration_code/jopari_{canonical_object}_{call_type}.txt'
    try:
        unprocessed_files = shared_operations.get_unprocessed_files_from_s3(
            directory, canonical_object, bucket, all_data
        )
        all_dfs = []

        if not unprocessed_files:
            return (df, unprocessed_files, processed_files)

        for file in unprocessed_files:
            if not is_files:
                aws_operations.download_s3(
                    bucket=bucket,
                    key=file,
                    local_location=data_file_path,
                )
                with open(data_file_path, 'r') as doc:
                    json_list = json.load(doc)
                df = pd.DataFrame(json_list)
                all_dfs.append(df)
                local_operations.clean_up_file(data_file_path)
                processed_file = file.replace('unprocessed', 'processed')
                processed_files.append(
                    f'{processed_file[:-5]}-{timestamp_string}{processed_file[-5:]}'
                )  # NOTE: removing the timestamp will cause copy_s3 and move_s3 functions to not work correctly
            else:
                if not local_files:
                    processed_file = file.replace('unprocessed', 'processed')
                    processed_files.append(
                        f'{processed_file[:-4]}-{timestamp_string}{processed_file[-4:]}'
                    )
                else:
                    data_file_path = f'integration/integration_code/{pathlib.Path(file).name}'

                    aws_operations.download_s3(
                        bucket=bucket,
                        key=file,
                        local_location=data_file_path,
                    )
                    processed_files.append(data_file_path)

        if not is_files:
            df = pd.concat(all_dfs, join='outer', ignore_index=True)
        else:
            df = pd.DataFrame(processed_files, columns=['path'])
        if df is None or df.empty:
            logger.error(f'No data fetched for {canonical_object}')
        else:
            df.columns = [s.lower() for s in (df.columns)]
    except Exception as e:
        local_operations.clean_up_file(data_file_path)
        logger.error(e)
    return (df, unprocessed_files, processed_files)


def get_data_jopari(
    timestamp: str, bucket: str, directory: str, call_type: str, all_data: bool
):
    # data location is excluding sub_directory
    (
        plaintiffs_data,
        plaintiffs_unprocessed_files,
        plaintiffs_processed_files,
    ) = get_from_jopari(
        timestamp, 'plaintiffs', bucket, directory, call_type, all_data
    )
    (
        cases_data,
        cases_unprocessed_files,
        cases_processed_files,
    ) = get_from_jopari(
        timestamp, 'cases', bucket, directory, call_type, all_data
    )
    (
        insurances_data,
        insurances_unprocessed_files,
        insurances_processed_files,
    ) = get_from_jopari(
        timestamp, 'insurances', bucket, directory, call_type, all_data
    )
    (
        medicalfacilities_data,
        medicalfacilities_unprocessed_files,
        medicalfacilities_processed_files,
    ) = get_from_jopari(
        timestamp, 'medicalfacilities', bucket, directory, call_type, all_data
    )
    (
        transactions_data,
        transactions_unprocessed_files,
        transactions_processed_files,
    ) = get_from_jopari(
        timestamp, 'transactions', bucket, directory, call_type, all_data
    )
    (
        billings_data,
        billings_unprocessed_files,
        billings_processed_files,
    ) = get_from_jopari(
        timestamp, 'billings', bucket, directory, call_type, all_data
    )
    (
        charges_data,
        charges_unprocessed_files,
        charges_processed_files,
    ) = get_from_jopari(
        timestamp, 'charges', bucket, directory, call_type, all_data
    )
    (
        files_data,
        files_unprocessed_files,
        files_processed_files,
    ) = get_from_jopari(
        timestamp, 'files', bucket, directory, call_type, all_data
    )

    (
        local_files_data,
        _,
        _,
    ) = get_from_jopari(
        timestamp, 'files', bucket, directory, call_type, all_data, True
    )

    # when trying to do delta ingestion, all_data being False will fetch only data after the timestamp
    all_s3_data = {
        'plaintiffs': plaintiffs_data,
        'cases': cases_data,
        'billings': billings_data,
        'charges': charges_data,
        'insurances': insurances_data,
        'transactions': transactions_data,
        'medicalfacilities': medicalfacilities_data,
        'files': files_data,
        'local_files': local_files_data,
    }
    all_s3_files = {
        'plaintiffs_unprocessed_files': plaintiffs_unprocessed_files,
        'plaintiffs_processed_files': plaintiffs_processed_files,
        'cases_unprocessed_files': cases_unprocessed_files,
        'cases_processed_files': cases_processed_files,
        'billings_unprocessed_files': billings_unprocessed_files,
        'billings_processed_files': billings_processed_files,
        'charges_unprocessed_files': charges_unprocessed_files,
        'charges_processed_files': charges_processed_files,
        'transactions_unprocessed_files': transactions_unprocessed_files,
        'transactions_processed_files': transactions_processed_files,
        'insurances_unprocessed_files': insurances_unprocessed_files,
        'insurances_processed_files': insurances_processed_files,
        'medicalfacilities_unprocessed_files': medicalfacilities_unprocessed_files,
        'medicalfacilities_processed_files': medicalfacilities_processed_files,
        'files_unprocessed_files': files_unprocessed_files,
        'files_processed_files': files_processed_files,
    }
    return (all_s3_data, all_s3_files)


def process_upsert_data_jopari(
    timestamp: str,
    bucket: str,
    directory: str,
    sub_directory: str,
    data: dict[str, pd.DataFrame | None],
    charges_update_flag: bool,
    billings_update_flag: bool,
    cases_update_flag: bool,
):
    all_s3_data = jopari_local_operations.jopari_generate_s3_upsert_data(
        data, bucket, directory, timestamp
    )  # data generation
    charges_gainid_set = set()
    billings_gainid_set = set()
    if billings_update_flag and all_s3_data['charges'] is not None:
        charges_gainid_set.update(all_s3_data['charges']['gainid'])
    if cases_update_flag and all_s3_data['billings'] is not None:
        billings_gainid_set.update(all_s3_data['billings']['gainid'])
    files = jopari_local_operations.jopari_generate_upsert_csv(
        all_s3_data, timestamp
    )  # data storage
    directory = f'{directory}/{sub_directory}'  # result location is including sub_directory
    for file in files.values():
        aws_operations.upload_s3(bucket, directory, file)
    for mapping, file in files.items():  # Redshift changes
        aws_operations.s3_to_postgres(bucket, directory, file, mapping)
    return (
        files,
        charges_gainid_set,
        billings_gainid_set,
    )


def post_process_data_jopari(
    bucket: str, all_s3_files: dict[str, list[str]]
) -> None:
    # After Redshift changes are complete, move raw data files from unprocessed to processed
    for plaintiffs_unprocessed_file, plaintiffs_processed_file in zip(
        all_s3_files['plaintiffs_unprocessed_files'],
        all_s3_files['plaintiffs_processed_files'],
    ):
        aws_operations.move_s3(
            bucket,
            plaintiffs_unprocessed_file,
            bucket,
            plaintiffs_processed_file,
        )
    for (
        medicalfacilities_unprocessed_file,
        medicalfacilities_processed_file,
    ) in zip(
        all_s3_files['medicalfacilities_unprocessed_files'],
        all_s3_files['medicalfacilities_processed_files'],
    ):
        aws_operations.move_s3(
            bucket,
            medicalfacilities_unprocessed_file,
            bucket,
            medicalfacilities_processed_file,
        )
    for cases_unprocessed_file, cases_processed_file in zip(
        all_s3_files['cases_unprocessed_files'],
        all_s3_files['cases_processed_files'],
    ):
        aws_operations.move_s3(
            bucket, cases_unprocessed_file, bucket, cases_processed_file
        )
    for insurances_unprocessed_file, insurances_processed_file in zip(
        all_s3_files['insurances_unprocessed_files'],
        all_s3_files['insurances_processed_files'],
    ):
        aws_operations.move_s3(
            bucket,
            insurances_unprocessed_file,
            bucket,
            insurances_processed_file,
        )
    for billings_unprocessed_file, billings_processed_file in zip(
        all_s3_files['billings_unprocessed_files'],
        all_s3_files['billings_processed_files'],
    ):
        aws_operations.move_s3(
            bucket,
            billings_unprocessed_file,
            bucket,
            billings_processed_file,
        )
    for charges_unprocessed_file, charges_processed_file in zip(
        all_s3_files['charges_unprocessed_files'],
        all_s3_files['charges_processed_files'],
    ):
        aws_operations.move_s3(
            bucket,
            charges_unprocessed_file,
            bucket,
            charges_processed_file,
        )
    for files_unprocessed_files, files_processed_files in zip(
        all_s3_files['files_unprocessed_files'],
        all_s3_files['files_processed_files'],
    ):
        aws_operations.move_s3(
            bucket,
            files_unprocessed_files,
            bucket,
            files_processed_files,
        )
    return
