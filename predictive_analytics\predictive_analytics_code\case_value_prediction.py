# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning
import json

import numpy as np
import sklearn.preprocessing
import sklearn.utils
import torch

from . import case_value_prediction_operations, logger, neural_network

try:
    with open(
        'predictive_analytics/predictive_analytics_code/case_value_prediction_config.json',
        'rb',
    ) as f:
        config = json.load(f)
    input_parameters_list = config['input_parameters_list']
    # Default values for the case value prediction model
    # These values were computed from the training data
    default_values = config['default_values']
except FileNotFoundError:
    print("File case_value_prediction_config.json not found")
    raise FileNotFoundError
except KeyError:
    print("File case_value_prediction_config.json is missing a key")
    raise KeyError
except Exception as e:
    print("An exception occurred")
    raise e


def load_model_and_scaler() -> (
    tuple[neural_network.Net, sklearn.preprocessing.StandardScaler]
):
    ''' '''
    model_data = torch.load(
        'predictive_analytics/predictive_analytics_code/case_value_prediction.pt',
        map_location='cpu',
    )
    model = neural_network.Net(
        model_data['config'],
        model_data['input_features'],
        model_data['output_features'],
    )
    state_dict_device = {
        k: v.to(model.device)
        for k, v in model_data['model_state_dict'].items()
    }  # Move model state dict to device
    model.load_state_dict(state_dict_device)
    return (model, model_data['scaler_inputs'])


def pre_process_data_validation(raw_input_data: dict[str, str]) -> None:
    if raw_input_data is None:
        raise ValueError('Input data is None')
    if 'age__c' not in raw_input_data:
        raise ValueError('Input parameter "age__c" not found')
    if 'gender__c' not in raw_input_data:
        raise ValueError('Input parameter "gender__c" not found')
    if 'injury_type__c' not in raw_input_data:
        raise ValueError('Input parameter "injury_type__c" not found')
    if 'insurance_company_ii__c' not in raw_input_data:
        raise ValueError('Input parameter "insurance_company_ii__c" not found')
    if 'number_of_potential_claimants__c' not in raw_input_data:
        raise ValueError(
            'Input parameter "number_of_potential_claimants__c" not found'
        )
    if 'any_other_lawsuits_pending_at_this_time__c' not in raw_input_data:
        raise ValueError(
            'Input parameter "any_other_lawsuits_pending_at_this_time__c" not found'
        )
    if 'what_type_of_case__c' not in raw_input_data:
        raise ValueError('Input parameter "what_type_of_case__c" not found')
    if 'type_of_funding__c' not in raw_input_data:
        raise ValueError('Input parameter "type_of_funding__c" not found')
    if 'prior_funding_company__c' not in raw_input_data:
        raise ValueError(
            'Input parameter "prior_funding_company__c" not found'
        )
    if 'prior_funding__c' not in raw_input_data:
        raise ValueError('Input parameter "prior_funding__c" not found')
    if 'origination_ar_book__c' not in raw_input_data:
        raise ValueError('Input parameter "origination_ar_book__c" not found')
    if 'date_of_first_treatment__c' not in raw_input_data:
        raise ValueError(
            'Input parameter "date_of_first_treatment__c" not found'
        )
    if 'date_of_accident__c' not in raw_input_data:
        raise ValueError('Input parameter "date_of_accident__c" not found')
    if 'insurance_limits__c' not in raw_input_data:
        raise ValueError('Input parameter "insurance_limits__c" not found')
    if 'insurance_limits_2__c' not in raw_input_data:
        raise ValueError('Input parameter "insurance_limits_2__c" not found')


def pre_process_data_helper(
    raw_input_data: dict[str, str],
) -> tuple[dict[str, bool | int | float], float]:
    ''' '''
    global input_parameters_list, default_values
    pre_processed_input_data = {}
    pre_processed_input_data['age__c'] = (
        case_value_prediction_operations.compute_age(
            raw_input_data['age__c'], default_values['age__c']
        )
    )
    pre_processed_input_data['gender__c'] = (
        case_value_prediction_operations.compute_gender(
            raw_input_data['gender__c'], default_values['gender__c']
        )
    )
    (
        pre_processed_input_data['injury_type__c_catastrophic'],
        pre_processed_input_data['injury_type__c_serious'],
        pre_processed_input_data['injury_type__c_soft_tissue'],
    ) = case_value_prediction_operations.compute_injury_type(
        raw_input_data['injury_type__c'],
    )
    pre_processed_input_data['insurance_company_ii__c'] = (
        case_value_prediction_operations.compute_insurance_company_ii__c(
            raw_input_data.get('insurance_company_ii__c', '')
        )
    )
    pre_processed_input_data['number_of_potential_claimants__c'] = (
        case_value_prediction_operations.compute_number_of_potential_claimants(
            raw_input_data.get('number_of_potential_claimants__c', ''),
            default_values['number_of_potential_claimants__c'],
        )
    )
    pre_processed_input_data['any_other_lawsuits_pending_at_this_time__c'] = (
        case_value_prediction_operations.compute_any_other_lawsuits_pending_at_this_time(
            raw_input_data['any_other_lawsuits_pending_at_this_time__c'],
            default_values['any_other_lawsuits_pending_at_this_time__c'],
        )
    )
    (
        pre_processed_input_data[
            'what_type_of_case__c_motor_vehicle_accident'
        ],
        pre_processed_input_data['what_type_of_case__c_s_f'],
        pre_processed_input_data['what_type_of_case__c_other'],
    ) = case_value_prediction_operations.compute_what_type_of_case(
        raw_input_data['what_type_of_case__c'],
    )
    (
        pre_processed_input_data['type_of_funding__c_medical'],
        pre_processed_input_data['type_of_funding__c_plaintiff'],
    ) = case_value_prediction_operations.compute_type_of_funding(
        raw_input_data['type_of_funding__c']
    )
    pre_processed_input_data['prior_funding_company__c'] = (
        case_value_prediction_operations.compute_prior_funding_company(
            raw_input_data['prior_funding_company__c']
        )
    )
    pre_processed_input_data['prior_funding__c'] = (
        case_value_prediction_operations.compute_prior_funding(
            raw_input_data['prior_funding__c'],
            pre_processed_input_data['prior_funding_company__c'],
        )
    )
    pre_processed_input_data['origination_ar_book__c'] = (
        case_value_prediction_operations.compute_origination_ar_book(
            raw_input_data['origination_ar_book__c']
        )
    )
    pre_processed_input_data['treatment_gap'] = (
        case_value_prediction_operations.compute_treatment_gap(
            raw_input_data['date_of_first_treatment__c'],
            raw_input_data['date_of_accident__c'],
            default_values['treatment_gap'],
        )
    )
    pre_processed_input_data['insurance_limits__c'] = (
        case_value_prediction_operations.compute_insurance_limits(
            raw_input_data['insurance_limits__c'],
            raw_input_data['insurance_limits_2__c'],
        )
    )
    return (
        pre_processed_input_data,
        pre_processed_input_data['insurance_limits__c'],
    )


def pre_process_data(
    raw_input_data: dict[str, str],
) -> tuple[dict[str, bool | int | float], float]:
    ''' '''
    global input_parameters_list, default_values
    pre_process_data_validation(raw_input_data)
    processed_input_data, insurance_limit = pre_process_data_helper(
        raw_input_data
    )
    return (processed_input_data, insurance_limit)


def process_data(
    processed_input_data: dict[str, bool | int | float],
    scaler_inputs: sklearn.preprocessing.StandardScaler,
) -> torch.Tensor:
    global input_parameters_list
    # NOTE: Do NOT remove the ordering! The neural network model expects the input features in this order
    final_input_data = {
        input_parameter: processed_input_data[input_parameter]
        for input_parameter in input_parameters_list
    }
    input_features = np.array(list(final_input_data.values())).reshape(1, -1)
    X = scaler_inputs.transform(input_features)
    X_t = torch.from_numpy(X).float()
    return X_t


def inference(model: neural_network.Net, X_t: torch.Tensor) -> float:
    ''' '''
    Y = model.inference_model(X_t)
    case_value = Y[0][0]
    return case_value


def make_prediction(
    model: neural_network.Net,
    scaler_inputs: sklearn.preprocessing.StandardScaler,
    raw_input_data: dict[str, str],
) -> tuple[str, float, bool]:
    ''' '''
    prediction_capped = False
    try:
        pre_processed_data, insurance_limit = pre_process_data(raw_input_data)
    except Exception as _:  # Invalid input data
        return ('Error: Invalid input data', 0, prediction_capped)
    processed_input_data = process_data(pre_processed_data, scaler_inputs)
    case_value = inference(model, processed_input_data)
    if case_value > insurance_limit:
        prediction_capped = True
    return (str(case_value), insurance_limit, prediction_capped)


def log_prediction(
    key: str,
    raw_input_data: dict[str, str],
    case_value: str,
    insurance_limit: float,
    prediction_capped: bool,
) -> None:
    '''
    Log the model, input parameters, predicted value,
    and whether the prediction was successful or not.
    '''
    serialized_input_data = json.dumps(raw_input_data)
    is_error = case_value.startswith('Error')
    exception_message = (
        case_value
        if is_error
        else (
            f'Case value prediction capped because of insurance limit: {insurance_limit}'
            if prediction_capped
            else None
        )
    )
    logger.audit_log(
        salesforce_id=key,
        model='case_value_prediction',
        input_parameters=serialized_input_data,
        predicted_value=case_value,
        is_error=is_error,
        exception_message=exception_message,
    )


def case_value(all_raw_input_data: dict[str, dict]) -> dict[str, str]:
    ''' '''
    model, scaler_inputs = load_model_and_scaler()
    predictions = {}
    for key, raw_input_data in all_raw_input_data.items():
        case_value, insurance_limit, prediction_capped = make_prediction(
            model, scaler_inputs, raw_input_data
        )
        predictions[key] = insurance_limit if prediction_capped else case_value
        log_prediction(
            key, raw_input_data, case_value, insurance_limit, prediction_capped
        )
    return predictions
