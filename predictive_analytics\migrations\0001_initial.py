# Generated by Django 4.0.6 on 2024-10-02 12:38

import django.db
import django.db.migrations
import django.db.models


class Migration(django.db.migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        django.db.migrations.CreateModel(
            name='audit_log',
            fields=[
                (
                    'audit_log_id',
                    django.db.models.BigAutoField(
                        primary_key=True, serialize=False
                    ),
                ),
                ('salesforce_id', django.db.models.CharField(max_length=20)),
                ('model', django.db.models.CharField(max_length=100)),
                (
                    'input_parameters',
                    django.db.models.TextField(default=None, null=True),
                ),
                (
                    'predicted_value',
                    django.db.models.TextField(default=None, null=True),
                ),
                ('is_error', django.db.models.BooleanField(default=False)),
                (
                    'exception_message',
                    django.db.models.TextField(default=None, null=True),
                ),
                (
                    'create_date_time',
                    django.db.models.DateTimeField(auto_now_add=True),
                ),
            ],
        ),
    ]
