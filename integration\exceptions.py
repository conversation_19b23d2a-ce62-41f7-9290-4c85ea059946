'''
This module is non-standard, but important to the integration app.
It is used to track all possible exceptions that can occur in the integration app.
By maintaining a list of all possible exceptions, we can ensure that all exceptions
are handled, and that appropiate responses are sent to the user.
'''

from integration.integration_code import logger_config


class _IntegrationException(Exception):
    '''
    Base exception for all exceptions in the integration app.
    All exceptions in the integration app should inherit from this class.
    This class should not be raised directly.
    This class handles logging of the exception to the integration audit log.
    '''

    def __init__(
        self,
        integration_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(*args)
        logger_config.audit_log(
            route_section=integration_exception_log.route_section,
            additional_details=integration_exception_log.additional_details,
            is_error=integration_exception_log.is_error,
            exception_message=integration_exception_log.exception_message,
        )
        self.log = integration_exception_log


class IntegrationExternalAuthenticationException(
    _IntegrationException
):  # Internal Server Error, 500
    '''
    Raised when internal app attempts to authenticate external service and fails.
    '''

    def __init__(
        self,
        integration_authentication_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(integration_authentication_exception_log, *args)


class IntegrationExternalConnectionException(
    _IntegrationException
):  # Internal Server Error, 500
    '''
    Raised when internal app attempts to connect to external service and fails.
    '''

    def __init__(
        self,
        integration_connection_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(integration_connection_exception_log, *args)


class IntegrationExternalTimeoutException(
    _IntegrationException
):  # Gateway Timeout, 504
    '''
    Raised when internal app attempts to connect to external service and
    a timeout occurs.
    '''

    def __init__(
        self,
        integration_timeout_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(integration_timeout_exception_log, *args)


class IntegrationExternalBadRequestException(
    _IntegrationException
):  # Internal Server Error, 500
    '''
    Raised when internal app attempts to connect to external service and
    a bad request is made.
    '''

    def __init__(
        self,
        integration_bad_request_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(integration_bad_request_exception_log, *args)


class IntegrationExternalDataException(
    _IntegrationException
):  # Internal Server Error, 500
    '''
    Raised when internal app attempts to connect to external service and
    the data received is not as expected.
    '''

    def __init__(
        self,
        integration_data_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(integration_data_exception_log, *args)


class IntegrationInternalBadRequestException(
    _IntegrationException
):  # Bad Request, 400
    '''
    Raised when an internal app receives a bad request.
    Since incoming bad requests are client errors, it is okay to expose
    the exception message to the user.
    Use the same logic for all other errors where it is okay to expose
    the exception message to the user.
    '''

    def __init__(
        self,
        integration_bad_request_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(integration_bad_request_exception_log, *args)
        self.log = integration_bad_request_exception_log

    def __str__(self) -> str:  # Overriding __str__ to return exception_message
        if self.log.exception_message is None:
            return super().__str__()
        return self.log.exception_message


class IntegrationInternalException(
    _IntegrationException
):  # Internal Server Error, 500
    '''
    Raised when an internal error occurs.
    '''

    def __init__(
        self,
        integration_internal_exception_log: logger_config.AuditLog = logger_config.AuditLog(),
        *args: object,
    ) -> None:
        super().__init__(integration_internal_exception_log, *args)
