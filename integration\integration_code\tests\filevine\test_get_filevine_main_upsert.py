import json
import typing
from unittest.mock import MagicMock, patch

import psycopg
import pytest

from tests import test_helper


class TestGetMainUpsert:
    @pytest.fixture()
    def mock_get_all(self):
        with patch(
            'integration.integration_code.filevine.filevine_operations.get_all'
        ) as mock_method:
            projects_raw_data = []
            with open(
                'integration/integration_code/fixtures/filevine/filevine_test_data.json',
                'r',
            ) as file:
                project_data = json.load(file)
                documents = project_data['documents']
                for document in documents:
                    document_id = document['documentId']['native']
                    document_url = f'dummy_url/{document_id}'
                    document['documentURL'] = document_url
                project_raw_data = {
                    'client_details': project_data['client_details'],
                    'contacts': project_data['contacts'],
                    'team': project_data['team'],
                    'project': project_data['project'],
                    'insurances': project_data['insurance'],
                    'liens': project_data['liens'],
                    'disbursals': project_data['disbursals'],
                    'meds': project_data['meds'],
                    'documents': documents,
                }
                projects_raw_data.append(project_raw_data)

                mock_method.return_value = projects_raw_data

                yield mock_method

    @pytest.mark.django_db
    @patch(
        'integration.integration_code.filevine.filevine_operations.authenticate'
    )
    @patch(
        'integration.integration_code.filevine.filevine_operations.setup_session'
    )
    @patch(
        'integration.integration_code.filevine_local_operations.hostilo_filevine_upsert_generate_files_s3_data'
    )
    def test_get_main_upsert(
        self,
        mock_authenticate: MagicMock,
        mock_setup_session: MagicMock,
        mock_generate_file: MagicMock,
        mock_get_all: MagicMock,
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
    ):
        """Test that the main upsert function correctly processes Filevine data."""
        from integration.integration_code import filevine

        mock_generate_file.return_value = 's3://test'

        filevine.get_main_upsert(
            True,
            'hostilo',
            all_data=True,
            skip_files=True,
            cases_update_flag=True,
        )

        test_helper.verify_audit_errors(verify_errors)
