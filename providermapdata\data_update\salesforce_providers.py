# pyright: reportCallIssue=warning,reportArgumentType=warning,reportOptionalSubscript=warning,reportAttributeAccessIssue=warning
import functools
import json
import typing

import pandas as pd
from simple_salesforce import (
    Salesforce,
    SalesforceExpiredSession,
    SalesforceMalformedRequest,
)

from .definitive_providers import return_definitive_providers

with open('./providermapdata/data_update/credentials.json', 'rb') as f:
    credentials = json.load(f)

# Connect to Salesforce using simple-salesforce
username = credentials['salesforce-production']['username']
password = credentials['salesforce-production']['password']
security_token = credentials['salesforce-production']['security_token']

# import requests
# session = requests.Session()
# For production
global sf
# sf = Salesforce(username=username, password=password,
#                 security_token=security_token, session=session)
sf = Salesforce(
    username=username, password=password, security_token=security_token
)
provider_account_info = sf.Business_Location__c.describe()
provider_account_field_names = [
    field['name'] for field in provider_account_info['fields']
]


def init_sf_session():
    sf = Salesforce(
        username=username, password=password, security_token=security_token
    )
    return sf


sf = init_sf_session()


def check_sf_session(func: typing.Any):
    @functools.wraps(func)
    def wrapper(*args: typing.Any, **kwargs: typing.Any):
        global sf
        try:
            sf.query('')
        except SalesforceExpiredSession:
            sf = init_sf_session()
        except SalesforceMalformedRequest:
            pass
        finally:
            return func(*args, **kwargs)

    return wrapper


@check_sf_session
def return_salesforce_providers():
    # Pull data from account
    SalesforceProviders_query = 'SELECT Account_Name__c, Account_Name__r.Provider_Map_Override__c, Account_Name__r.Provider_Logo__c,Account_Name__r.texcellency__DefHC_HospitalID__c, Account_Name__r.White_Label__c, Account_Name__r.Contract_Type__c, Account_Name__r.Specialty__c,Account_Name__r.Accepts_PI__c, Account_Name__r.Website,Location_Name__c, Specialty_from_Provider__c, Specialty_for_Location__c, State_Location__c, City_Location__c,Street_Location__c, Address_1__c, Zip_Location__c,  GeoLocation__latitude__s, GeoLocation__longitude__s, Definitive_Specialty__c, Other_Specialties__c, Location_Phone__c, Location_Accepts_PI__c, Location_Website__c, Provider_Website__c, Location_Provider_Map_Override__c FROM Business_Location__c'
    SalesforceProviders_results = sf.bulk.Business_Location__c.query(
        SalesforceProviders_query
    )
    df_SalesforceProviders = pd.DataFrame(SalesforceProviders_results).drop(
        columns='attributes'
    )

    definitiveID = []
    for i in range(len(df_SalesforceProviders)):
        def_id = str(
            df_SalesforceProviders['Account_Name__r'][i][
                'texcellency__DefHC_HospitalID__c'
            ]
        )
        if str(def_id) == 'None':
            definitiveID.append('')
        else:
            definitiveID.append(
                int(
                    df_SalesforceProviders['Account_Name__r'][i][
                        'texcellency__DefHC_HospitalID__c'
                    ]
                )
            )

    df_SalesforceProviders.insert(2, 'definitiveID', definitiveID)
    definitiveHQID = list(set(definitiveID))
    definitiveHQID.remove('')
    df_DefinitiveProviders = return_definitive_providers()
    df_DefinitiveProviders = df_DefinitiveProviders[
        ~df_DefinitiveProviders['Definitive ID'].isin(definitiveHQID)
    ]

    accept_PI = []
    for i in range(len(df_SalesforceProviders)):
        if df_SalesforceProviders['Location_Accepts_PI__c'][i] is None:
            accept_PI.append(
                df_SalesforceProviders['Account_Name__r'][i]['Accepts_PI__c']
            )
        else:
            accept_PI.append(
                df_SalesforceProviders['Location_Accepts_PI__c'][i]
            )

    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Location_Accepts_PI__c', axis=1
    )
    df_SalesforceProviders.insert(4, 'Accepts_PI', accept_PI)

    # Set acceptPI blank as -1, No as 0, Yes as 1
    df_SalesforceProviders.Accepts_PI.fillna(value='None', inplace=True)
    df_SalesforceProviders.Accepts_PI.replace(
        to_replace=['None', 'No', 'Yes', 'Yes but won’t work with us'],
        value=[0, -1, 1, 2],
        inplace=True,
    )

    # Update the provider type value based on whether it accept PI or not
    df_SalesforceProviders.loc[:, 'ProviderType'] = df_SalesforceProviders[
        'Accepts_PI'
    ]
    df_SalesforceProviders.loc[
        df_SalesforceProviders.Accepts_PI == 1, 'ProviderType'
    ] = 1
    df_SalesforceProviders.loc[
        df_SalesforceProviders.Accepts_PI == 0, 'ProviderType'
    ] = 2
    df_SalesforceProviders.loc[
        df_SalesforceProviders.Accepts_PI == 2, 'ProviderType'
    ] = 2
    df_SalesforceProviders.loc[
        df_SalesforceProviders.Accepts_PI == 2, 'Accepts_PI'
    ] = 1

    preferred_Provider = []
    for i in range(len(df_SalesforceProviders)):
        if (
            str(df_SalesforceProviders['Location_Provider_Map_Override__c'][i])
            == 'Preferred Provider'
        ):
            preferred_Provider.append(1)
            continue
        if (
            str(
                df_SalesforceProviders['Account_Name__r'][i][
                    'Provider_Map_Override__c'
                ]
            )
            == 'Preferred Provider'
        ):
            preferred_Provider.append(1)
            continue
        if (
            str(df_SalesforceProviders['Account_Name__r'][i]['White_Label__c'])
            == 'Yes'
        ):
            preferred_Provider.append(0)
            continue
        else:
            if (
                df_SalesforceProviders['Account_Name__r'][i][
                    'Contract_Type__c'
                ]
                == 'Partial Advance'
                or df_SalesforceProviders['Account_Name__r'][i][
                    'Contract_Type__c'
                ]
                == 'Servicing'
            ):
                preferred_Provider.append(1)
            else:
                preferred_Provider.append(0)

    df_SalesforceProviders.loc[:, 'PreferredProvider'] = preferred_Provider
    providerTypes = []
    for i in range(
        len(df_SalesforceProviders['Location_Provider_Map_Override__c'])
    ):
        if (
            df_SalesforceProviders['Location_Provider_Map_Override__c'][i]
            == 'Preferred Provider'
        ):
            providerTypes.append(0)
        elif (
            df_SalesforceProviders['Location_Provider_Map_Override__c'][i]
            == 'Gain Provider'
        ):
            providerTypes.append(1)
        elif (
            df_SalesforceProviders['Location_Provider_Map_Override__c'][i]
            == 'Other Provider'
        ):
            providerTypes.append(2)
        elif (
            df_SalesforceProviders['Location_Provider_Map_Override__c'][i]
            == 'Do not show'
        ):
            providerTypes.append(-1)
        else:
            if (
                df_SalesforceProviders['Account_Name__r'][i][
                    'Provider_Map_Override__c'
                ]
                == 'Preferred Provider'
            ):
                providerTypes.append(0)
            elif (
                df_SalesforceProviders['Account_Name__r'][i][
                    'Provider_Map_Override__c'
                ]
                == 'Gain Provider'
            ):
                providerTypes.append(1)
            elif (
                df_SalesforceProviders['Account_Name__r'][i][
                    'Provider_Map_Override__c'
                ]
                == 'Other Provider'
            ):
                providerTypes.append(2)
            elif (
                df_SalesforceProviders['Account_Name__r'][i][
                    'Provider_Map_Override__c'
                ]
                == 'Do not show'
            ):
                providerTypes.append(-1)
            else:
                providerTypes.append(df_SalesforceProviders['ProviderType'][i])

    df_SalesforceProviders = df_SalesforceProviders.drop(
        'ProviderType', axis=1
    )
    df_SalesforceProviders.insert(10, 'ProviderType', providerTypes)
    df_SalesforceProviders.loc[
        df_SalesforceProviders.ProviderType == 0, 'PreferredProvider'
    ] = 1
    df_SalesforceProviders.loc[
        df_SalesforceProviders.ProviderType == 1, 'PreferredProvider'
    ] = 0
    df_SalesforceProviders.loc[
        df_SalesforceProviders.ProviderType == 2, 'PreferredProvider'
    ] = 0
    df_SalesforceProviders.loc[
        df_SalesforceProviders.ProviderType == -1, 'PreferredProvider'
    ] = 0
    df_SalesforceProviders.loc[
        df_SalesforceProviders.ProviderType == 0, 'ProviderType'
    ] = 1
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Location_Provider_Map_Override__c', axis=1
    )

    # explain the logic
    ProviderSpecialty = []
    OtherSpecialties = []
    for i in range(len(df_SalesforceProviders)):
        if (
            str(df_SalesforceProviders['Specialty_for_Location__c'][i])
            == 'None'
        ):
            if (
                str(
                    df_SalesforceProviders['Account_Name__r'][i][
                        'Specialty__c'
                    ]
                )
                == 'None'
            ):
                ProviderSpecialty.append(
                    df_SalesforceProviders['Definitive_Specialty__c'][i]
                )
                OtherSpecialties.append(
                    df_SalesforceProviders['Other_Specialties__c'][i]
                )
            else:
                ProviderSpecialty.append(
                    df_SalesforceProviders['Account_Name__r'][i][
                        'Specialty__c'
                    ]
                )
                if (
                    str(df_SalesforceProviders['Definitive_Specialty__c'][i])
                    != 'None'
                    and str(df_SalesforceProviders['Other_Specialties__c'][i])
                    != 'None'
                ):
                    OtherSpecialties.append(
                        df_SalesforceProviders['Definitive_Specialty__c'][i]
                        + ', '
                        + df_SalesforceProviders['Other_Specialties__c'][i]
                    )
                elif (
                    str(df_SalesforceProviders['Definitive_Specialty__c'][i])
                    == 'None'
                    and str(df_SalesforceProviders['Other_Specialties__c'][i])
                    != 'None'
                ):
                    OtherSpecialties.append(
                        df_SalesforceProviders['Other_Specialties__c'][i]
                    )
                else:
                    OtherSpecialties.append(
                        df_SalesforceProviders['Definitive_Specialty__c'][i]
                    )
        else:
            ProviderSpecialty.append(
                df_SalesforceProviders['Specialty_for_Location__c'][i]
            )
            if (
                str(df_SalesforceProviders['Definitive_Specialty__c'][i])
                != 'None'
                and str(df_SalesforceProviders['Other_Specialties__c'][i])
                != 'None'
            ):
                OtherSpecialties.append(
                    df_SalesforceProviders['Definitive_Specialty__c'][i]
                    + ', '
                    + df_SalesforceProviders['Other_Specialties__c'][i]
                )
            elif (
                str(df_SalesforceProviders['Definitive_Specialty__c'][i])
                == 'None'
                and str(df_SalesforceProviders['Other_Specialties__c'][i])
                != 'None'
            ):
                OtherSpecialties.append(
                    df_SalesforceProviders['Other_Specialties__c'][i]
                )
            else:
                OtherSpecialties.append(
                    df_SalesforceProviders['Definitive_Specialty__c'][i]
                )

    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Specialty_for_Location__c', axis=1
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Specialty_from_Provider__c', axis=1
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Definitive_Specialty__c', axis=1
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Other_Specialties__c', axis=1
    )
    df_SalesforceProviders.insert(2, 'ProviderSpecialty', ProviderSpecialty)
    df_SalesforceProviders.insert(3, 'OtherSpecialties', OtherSpecialties)

    df_SalesforceProviders['Street_Location__c'] = df_SalesforceProviders[
        'Street_Location__c'
    ].fillna('')
    df_SalesforceProviders = df_SalesforceProviders[
        df_SalesforceProviders['Street_Location__c'] != ''
    ]
    addresses = []
    for i in df_SalesforceProviders['Street_Location__c'].index.tolist():
        if str(df_SalesforceProviders['Address_1__c'][i]) != 'None':
            addresses.append(
                df_SalesforceProviders['Street_Location__c'][i]
                + ', '
                + str(df_SalesforceProviders['Address_1__c'][i])
                + ', '
                + df_SalesforceProviders['City_Location__c'][i]
                + ', '
                + df_SalesforceProviders['State_Location__c'][i]
            )
        else:
            addresses.append(
                df_SalesforceProviders['Street_Location__c'][i]
                + ', '
                + df_SalesforceProviders['City_Location__c'][i]
                + ', '
                + df_SalesforceProviders['State_Location__c'][i]
            )

    df_SalesforceProviders.insert(4, 'Address', addresses)
    df_SalesforceProviders.insert(
        8, 'ZipCode', df_SalesforceProviders.loc[:, 'Zip_Location__c'].str[:5]
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Zip_Location__c', axis=1
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Address_1__c', axis=1
    )

    df_SalesforceProviders.Location_Phone__c = (
        df_SalesforceProviders.Location_Phone__c.str.replace(
            ' ', '', regex=False
        )
    )
    df_SalesforceProviders.Location_Phone__c = (
        df_SalesforceProviders.Location_Phone__c.str.replace(
            '(', '', regex=False
        )
    )
    df_SalesforceProviders.Location_Phone__c = (
        df_SalesforceProviders.Location_Phone__c.str.replace(
            ')', '', regex=False
        )
    )
    df_SalesforceProviders.Location_Phone__c = (
        df_SalesforceProviders.Location_Phone__c.str.replace(
            '-', '', regex=False
        )
    )
    df_SalesforceProviders.Location_Phone__c = (
        df_SalesforceProviders.Location_Phone__c.str.replace(
            '.', '', regex=False
        )
    )
    phones = []
    for i in df_SalesforceProviders['Location_Phone__c'].index.tolist():
        if str(df_SalesforceProviders['Location_Phone__c'][i]) != 'None':
            phones.append(
                format(
                    int(df_SalesforceProviders['Location_Phone__c'][i][:-1]),
                    ",",
                ).replace(",", "-")
                + df_SalesforceProviders['Location_Phone__c'][i][-1]
            )
        else:
            phones.append('')
    df_SalesforceProviders.insert(11, 'Phone', phones)
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Location_Phone__c', axis=1
    )

    websites = []
    for i in df_SalesforceProviders.index.values.tolist():
        if df_SalesforceProviders['Location_Website__c'][i] is None:
            websites.append(
                df_SalesforceProviders['Account_Name__r'][i]['Website']
            )
        else:
            websites.append(df_SalesforceProviders['Location_Website__c'][i])

    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Location_Website__c', axis=1
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Provider_Website__c', axis=1
    )
    df_SalesforceProviders.insert(10, 'Website', websites)

    logosUrl = []
    for i in df_SalesforceProviders.index.values.tolist():
        logosUrl.append(
            df_SalesforceProviders['Account_Name__r'][i]['Provider_Logo__c']
        )

    # df_SalesforceProviders_dfid = df_SalesforceProviders['definitiveID']
    # df_DefinitiveProviders_dfid = df_DefinitiveProviders['Definitive ID']
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'definitiveID', axis=1
    )
    df_DefinitiveProviders = df_DefinitiveProviders.drop(
        'Definitive ID', axis=1
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Account_Name__c', axis=1
    )
    df_SalesforceProviders = df_SalesforceProviders.drop(
        'Account_Name__r', axis=1
    )

    df_SalesforceProviders.loc[:, 'ImageUrl'] = logosUrl
    df_SalesforceProviders.loc[:, 'DiscountSelfPay'] = 0

    cols = [
        'Location_Name__c',
        'Accepts_PI',
        'ProviderSpecialty',
        'OtherSpecialties',
        'Address',
        'State_Location__c',
        'City_Location__c',
        'Street_Location__c',
        'ZipCode',
        'GeoLocation__Latitude__s',
        'GeoLocation__Longitude__s',
        'Phone',
        'Website',
        'ProviderType',
        'ImageUrl',
        'DiscountSelfPay',
        'PreferredProvider',
    ]
    df_SalesforceProviders = df_SalesforceProviders[cols]
    df_SalesforceProviders_columns = [
        'ProviderName',
        'AcceptPI',
        'ProviderSpecialty',
        'OtherSpecialties',
        'Address',
        'State',
        'City',
        'Street',
        'ZipCode',
        'Latitude',
        'Longitude',
        'Phone',
        'Website',
        'ProviderType',
        'ImageUrl',
        'DiscountSelfPay',
        'PreferredProvider',
    ]
    df_SalesforceProviders.columns = df_SalesforceProviders_columns
    df_SalesforceProviders = df_SalesforceProviders[
        df_SalesforceProviders['AcceptPI'] != -1
    ]
    df_SalesforceProviders = df_SalesforceProviders[
        df_SalesforceProviders['ProviderType'] != -1
    ]
    df_DefinitiveProviders[~df_DefinitiveProviders['AcceptPI'] == -1]

    return [df_SalesforceProviders, df_DefinitiveProviders]
