from unittest.mock import MagicMock


class TestSalesforceDeleteTask:
    def test_salesforce_delete_task_with_default_args(
        self,
        mock_post_main_delete: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test Salesforce delete task with default arguments."""
        from integration.integration_code.tasks import salesforce_delete_task

        args_dict = {}
        result = salesforce_delete_task(args_dict)

        expected_partial_config = {
            'plaintiffs': False,
            'lawfirms': False,
            'legalpersonnel': False,
            'cases': True,
            'billings': True,
            'charges': False,
            'files': False,
            'notes': False,
        }
        mock_post_main_delete.assert_called_once_with(
            False, expected_partial_config, False
        )
        assert result == 'Salesforce Delete Complete'

    def test_salesforce_delete_task_with_custom_args(
        self,
        mock_post_main_delete: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test Salesforce delete task with custom arguments."""
        from integration.integration_code.tasks import salesforce_delete_task

        custom_partial_config = {
            'plaintiffs': True,
            'cases': True,
            'billings': False,
        }
        args_dict = {
            'test': True,
            'partial_config': custom_partial_config,
            'all_data': True,
        }
        result = salesforce_delete_task(args_dict)
        mock_post_main_delete.assert_called_once_with(
            True, custom_partial_config, True
        )
        assert result == 'Salesforce Delete Complete'

    def test_salesforce_delete_task_with_partial_args(
        self,
        mock_post_main_delete: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test Salesforce delete task with partial arguments."""
        from integration.integration_code.tasks import salesforce_delete_task

        args_dict = {'test': True, 'all_data': True}
        result = salesforce_delete_task(args_dict)
        expected_partial_config = {
            'plaintiffs': False,
            'lawfirms': False,
            'legalpersonnel': False,
            'cases': True,
            'billings': True,
            'charges': False,
            'files': False,
            'notes': False,
        }
        mock_post_main_delete.assert_called_once_with(
            True, expected_partial_config, True
        )
        assert result == 'Salesforce Delete Complete'

    def test_salesforce_delete_task_lock_not_acquired(
        self,
        mock_advisory_lock_failure: MagicMock,
    ):
        """
        Test that salesforce_delete_task returns the correct message when the advisory lock is not acquired.
        """
        from integration.integration_code.tasks import salesforce_delete_task

        args_dict = {}

        result = salesforce_delete_task(args_dict)
        assert result == "Salesforce Delete Failed: Lock not acquired"
