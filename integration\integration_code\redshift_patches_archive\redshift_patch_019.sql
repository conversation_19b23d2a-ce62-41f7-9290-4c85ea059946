CREATE TABLE integration.dev.notes (
    gainid CHARACTER VARYING(16) NOT NULL DEFAULT '':: CHARACTER VARYING ENCODE lzo,
    note CHARACTER VARYING(10000) ENCODE lzo,
    notecreatorname CHARACTER VARYING(100) ENCOD<PERSON> lzo,
    plaintiffid CHARACTER VARYING(20) ENCODE lzo,
    medicalfacilityid CHARACTER VARYING(20) ENCODE lzo,
    lawfirmid CHARACTER VARYING(20) ENCODE lzo,
    legalpersonnelid CHARACTER VARYING(20) ENCODE lzo,
    caseid CHARACTER VARYING(20) ENCODE lzo,
    intakeid CHARACTER VARYING(20) ENCODE lzo,
    insuranceid CHARACTER VARYING(20) ENCODE lzo,
    lienid CHARACTER VARYING(20) ENCODE lzo,
    disbursalid CHARACTER VARYING(20) ENCODE lzo,
    billingid CHARACTER VARYING(20) ENCODE lzo,
    chargeid CHARACTER VARYING(20) <PERSON>NCODE lzo,
    transactionid CHARACTER VARYING(20) <PERSON><PERSON><PERSON><PERSON> lzo,
    fileid CHARACTER VARYING(20) <PERSON>NCOD<PERSON> lzo,
    surgeryid CHARACTER VARYING(20) ENCODE lzo,
    relevanttogain BOOLEAN ENCODE raw,
    sourcecreatedatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    sourcemodifieddatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    modifieddatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    todelete BOOLEAN DEFAULT false ENCODE raw,
    todeletesystem CHARACTER VARYING(100) ENCODE lzo,
    deletepreventoverride BOOLEAN DEFAULT false ENCODE raw,
    deletepreventoverridereason CHARACTER VARYING(1000) ENCODE lzo,
    createdatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    PRIMARY KEY (gainid),
    FOREIGN KEY (plaintiffid) REFERENCES integration.dev.plaintiffs(gainid),
    FOREIGN KEY (medicalfacilityid) REFERENCES integration.dev.medicalfacilities(gainid),
    FOREIGN KEY (lawfirmid) REFERENCES integration.dev.lawfirms(gainid),
    FOREIGN KEY (legalpersonnelid) REFERENCES integration.dev.legalpersonnel(gainid),
    FOREIGN KEY (caseid) REFERENCES integration.dev.cases(gainid),
    FOREIGN KEY (intakeid) REFERENCES integration.dev.intakes(gainid),
    FOREIGN KEY (insuranceid) REFERENCES integration.dev.insurances(gainid),
    FOREIGN KEY (lienid) REFERENCES integration.dev.liens(gainid),
    FOREIGN KEY (disbursalid) REFERENCES integration.dev.disbursals(gainid),
    FOREIGN KEY (billingid) REFERENCES integration.dev.billings(gainid),
    FOREIGN KEY (chargeid) REFERENCES integration.dev.charges(gainid),
    FOREIGN KEY (transactionid) REFERENCES integration.dev.transactions(gainid),
    FOREIGN KEY (fileid) REFERENCES integration.dev.files(gainid),
    FOREIGN KEY (surgeryid) REFERENCES integration.dev.surgery(gainid)
) DISTSTYLE AUTO;

CREATE TABLE integration.staging.notes (
    gainid CHARACTER VARYING(16) NOT NULL DEFAULT '':: CHARACTER VARYING ENCODE lzo,
    note CHARACTER VARYING(10000) ENCODE lzo,
    notecreatorname CHARACTER VARYING(100) ENCODE lzo,
    plaintiffid CHARACTER VARYING(20) ENCODE lzo,
    medicalfacilityid CHARACTER VARYING(20) ENCODE lzo,
    lawfirmid CHARACTER VARYING(20) ENCODE lzo,
    legalpersonnelid CHARACTER VARYING(20) ENCODE lzo,
    caseid CHARACTER VARYING(20) ENCODE lzo,
    intakeid CHARACTER VARYING(20) ENCODE lzo,
    insuranceid CHARACTER VARYING(20) ENCODE lzo,
    lienid CHARACTER VARYING(20) ENCODE lzo,
    disbursalid CHARACTER VARYING(20) ENCODE lzo,
    billingid CHARACTER VARYING(20) ENCODE lzo,
    chargeid CHARACTER VARYING(20) ENCODE lzo,
    transactionid CHARACTER VARYING(20) ENCODE lzo,
    fileid CHARACTER VARYING(20) ENCODE lzo,
    surgeryid CHARACTER VARYING(20) ENCODE lzo,
    relevanttogain BOOLEAN ENCODE raw,
    sourcecreatedatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    sourcemodifieddatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    modifieddatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    todelete BOOLEAN DEFAULT false ENCODE raw,
    todeletesystem CHARACTER VARYING(100) ENCODE lzo,
    deletepreventoverride BOOLEAN DEFAULT false ENCODE raw,
    deletepreventoverridereason CHARACTER VARYING(1000) ENCODE lzo,
    createdatetime TIMESTAMP WITHOUT TIME ZONE ENCODE az64,
    PRIMARY KEY (gainid),
    FOREIGN KEY (plaintiffid) REFERENCES integration.staging.plaintiffs(gainid),
    FOREIGN KEY (medicalfacilityid) REFERENCES integration.staging.medicalfacilities(gainid),
    FOREIGN KEY (lawfirmid) REFERENCES integration.staging.lawfirms(gainid),
    FOREIGN KEY (legalpersonnelid) REFERENCES integration.staging.legalpersonnel(gainid),
    FOREIGN KEY (caseid) REFERENCES integration.staging.cases(gainid),
    FOREIGN KEY (intakeid) REFERENCES integration.staging.intakes(gainid),
    FOREIGN KEY (insuranceid) REFERENCES integration.staging.insurances(gainid),
    FOREIGN KEY (lienid) REFERENCES integration.staging.liens(gainid),
    FOREIGN KEY (disbursalid) REFERENCES integration.staging.disbursals(gainid),
    FOREIGN KEY (billingid) REFERENCES integration.staging.billings(gainid),
    FOREIGN KEY (chargeid) REFERENCES integration.staging.charges(gainid),
    FOREIGN KEY (transactionid) REFERENCES integration.staging.transactions(gainid),
    FOREIGN KEY (fileid) REFERENCES integration.staging.files(gainid),
    FOREIGN KEY (surgeryid) REFERENCES integration.staging.surgery(gainid)
) DISTSTYLE AUTO;