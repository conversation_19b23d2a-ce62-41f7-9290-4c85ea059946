Stack trace:
Frame         Function      Args
0007FFFFB740  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x2118E
0007FFFFB740  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x69BA
0007FFFFB740  0002100469F2 (00021028DF99, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB740  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB740  00021006A545 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA20  00021006B9A5 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF976530000 ntdll.dll
7FF974BF0000 KERNEL32.DLL
7FF973620000 KERNELBASE.dll
7FF975B60000 USER32.dll
7FF973FE0000 win32u.dll
7FF974D90000 GDI32.dll
7FF974010000 gdi32full.dll
7FF973DD0000 msvcp_win.dll
7FF973CB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF974A80000 advapi32.dll
7FF9762B0000 msvcrt.dll
7FF974B40000 sechost.dll
7FF973FB0000 bcrypt.dll
7FF975DF0000 RPCRT4.dll
7FF972E30000 CRYPTBASE.DLL
7FF973C30000 bcryptPrimitives.dll
7FF9741C0000 IMM32.DLL
