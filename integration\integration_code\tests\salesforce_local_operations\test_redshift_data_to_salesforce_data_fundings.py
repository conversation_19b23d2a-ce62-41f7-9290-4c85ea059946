# test_salesforce_local_operations.py

import datetime
from typing import Any
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

from integration.integration_code import salesforce_local_operations as slo


@pytest.fixture
def mock_get_gainid_to_sourcename_map():
    with patch(
        "integration.integration_code.salesforce_local_operations.aws_operations.get_gainid_to_sourcename_map"
    ) as mock_get_gainid_to_sourcename_map:
        mock_get_gainid_to_sourcename_map.return_value = {"101": "TestSource"}
        yield mock_get_gainid_to_sourcename_map


@pytest.fixture
def mock_get_gainid_to_sourceid_map():
    with patch(
        "integration.integration_code.salesforce_local_operations.aws_operations.get_gainid_to_sourceid_map"
    ) as mock_get_gainid_to_sourceid_map:
        mock_get_gainid_to_sourceid_map.return_value = {"101": "TestSourceId"}
        yield mock_get_gainid_to_sourceid_map


@pytest.fixture
def mock_get_sf_record_type_id():
    with patch(
        "integration.integration_code.salesforce_local_operations.salesforce_operations.get_sf_record_type_id"
    ) as mock_get_sf_record_type_id:
        mock_get_sf_record_type_id.return_value = "recordtypeid123"
        yield mock_get_sf_record_type_id


def setup_funding_flip_scenario(
    funding_stage: str, funding_sub_stage: str
) -> dict[str, Any]:
    test_bucket = "test-bucket"
    test_directory = "test-directory"
    slo.automated_sf_updates['Billings'] = ["TestSource"]

    # Prepare Redshift cases data (opportunity exists)
    redshift_cases_data = pd.DataFrame(
        [
            {
                "GainId": 1,
                "PlaintiffName": "John Doe",
            }
        ]
    )

    redshift_cases_data_ids = {"1": "sf_case_id_1"}

    # Prepare Redshift billings data (Medical Funding - Serviced)
    redshift_billings_data = pd.DataFrame(
        [
            {
                "GainId": 101,
                "CaseId": 1,
                "Type": "Medical Funding",
                "GainType": "Medical Funding - Serviced",
                "MedicalFacilityId": "med1",
                "PartnerAccountId": "partner1",
                "MedicalClaimNumber": "MCN123",
                "DateOfService": datetime.datetime(2023, 1, 1),
                "TotalAmount": 1000,
                "TotalNonGainAdjustment": 0,
                "TotalNonGainAmountPaidToProvider": 0,
                "TotalBalance": 500,
                "TotalAmountSent": 0,
                "TotalGainPreNegotiationAdjustment": 0,
                "TotalGainPreNegotiationAmountPaidToProvider": 0,
                "TotalDeductible": 0,
                "TotalCoinsurance": 0,
                "TotalCopayment": 0,
            }
        ]
    )

    redshift_billings_data_ids = {"101": "sf_funding_id_101"}

    # Salesforce fundings data: funding is Historic/Withdrawn by Provider
    sf_fundings_data = {
        "fundings_check_stage_sub_stage_before_update": {
            "sf_funding_id_101": {
                "Funding_Stage__c": funding_stage,
                "Funding_Sub_Stage__c": funding_sub_stage,
            }
        },
        "fundings_mapping_res": {},
        "fundings_potential_duplicates_check_before_insert": {},
    }

    # Name/ID maps
    name_id_map = {}
    locationid_id_map = {"med1": "sf_med1", "partner1": "sf_partner1"}
    id_parentid_map = {}

    curr_timestamp = datetime.datetime.now()

    return {
        "test_bucket": test_bucket,
        "test_directory": test_directory,
        "redshift_cases_data": redshift_cases_data,
        "redshift_cases_data_ids": redshift_cases_data_ids,
        "redshift_billings_data": redshift_billings_data,
        "redshift_billings_data_ids": redshift_billings_data_ids,
        "sf_fundings_data": sf_fundings_data,
        "name_id_map": name_id_map,
        "locationid_id_map": locationid_id_map,
        "id_parentid_map": id_parentid_map,
        "curr_timestamp": curr_timestamp,
    }


class TestRedshiftDataToSalesforceDataFundings:
    def test_funding_stage_flip_historic_withdrawn_by_provider(
        self,
        mock_get_gainid_to_sourcename_map: MagicMock,
        mock_get_gainid_to_sourceid_map: MagicMock,
        mock_get_sf_record_type_id: MagicMock,
    ):
        args = setup_funding_flip_scenario("Historic", "Withdrawn by Provider")
        result = slo.redshift_data_to_salesforce_data_fundings(
            args["redshift_cases_data"],
            args["redshift_cases_data_ids"],
            args["redshift_billings_data"],
            args["redshift_billings_data_ids"],
            args["sf_fundings_data"],
            args["locationid_id_map"],
        )
        assert len(result["update"]) == 1
        update_record = result["update"][0]
        assert update_record["Funding_Stage__c"] == "Serviced"
        assert update_record["Funding_Sub_Stage__c"] == ""
        assert update_record["Id"] == "sf_funding_id_101"

    def test_funding_stage_flip_historic_not_withdrawn_by_provider(
        self,
        mock_get_gainid_to_sourcename_map: MagicMock,
        mock_get_gainid_to_sourceid_map: MagicMock,
        mock_get_sf_record_type_id: MagicMock,
    ):
        args = setup_funding_flip_scenario("Historic", "Test Sub Stage")
        result = slo.redshift_data_to_salesforce_data_fundings(
            args["redshift_cases_data"],
            args["redshift_cases_data_ids"],
            args["redshift_billings_data"],
            args["redshift_billings_data_ids"],
            args["sf_fundings_data"],
            args["locationid_id_map"],
        )
        assert len(result["update"]) == 1
        update_record = result["update"][0]
        assert "Funding_Stage__c" not in update_record
        assert "Funding_Sub_Stage__c" not in update_record
        assert update_record["Id"] == "sf_funding_id_101"

    def test_funding_stage_flip_not_historic_withdrawn_by_provider(
        self,
        mock_get_gainid_to_sourcename_map: MagicMock,
        mock_get_gainid_to_sourceid_map: MagicMock,
        mock_get_sf_record_type_id: MagicMock,
    ):
        args = setup_funding_flip_scenario(
            "Test Stage", "Withdrawn by Provider"
        )
        result = slo.redshift_data_to_salesforce_data_fundings(
            args["redshift_cases_data"],
            args["redshift_cases_data_ids"],
            args["redshift_billings_data"],
            args["redshift_billings_data_ids"],
            args["sf_fundings_data"],
            args["locationid_id_map"],
        )
        assert len(result["update"]) == 1
        update_record = result["update"][0]
        assert "Funding_Stage__c" not in update_record
        assert "Funding_Sub_Stage__c" not in update_record
        assert update_record["Id"] == "sf_funding_id_101"

    def test_funding_stage_flip_not_historic_not_withdrawn_by_provider(
        self,
        mock_get_gainid_to_sourcename_map: MagicMock,
        mock_get_gainid_to_sourceid_map: MagicMock,
        mock_get_sf_record_type_id: MagicMock,
    ):
        args = setup_funding_flip_scenario("Test Stage", "Test Sub Stage")
        result = slo.redshift_data_to_salesforce_data_fundings(
            args["redshift_cases_data"],
            args["redshift_cases_data_ids"],
            args["redshift_billings_data"],
            args["redshift_billings_data_ids"],
            args["sf_fundings_data"],
            args["locationid_id_map"],
        )
        assert len(result["update"]) == 1
        update_record = result["update"][0]
        assert "Funding_Stage__c" not in update_record
        assert "Funding_Sub_Stage__c" not in update_record
        assert update_record["Id"] == "sf_funding_id_101"
