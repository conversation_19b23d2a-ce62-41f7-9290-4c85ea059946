import dataclasses
import datetime
import json
import logging
import os
import typing

import django.conf
import django.db
import pandas as pd
from opensearchpy import OpenSearch

from .. import models
from . import context
from .mod_aws_operations import s3_operations
from .utils import file_utils

logging.getLogger("opensearch").disabled = True

app_config = django.conf.settings
settings = app_config.SETTINGS
APP_LOGGER_NAME = 'app_logger'


@dataclasses.dataclass
class AuditLog:
    route_section: str = 'Function not passed'
    additional_details: typing.Optional[str] = None
    is_error: int = 0
    exception_message: typing.Optional[str] = None


def reset_db_connection():
    django.db.close_old_connections()


def audit_log(
    route_section: str = 'Function not passed',
    additional_details: typing.Optional[str] = None,
    is_error: int = 0,
    exception_message: typing.Optional[str | Exception] = None,
):
    request_context = context.request_context.get()
    # Close the current database connection to force a new one on the next query
    reset_db_connection()
    route = request_context.get('route')
    assert route is not None, 'Route not passed'
    with django.db.connection.cursor() as _:
        audit_log_entry = models.audit_log(
            route=route,
            route_section=route_section,
            additional_details=additional_details,
            is_error=is_error,
            exception_message=exception_message,
        )
        audit_log_entry.save()


class AuditDBHandler(logging.Handler):
    def emit(self, record: logging.LogRecord):
        try:
            if record.name != APP_LOGGER_NAME:
                return

            filename = getattr(record, 's3_filename', None)
            flush = getattr(record, 'flush', None)
            if filename or flush:  # skip s3 logging
                return

            route_section = (
                getattr(record, "route_section", None)
                or record.module + ' | ' + record.funcName
            )
            additional_details = getattr(record, "additional_details", None)
            is_error = 1 if record.levelname == 'ERROR' else 0
            exception_message = record.message

            audit_log(
                route_section=route_section,
                additional_details=additional_details,
                is_error=is_error,
                exception_message=exception_message,
            )

        except Exception:
            self.handleError(record)


class OpenSearchHandler(logging.Handler):
    """
    A logging handler that sends log records to OpenSearch.
    """

    def __init__(
        self,
        hosts: typing.Optional[typing.List[str]] = None,
        index_name: str = "application-logs",
        use_ssl: bool = True,
        verify_certs: bool = True,
        **kwargs: typing.Any,
    ):
        super().__init__()

        # Configure OpenSearch client
        client_config = {
            'hosts': hosts,
            'use_ssl': use_ssl,
            'verify_certs': verify_certs,
            'ssl_show_warn': False,
        }

        # Add any additional configuration
        client_config.update(kwargs)

        try:
            self.client = OpenSearch(**client_config)
            self.index_name = index_name
        except Exception as e:
            # If OpenSearch is not available, disable this handler
            self.client = None
            print(f"Warning: OpenSearch connection failed: {e}")

    def emit(self, record: logging.LogRecord):
        """
        Emit a log record to OpenSearch.
        """
        flush = getattr(record, 'flush', None)
        if not self.client or flush or record.name == 'opensearch':
            return

        try:
            # Get request context if available
            request_context = context.request_context.get()

            # Create the log document
            log_doc = {
                '@timestamp': datetime.datetime.now(
                    datetime.timezone.utc
                ).isoformat(),
                'level': record.levelname,
                'logger': record.name,
                'message': record.getMessage(),
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno,
                'pathname': record.pathname,
                'thread': record.thread,
                'thread_name': record.threadName,
                'process': record.process,
            }

            # Add request context if available
            if request_context:
                log_doc['route'] = request_context.get('route')
                log_doc['request_id'] = request_context.get('request_id')

            # Add custom attributes from the log record
            route_section = getattr(record, "route_section", None)
            if route_section:
                log_doc['route_section'] = route_section

            info_record = getattr(record, "record", None)
            if info_record:
                log_doc['record'] = json.dumps(info_record)

            additional_details = getattr(record, "additional_details", None)
            if additional_details:
                log_doc['additional_details'] = additional_details

            # Add exception information if present
            if record.exc_info:
                log_doc['exception'] = {
                    'type': (
                        record.exc_info[0].__name__
                        if record.exc_info[0]
                        else None
                    ),
                    'message': (
                        str(record.exc_info[1]) if record.exc_info[1] else None
                    ),
                    'traceback': (
                        self.format(record) if self.formatter else None
                    ),
                }

            # Add stack trace if present
            if record.stack_info:
                log_doc['stack_info'] = record.stack_info

            # Generate date-based index name: IndexName-yyyy.MM.dd
            current_date = datetime.datetime.now(datetime.timezone.utc)
            date_suffix = current_date.strftime("%Y.%m.%d")
            dynamic_index_name = f"{self.index_name}-{date_suffix}"

            # Index the document
            self.client.index(
                index=dynamic_index_name,
                body=log_doc,
                refresh=False,  # Don't force refresh for performance
            )

        except Exception:
            self.handleError(record)


class S3Handler(logging.Handler):
    """
    A simple logging handler that writes log records to S3 files based on 'filename' and 'directory'
    fields in the log record's extra data.

    Groups records by filename and flushes them in batches for better performance.
    No threads - simple and synchronous operation.
    """

    def __init__(
        self,
    ):
        super().__init__()
        # Buffer to group records by filename
        self.record_buffers: typing.Dict[
            str, typing.List[logging.LogRecord]
        ] = {}

    def emit(self, record: logging.LogRecord) -> None:
        """
        Emit a log record by adding it to the appropriate buffer.
        Flushes the buffer if it reaches max size.
        """
        try:
            flush = getattr(record, 'flush', None)
            if flush:
                self._flush_all_buffers()
                return

            filename = getattr(record, 's3_filename', None)

            if not filename:
                return

            directory = getattr(record, 's3_directory', '')

            buffer_key = f"{directory}/{filename}"

            if buffer_key not in self.record_buffers:
                self.record_buffers[buffer_key] = []

            self.record_buffers[buffer_key].append(record)

        except Exception:
            self.handleError(record)

    def _generate_and_log_manual_review_s3_file(
        self,
        bucket: str,
        directory: str,
        subdirectory: str,
        timestamp: str | datetime.datetime | None,
        records: list[typing.Any],
        filename_suffix: str,
    ) -> None:
        if isinstance(timestamp, datetime.datetime):
            timestamp_string = timestamp.strftime('%Y-%m-%d-%H-%M-%S')
        elif isinstance(timestamp, str):
            timestamp_string = timestamp
        else:
            timestamp_string = datetime.datetime.now().strftime(
                '%Y-%m-%d-%H-%M-%S'
            )

        filename = f'integration/integration_code/{filename_suffix}_{timestamp_string}.csv'
        df = pd.DataFrame(records)
        df.to_csv(filename, index=False)
        s3_operations.upload_s3(
            bucket,
            f'{directory}/{subdirectory}',
            filename,
        )
        file_utils.clean_up_file(filename)

    def _flush_buffer(self, buffer_key: str) -> None:
        """Flush a specific buffer to S3."""
        try:
            if (
                buffer_key not in self.record_buffers
                or not self.record_buffers[buffer_key]
            ):
                return

            records = self.record_buffers[buffer_key].copy()
            self.record_buffers[buffer_key].clear()

            record = records[0]

            request_context = context.request_context.get()

            bucket = getattr(
                record, 's3_bucket', request_context.get('bucket', '')
            )
            directory = getattr(
                record, 's3_directory', request_context.get('directory', '')
            )
            subdirectory = getattr(record, 's3_subdirectory', '')
            filename = getattr(record, 's3_filename', '')
            timestamp = getattr(
                record, 'timestamp', request_context.get('timestamp')
            )

            info_records = list(
                map(
                    lambda record: getattr(record, 'record'),
                    records,
                )
            )
            self._generate_and_log_manual_review_s3_file(
                bucket,
                directory,
                subdirectory,
                timestamp,
                info_records,
                filename,
            )

        except Exception:
            # Create a dummy record for error handling
            dummy_record = logging.LogRecord(
                name="S3Handler",
                level=logging.ERROR,
                pathname="",
                lineno=0,
                msg=f"Failed to flush buffer {buffer_key}",
                args=(),
                exc_info=None,
            )
            self.handleError(dummy_record)

    def _flush_all_buffers(self) -> None:
        """Flush all buffers to S3."""
        buffer_keys = list(self.record_buffers.keys())
        for buffer_key in buffer_keys:
            self._flush_buffer(buffer_key)

    def flush(self) -> None:
        """
        Manually flush all buffers to S3.
        This can be called to force immediate writing of all buffered records.
        """
        self._flush_all_buffers()

    def close(self) -> None:
        """
        Close the handler and clean up resources.
        Ensures all buffered records are flushed before closing.
        """
        try:
            # Flush all remaining buffers
            self._flush_all_buffers()
        except Exception:
            pass
        finally:
            super().close()


log_level = os.getenv("LOG_LEVEL", "INFO").upper()
log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
date_format = "%Y-%m-%d %H:%M:%S"

formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

# App Logger
app_logger = logging.getLogger()
app_logger.setLevel(log_level)


# Console Handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
console_handler.setLevel(log_level)
app_logger.addHandler(console_handler)

# DB Handler
audit_db_handler = AuditDBHandler()
audit_db_handler.setLevel(log_level)
app_logger.addHandler(audit_db_handler)

# Opensearch Handler
opensearch_enabled = settings['Opensearch']['Enabled'].lower() == 'true'
if opensearch_enabled:
    opensearch_hosts = settings['Opensearch']['Hosts'].split(",")
    opensearch_index = settings['Opensearch']['IndexName']

    opensearch_handler = OpenSearchHandler(
        hosts=opensearch_hosts,
        index_name=opensearch_index,
        use_ssl=True,
        verify_certs=True,
    )
    opensearch_handler.setLevel(log_level)
    opensearch_handler.setFormatter(formatter)
    app_logger.addHandler(opensearch_handler)

# S3 Handler
s3_enabled = os.getenv("S3_LOGGING_ENABLED", "true").lower() == 'true'
if s3_enabled:
    s3_handler = S3Handler()
    s3_handler.setLevel(log_level)
    s3_handler.setFormatter(formatter)
    app_logger.addHandler(s3_handler)


def get_logger() -> logging.Logger:
    return logging.getLogger(APP_LOGGER_NAME)
