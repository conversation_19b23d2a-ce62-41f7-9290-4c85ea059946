import typing
from unittest.mock import <PERSON><PERSON>ock

import psycopg
import psycopg.rows
import psycopg.sql
import pytest
from mypy_boto3_s3 import S3Client
from psycopg.rows import dict_row
from typing_extensions import LiteralString

from integration.integration_code.mod_aws_operations import s3_operations
from integration.integration_code.utils import file_utils


def verify_audit_errors(mock: MagicMock):

    for call_args in mock.call_args_list:
        if call_args.kwargs.get("is_error") == 1 or (
            len(call_args.args) > 3 and call_args.args[3] == 1
        ):
            pytest.fail(
                f"audit_log was called with is_error=1. Kwargs: {call_args.kwargs}, args: {call_args.args}"
            )


def verify_if_log_exists(mock: MagicMock, log: str) -> bool:

    for call_args in mock.call_args_list:
        if (
            call_args.kwargs.get("exception_message") == log
            or log in call_args.args
        ):
            return True

    return False


def get_object(
    conn: psycopg.Connection[typing.Any], obj: str, ids: list[str]
) -> psycopg.rows.DictRow:
    cursor = conn.cursor(row_factory=dict_row)
    ids_string = ",'".join(ids)
    cursor.execute(
        psycopg.sql.SQL(
            typing.cast(
                LiteralString,
                f'''
            SELECT * FROM {obj}
            where gainid in ('{ids_string}')
        ''',
            )
        )
    )
    result = cursor.fetchone()
    assert result is not None
    return result


def get_tables_count(
    conn: psycopg.Connection[typing.Any], tables: list[str] | None = None
) -> psycopg.rows.DictRow:
    default_tables = [
        'plaintiffs',
        'medicalfacilities',
        'lawfirms',
        'legalpersonnel',
        'cases',
        'billings',
        'charges',
        'transactions',
        'files',
        'notes',
        'insurances',
    ]

    # Use provided tables or default to all tables
    tables_to_count = tables if tables is not None else default_tables

    select_clauses = [
        f"(SELECT count(*) FROM {table}) AS {table}"
        for table in tables_to_count
    ]
    query = psycopg.sql.SQL(
        typing.cast(
            LiteralString,
            f"SELECT\n    {',    '.join(select_clauses)}",
        )
    )

    cursor = conn.cursor(row_factory=dict_row)
    cursor.execute(query)
    result = cursor.fetchone()
    assert result is not None
    return result


def get_s3_to_postgres_data(s3: S3Client, mock_s3_to_postgres: MagicMock):
    bucket = mock_s3_to_postgres.call_args[0][0]
    results = mock_s3_to_postgres.call_args[0][1]
    items = s3.list_objects(Bucket=bucket, Prefix=results)
    assert 'Contents' in items
    files = [
        s3_operations.download_s3_inmemory(bucket, item['Key'])
        for item in items['Contents']
        if 'Key' in item
    ]
    json_files = [file_utils.csv_bytes_to_json(file) for file in files if file]
    for json_file in json_files:
        for row in json_file:
            auto_generated_fields = [
                'gainid',
                'caseid',
                'medicalfacilityid',
                'lawfirmid',
                'legalpersonnelid',
                'plaintiffid',
                'attorneyid',
                'paralegalid',
                'casemanagerid',
                'cocounselid',
                'coparalegalid',
                'cocasemanagerid',
                'billingid',
                'chargeid',
                'transactionid',
                'fileid',
                'noteid',
                'partneraccountid',
            ]
            for auto_generated_field in auto_generated_fields:
                if auto_generated_field in row:
                    row[auto_generated_field] = '[AUTO_GENERATED]'
    return json_files
