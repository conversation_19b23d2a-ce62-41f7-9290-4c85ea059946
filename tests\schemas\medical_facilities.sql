CREATE TABLE medicalfacilities (
    gainid character varying(16) NOT NULL,
    name character varying(250) NOT NULL,
    phone character varying(100),
    fax character varying(100),
    email character varying(100),
    followupemail character varying(100),
    billingaddressline1 character varying(250),
    billingaddressline2 character varying(250),
    billingaddresscity character varying(100),
    billingaddressstate character varying(100),
    billingaddresszip character varying(20),
    physicaladdressline1 character varying(250),
    physicaladdressline2 character varying(250),
    physicaladdresscity character varying(100),
    physicaladdressstate character varying(100),
    physicaladdresszip character varying(20),
    website character varying(100),
    contracttype character varying(100),
    specialties character varying(1000),
    description character varying(1000),
    portalaccount boolean,
    notes character varying(1000),
    parentid character varying(100),
    relevanttogain boolean,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    modifieddatetime timestamp without time zone,
    PRIMARY KEY (gainid),
    UNIQUE (gainid)
);

ALTER TABLE medicalfacilities
ADD CONSTRAINT fk_medicalfacilities_parentid
FOREIGN KEY (parentid) REFERENCES medicalfacilities(gainid);