# pyright: reportGeneralTypeIssues=error,reportAssignmentType=error
import base64
import copy
import datetime
import json
import pathlib
import typing

import pandas as pd

from . import (
    aws_operations,
    data_review_logger,
    id_record,
    local_operations,
    logger_config,
    salesforce_operations,
    shared,
)
from .models import pipeline, types
from .utils import file_utils

logger = logger_config.get_logger()

with (
    open(
        'integration/integration_code/canonical_model.json', 'rb'
    ) as canonical_map_file,
    open(
        'integration/integration_code/canonical_data_model.json', 'rb'
    ) as canonical_data_map_file,
    open(
        'integration/integration_code/variable_balance_providers.json', 'rb'
    ) as variable_balance_providers_file,
    open(
        'integration/integration_code/automated_sf_updates.json', 'rb'
    ) as automated_sf_updates_file,
    open(
        'integration/integration_code/state_name_mapping.json',
        'rb',
    ) as state_name_mapping_file,
):
    canonical = json.load(canonical_map_file)
    canonical_objects = set(canonical.keys())
    canonical_data = json.load(canonical_data_map_file)
    variable_balance_providers = json.load(variable_balance_providers_file)[
        "name"
    ]
    automated_sf_updates = json.load(automated_sf_updates_file)
    state_name_mapping = json.load(state_name_mapping_file)
    state_code_to_name_map = state_name_mapping["state_code_to_name_map"]
    state_name_to_code_map = state_name_mapping["state_name_to_code_map"]


def search_rs_record_in_sf(
    rs_record: typing.NamedTuple,
    canonical_model: dict[str, str],
    sf_records: dict[str, set[str]],
    rs_record_columns: list[str],
) -> list[str]:
    '''
    Search for a redshift record in salesforce.
    Only matches the columns that are specified in the rs_record_columns list.
    '''
    rs_record_dict = rs_record._asdict()
    for column in rs_record_columns:
        if rs_record_dict[column] is None or rs_record_dict[column] == '':
            continue
        column_value = rs_record_dict[column]
        column_value = local_operations.remove_nonalphanumeric_characters(
            column_value
        ).lower()
        if column_value in sf_records.keys():
            return list(sf_records[column_value])
    return []


def remove_sf_records(sf_ids: list[str]):
    result = aws_operations.get_gainid_records_by_ids(
        None, sf_ids, None, None, None, None, ''
    )

    if not result:
        return

    gain_records = list(set([record['gain_record'] for record in result]))

    aws_operations.update_gainid_records_by_sf_ids(
        gain_records,
        [
            id_record.IdRecord(
                '', datetime.datetime.now(), datetime.datetime.now()
            )
        ]
        * len(gain_records),
    )

    for sf_id in sf_ids:
        data_review_logger.invalidate_manual_review_data_by_sfid(sf_id)


def redshift_data_to_salesforce_data_plaintiffaccounts(
    redshift_plaintiffs_data: pd.DataFrame,
    redshift_plaintiffs_data_ids: dict[str, str],
    sf_plaintiffaccounts_data: dict[str, typing.Any],
) -> pipeline.AffectedData:
    redshift_to_sf_plaintiffaccount_update = []
    redshift_plaintiffs_gainid_update = []
    redshift_to_sf_plaintiffaccount_insert = []
    redshift_plaintiffs_gainid_insert = []
    manual_review_for_plaintiff_update = []

    record_type_id = salesforce_operations.get_sf_record_type_id(
        'Account', 'Plaintiff'
    )
    automated_sf_updates_sources = set(automated_sf_updates['Plaintiffs'])

    gainids = redshift_plaintiffs_data['GainId'].tolist()
    gainid_to_source_name = aws_operations.get_gainid_to_sourcename_map(
        gainids
    )

    for plaintiff in redshift_plaintiffs_data.itertuples():
        if not plaintiff.Name or not plaintiff.DateOfBirth:
            logger.warning(
                f"Plaintiff missing name or DOB manual review",
                extra={
                    'record': plaintiff._asdict(),
                    's3_filename': f'plaintiff_missing_name_or_DOB_manual_review',
                    's3_subdirectory': 'plaintiffs_manual_review',
                },
            )
            continue

        record = {
            'Gain_ID__c': plaintiff.GainId,
            'Name': plaintiff.Name,
            'Date_of_Birth__c': plaintiff.DateOfBirth.strftime('%Y-%m-%d'),
            'RecordTypeId': record_type_id,
        }

        automated_sf_update_record = (
            gainid_to_source_name.get(str(plaintiff.GainId), None)
            in automated_sf_updates_sources
        )
        salesforce_id = redshift_plaintiffs_data_ids.get(
            plaintiff.GainId, None
        )

        if salesforce_id:
            record['Id'] = salesforce_id
            if automated_sf_update_record:
                redshift_to_sf_plaintiffaccount_update.append(record)
                redshift_plaintiffs_gainid_update.append(plaintiff.GainId)
            else:
                record['RedshiftId'] = str(plaintiff.GainId)
                manual_review_for_plaintiff_update.append(record)
            continue

        if gainid_to_source_name.get(str(plaintiff.GainId), None) not in {
            'ATI',
            'Jopari',
        }:
            name_dob_key = (
                plaintiff.Name.lower(),
                plaintiff.DateOfBirth.strftime('%Y-%m-%d'),
            )
            matching_accounts = sf_plaintiffaccounts_data[
                'plaintiff_accounts_nameDOB'
            ].get(name_dob_key, [])
            if len(matching_accounts) > 1:
                logger.warning(
                    f"Multiple matching plaintiff accounts manual review",
                    extra={
                        'record': record,
                        's3_filename': f'multiple_matching_accounts',
                        's3_subdirectory': 'plaintiffs_manual_review',
                    },
                )
                continue
            elif len(matching_accounts) == 1:
                record['Id'] = matching_accounts[0]
                if automated_sf_update_record:
                    redshift_to_sf_plaintiffaccount_update.append(record)
                    redshift_plaintiffs_gainid_update.append(plaintiff.GainId)
                else:
                    record['RedshiftId'] = str(plaintiff.GainId)
                    manual_review_for_plaintiff_update.append(record)
                continue

        record['Lead_Source__c'] = 'Data Integration'
        record['Lead_Source_Name__c'] = gainid_to_source_name.get(
            str(plaintiff.GainId), None
        )
        redshift_to_sf_plaintiffaccount_insert.append(record)
        redshift_plaintiffs_gainid_insert.append(str(plaintiff.GainId))

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_plaintiffaccount_update,
        'gainid_update': redshift_plaintiffs_gainid_update,
        'update_manual_review': manual_review_for_plaintiff_update,
        'insert': redshift_to_sf_plaintiffaccount_insert,
        'gainid_insert': redshift_plaintiffs_gainid_insert,
    }


def redshift_data_to_salesforce_data_medicalfacilities_accounts(
    redshift_medicalfacilities_data: pd.DataFrame,
    redshift_medicalfacilities_data_ids: dict[str, str],
    sf_medicalfacilities_data: dict[str, typing.Any],
    sf_accounts_data: dict[str, typing.Any],
) -> pipeline.AffectedData:
    redshift_to_sf_medicalfacilities_update = []
    redshift_medicalfacilities_gainid_update = []
    redshift_to_sf_medicalfacilities_insert = []
    redshift_medicalfacilities_gainid_insert = []
    manual_review_for_medicalfacilities_update = []

    gainids = redshift_medicalfacilities_data['GainId'].tolist()
    gainid_to_source_name = aws_operations.get_gainid_to_sourcename_map(
        gainids
    )

    def safe_str(value: typing.Any) -> str | None:
        return value if isinstance(value, str) else None

    for rs_medicalfacility in redshift_medicalfacilities_data.itertuples():
        if (
            rs_medicalfacility.Name is None
            or rs_medicalfacility.Name == ''
            or rs_medicalfacility.GainId is None
            or rs_medicalfacility.GainId == ''
        ):
            logger.warning(
                f"Medical facility missing name or gain id",
                extra={
                    'record': rs_medicalfacility,
                    's3_filename': 'medicalfacilities_missing_info_manual_review',
                    's3_subdirectory': 'medicalfacilities_manual_review',
                },
            )
            continue
        canonical_gainid = str(rs_medicalfacility.GainId)
        salesforce_id = redshift_medicalfacilities_data_ids.get(
            canonical_gainid,
            None,
        )  # use lookup table data to fetch salesforce id with a combination of source id and source name
        record: types.MedicalFacilityRecord = {
            'Gain_ID__c': canonical_gainid,
            'External_Location_ID__c': canonical_gainid,
            'Name': safe_str(rs_medicalfacility.Name),
            'Phone': safe_str(rs_medicalfacility.Phone),
            'Fax': safe_str(rs_medicalfacility.Fax),
            'Email__c': safe_str(rs_medicalfacility.Email),
            'Follow_up_email_for_entire_account__c': safe_str(
                rs_medicalfacility.FollowUpEmail
            ),
            'ShippingPostalCode': safe_str(
                rs_medicalfacility.BillingAddressZip
            ),
            'BillingStreet': safe_str(rs_medicalfacility.PhysicalAddressLine1),
            'BillingCity': safe_str(rs_medicalfacility.PhysicalAddressCity),
            'BillingState': safe_str(rs_medicalfacility.PhysicalAddressState),
            'BillingPostalCode': safe_str(
                rs_medicalfacility.PhysicalAddressZip
            ),
            'Website': safe_str(rs_medicalfacility.Website),
        }

        if (
            canonical_data["Salesforce"]["FromCanonical"]["Clients"][
                gainid_to_source_name[canonical_gainid]
            ]
            != 'Jopari Solutions'
        ):
            record['Root_Parent_Account__c'] = sf_accounts_data[
                canonical_data["Salesforce"]["FromCanonical"]["Clients"][
                    gainid_to_source_name[canonical_gainid]
                ]
            ]

        if salesforce_id is not None and salesforce_id != '':
            record['Id'] = salesforce_id
            # This is for future usage (automatic update functionality)
            # redshift_to_sf_medicalfacilities_update.append(record)
            # redshift_medicalfacilities_gainid_update.append(
            #     rs_medicalfacility.gainid
            # )
            record['RedshiftId'] = str(rs_medicalfacility.GainId)
            manual_review_for_medicalfacilities_update.append(record)
            continue
        salesforce_ids = []
        salesforce_ids.extend(
            search_rs_record_in_sf(
                rs_medicalfacility,
                canonical['MedicalFacilities'],
                sf_medicalfacilities_data['medicalfacility_email_group'],
                ['Email'],
            )
        )
        salesforce_ids.extend(
            search_rs_record_in_sf(
                rs_medicalfacility,
                canonical['MedicalFacilities'],
                sf_medicalfacilities_data['medicalfacility_phone_group'],
                ['Phone'],
            )
        )
        unique_key = (
            str(rs_medicalfacility.PhysicalAddressLine1).split(' ')[0]
            + str(rs_medicalfacility.PhysicalAddressCity)
            + str(rs_medicalfacility.PhysicalAddressState)
            + str(rs_medicalfacility.PhysicalAddressZip)
        )
        unique_key = local_operations.remove_nonalphanumeric_characters(
            unique_key
        ).lower()
        if (
            unique_key
            in sf_medicalfacilities_data[
                'medicalfacility_unique_key_group'
            ].keys()
        ):
            salesforce_ids.extend(
                sf_medicalfacilities_data['medicalfacility_unique_key_group'][
                    unique_key
                ]
            )
        salesforce_ids = list(set(salesforce_ids))
        if len(salesforce_ids) > 0:
            if len(salesforce_ids) == 1:
                record['Id'] = salesforce_ids[0]
                record['RedshiftId'] = str(rs_medicalfacility.GainId)
                manual_review_for_medicalfacilities_update.append(record)
            else:
                record['Id'] = ','.join(list(set(salesforce_ids)))
                logger.warning(
                    f"Multiple matching medical facilities found",
                    extra={
                        'record': record,
                        's3_filename': 'multiple_matching_medicalfacilities_manual_review',
                        's3_subdirectory': 'medicalfacilities_manual_review',
                    },
                )
            continue

        redshift_to_sf_medicalfacilities_insert.append(record)
        redshift_medicalfacilities_gainid_insert.append(
            rs_medicalfacility.GainId
        )
    if len(redshift_to_sf_medicalfacilities_insert) > 0:
        record_type_id = salesforce_operations.get_sf_record_type_id(
            'Account', 'Medical Facility'
        )
        for record in redshift_to_sf_medicalfacilities_insert:
            record['RecordTypeId'] = record_type_id

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_medicalfacilities_update,
        'gainid_update': redshift_medicalfacilities_gainid_update,
        'update_manual_review': manual_review_for_medicalfacilities_update,
        'insert': redshift_to_sf_medicalfacilities_insert,
        'gainid_insert': redshift_medicalfacilities_gainid_insert,
    }


def redshift_data_to_salesforce_data_contacts(
    redshift_legalpersonnel_data: pd.DataFrame,
    redshift_legalpersonnel_data_ids: dict[str, str],
    redshift_lawfirms_data_ids: dict[str, str],
    sf_contacts_data: dict[str, typing.Any],
) -> pipeline.AffectedData:
    redshift_to_sf_contact_update = []
    redshift_legalpersonnel_gainid_update = []
    redshift_to_sf_contact_insert = []
    redshift_legalpersonnel_gainid_insert = []
    manual_review_for_contact_update = []

    for legalperson in redshift_legalpersonnel_data.itertuples():
        first_name, last_name = None, None
        if len(legalperson.Name.split(' ')) == 1:
            last_name = legalperson.Name
        else:
            last_name = str(legalperson.Name).split(' ')[-1]
            first_name = ' '.join(str(legalperson.Name).split(' ')[:-1])
        record = {
            'Gain_ID__c': legalperson.GainId,
            'FirstName': first_name,
            'LastName': last_name,
            'Title': legalperson.Title,
            'HomePhone': legalperson.HomePhone,
            'MobilePhone': legalperson.CellPhone,
            'Phone': legalperson.BusinessPhone,
            'OtherPhone': legalperson.OtherPhone,
            'Fax': legalperson.Fax,
            'Email': legalperson.PrimaryEmail,
            'Personal_Email__c': legalperson.SecondaryEmail,
            'MailingStreet': str(legalperson.PrimaryAddressLine1)
            + '\n'
            + str(legalperson.PrimaryAddressLine2),
            'MailingCity': legalperson.PrimaryAddressCity,
            'MailingState': legalperson.PrimaryAddressState,
            'MailingPostalCode': legalperson.PrimaryAddressZip,
            'MailingCountry': 'United States',
            'OtherStreet': str(legalperson.OtherAddressLine1)
            + '\n'
            + str(legalperson.OtherAddressLine2),
            'OtherCity': str(legalperson.OtherAddressCity),
            'OtherState': legalperson.OtherAddressState,
            'OtherPostalCode': legalperson.OtherAddressZip,
            'OtherCountry': 'United States',
        }
        lawfirm_account_id = redshift_lawfirms_data_ids.get(
            str(legalperson.LawFirmId),
            None,
        )
        if lawfirm_account_id is None:
            logger.warning(
                f"Contacts missing lawfirm manual review",
                extra={
                    'record': legalperson._asdict(),
                    's3_filename': f'contacts_missing_lawfirm_manual_review',
                    's3_subdirectory': 'contacts_manual_review',
                },
            )
            continue
        record['AccountId'] = lawfirm_account_id

        salesforce_id = redshift_legalpersonnel_data_ids.get(
            str(legalperson.GainId),
            None,
        )
        if salesforce_id is not None and salesforce_id != '':
            record['Id'] = salesforce_id
            record['RedshiftId'] = str(legalperson.GainId)
            '''
            # Automated update, commented for now
            if (curr_timestamp - legalperson[canonical['LegalPersonnel']['ModifiedDateTime']]).seconds//3600 > 0:#24: # difference between update time (now) and last modified is > 24 hours i.e. 1 day
                redshift_to_sf_contact_update.append(record)
                redshift_legalpersonnel_gainid_update.append(legalperson[canonical['LegalPersonnel']['GainId']])
            '''
            manual_review_for_contact_update.append(record)
            continue

        salesforce_ids = []
        if lawfirm_account_id in sf_contacts_data['contact_email_group']:
            salesforce_ids += search_rs_record_in_sf(
                legalperson,
                canonical['LegalPersonnel'],
                sf_contacts_data['contact_email_group'][lawfirm_account_id],
                ['PrimaryEmail', 'SecondaryEmail'],
            )
        # We do not search for business phone duplicates because two attorneys can have the same business phone number
        if lawfirm_account_id in sf_contacts_data['contact_phone_group']:
            salesforce_ids += search_rs_record_in_sf(
                legalperson,
                canonical['LegalPersonnel'],
                sf_contacts_data['contact_phone_group'][lawfirm_account_id],
                ['HomePhone', 'CellPhone', 'OtherPhone'],
            )
        salesforce_ids = list(set(salesforce_ids))
        if len(salesforce_ids) > 0:
            if len(salesforce_ids) == 1:
                record['Id'] = salesforce_ids[0]
                record['RedshiftId'] = str(legalperson.GainId)
                manual_review_for_contact_update.append(record)
                continue
            record['Id'] = ','.join(salesforce_ids)
            logger.warning(
                f"Multiple matching contacts manual review",
                extra={
                    'record': record,
                    's3_filename': f'multiple_matching_contacts_manual_review',
                    's3_subdirectory': 'contacts_manual_review',
                },
            )
            continue
        redshift_to_sf_contact_insert.append(record)
        redshift_legalpersonnel_gainid_insert.append(str(legalperson.GainId))
    if len(redshift_to_sf_contact_insert) > 0:
        record_type_id = salesforce_operations.get_sf_record_type_id(
            'Contact', 'Contact Record Legal'
        )
        for record in redshift_to_sf_contact_insert:
            record['RecordTypeId'] = record_type_id

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_contact_update,
        'gainid_update': redshift_legalpersonnel_gainid_update,
        'update_manual_review': manual_review_for_contact_update,
        'insert': redshift_to_sf_contact_insert,
        'gainid_insert': redshift_legalpersonnel_gainid_insert,
    }


def redshift_data_to_salesforce_data_lawfirm_accounts(
    redshift_lawfirm_data: pd.DataFrame,
    redshift_lawfirm_data_ids: dict[str, str],
    sf_lawfirms_data: dict[str, typing.Any],
) -> pipeline.AffectedData:
    redshift_to_sf_lawfirm_update = []
    redshift_lawfirm_gainid_update = []
    redshift_to_sf_lawfirm_insert = []
    redshift_lawfirm_gainid_insert = []
    manual_review_for_lawfirm_update = []

    for rs_lawfirm in redshift_lawfirm_data.itertuples():
        if (
            rs_lawfirm.Name is None
            or rs_lawfirm.Name == ''
            or rs_lawfirm.GainId is None
            or rs_lawfirm.GainId == ''
        ):
            logger.warning(
                f"Lawfirm missing name or gain id manual review",
                extra={
                    'record': rs_lawfirm._asdict(),
                    's3_filename': f'lawfirm_missing_name_or_gainid_manual_review',
                    's3_subdirectory': 'lawfirms_manual_review',
                },
            )
            continue
        canonical_gainid = rs_lawfirm.GainId
        salesforce_id = redshift_lawfirm_data_ids.get(
            canonical_gainid,
            None,
        )  # use lookup table data to fetch salesforce id with a combination of source id and source name
        record = {
            'Gain_ID__c': canonical_gainid,
            'Name': rs_lawfirm.Name,
            'Phone': rs_lawfirm.Phone,
            'Fax': rs_lawfirm.Fax,
            'Email__c': rs_lawfirm.Email,
            'Follow_up_email_for_entire_account__c': rs_lawfirm.FollowUpEmail,
            'BillingStreet': rs_lawfirm.BillingAddressLine1,
            'BillingCity': rs_lawfirm.BillingAddressCity,
            'BillingState': rs_lawfirm.BillingAddressState,
            'BillingPostalCode': rs_lawfirm.BillingAddressZip,
            'ShippingStreet': rs_lawfirm.BillingAddressLine1,
            'ShippingCity': rs_lawfirm.BillingAddressCity,
            'ShippingState': rs_lawfirm.BillingAddressState,
            'ShippingPostalCode': rs_lawfirm.BillingAddressZip,
            'Website': rs_lawfirm.Website,
            'Type_of_Law__c': rs_lawfirm.TypeOfLaw,
            'Description': rs_lawfirm.Description,
            'Employee_Size_Range__c': rs_lawfirm.EmployeeCountRange,
            'Automatic_Case_Update_Requests__c': (
                "On" if rs_lawfirm.AutomaticCaseUpdateRequest else "Off"
            ),
            # The below fields are not provided by Providers. They are specific to SalesForce.
            # 'DNF__c': lawfirm[canonical['LawFirms']['DoNotFund']],
            # 'DNF_Type__c': lawfirm[canonical['LawFirms']['DoNotFundType']],
            # 'Non_Responsive__c': lawfirm[canonical['LawFirms']['NonResponsive']],
            # 'Difficult_Case_Update_Notes__c': lawfirm[canonical['LawFirms']['NonResponsiveNote']],
            # 'Portal_Account__c': lawfirm[canonical['LawFirms']['PortalAccount']],
            # 'Portal_Rewards__c': lawfirm[canonical['LawFirms']['PortalRewardsParticipant']],
            # 'ParentId': lawfirm[canonical['LawFirms']['ParentId']],
        }
        if salesforce_id is not None and salesforce_id != '':
            record['Id'] = salesforce_id
            record['RedshiftId'] = str(rs_lawfirm.GainId)
            manual_review_for_lawfirm_update.append(record)
            continue
        salesforce_ids = []
        salesforce_ids += search_rs_record_in_sf(
            rs_lawfirm,
            canonical['LawFirms'],
            sf_lawfirms_data['lawfirm_email_group'],
            ['Email'],
        )
        salesforce_ids += search_rs_record_in_sf(
            rs_lawfirm,
            canonical['LawFirms'],
            sf_lawfirms_data['lawfirm_phone_group'],
            ['Phone'],
        )
        unique_key = (
            str(rs_lawfirm.BillingAddressLine1).split(' ')[0]
            + str(rs_lawfirm.BillingAddressCity)
            + str(rs_lawfirm.BillingAddressState)
            + str(rs_lawfirm.BillingAddressZip)
            + str(rs_lawfirm.Phone)
        )
        unique_key = local_operations.remove_nonalphanumeric_characters(
            unique_key
        ).lower()
        if unique_key in sf_lawfirms_data['lawfirm_unique_key_group'].keys():
            salesforce_ids += sf_lawfirms_data['lawfirm_unique_key_group'][
                unique_key
            ]
        salesforce_ids = list(set(salesforce_ids))
        if len(salesforce_ids) > 0:
            if len(salesforce_ids) == 1:
                record['Id'] = salesforce_ids[0]
                record['RedshiftId'] = str(rs_lawfirm.GainId)
                manual_review_for_lawfirm_update.append(record)
            else:
                record['Id'] = ','.join(list(set(salesforce_ids)))
                logger.warning(
                    f"Multiple matching lawfirms manual review",
                    extra={
                        'record': record,
                        's3_filename': f'multiple_matching_lawfirms_manual_review',
                        's3_subdirectory': 'lawfirms_manual_review',
                    },
                )
            continue

        redshift_to_sf_lawfirm_insert.append(record)
        redshift_lawfirm_gainid_insert.append(rs_lawfirm.GainId)
    if len(redshift_to_sf_lawfirm_insert) > 0:
        record_type_id = salesforce_operations.get_sf_record_type_id(
            'Account', 'Law Firm'
        )
        for record in redshift_to_sf_lawfirm_insert:
            record['RecordTypeId'] = record_type_id

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_lawfirm_update,
        'gainid_update': redshift_lawfirm_gainid_update,
        'update_manual_review': manual_review_for_lawfirm_update,
        'insert': redshift_to_sf_lawfirm_insert,
        'gainid_insert': redshift_lawfirm_gainid_insert,
    }


def redshift_data_to_salesforce_data_opportunities(
    redshift_plaintiffs_data: pd.DataFrame,
    redshift_plaintiffs_data_ids: dict[str, str],
    redshift_cases_data: pd.DataFrame,
    redshift_cases_data_ids: dict[str, str],
    redshift_lawfirms_data_mapping_ids: dict[str, str],
    redshift_legalpersonnel_data_ids: dict[str, str],
    sf_opportunities_data: dict[str, typing.Any],
    name_id_map: dict[str, str],
    name_type_map: dict[str, str],
    curr_timestamp: datetime.datetime,
) -> pipeline.AffectedData:
    # For creating or inserting opportuniteis, it will use data from both info from cases table and plaintiffs table in Redshift
    redshift_to_sf_opportunity_update = []
    redshift_cases_gainid_update = []
    redshift_to_sf_opportunity_insert = []
    redshift_cases_gainid_insert = []
    plaintiff_lookup = {}
    manual_review_for_opportunity_update = []

    # surgery_lookup = {}

    plaintiff_lookup = redshift_plaintiffs_data.set_index('GainId').T.to_dict()
    # surgery_lookup = redshift_surgery_data.set_index('GainId').to_dict(orient='index')

    default_law_firm_id, default_partner_account_id = (
        salesforce_operations.get_sf_id('Account', 'No Law Firm'),
        None,
    )
    case_legaldata_mapping = (
        aws_operations.get_case_map_legalpersonnel_gainid_map()
    )
    legal_info = aws_operations.get_legalpersonnel_lawfirm_sourceid_sfid_map()
    legal_info_lawfirm_external_id_container_sf_ids = (
        legal_info['lawfirmsmapsfexternalidcontainer']
        .dropna()
        .unique()
        .tolist()
    )
    legal_info_legalpersonnel_external_id_container_sf_ids = (
        legal_info['legalpersonnelmapsfexternalidcontainer']
        .dropna()
        .unique()
        .tolist()
    )
    legal_info_external_id_container_sf_ids = (
        legal_info_lawfirm_external_id_container_sf_ids
        + legal_info_legalpersonnel_external_id_container_sf_ids
    )
    external_id_container_info = (
        salesforce_operations.get_externalidcontainer_primary_gain_id_map(
            legal_info_external_id_container_sf_ids
        )
    )
    if external_id_container_info.empty:
        legal_info['primary_lawfirm_salesforceid'] = legal_info['lawfirmsfid']
        legal_info['primary_legalpersonnel_salesforceid'] = legal_info[
            'legalpersonnelsfid'
        ]
    else:
        external_id_container_info_dict = external_id_container_info.set_index(
            'Id'
        )['Primary_Gain_ID__c'].to_dict()
        rs_gain_id_lawfirm_external_id_container_dict = legal_info.set_index(
            'lawfirmgainid'
        )['lawfirmsmapsfexternalidcontainer'].to_dict()
        rs_gain_id_legalpersonnel_external_id_container_dict = (
            legal_info.set_index('gainid')[
                'legalpersonnelmapsfexternalidcontainer'
            ].to_dict()
        )
        while legal_info['lawfirmsmapsfexternalidcontainer'].notna().any():
            mask = legal_info['lawfirmsmapsfexternalidcontainer'].notna()
            mask &= legal_info['lawfirmsmapsfexternalidcontainer'].isin(
                external_id_container_info_dict
            )
            if mask.sum() == 0:
                break
            legal_info.loc[mask, 'primary_lawfirm_gainid'] = legal_info.loc[
                mask, 'lawfirmsmapsfexternalidcontainer'
            ].map(external_id_container_info_dict)
            legal_info.loc[mask, 'lawfirmsmapsfexternalidcontainer'] = (
                legal_info.loc[mask, 'primary_lawfirm_gainid'].map(
                    rs_gain_id_lawfirm_external_id_container_dict
                )
            )
        while (
            legal_info['legalpersonnelmapsfexternalidcontainer'].notna().any()
        ):
            mask = legal_info['legalpersonnelmapsfexternalidcontainer'].notna()
            mask &= legal_info['legalpersonnelmapsfexternalidcontainer'].isin(
                external_id_container_info_dict
            )
            if mask.sum() == 0:
                break
            legal_info.loc[mask, 'primary_legalpersonnel_gainid'] = (
                legal_info.loc[
                    mask, 'legalpersonnelmapsfexternalidcontainer'
                ].map(external_id_container_info_dict)
            )
            legal_info.loc[mask, 'legalpersonnelmapsfexternalidcontainer'] = (
                legal_info.loc[mask, 'primary_legalpersonnel_gainid'].map(
                    rs_gain_id_legalpersonnel_external_id_container_dict
                )
            )
        legal_info['primary_lawfirm_salesforceid'] = legal_info.apply(
            lambda x: redshift_lawfirms_data_mapping_ids.get(
                x.get('primary_lawfirm_gainid'), x.get('lawfirmsfid')
            ),
            axis=1,
        )
        legal_info['primary_legalpersonnel_salesforceid'] = legal_info.apply(
            lambda x: redshift_legalpersonnel_data_ids.get(
                x.get('primary_legalpersonnel_gainid'),
                x.get('legalpersonnelsfid'),
            ),
            axis=1,
        )

    opportunity_record_type_id = salesforce_operations.get_sf_record_type_id(
        'Opportunity', 'Plaintiff'
    )

    automated_sf_updates_sources = set(automated_sf_updates['Cases'])

    gainids = redshift_cases_data['GainId'].tolist()

    gainid_to_source_name = aws_operations.get_gainid_to_sourcename_map(
        gainids
    )
    for case in redshift_cases_data.itertuples():
        plaintiff_gain_id = str(case.PlaintiffId)
        case_gain_id = str(case.GainId)
        plaintiff = plaintiff_lookup.get(plaintiff_gain_id)

        plaintiff_account_id = redshift_plaintiffs_data_ids.get(
            plaintiff_gain_id,
            None,
        )

        if not plaintiff_account_id or plaintiff_account_id == '':
            logger.warning(
                f"Opportunity failed upsert missing plaintiff",
                extra={
                    'record': case._asdict(),
                    's3_filename': f'opportunity_failed_upsert_missing_plaintiff',
                    's3_subdirectory': 'opportunities_manual_review',
                },
            )
            continue
        else:
            plaintiff = (
                plaintiff.copy()
            )  # Detach the current item from the plaintiff_lookup list

        source_name = gainid_to_source_name.get(case_gain_id)
        source_sf_name = local_operations.get_client_salesforce_name(
            source_name
        )

        if (
            plaintiff['DateOfBirth'] is not None
            and plaintiff['DateOfBirth'] != ''
        ):
            plaintiff['DateOfBirth'] = (
                plaintiff['DateOfBirth'].strftime('%Y-%m-%d')
                if plaintiff['DateOfBirth'] is not None
                else None
            )
        '''
        surgery = surgery_lookup[plaintiff[canonical['Plaintiffs']['CaseId']]]
        if surgery[canonical['Surgery']['Date']]] is not None:
            surgery = list(surgery)
            surgery[canonical['Surgery']['Date']]] = surgery[canonical['Surgery']['Date']]].strftime('%Y-%m-%d') # Format Surgery Date
            surgery = tuple(surgery)
        '''
        case_accident_date = None
        if case.AccidentDate is not None and case.AccidentDate != '':
            case_accident_date = case.AccidentDate.strftime('%Y-%m-%d')

        salesforce_id = redshift_cases_data_ids.get(
            case_gain_id,
            None,
        )  # use lookup table data to fetch salesforce id with a combination of source id and source name
        record: types.OpportunityRecord = {
            'Gain_ID__c': case_gain_id,
            'Name': plaintiff['Name'],
            'Date_of_Birth__c': plaintiff['DateOfBirth'],
            'SSN__c': plaintiff['Ssn'],
            'Drivers_License__c': plaintiff['DriverLicense'],
            'Gender__c': plaintiff['Gender'],
            'Home_Phone__c': plaintiff['HomePhone'],
            'Cell_Phone__c': plaintiff['CellPhone'],
            'Other_Phone__c': plaintiff['OtherPhone'],
            'Plaintiff_Email__c': plaintiff['PrimaryEmail'],
            'Address__c': plaintiff['PrimaryAddressLine1'],
            'Address_2__c': plaintiff['PrimaryAddressLine2'],
            'City__c': plaintiff['PrimaryAddressCity'],
            'State__c': state_name_to_code_map.get(
                plaintiff['PrimaryAddressState'],
                plaintiff['PrimaryAddressState'],
            ),
            'Zip__c': plaintiff['PrimaryAddressZip'],
            'Case_Status__c': (
                case.Status
                if case.Status is not None or case.Status != ''
                else 'Still Treating'
            ),
            'Date_of_Accident__c': case_accident_date,
            'Description_of_Accident_Incident__c': case.AccidentDescription,
            'What_type_of_case__c': case.Type,
            'CloseDate': curr_timestamp.date().strftime('%Y-%m-%d'),
            'Plaintiff_Account__c': plaintiff_account_id,
            'Attorney__c': None,
            'RecordTypeId': opportunity_record_type_id,
            # 'Marital_Status__c': plaintiff[canonical['Plaintiffs']['MaritalStatus']],
            # 'Company_Name__c': plaintiff[canonical['Plaintiffs']['Company']],
            # 'Surgery_Date__c': surgery[canonical['Surgery']['Date']]],
            # 'Surgeon__c': surgery[canonical['Surgery']['Surgeon']]],
            # 'Type_of_Surgery__c': 'General',
            # 'Type_of_Surgery__c': surgery[canonical['Surgery']['Type']]],
            # 'Surgery_Comments__c': surgery[canonical['Surgery']['Comments']]],
            # 'Estimated_Surgical_Charges__c': surgery[canonical['Surgery']['Charges']]],
        }

        if (
            case.GrandTotalDeductible is not None
            and case.GrandTotalDeductible != ''
        ):
            record['Grand_Total_Deductible__c'] = str(
                case.GrandTotalDeductible
            )
        if (
            case.GrandTotalCoinsurance is not None
            and case.GrandTotalCoinsurance != ''
        ):
            record['Grand_Total_Coinsurance__c'] = str(
                case.GrandTotalCoinsurance
            )
        if (
            case.GrandTotalCopayment is not None
            and case.GrandTotalCopayment != ''
        ):
            record['Grand_Total_Copayment__c'] = str(case.GrandTotalCopayment)
        automated_sf_update_record = (
            True if (source_name in automated_sf_updates_sources) else False
        )  # If source system is in automated_sf_updates_sources, then mark for automated update as opposed to manual review update
        if (
            source_sf_name and name_type_map[source_sf_name] == 'Law Firm'
        ):  # check if account is law firm, if it is assigned AccountId field to record
            record['AccountId'] = name_id_map[
                source_sf_name
            ]  # name_id_map fetches id of salesforce account name
            record['Partner_Account__c'] = default_partner_account_id
        elif (
            source_sf_name
            and name_type_map[source_sf_name] == 'Medical Facility'
        ):  # check if account is medical facility, if it is assigned Medical_Facility_P__c field to record
            record['AccountId'] = (
                default_law_firm_id  # AccountId is a mandatory field on Opportunity, if Source is not a Law Firm set source to 'No Law Firm' for time being
            )
            record['Partner_Account__c'] = name_id_map[
                source_sf_name
            ]  # name_id_map fetches id of salesforce account name
        else:
            record['AccountId'] = (
                default_law_firm_id  # AccountId is a mandatory field, so set at least that
            )
            record['Partner_Account__c'] = default_partner_account_id
        if gainid_to_source_name.get(case_gain_id, None) != 'ATI':
            del record['Partner_Account__c']
        # record['Does_client_have_healthcare_insurance__c'] = 'Yes' if case[canonical['Cases']['PlaintiffInsurance']] is not None else 'No'
        if not case_legaldata_mapping.empty:
            legalpersonnel_record = case_legaldata_mapping[
                (case_legaldata_mapping['gainid'] == case_gain_id)
            ]
            if not legalpersonnel_record.empty:
                attorney_id = legalpersonnel_record['attorneyid']
                if not attorney_id.empty:
                    attorney_id = attorney_id.tolist()[0]
                paralegal_id = legalpersonnel_record['paralegalid']
                if not paralegal_id.empty:
                    paralegal_id = paralegal_id.tolist()[0]
                casemanager_id = legalpersonnel_record['casemanagerid']
                if not casemanager_id.empty:
                    casemanager_id = casemanager_id.tolist()[0]
                cocounsel_id = legalpersonnel_record['cocounselid']
                if not cocounsel_id.empty:
                    cocounsel_id = cocounsel_id.tolist()[0]
                coparalegalid_id = legalpersonnel_record['coparalegalid']
                if not coparalegalid_id.empty:
                    coparalegalid_id = coparalegalid_id.tolist()[0]
                cocasemanager_id = legalpersonnel_record['cocasemanagerid']
                if not cocasemanager_id.empty:
                    cocasemanager_id = cocasemanager_id.tolist()[0]
            if attorney_id:
                if not legal_info.empty:
                    legal_record = legal_info[
                        (legal_info['gainid'] == attorney_id)
                    ]
                    if not legal_record.empty:
                        lawfirm_id = legal_record[
                            'primary_lawfirm_salesforceid'
                        ].tolist()[0]
                        if lawfirm_id:
                            record['AccountId'] = lawfirm_id
                        legalpersonnel_id = legal_record[
                            'primary_legalpersonnel_salesforceid'
                        ].tolist()[0]
                        if legalpersonnel_id:
                            record['Attorney__c'] = legalpersonnel_id
            if paralegal_id:
                if not legal_info.empty:
                    legal_record = legal_info[
                        (legal_info['gainid'] == paralegal_id)
                    ]
                    if not legal_record.empty:
                        lawfirm_id = legal_record[
                            'primary_lawfirm_salesforceid'
                        ].tolist()[0]
                        if lawfirm_id:
                            record['AccountId'] = lawfirm_id
                        legalpersonnel_id = legal_record[
                            'primary_legalpersonnel_salesforceid'
                        ].tolist()[0]
                        if legalpersonnel_id:
                            record['Paralegal_or_Case_Manager__c'] = (
                                legalpersonnel_id
                            )
            if casemanager_id:
                if not legal_info.empty:
                    legal_record = legal_info[
                        (legal_info['gainid'] == casemanager_id)
                    ]
                    if not legal_record.empty:
                        lawfirm_id = legal_record[
                            'primary_lawfirm_salesforceid'
                        ].tolist()[0]
                        if lawfirm_id:
                            record['AccountId'] = lawfirm_id
                        legalpersonnel_id = legal_record[
                            'primary_legalpersonnel_salesforceid'
                        ].tolist()[0]
                        if legalpersonnel_id:
                            record['Paralegal_or_Case_Manager__c'] = (
                                legalpersonnel_id
                            )
            if cocounsel_id:
                if not legal_info.empty:
                    legal_record = legal_info[
                        (legal_info['gainid'] == cocounsel_id)
                    ]
                    if not legal_record.empty:
                        lawfirm_id = legal_record[
                            'primary_lawfirm_salesforceid'
                        ].tolist()[0]
                        if lawfirm_id:
                            record['AccountId'] = lawfirm_id
                        legalpersonnel_id = legal_record[
                            'primary_legalpersonnel_salesforceid'
                        ].tolist()[0]
                        if legalpersonnel_id:
                            record['Cocounsel__c'] = legalpersonnel_id
            # if coparalegalid_id:
            #     record['Co_Paralegal__c'] = coparalegalid_id
            # if cocasemanager_id:
            #     record['Co_Case_Manager__c'] = cocasemanager_id
        if (
            plaintiff['DateOfBirth'] is None
            or case_accident_date is None
            or plaintiff['DateOfBirth'] == ''
            or case_accident_date == ''
        ):  # If opportunity has DOB and DOA
            logger.warning(
                f"Opportunity missing DOB or DOA manual review",
                extra={
                    'record': record,
                    's3_filename': f'opportunity_missing_DOB_or_DOA_manual_review',
                    's3_subdirectory': 'opportunities_manual_review',
                },
            )
            continue
        if (
            salesforce_id and salesforce_id != ''
        ):  # if case has a salesforce Id
            record['Id'] = salesforce_id
            if automated_sf_update_record:
                if (
                    salesforce_id
                    not in sf_opportunities_data['opportunity_account_id']
                    or salesforce_id
                    not in sf_opportunities_data['opportunity_attorney_id']
                    or salesforce_id
                    not in sf_opportunities_data['opportunity_case_status']
                    or salesforce_id
                    not in sf_opportunities_data['opportunity_stage_name']
                ):
                    logger.error(
                        f"Salesforce ID {salesforce_id} not found in one or more opportunity data dictionaries",
                        extra={
                            'additional_details': str(
                                {"salesforce_id": salesforce_id}
                            ),
                        },
                    )
                    record['RedshiftId'] = str(case_gain_id)
                    manual_review_for_opportunity_update.append(record)
                    continue

                sf_opportunities_account_id = sf_opportunities_data[
                    'opportunity_account_id'
                ][salesforce_id]
                sf_opportunities_attorney_id = sf_opportunities_data[
                    'opportunity_attorney_id'
                ][salesforce_id]
                sf_opportunities_case_status = sf_opportunities_data[
                    'opportunity_case_status'
                ][salesforce_id]
                sf_opportunities_stage_name = sf_opportunities_data[
                    'opportunity_stage_name'
                ][salesforce_id]

                meets_update_conditions = (
                    (
                        (
                            sf_opportunities_account_id is None
                            or sf_opportunities_account_id == ''
                            or bool(pd.isna(sf_opportunities_account_id))
                            or sf_opportunities_account_id
                            == default_law_firm_id
                        )
                        and (
                            sf_opportunities_attorney_id is None
                            or sf_opportunities_attorney_id == ''
                            or bool(pd.isna(sf_opportunities_attorney_id))
                        )
                    )
                    or (sf_opportunities_case_status == 'No Longer Represent')
                    or (sf_opportunities_stage_name == 'Problem (Monitored)')
                )
                if not meets_update_conditions:
                    logger.error(
                        f"Salesforce ID {salesforce_id} does not meet criteria for automatic update",
                        extra={
                            'additional_details': str(
                                {
                                    "salesforce_id": salesforce_id,
                                    "sf_account_id": sf_opportunities_account_id,
                                    "sf_attorney_id": sf_opportunities_attorney_id,
                                    "sf_case_status": sf_opportunities_case_status,
                                    "sf_stage_name": sf_opportunities_stage_name,
                                }
                            ),
                        },
                    )
                    record['RedshiftId'] = str(case_gain_id)
                    manual_review_for_opportunity_update.append(record)
                    continue

                # DI-498: Never-nest version - Check if we have new data
                has_new_data = (record.get('Attorney__c') is not None) or (
                    (record.get('AccountId') is not None)
                    and (record.get('AccountId') != default_law_firm_id)
                )

                # DI-498: Never-nest version - Early exit if missing new data
                if not has_new_data:
                    logger.error(
                        f"Salesforce ID {salesforce_id} qualifies for update but missing Attorney__c and AccountId information",
                        extra={
                            'additional_details': str(
                                {
                                    "salesforce_id": salesforce_id,
                                    "attorney_id": record.get('Attorney__c'),
                                    "account_id": record.get('AccountId'),
                                }
                            ),
                        },
                    )
                    record['RedshiftId'] = str(case_gain_id)
                    manual_review_for_opportunity_update.append(record)
                    continue

                legalpersonnel_data_update_record = {
                    'Id': record['Id'],
                    'AccountId': record['AccountId'],
                    'Attorney__c': record.get('Attorney__c'),
                }
                redshift_to_sf_opportunity_update.append(
                    legalpersonnel_data_update_record
                )
                redshift_cases_gainid_update.append(case_gain_id)
            else:
                record['RedshiftId'] = str(case_gain_id)
                manual_review_for_opportunity_update.append(record)
            continue
        # if case does not have a salesforce id
        # if case is from ATI, do not do a secondary search using DOB and DOA for Salesforce Id
        # this is because ATI source system has multiple cases for the same case, because they separate cases by injury
        # hence the same opportunity, with the same DOB, would have the same DOA potentially multiple times, and get flagged as a manual review duplicate, when it isn't
        # so ignore secondary search if source system is ATI, identified by source name
        if gainid_to_source_name.get(case_gain_id, None) != 'ATI':
            if (
                sf_opportunities_data['opportunities_DOB_DOA'].get(
                    (
                        plaintiff['DateOfBirth'],
                        case_accident_date,
                    ),
                    '',
                )
                != ''
            ):  # if there is a partial opportunity match in salesforce
                if (
                    sf_opportunities_data['opportunities_DOB_DOA_phone'].get(
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['HomePhone'],
                        ),
                        None,
                    )
                    is not None
                    and sf_opportunities_data['opportunities_DOB_DOA_phone'][
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['HomePhone'],
                        )
                    ][0]
                    != ''
                ):  # if salesforce has a full match
                    logger.warning(
                        f"Opportunity potential duplicate manual review",
                        extra={
                            'record': record,
                            's3_filename': f'opportunity_potential_duplicate_manual_review',
                            's3_subdirectory': 'opportunities_manual_review',
                        },
                    )
                    continue
                elif (
                    sf_opportunities_data['opportunities_DOB_DOA_phone'].get(
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['CellPhone'],
                        ),
                        None,
                    )
                    is not None
                    and sf_opportunities_data['opportunities_DOB_DOA_phone'][
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['CellPhone'],
                        )
                    ][0]
                    != ''
                ):  # if salesforce has a full match
                    logger.warning(
                        f"Opportunity potential duplicate manual review",
                        extra={
                            'record': record,
                            's3_filename': f'opportunity_potential_duplicate_manual_review',
                            's3_subdirectory': 'opportunities_manual_review',
                        },
                    )
                    continue
                elif (
                    sf_opportunities_data['opportunities_DOB_DOA_phone'].get(
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['BusinessPhone'],
                        ),
                        None,
                    )
                    is not None
                    and sf_opportunities_data['opportunities_DOB_DOA_phone'][
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['BusinessPhone'],
                        )
                    ][0]
                    != ''
                ):  # if salesforce has a full match
                    logger.warning(
                        f"Opportunity potential duplicate manual review",
                        extra={
                            'record': record,
                            's3_filename': f'opportunity_potential_duplicate_manual_review',
                            's3_subdirectory': 'opportunities_manual_review',
                        },
                    )
                    continue
                elif (
                    sf_opportunities_data['opportunities_DOB_DOA_phone'].get(
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['OtherPhone'],
                        ),
                        None,
                    )
                    is not None
                    and sf_opportunities_data['opportunities_DOB_DOA_phone'][
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['OtherPhone'],
                        )
                    ][0]
                    != ''
                ):  # if salesforce has a full match
                    logger.warning(
                        f"Opportunity potential duplicate manual review",
                        extra={
                            'record': record,
                            's3_filename': f'opportunity_potential_duplicate_manual_review',
                            's3_subdirectory': 'opportunities_manual_review',
                        },
                    )
                    continue
                elif (
                    sf_opportunities_data['opportunities_DOB_DOA_email'].get(
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['PrimaryEmail'],
                        ),
                        None,
                    )
                    is not None
                    and sf_opportunities_data['opportunities_DOB_DOA_email'][
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['PrimaryEmail'],
                        )
                    ][0]
                    != ''
                ):  # if salesforce has a full match
                    logger.warning(
                        f"Opportunity potential duplicate manual review",
                        extra={
                            'record': record,
                            's3_filename': f'opportunity_potential_duplicate_manual_review',
                            's3_subdirectory': 'opportunities_manual_review',
                        },
                    )
                    continue
                elif (
                    sf_opportunities_data['opportunities_DOB_DOA_email'].get(
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['SecondaryEmail'],
                        ),
                        None,
                    )
                    is not None
                    and sf_opportunities_data['opportunities_DOB_DOA_email'][
                        (
                            plaintiff['DateOfBirth'],
                            case_accident_date,
                            plaintiff['SecondaryEmail'],
                        )
                    ][0]
                    != ''
                ):  # if salesforce has a full match
                    logger.warning(
                        f"Opportunity potential duplicate manual review",
                        extra={
                            'record': record,
                            's3_filename': f'opportunity_potential_duplicate_manual_review',
                            's3_subdirectory': 'opportunities_manual_review',
                        },
                    )
                    continue
                # name checking
                # fuzzy name checking
        # If Opportunity was Update, it would already continue; would not hit this point => Hitting this point means to be inserted
        # For insertions, get Record Type Id as well => Not necessary for Opportunity as the Default "Plaintiff" Record Type is the one to be inserted
        record['StageName'] = (
            'Preliminary'  # if record is to be inserted, insert with Opportunity Stage preliminary; this is NOT to be set on update, hence it is being set just before insert
        )
        redshift_to_sf_opportunity_insert.append(
            record
        )  # if salesforce opportunity does not exist yet, insert it
        redshift_cases_gainid_insert.append(case_gain_id)
    # Deal with the potential duplicated opportunities

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_opportunity_update,
        'gainid_update': redshift_cases_gainid_update,
        'update_manual_review': manual_review_for_opportunity_update,
        'insert': redshift_to_sf_opportunity_insert,
        'gainid_insert': redshift_cases_gainid_insert,
    }


def redshift_data_to_salesforce_data_fundings(
    redshift_cases_data: pd.DataFrame,
    redshift_cases_data_ids: dict[str, str],
    redshift_billings_data: pd.DataFrame,
    redshift_billings_data_ids: dict[str, str],
    sf_fundings_data: dict[str, typing.Any],
    locationid_id_map: dict[str, str],
) -> pipeline.AffectedData:
    redshift_to_sf_funding_update = []
    redshift_billings_gainid_update = []
    redshift_to_sf_funding_insert = []
    redshift_billings_gainid_insert = []
    case_id_lookup = {}
    manual_review_for_funding_update = []

    global variable_balance_providers

    automated_sf_updates_sources = set(automated_sf_updates['Billings'])
    for case in redshift_cases_data.itertuples():
        case_gain_id = str(case.GainId)

        case_account_id = redshift_cases_data_ids.get(
            case_gain_id,
            None,
        )  # use lookup table data to fetch salesforce id with a combination of source id and source name
        if case_account_id and case_account_id != '':
            case_id_lookup[case_gain_id] = (
                case.PlaintiffName,
                case_account_id,
            )  # key is the case GainId and the value is a tuple of plaintiff salesforce name and id

    gainids = redshift_billings_data['GainId'].tolist()
    case_gainids = redshift_billings_data['CaseId'].tolist()
    gainid_to_source_name = aws_operations.get_gainid_to_sourcename_map(
        gainids
    )
    case_gainid_to_case_source_id = aws_operations.get_gainid_to_sourceid_map(
        case_gainids
    )

    for billing in redshift_billings_data.itertuples():
        billing_case_id = str(billing.CaseId)
        billing_gain_id = str(billing.GainId)

        if (
            billing_case_id in case_id_lookup
        ):  # only creates funding if corresponding case exists in salesforce
            case_account_name, case_account_id = case_id_lookup[
                billing_case_id
            ]
            # use lookup table data to fetch salesforce id with a combination of source id and source name
            salesforce_id = redshift_billings_data_ids.get(billing_gain_id)
            if case_account_id and case_account_id != '':
                if (
                    billing.Type == 'Medical Funding'
                ):  # if Medical Funding, Salesforce Funding mapping will be different
                    medical_location_id = locationid_id_map.get(
                        billing.MedicalFacilityId,
                        None,
                    )

                    partner_account_id = locationid_id_map.get(
                        billing.PartnerAccountId,
                        None,
                    )

                    if (
                        medical_location_id is None
                        or medical_location_id == ''
                        or partner_account_id is None
                        or partner_account_id == ''
                    ):
                        logger.warning(
                            f"Funding insert failed medical facility missing",
                            extra={
                                'record': billing._asdict(),
                                's3_filename': f'funding_insert_failed_medical_facility_missing',
                                's3_subdirectory': 'fundings_manual_review',
                            },
                        )
                        continue

                    record: types.FundingsRecord = {
                        'Gain_ID__c': billing_gain_id,
                        'Name': case_account_name,
                        'Medical_Claim_Number__c': billing.MedicalClaimNumber,
                        'Date_of_Service__c': billing.DateOfService.strftime(
                            "%Y-%m-%d"
                        ),
                        'Partner_Account__c': partner_account_id,
                        'Plaintiff__c': case_account_id,
                        'Medical_Case__c': case_gainid_to_case_source_id.get(
                            billing_case_id, None
                        ),
                        'RecordTypeId': salesforce_operations.get_sf_record_type_id(
                            'Funding__c',
                            canonical_data['Salesforce']['FromCanonical'][
                                'BillingTypes'
                            ].get(
                                billing.GainType,
                                billing.Type,
                            ),
                        ),
                        # For record type id, try using canonical gain type id => if blank, use canonical type; canonical type would be filled when executing because inside if block since type was filled in as "Medical Funding"
                    }
                    automated_sf_update_record = (
                        True
                        if (
                            gainid_to_source_name.get(billing_gain_id, None)
                            in automated_sf_updates_sources
                        )
                        else False
                    )  # If source system is in automated_sf_updates_sources, then mark for automated update as opposed to manual review update
                    if (
                        billing.TotalAmount is not None
                        and billing.TotalAmount != ''
                    ):
                        record['Non_Rollup_Charge_Amounts_Total__c'] = str(
                            billing.TotalAmount
                        )
                    if (
                        billing.TotalNonGainAdjustment is not None
                        and billing.TotalNonGainAdjustment != ''
                    ):
                        record['Non_Rollup_Non_Gain_Adjustments_Total__c'] = (
                            str(billing.TotalNonGainAdjustment)
                        )
                    if (
                        billing.TotalNonGainAmountPaidToProvider is not None
                        and billing.TotalNonGainAmountPaidToProvider != ''
                    ):
                        record['Non_Rollup_Non_Gain_Payments_Total__c'] = str(
                            billing.TotalNonGainAmountPaidToProvider
                        )
                    if (
                        billing.TotalBalance is not None
                        and billing.TotalBalance != ''
                    ):
                        record['Non_Rollup_Balance_Total__c'] = str(
                            billing.TotalBalance
                        )
                    if (
                        billing.TotalAmountSent is not None
                        and billing.TotalAmountSent != ''
                    ):
                        record['Non_Rollup_Amounts_to_Partner_Total__c'] = str(
                            billing.TotalAmountSent
                        )
                    if (
                        billing.TotalGainPreNegotiationAdjustment is not None
                        and billing.TotalGainPreNegotiationAdjustment != ''
                    ):
                        record['Total_Gain_Adjustment__c'] = str(
                            billing.TotalGainPreNegotiationAdjustment
                        )
                    if (
                        billing.TotalGainPreNegotiationAmountPaidToProvider
                        is not None
                        and billing.TotalGainPreNegotiationAmountPaidToProvider
                        != ''
                    ):
                        record['Total_Gain_Pre_Negotiation_Amount_Paid__c'] = (
                            str(
                                billing.TotalGainPreNegotiationAmountPaidToProvider
                            )
                        )
                    if medical_location_id:
                        record['Medical_Location__c'] = medical_location_id
                        record['Medical_Facility__c'] = (
                            medical_location_id  # for now medical facility and location are the same
                        )
                        # record['Medical_Facility__c'] = medical_facility_id # if want to see parent account of medical location as medical facility
                    if (
                        billing.TotalDeductible is not None
                        and billing.TotalDeductible != ''
                    ):
                        record['Total_Deductible__c'] = str(
                            billing.TotalDeductible
                        )
                    if (
                        billing.TotalCoinsurance is not None
                        and billing.TotalCoinsurance != ''
                    ):
                        record['Total_Coinsurance__c'] = str(
                            billing.TotalCoinsurance
                        )
                    if (
                        billing.TotalCopayment is not None
                        and billing.TotalCopayment != ''
                    ):
                        record['Total_Copayment__c'] = str(
                            billing.TotalCopayment
                        )
                    # For checking whether the SalesforceId populated in the billing table is valid or not => group them into insert or update
                    rs_plaintiffid_mapping_sf_opportunity_id = case_account_id
                    rs_DOS_mapping_sf_DOS = billing.DateOfService.strftime(
                        "%Y-%m-%d"
                    )
                    rs_total_balance_mapping_sf_invoice_amount = float(
                        str(billing.TotalBalance)
                        if billing.TotalBalance is not None
                        else 0
                    )
                    medical_claim_number = billing.MedicalClaimNumber
                    redshift_billing_key_with_total_amount = (
                        rs_plaintiffid_mapping_sf_opportunity_id,
                        medical_location_id,
                        partner_account_id,
                        rs_DOS_mapping_sf_DOS,
                        medical_claim_number,
                        rs_total_balance_mapping_sf_invoice_amount,
                    )  # note: used to be "medical_facility_id" in the key, now medical_location_id to be as specific as possible, and "partner_account_id"
                    redshift_billing_key_without_total_amount = (
                        rs_plaintiffid_mapping_sf_opportunity_id,
                        medical_location_id,
                        partner_account_id,
                        rs_DOS_mapping_sf_DOS,
                        medical_claim_number,
                    )  # note: used to be "medical_facility_id" in the key, now medical_location_id to be as specific as possible, and "partner_account_id"
                    provider_name = gainid_to_source_name.get(
                        billing_gain_id, None
                    )

                    if salesforce_id and salesforce_id != '':
                        record['Id'] = salesforce_id
                        if automated_sf_update_record:
                            # Flip the funding stage only if the funding is Historic, Withdrawn by Provider in SF
                            if (
                                salesforce_id
                                in sf_fundings_data[
                                    'fundings_check_stage_sub_stage_before_update'
                                ]
                            ):
                                sf_fundings_data_funding_stage = sf_fundings_data[
                                    'fundings_check_stage_sub_stage_before_update'
                                ][
                                    salesforce_id
                                ][
                                    'Funding_Stage__c'
                                ]
                                sf_fundings_data_funding_sub_stage = sf_fundings_data[
                                    'fundings_check_stage_sub_stage_before_update'
                                ][
                                    salesforce_id
                                ][
                                    'Funding_Sub_Stage__c'
                                ]
                                if (
                                    sf_fundings_data_funding_stage
                                    == 'Historic'
                                    and sf_fundings_data_funding_sub_stage
                                    == 'Withdrawn by Provider'
                                ):
                                    if (
                                        billing.GainType
                                        == 'Medical Funding - Serviced'
                                    ):
                                        record['Funding_Stage__c'] = 'Serviced'
                                        record['Funding_Sub_Stage__c'] = ''

                            redshift_to_sf_funding_update.append(
                                record
                            )  # updates salesforce record
                            redshift_billings_gainid_update.append(
                                billing_gain_id
                            )
                        else:
                            record['RedshiftId'] = str(billing_gain_id)
                            manual_review_for_funding_update.append(record)
                    else:
                        if (
                            redshift_billing_key_with_total_amount
                            in sf_fundings_data['fundings_mapping_res']
                        ):
                            fundings_for_key = sf_fundings_data[
                                'fundings_mapping_res'
                            ][redshift_billing_key_with_total_amount]
                            if len(fundings_for_key) == 1:
                                record['Id'] = fundings_for_key[0]
                            elif salesforce_id in fundings_for_key:
                                record[
                                    'Id'
                                ] = (  # pyright: ignore[reportGeneralTypeIssues]
                                    salesforce_id
                                )
                            else:
                                logger.warning(
                                    f"Multiple matching fundings manual review",
                                    extra={
                                        'record': record,
                                        's3_filename': f'multiple_matching_fundings_manual_review',
                                        's3_subdirectory': 'fundings_manual_review',
                                    },
                                )
                                continue
                            if automated_sf_update_record:
                                redshift_to_sf_funding_update.append(
                                    record
                                )  # updates salesforce record
                                redshift_billings_gainid_update.append(
                                    billing_gain_id
                                )

                            else:
                                record['RedshiftId'] = str(billing_gain_id)
                                manual_review_for_funding_update.append(record)
                        else:
                            if (
                                redshift_billing_key_without_total_amount
                                in sf_fundings_data[
                                    'fundings_potential_duplicates_check_before_insert'
                                ]
                            ):
                                (
                                    sf_id,
                                    salesforce_invoice_amount,
                                ) = sf_fundings_data[
                                    'fundings_potential_duplicates_check_before_insert'
                                ][
                                    redshift_billing_key_without_total_amount
                                ]
                                record['Id'] = sf_id
                                if provider_name in variable_balance_providers:
                                    if automated_sf_update_record:
                                        redshift_to_sf_funding_update.append(
                                            record
                                        )  # updates salesforce record
                                        redshift_billings_gainid_update.append(
                                            billing_gain_id
                                        )

                                    else:
                                        record['RedshiftId'] = str(
                                            billing_gain_id
                                        )
                                        manual_review_for_funding_update.append(
                                            record
                                        )
                                else:
                                    record[
                                        'redshift_sf_id'
                                    ] = (  # pyright: ignore[reportGeneralTypeIssues]
                                        salesforce_id
                                    )
                                    record['sf_id'] = sf_id
                                    record['Salesforce Invoice Amount'] = (
                                        salesforce_invoice_amount
                                    )
                                    logger.warning(
                                        f"Funding potential duplicate with wrong amount value manual review",
                                        extra={
                                            'record': record,
                                            's3_filename': f'funding_potential_duplicate_with_wrong_amount_value_manual_review',
                                            's3_subdirectory': 'fundings_manual_review',
                                        },
                                    )
                            else:
                                redshift_to_sf_funding_insert.append(record)
                                redshift_billings_gainid_insert.append(
                                    billing_gain_id
                                )

                if (
                    billing.Type == 'Cash Advance'
                ):  # if Cash Advance, Salesforce Funding mapping will be different
                    pass  # TBD
            else:  # if case id exists in Redshift, but salesforce id does not, the opportunity does not exist in Salesforce for some reason => mark it for manual review
                logger.warning(
                    f"Funding insert failed opportunity salesforce id missing",
                    extra={
                        'record': billing,
                        's3_filename': f'funding_insert_failed_opportunity_salesforce_id_missing',
                        's3_subdirectory': 'fundings_manual_review',
                    },
                )
        else:  # if related opportunity does not exist in salesforce
            logger.warning(
                f"Funding insert failed no matching opportunity",
                extra={
                    'record': billing,
                    's3_filename': f'funding_insert_failed_no_matching_opportunity',
                    's3_subdirectory': 'fundings_manual_review',
                },
            )
    # Deal with the potential duplicate fundings

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_funding_update,
        'gainid_update': redshift_billings_gainid_update,
        'update_manual_review': manual_review_for_funding_update,
        'insert': redshift_to_sf_funding_insert,
        'gainid_insert': redshift_billings_gainid_insert,
    }


def redshift_data_to_salesforce_data_charges(
    redshift_billings_data: pd.DataFrame,
    redshift_billings_data_ids: dict[str, str],
    redshift_charges_data: pd.DataFrame,
    redshift_charges_data_ids: dict[str, str],
    sf_charges_data: dict[str, typing.Any],
) -> pipeline.AffectedData:
    # For creating or inserting funding charges, it will use data from both info from charges table and billings table in Redshift
    redshift_to_sf_charge_update = []
    redshift_charges_gainid_update = []
    redshift_to_sf_charge_insert = []
    redshift_charges_gainid_insert = []
    billing_lookup = {}
    manual_review_for_charge_update = []

    billing_lookup = redshift_billings_data.set_index('GainId').T.to_dict()

    def clean_cpt_code(cpt: str) -> str:
        return (
            '' if not cpt else cpt.strip()[:5] if (len(cpt) > 5) else cpt
        )  # keep first 5 characters only

    def clean_decimal_values(amount: int) -> str:
        return (
            str(amount).replace(',', '') if amount else ''
        )  # convert to string and remove commas => needed for Salesforce currency type value insert/update; if/else needed because None values cause error hence blank string

    automated_sf_updates_sources = set(automated_sf_updates['Charges'])

    gainids = redshift_charges_data['GainId'].tolist()
    gainid_to_source_name = aws_operations.get_gainid_to_sourcename_map(
        gainids
    )

    gainid_to_source_id = aws_operations.get_gainid_to_sourceid_map(gainids)
    for charge in redshift_charges_data.itertuples():
        billing_account_id = None
        charge_gain_id = str(charge.GainId)
        charge_billing_id = str(charge.BillingId)

        billing = billing_lookup.get(
            charge_billing_id, None
        )  # get related billing

        if billing:
            billing_account_id = redshift_billings_data_ids.get(
                charge_billing_id,
                None,
            )  # use lookup table data to fetch salesforce id with a combination of source id and source name
        if not billing_account_id:
            logger.warning(
                f"Charge missing BillingId or DOS manual review",
                extra={
                    'record': charge._asdict(),
                    's3_filename': f'charge_missing_BillingId_or_DOS_manual_review',
                    's3_subdirectory': 'charges_manual_review',
                },
            )
        else:
            salesforce_id = redshift_charges_data_ids.get(
                charge_gain_id,
                None,
            )  # use lookup table data to fetch salesforce id with a combination of source id and source name
            record: types.ChargesRecord = {
                'Date_of_Service__c': charge.DateOfService.strftime(
                    "%Y-%m-%d"
                ),
                'Amount__c': clean_decimal_values(charge.Amount),
                'CPT_Code__c': clean_cpt_code(charge.CPTCode),
                'CPT_Modifier__c': charge.CPTModifier,
                'Non_Gain_Adjustment__c': clean_decimal_values(
                    charge.NonGainAdjustment
                ),
                'Non_Gain_Amount_Paid_to_Provider__c': clean_decimal_values(
                    charge.NonGainAmountPaidToProvider
                ),
                'Reimbursement_Rate__c': clean_decimal_values(
                    charge.ReimbursementRate
                ),
                'Gain_Adjustment__c': clean_decimal_values(
                    charge.GainPreNegotiationAdjustment
                ),
                'Gain_Pre_Negotiation_Amount_Paid__c': clean_decimal_values(
                    charge.GainPreNegotiationAmountPaidToProvider
                ),
                'Deductible__c': clean_decimal_values(charge.Deductible),
                'Coinsurance__c': clean_decimal_values(charge.Coinsurance),
                'Copayment__c': clean_decimal_values(charge.Copayment),
                # 'Amount_to_Partner__c': clean_decimal_values(charge[canonical['Charges']['AmountSent']]), # formula field, can't be set for Salesforce
                'Funding__c': billing_account_id,
                'Charge_Id__c': gainid_to_source_id.get(
                    charge_gain_id, None
                ),  # only way to match if charge is in Salesforce
                'Gain_ID__c': charge_gain_id,
                # unlike Opportunity which has DOB_DOA and Funding which has Claim_DOS_Total, charges have no unique identifying keys
                # however, also unlike other objects, one charge should exist in only one source system, hence can have a unique external source id
            }
            automated_sf_update_record = (
                True
                if (
                    gainid_to_source_name.get(charge_gain_id, None)
                    in automated_sf_updates_sources
                )
                else False
            )  # If source system is in automated_sf_updates_sources, then mark for automated update as opposed to manual review update
            if salesforce_id:  # if exists in mapped table
                record['Id'] = salesforce_id
                if automated_sf_update_record:
                    redshift_to_sf_charge_update.append(
                        record
                    )  # update existing salesforce funding charge
                    redshift_charges_gainid_update.append(charge_gain_id)

                else:
                    record['RedshiftId'] = str(charge_gain_id)
                    manual_review_for_charge_update.append(record)
                continue
            elif (
                charge_gain_id
                in sf_charges_data['charges_externalsource_mapping_res']
            ):  # if does not exist in mapped table, but exists in Salesforce
                record['Id'] = sf_charges_data[
                    'charges_externalsource_mapping_res'
                ][charge_gain_id]
                if automated_sf_update_record:
                    redshift_to_sf_charge_update.append(
                        record
                    )  # update existing salesforce funding charge
                    redshift_charges_gainid_update.append(charge_gain_id)

                else:
                    record['RedshiftId'] = str(charge_gain_id)
                    manual_review_for_charge_update.append(record)
                continue
            # if reached this point, means it is not an update, rather an insert
            redshift_to_sf_charge_insert.append(record)
            redshift_charges_gainid_insert.append(charge_gain_id)

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_charge_update,
        'gainid_update': redshift_charges_gainid_update,
        'update_manual_review': manual_review_for_charge_update,
        'insert': redshift_to_sf_charge_insert,
        'gainid_insert': redshift_charges_gainid_insert,
    }


def redshift_data_to_salesforce_data_files(
    bucket: str,
    redshift_data_cases: pd.DataFrame,
    redshift_data_files: pd.DataFrame,
    name_id_map: dict[str, str],
    name_type_map: dict[str, str],
) -> pipeline.AffectedData:
    case_id_lookup = {}
    case_dates = {}
    redshift_files_gainid_insert = []
    redshift_to_sf_files_update = []
    redshift_to_sf_files_insert = []
    manual_review_for_file_update = []
    files_id_subset = local_operations.get_canonical_ids_from_df(
        redshift_data_files
    )

    cases_id_subset = local_operations.get_canonical_ids_from_df(
        redshift_data_cases
    )

    redshift_files_sf_data, _ = aws_operations.query_postgres_sf_map(
        mappings=["files"],
        id_subset=files_id_subset,
    )
    redshift_files_sf_map = redshift_files_sf_data['files']
    redshift_cases_sf_data, _ = aws_operations.query_postgres_sf_map(
        mappings=["cases"],
        id_subset=cases_id_subset,
    )
    redshift_cases_sf_map = redshift_cases_sf_data['cases']
    # redshift_file_sf_ids = list(redshift_files_sf_map.values())
    # sf_file_names = salesforce_operations.get_sf_file_names(redshift_file_sf_ids)
    for case in redshift_data_cases.itertuples():
        case_gain_id = str(case.GainId)
        case_plaintiff_id = str(case.PlaintiffId)

        if (
            not case_plaintiff_id in case_id_lookup
        ):  # create lookup from plaintiff id to case id
            case_id_lookup[case_plaintiff_id] = [case_gain_id]
        else:
            case_id_lookup[case_plaintiff_id].append(case_gain_id)
        case_dates[case_gain_id] = case.AccidentDate
        case_key = case_gain_id

    gainids = redshift_data_files['GainId'].tolist()

    gainid_to_source_name = aws_operations.get_gainid_to_sourcename_map(
        gainids
    )
    for doc in redshift_data_files.itertuples():
        # File URL example: s3://gain-servicing/integration/ati/raw_data/processed/files/22874635_DC_20230414_ATI-2023-04-26_14-14-06.pdf
        doc_url = str(doc.Url)
        doc_gain_id = str(doc.GainId)

        file_split_url = doc_url[5:].split("/", 1)
        bucket = file_split_url[0]
        key = file_split_url[1]
        file_title_list = file_split_url[1].split("/")
        file_name_with_extension = file_title_list[-1]
        file_name = file_name_with_extension.split(".")[0]
        file_name_without_timestamp = file_name.split("-")[0]
        if (
            canonical_data["Salesforce"]["FromCanonical"]["DocumentTypes"].get(
                doc.Type, ''
            )
            == ''
        ):
            if (
                doc.Type == ''
            ):  # if the file has a blank document type, add to a list for manual review
                file_split_url = doc_url[5:].split("/", 1)[1].split("/")
                missing = {
                    'Title': file_name_without_timestamp,
                    'PathOnClient': f'{doc_url}',
                }
                logger.warning(
                    f"Files missing doc type manual review",
                    extra={
                        'record': missing,
                        's3_filename': f'files_missing_doc_type_manual_review',
                        's3_subdirectory': 'files_manual_review',
                    },
                )
            # if the document type does not exist in salesforce, skip document. Once added to manual review, skip document.
            continue
        # If the file is not relevant to gain, skip document.
        if not doc.RelevantToGain:
            continue
        record = {
            'Title': file_name_without_timestamp,
            # 'FirstPublishLocationId': case_id_lookup[canonical['Files']['PlaintiffId']]
            'Document_Type__c': canonical_data["Salesforce"]["FromCanonical"][
                "DocumentTypes"
            ].get(doc.Type, ''),
            'PathOnClient': f'{doc_url}',
        }
        gainid = gainid_to_source_name.get(doc_gain_id)
        client_salesforce_name_by_gainid = (
            local_operations.get_client_salesforce_name(gainid)
        )
        client_salesforce_name_by_doc_gainid = (
            local_operations.get_client_salesforce_name(
                gainid_to_source_name.get(doc_gain_id)
            )
        )

        if (
            client_salesforce_name_by_gainid
            and name_type_map[client_salesforce_name_by_gainid] == 'Law Firm'
        ):  # sets source name as law firm if law firm
            record['Law_Firm__c'] = name_id_map[
                canonical_data["Salesforce"]["FromCanonical"]["Clients"].get(
                    gainid,
                    '',
                )
            ]
        elif (
            client_salesforce_name_by_doc_gainid
            and name_type_map[client_salesforce_name_by_doc_gainid]
            == 'Medical Facility'
        ):  # sets source name as medical facility if medical facility
            record['Medical_Facility__c'] = name_id_map[
                canonical_data["Salesforce"]["FromCanonical"]["Clients"].get(
                    gainid_to_source_name.get(doc_gain_id, None),
                    '',
                )
            ]
        sf_map_key = doc_gain_id
        local_location = (
            f'integration/integration_code/{file_name_with_extension}'
        )
        aws_operations.download_s3(
            bucket=bucket,
            key=key,
            local_location=local_location,
        )
        if pathlib.Path(local_location).is_file():
            with open(local_location, 'rb') as f:
                record['VersionData'] = (base64.b64encode(f.read())).decode()
        if case_id_lookup.get(doc.PlaintiffId, None) is not None:
            for case in case_id_lookup[
                doc.PlaintiffId
            ]:  # iterates through all cases that belong to the file's plaintiff, connects file to all valid cases
                if gainid_to_source_name.get(doc_gain_id, None) in (
                    'ATI',
                    'Jopari',
                ):  # For ATI, only mapping files to cases with matching case ids
                    if case != doc.CaseId:
                        continue
                case_key = doc.CaseId
                if (
                    "null" in case or "Null" in case
                ):  # passes over null case ids
                    continue
                if gainid_to_source_name.get(doc_gain_id, None) not in [
                    'ATI',
                    'Jopari',
                ] and (
                    case_dates.get(case, None) is None
                    or doc.SourceCreateDateTime.date() < case_dates[case]
                ):  # skips cases where file was created before case date
                    continue
                new_record = copy.deepcopy(record)
                case_salesforce_id = redshift_cases_sf_map.get(case_key, None)
                if case_salesforce_id is not None:
                    # attach bidirectionally in Salesforce
                    new_record['Opportunity__c'] = case_salesforce_id
                    new_record['FirstPublishLocationId'] = case_salesforce_id
                    salesforce_id = redshift_files_sf_map.get(sf_map_key, None)
                    if salesforce_id and salesforce_id != '':
                        '''
                        # Edge case; to be finalized later
                        # record['Id'] = file[canonical['Files']['SalesforceId']]
                        # if (curr_timestamp - legalperson[24]).seconds//3600 > 0:#24: # difference between update time (now) and last modified is > 24 hours i.e. 1 day
                        #   redshift_to_sf_files_update.append(record)
                        '''
                        # if record['Title'] in sf_file_names:
                        #     if sf_file_names[record['Title']] == record['Title']: # if old file did not have version number
                        #         file_name = record['Title'] + "_0"
                        #     else: # if old file already had version number
                        #         file_name = record['Title'] + "_" + str((int(sf_file_names[record['Title']].split("_")[-1]))+1)
                        #     sf_file_names[record['Title']] = file_name
                        record['Id'] = salesforce_id
                        record['RedshiftId'] = doc_gain_id
                        manual_review_for_file_update.append(record)
                        continue
                    redshift_to_sf_files_insert.append(new_record)
                    redshift_files_gainid_insert.append(doc_gain_id)

                else:
                    logger.warning(
                        f"File insert failed no matching opportunity",
                        extra={
                            'record': new_record,
                            's3_filename': f'file_insert_failed_no_matching_opportunity',
                            's3_subdirectory': 'files_manual_review',
                        },
                    )
        file_utils.clean_up_file(local_location)

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_files_update,
        'update_manual_review': manual_review_for_file_update,
        'insert': redshift_to_sf_files_insert,
        'gainid_insert': redshift_files_gainid_insert,
        'gainid_update': [],
    }


def redshift_data_to_salesforce_data_notes(
    redshift_data_notes: pd.DataFrame,
) -> pipeline.AffectedData:
    redshift_to_sf_notes_update = []
    redshift_to_sf_notes_insert = []
    redshift_notes_gainid_insert = []
    manual_review_for_note_update = []
    notes_id_subset = local_operations.get_canonical_ids_from_df(
        redshift_data_notes
    )
    cases_id_subset = redshift_data_notes['CaseId'].tolist()

    gainids = redshift_data_notes['GainId'].tolist()

    redshift_notes_sf_data, _ = aws_operations.query_postgres_sf_map(
        mappings=["notes"],
        id_subset=notes_id_subset,
    )
    redshift_notes_sf_map = redshift_notes_sf_data['notes']
    redshift_cases_sf_data, _ = aws_operations.query_postgres_sf_map(
        mappings=["cases"],
        id_subset=cases_id_subset,
    )
    redshift_cases_sf_map = redshift_cases_sf_data['cases']

    gainid_to_source_name = aws_operations.get_gainid_to_sourcename_map(
        gainids
    )

    source_name_to_gainids = {}
    for gainid, sourcename in gainid_to_source_name.items():
        if sourcename not in source_name_to_gainids:
            source_name_to_gainids[sourcename] = []
        source_name_to_gainids[sourcename].append(gainid)
    df_note_creation_date_time = pd.DataFrame()
    for source_name, gainids in source_name_to_gainids.items():
        if df_note_creation_date_time.empty:
            df_note_creation_date_time = (
                aws_operations.get_postgres_sourcecreatedatetime(
                    gainids, source_name
                )
            )
        else:
            df_note_creation_date_time = pd.concat(
                [
                    df_note_creation_date_time,
                    aws_operations.get_postgres_sourcecreatedatetime(
                        gainids, source_name
                    ),
                ]
            )
    note_creation_date_time_map = df_note_creation_date_time.set_index(
        'gainid'
    ).to_dict()['sourcecreatedatetime']
    for note in redshift_data_notes.itertuples():
        note_gain_id = str(note.GainId)
        case_id = str(note.CaseId)

        record = {}
        source_name = gainid_to_source_name.get(note_gain_id, None)

        if source_name is None:
            continue
        case_salesforce_id = redshift_cases_sf_map.get(case_id, None)
        if case_salesforce_id is None or case_salesforce_id == '':
            logger.warning(
                f"Note insert failed no matching opportunity",
                extra={
                    'record': {
                        'note_gain_id': note_gain_id,
                        'case_id': case_id,
                        'source_name': source_name,
                    },
                    's3_filename': f'note_insert_failed_no_matching_opportunity',
                    's3_subdirectory': 'notes_manual_review',
                },
            )
            continue
        record = {
            'Title': source_name
            + ' Note - '
            + str(note_creation_date_time_map.get(note_gain_id, ''))
            + '; '
            + str(note.NoteCreatorName),
            'Content': base64.b64encode(
                (str(note.Note)).encode('latin-1')
            ).decode(),
        }
        note_salesforce_id = redshift_notes_sf_map.get(note_gain_id, None)
        if note_salesforce_id and note_salesforce_id != '':
            record['Id'] = note_salesforce_id
            record['RedshiftId'] = note_gain_id
            manual_review_for_note_update.append(record)
            continue
        redshift_to_sf_notes_insert.append(record)
        redshift_notes_gainid_insert.append(note_gain_id)

    logger.warning('flush', extra={'flush': True})
    return {
        'update': redshift_to_sf_notes_update,
        'update_manual_review': manual_review_for_note_update,
        'insert': redshift_to_sf_notes_insert,
        'gainid_insert': redshift_notes_gainid_insert,
        'gainid_update': [],
    }


def redshift_data_to_salesforce_data_accountopportunityrelation(
    redshift_accountopportunityrelation_data: dict[str, pd.DataFrame],
    redshift_accountopportunityrelation_data_ids: dict[str, dict[str, str]],
    salesforce_accountopportunityrelation_data: pd.DataFrame,
) -> list[dict[typing.Hashable, typing.Any]]:
    if (
        redshift_accountopportunityrelation_data['cases'].empty
        or 'cases' not in redshift_accountopportunityrelation_data_ids
        or not redshift_accountopportunityrelation_data_ids['cases']
        or not redshift_accountopportunityrelation_data_ids
        or salesforce_accountopportunityrelation_data.empty
    ):
        return []

    redshift_accountopportunityrelation_case_data_ids = (
        redshift_accountopportunityrelation_data_ids['cases']
    )
    redshift_accountopportunityrelation_case_data_ids = [
        [x, y]
        for (x, y) in redshift_accountopportunityrelation_case_data_ids.items()
    ]
    redshift_accountopportunityrelation_data_ids_df = pd.DataFrame(
        redshift_accountopportunityrelation_case_data_ids
    )
    redshift_accountopportunityrelation_data_ids_df = (
        redshift_accountopportunityrelation_data_ids_df.rename(
            columns={0: 'gainid', 1: 'salesforceid'}
        )
    )

    redshift_accountopportunityrelation_case_data = (
        redshift_accountopportunityrelation_data['cases']
    )

    redshift_accountopportunityrelation_case_data = [
        {
            canonical_column_name: redshift_accountopportunityrelation_case_record.iloc[
                canonical_column_index
            ]
            for (canonical_column_name, canonical_column_index) in canonical[
                'Cases'
            ].items()
        }
        for _, redshift_accountopportunityrelation_case_record in redshift_accountopportunityrelation_case_data.iterrows()
    ]
    redshift_accountopportunityrelation_case_data = pd.DataFrame(
        redshift_accountopportunityrelation_case_data
    )
    redshift_accountopportunityrelation_case_data.columns = (
        redshift_accountopportunityrelation_case_data.columns.str.lower()
    )
    # We need to merge salesforceid to the accountopportunityrelation data for lookup in the SF object
    redshift_accountopportunityrelation_case_data = pd.merge(
        redshift_accountopportunityrelation_case_data,
        redshift_accountopportunityrelation_data_ids_df,
        on=['gainid'],
        how='inner',
    )
    redshift_accountopportunityrelation_cases_salesforce_ids = (
        redshift_accountopportunityrelation_case_data['salesforceid']
        .unique()
        .tolist()
    )
    redshift_accountopportunityrelation_case_data[
        'insurancevendorassigned'
    ] = redshift_accountopportunityrelation_case_data[
        'insurancevendorassigned'
    ].map(
        {True: 'Yes', False: 'No', None: ''}
    )
    # Update Tail Claim field in Salesforce with the value from Redshift
    redshift_accountopportunityrelation_tail_claim_data = (
        redshift_accountopportunityrelation_case_data[
            ['salesforceid', 'tailclaimcase']
        ]
    )
    redshift_accountopportunityrelation_tail_claim_data.dropna(
        subset=['tailclaimcase'], inplace=True
    )
    redshift_accountopportunityrelation_tail_claim_data_dict = (
        redshift_accountopportunityrelation_tail_claim_data.set_index(
            'salesforceid'
        )['tailclaimcase'].to_dict()
    )
    salesforce_accountopportunityrelation_data['Tail_Claim__c'] = (
        salesforce_accountopportunityrelation_data.apply(
            lambda x: redshift_accountopportunityrelation_tail_claim_data_dict.get(
                x['OpportunityId__c'], x['Tail_Claim__c']
            ),
            axis=1,
        )
    )
    for row in salesforce_accountopportunityrelation_data.itertuples(
        index=True
    ):
        index = row.Index
        if (
            row.OpportunityId__c
            in redshift_accountopportunityrelation_cases_salesforce_ids
        ):
            salesforce_accountopportunityrelation_data.at[
                index, 'Insurance_Vendor__c'
            ] = shared.get_first_value(
                redshift_accountopportunityrelation_case_data.loc[
                    redshift_accountopportunityrelation_case_data[
                        'salesforceid'
                    ]
                    == row.OpportunityId__c,
                    'insurancevendorassigned',
                ]
            )

            treatment_complete_flag = shared.get_first_value(
                redshift_accountopportunityrelation_case_data.loc[
                    redshift_accountopportunityrelation_case_data[
                        'salesforceid'
                    ]
                    == row.OpportunityId__c,
                    'treatmentcompleted',
                ]
            )
            if treatment_complete_flag is not None:
                salesforce_accountopportunityrelation_data.at[
                    index, 'Treatment_Complete__c'
                ] = treatment_complete_flag
    salesforce_accountopportunityrelation_data_dict = (
        salesforce_accountopportunityrelation_data[
            [
                'Id',
                'Insurance_Vendor__c',
                'Treatment_Complete__c',
                'Tail_Claim__c',
            ]
        ]
    ).to_dict(orient='records')
    return salesforce_accountopportunityrelation_data_dict


def redshift_generate_salesforce_accountopportunityrelation_upsert_data(
    input_df: pd.DataFrame,
) -> list[dict[typing.Hashable, typing.Any]]:
    account_opportunity_update_data = []
    if input_df is not None:
        account_opportunity_update_data = input_df.to_dict(orient='records')

    return account_opportunity_update_data


def duplicates_from_salesforce(data: dict[str, list[dict[str, str]]]) -> None:
    invalid_objects = {}
    logger.info(f'{", ".join(list(data.keys()))} merged in Salesforce.')
    for canonical_object, records in data.items():
        if canonical_object not in canonical:
            invalid_objects[canonical_object] = records
        else:
            records_to_update = []
            for record in records:
                records_to_update.append(
                    (
                        record["gainid"],
                        record["external_id_container"],
                    )
                )
            aws_operations.update_postgres_sf_id_and_external_id_container_map(
                records_to_update
            )

    if invalid_objects:
        logger.error(
            f"Failed to update following data: {str(invalid_objects)}",
            extra={
                'additional_details': str(invalid_objects),
            },
        )
        raise ValueError(invalid_objects)
