CREATE
OR REPLACE VIEW "integration"."dev"."cases_with_settled_billings_status" AS
SELECT DISTINCT 
  cases.sourceid,
  cases.sourcename,
  billings.status
FROM
  integration.dev.cases cases
  JOIN 
     integration.dev.billings billings 
       ON 
         billings.caseid:: text = cases.sourceid:: text
         AND billings.sourcename:: text = cases.sourcename:: text
  WHERE 
     billings.todelete = false
;

CREATE
OR REPLACE VIEW "integration"."staging"."cases_with_settled_billings_status" AS
SELECT DISTINCT 
  cases.sourceid,
  cases.sourcename,
  billings.status
FROM
  integration.staging.cases cases
  JOIN 
     integration.staging.billings billings 
       ON 
         billings.caseid:: text = cases.sourceid:: text
         AND billings.sourcename:: text = cases.sourcename:: text
  WHERE 
     billings.todelete = false
;

CREATE
OR REPLACE VIEW "integration"."prod"."cases_with_settled_billings_status" AS
SELECT DISTINCT 
  cases.sourceid,
  cases.sourcename,
  billings.status
FROM
  integration.prod.cases cases
  JOIN 
     integration.prod.billings billings 
       ON 
         billings.caseid:: text = cases.sourceid:: text
         AND billings.sourcename:: text = cases.sourcename:: text
  WHERE 
     billings.todelete = false
;

CREATE
OR REPLACE VIEW "integration"."main"."cases_with_settled_billings_status" AS
SELECT DISTINCT 
  cases.sourceid,
  cases.sourcename,
  billings.status
FROM
  integration.main.cases cases
  JOIN 
     integration.main.billings billings 
       ON 
         billings.caseid:: text = cases.sourceid:: text
         AND billings.sourcename:: text = cases.sourcename:: text
  WHERE 
     billings.todelete = false
;