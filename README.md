# GainInternalWebApp

## Getting started

https://gainservicing.atlassian.net/wiki/spaces/TEC/pages/215416833/Data+Integration+Environment+Setup+Guide

## Useful Commands

### Django

```
python manage.py runserver
python manage.py runserver --settings GainInternalWebApp.devSettings
python manage.py makemigrations integration
python manage.py migrate --settings GainInternalWebApp.devSettings --fake integration zero # Undo all previous migrations
python manage.py migrate integration # default migrate
python manage.py migrate --settings GainInternalWebApp.devSettings integration # NOTE: Might have managed = False set up in migration; comment migrate = False to be able to run migration
```

### Docker

```
docker compose up
docker ps
docker ps a
```

### PDM

#### Installing dependencies

```
pip install pdm
pdm install
```

#### Common comands

[PDM Installation](https://pypi.org/project/pdm/)

```
python -m venv venv
venv\Scripts\activate.bat # Windows
pip install pdm
python -m pdm install
```

```
pdm add {lib}
pdm add --dev {lib}
pdm add --dev --group test {lib}
```

### CI/CD

`docker build -f Dockerfile-ci -t test_ci .`

`docker build -t test_app .`

`docker run -p 8000:8000 test_develop`

# Testing

### Run test with coverage

`pdm run pytest --cov --cov-report html`

### Start local postgres database for tests

`docker run -p 5432:5432 --tmpfs /var/lib/pg/data -e PGDATA=/var/lib/pg/data -e POSTGRES_PASSWORD=postgres --name integration --rm postgres:14`

> **Disclaimer:** If you stop the tests during a debug, the template schema will not be deleted and the docker container will need to be recreated.

## 📁 Folder Breakdown

### `tests/`

- Contains all source code related to common tests.

### `tests/schemas/*.sql`

- Have all the sql scripts to create tables on the template schema test database.
- Example:
  - `billings.sql` → Script for the billing table.

### `tests/seeds/*.sql`

- Have all the sql scripts to create seed data that will be in the template schema to be used in the tests.
- Example:
  - `billings.sql` → Script adding data to billing and the reference to gain_id_map.

### `tests/database_fixtures.py`

- Fixtures related to the database creation on tests, all the scripts for schema and seed and future patches should be added there.

### `tests/s3_fixture.py`

- Fixtures related to adding data to our local s3 (moto) for testing data.

### `tests/salesforce_fixtures.py`

- Common fixtures that creates a mock for salesforce calls, that way we can run the tests without really calling salesforce and check if the values send are correct on the assertions of the tests.
- Example:
  - `mock_get_sf_bulk` → mock the get_sf_bulk, that is being used in most of the salesforce calls.

### `tests/test_helper.py`

- General fixtures and helper functions
- Example:
  - `verify_audit_errors` → helper to verify if an audit log was called with the flag is_error as True, if so the tests would fail.
  - `get_tables_count` → generic helper to count records in database tables, accepts a list of tables to count.

### `integration/integration_code/tests/`

- Contains integration-specific tests organized by module.
- Example:
  - `integration/integration_code/tests/ati/` → Tests for ATI module
  - `integration/integration_code/tests/jopari/` → Tests for Jopari module
  - `integration/integration_code/tests/salesforce_operations/` → Tests for Salesforce operations

### `{app}/**/{file being tested}_test.py`

- Test files to test the specific file
- Example:
  - `integration/integration_code/ati_test.py` → Testing ATI main operations

### `{app}/**/fixtures/**/*.*`

- JSON/PDfs any kind of data that will be used on the tests
- Example:
  - `integration/integration_code/fixtures/ati/base_data.json` → Json containing some example data of canonical objects for ATI

### `conftest.py`

- Initial script called by pytest