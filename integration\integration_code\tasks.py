from typing import Any

from . import context


def extract_common_args(args_dict: dict[str, Any]):
    """Extract common arguments from args_dict with default values."""
    return {
        'test': args_dict.get('test', False),
        'all_data': args_dict.get('all_data', False),
        'move_flag': args_dict.get('move_flag', True),
        'charges_update_flag': args_dict.get('charges_update_flag', True),
        'billings_update_flag': args_dict.get('billings_update_flag', True),
        'cases_update_flag': args_dict.get('cases_update_flag', True),
        'partial_config': args_dict.get('partial_config', None),
    }


def get_default_partial_config():
    return {
        'plaintiffs': True,
        'lawfirms': True,
        'legalpersonnel': True,
        'cases': True,
        'billings': True,
        'charges': True,
        'files': True,
        'notes': True,
    }


def get_default_delete_partial_config():
    return {
        'plaintiffs': False,
        'lawfirms': False,
        'legalpersonnel': False,
        'cases': True,
        'billings': True,
        'charges': False,
        'files': False,
        'notes': False,
    }


def get_default_settled_partial_config():
    return {
        'plaintiffs': False,
        'lawfirms': False,
        'legalpersonnel': False,
        'cases': False,
        'billings': True,
        'charges': False,
        'files': False,
        'notes': False,
    }


def ati_delete_task(args_dict: dict[str, Any]) -> str:
    """Django Q task to run ATI delete logic in the background."""
    from . import ati

    args = extract_common_args(args_dict)

    try:
        ati.get_main_delete(args['move_flag'], args['test'], args['all_data'])
    except Exception as e:
        if str(e) == "LOCK_NOT_ACQUIRED":
            return 'ATI Delete Failed: Lock not acquired'
        return f'ATI Delete Failed: {str(e)}'

    return 'ATI Delete Complete'


def salesforce_delete_task(args_dict: dict[str, Any]) -> str:
    """Django Q task to run Salesforce delete logic in the background."""
    from . import salesforce

    context.request_context.set({'route': 'salesforce_delete'})

    args = extract_common_args(args_dict)
    partial_config = (
        args['partial_config'] or get_default_delete_partial_config()
    )

    try:
        salesforce.post_main_delete(
            args['test'], partial_config, args['all_data']
        )
    except Exception as e:
        if str(e) == "LOCK_NOT_ACQUIRED":
            return 'Salesforce Delete Failed: Lock not acquired'
        return f'Salesforce Delete Failed: {str(e)}'

    return 'Salesforce Delete Complete'


def ati_upsert_task(args_dict: dict[str, Any]) -> str:
    """Django Q task to run ATI upsert logic in the background."""
    from . import ati

    args = extract_common_args(args_dict)

    try:
        ati.get_main_upsert(
            args['move_flag'],
            args['charges_update_flag'],
            args['billings_update_flag'],
            args['cases_update_flag'],
            args['test'],
            args['all_data'],
        )
    except Exception as e:
        if str(e) == "LOCK_NOT_ACQUIRED":
            return 'ATI Upsert Failed: Lock not acquired'
        return f'ATI Upsert Failed: {str(e)}'

    return 'ATI Upsert Complete'


def salesforce_upsert_task(args_dict: dict[str, Any]) -> str:
    """Django Q task to run Salesforce upsert logic in the background."""
    from . import salesforce

    context.request_context.set({'route': 'salesforce_upsert'})

    args = extract_common_args(args_dict)
    partial_config = args['partial_config'] or get_default_partial_config()

    try:
        salesforce.post_main_upsert(
            args['test'], partial_config, args['all_data']
        )
        salesforce.post_upsert_update(args['test'], args['all_data'])
    except Exception as e:
        if str(e) == "LOCK_NOT_ACQUIRED":
            return 'Salesforce Upsert Failed: Lock not acquired'
        return f'Salesforce Upsert Failed: {str(e)}'

    return 'Salesforce Upsert Complete'


def ati_update_settled_task(args_dict: dict[str, Any]) -> str:
    """Django Q task to run ATI update settled logic in the background."""
    from . import ati

    args = extract_common_args(args_dict)

    try:
        ati.get_main_update_settled(
            args['move_flag'], args['test'], args['all_data']
        )
    except Exception as e:
        if str(e) == "LOCK_NOT_ACQUIRED":
            return 'ATI Update Settled Failed: Lock not acquired'
        return f'ATI Update Settled Failed: {str(e)}'

    return 'ATI Update Settled Complete'


def salesforce_update_settled_task(args_dict: dict[str, Any]) -> str:
    """Django Q task to run Salesforce update settled logic in the background."""
    from . import salesforce

    args = extract_common_args(args_dict)
    partial_config = (
        args['partial_config'] or get_default_settled_partial_config()
    )

    try:
        salesforce.post_main_update_settled(
            args['test'], partial_config, args['all_data']
        )
    except Exception as e:
        if str(e) == "LOCK_NOT_ACQUIRED":
            return 'Salesforce Update Settled Failed: Lock not acquired'
        return f'Salesforce Update Settled Failed: {str(e)}'

    return 'Salesforce Update Settled Complete'
