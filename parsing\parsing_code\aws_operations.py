import functools
import glob
import multiprocessing

import boto3
import botocore.config
import django.conf

from . import image_operations, local_operations, textract_to_result

app_config = django.conf.settings
credentials, settings = app_config.CREDENTIALS, app_config.SETTINGS

aws_session = boto3.Session(
    aws_access_key_id=credentials['parsing']['aws']['aws_access_key_id'],
    aws_secret_access_key=credentials['parsing']['aws'][
        'aws_secret_access_key'
    ],
    region_name=settings['Common']['AWS']['S3']['region_name'],
)
s3 = aws_session.resource('s3')
aws_client = aws_session.client(
    'textract',
    config=botocore.config.Config(
        retries={'max_attempts': 20, 'mode': 'standard'}
    ),
)


def write_s3(bucket: str, directory: str, file: str):
    s3.meta.client.upload_file(
        Filename=file,
        Bucket=f'{bucket}',
        Key=f'{directory}/{local_operations.aws_name(file)}',
    )
    image_operations.convert_to_image(file)
    outputs = glob.glob(f'*{file[:-4]}*.png')
    for output in outputs:
        s3.meta.client.upload_file(
            Filename=output,
            Bucket=f'{bucket}',
            Key=f'{directory}/{local_operations.aws_name(output)}',
        )
    return outputs


def textract_text(bucket: str, directory: str, doc: str):
    doc = local_operations.aws_name(doc)
    response = aws_client.detect_document_text(
        Document={
            'S3Object': {'Bucket': f'{bucket}', 'Name': f'{directory}/{doc}'}
        }
    )
    # if 'intermediate chunk 4 - sub-chunk 3' in doc:
    #    hcfa_parse.write_json('parsing/response.json', response)
    result = textract_to_result.extract_lines(response)
    # print(f'Doc: {doc}\nResult: {result}\n')
    return result


def textract(bucket: str, directory: str, docs: list[str]):
    res = textract_text(bucket, directory, docs[0])
    docs = docs[1:]
    func = functools.partial(textract_text, bucket, directory)
    with multiprocessing.get_context('spawn').Pool(3) as p:
        results = p.map(func, docs)
    return res, results


def clean_s3(bucket: str, directory: str, files: list[str]):
    for file in files:
        s3.Object(
            bucket, f'{directory}/{local_operations.aws_name(file)}'
        ).delete()
    return


def write_s3_result(bucket: str, directory: str, file: str):
    s3.meta.client.upload_file(
        Filename=file,
        Bucket=f'{bucket}',
        Key=f'{directory}/{local_operations.aws_name(file)}',
    )
    return
