import datetime
import math
import typing

import pandas as pd


class IdRecord:
    def is_none(
        self,
        timestamp: (
            typing.Optional[datetime.datetime] | typing.Optional[datetime.date]
        ),
    ) -> bool:
        if timestamp is None or pd.isna(timestamp):
            return True
        if isinstance(timestamp, (int, float)) and math.isnan(timestamp):
            return True
        return False

    def __init__(
        self,
        unique_id: str,
        creation_time: (
            typing.Optional[datetime.datetime] | typing.Optional[datetime.date]
        ) = None,
        modification_time: (
            typing.Optional[datetime.datetime] | typing.Optional[datetime.date]
        ) = None,
    ) -> None:
        self.id = unique_id
        # This conversion is being done because otherwise int value is passed
        # into gain_id_map, but string value is retrieved
        # This can leade to unintentional duplications in the gain id map
        # To make the ids consistent, we are converting them to strings
        if self.id is not None:
            self.id = str(self.id)
        self.creation_time = (
            None if self.is_none(creation_time) else creation_time
        )
        self.modification_time = (
            None if self.is_none(modification_time) else modification_time
        )

    def update_modification_time(
        self,
        modification_time: (
            typing.Optional[datetime.datetime] | typing.Optional[datetime.date]
        ) = None,
    ) -> None:
        self.modification_time = (
            None if self.is_none(modification_time) else modification_time
        )

    def __str__(self) -> str:
        return f"ID: {self.id}\nCreated: {self.creation_time}\nLast Modified: {self.modification_time}"
