

INSERT INTO notes (
    gainid,
    note,
    notecreatorname,
    caseid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    createdatetime
) VALUES (
    '587bbad283374a84',
    '*Gain Reduction*Firm: Shuman Legal Personal Injury Lawyers total Amount Paid In Full: $9,919.59 Account(S): ********, ********',
    '<PERSON>, <PERSON>',
    '04b1ace113fe41b2',
    'true',
    '2023-04-02 00:00:00',
    NULL,
    '2024-09-18 09:56:54',
    'false',
    '',
    'false',
    '',
    NULL
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
) VALUES (
    '587bbad283374a84',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '587bbad283374a84',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'notes'
);

INSERT INTO notes (
    gainid,
    note,
    notecreatorname,
    caseid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    createdatetime
) VALUES (
    '587bbad283374a85',
    'Note manual review',
    'Hernandez, Paula',
    '04b1ace113fe41b3',
    'true',
    '2023-04-02 00:00:00',
    NULL,
    '2024-09-18 09:56:54',
    'false',
    '',
    'false',
    '',
    NULL
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '587bbad283374a85',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '587bbad283374a85',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_notes_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'notes'
);