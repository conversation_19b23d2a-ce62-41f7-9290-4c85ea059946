from unittest.mock import MagicMock

import pytest

from integration.integration_code import aws_operations


def test_with_advisory_lock_success(mock_advisory_lock_success: MagicMock):
    # Simulate successful lock acquisition
    @aws_operations.with_advisory_lock(123)
    def my_func(x: int, y: int) -> int:
        return x + y

    result = my_func(2, 3)
    assert result == 5


def test_with_advisory_lock_conflict_raises_exception(
    mock_advisory_lock_failure: MagicMock,
):
    @aws_operations.with_advisory_lock(123, conflict_message="Custom conflict")
    def my_func(x: int) -> int:
        return x

    with pytest.raises(Exception) as excinfo:
        my_func(10)
    assert str(excinfo.value) == "LOCK_NOT_ACQUIRED"


def test_with_advisory_lock_default_conflict_raises_exception(
    mock_advisory_lock_failure: MagicMock,
):
    @aws_operations.with_advisory_lock(123)
    def my_func() -> int:
        return 42

    with pytest.raises(Exception) as excinfo:
        my_func()
    assert str(excinfo.value) == "LOCK_NOT_ACQUIRED"
