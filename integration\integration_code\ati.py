import datetime
import pathlib

import django.conf

from . import (
    ati_operations,
    aws_operations,
    context,
    local_operations,
    logger_config,
)

logger = logger_config.get_logger(__name__)

app_config = django.conf.settings
settings = app_config.SETTINGS


def get_data(
    timestamp: str,
    bucket: str,
    directory: str,
    sub_directory: str,
    move_flag: bool,
    call_type: str,
    all_data: bool,
    charges_update_flag: bool = False,
    billings_update_flag: bool = False,
    cases_update_flag: bool = False,
) -> tuple[set[str], set[str], set[str]]:
    transactions_gainid_set = set()
    charges_gainid_set = set()
    billings_gainid_set = set()
    local_files = None
    data_to_process, s3_files_to_move = ati_operations.get_data_ati(
        timestamp, bucket, directory, call_type, all_data
    )
    if call_type == 'upsert':
        (
            local_files,
            transactions_gainid_set,
            charges_gainid_set,
            billings_gainid_set,
        ) = ati_operations.process_upsert_data_ati(
            timestamp,
            bucket,
            directory,
            sub_directory,
            data_to_process,
            charges_update_flag,
            billings_update_flag,
            cases_update_flag,
        )
    elif call_type == 'delete':
        local_files = ati_operations.process_delete_data_ati(
            timestamp, data_to_process
        )
    elif call_type == 'update_settled':
        local_files = ati_operations.process_update_settled_data_ati(
            timestamp, data_to_process
        )
    if (
        move_flag
    ):  # whether to move files from Unprocessed to Processed => True by default, pass False for QA/testing ease-of-use
        ati_operations.post_process_data_ati(bucket, s3_files_to_move)
    if local_files is not None:
        for file in local_files.values():
            pathlib.Path(file).unlink(missing_ok=True)
    return (
        transactions_gainid_set,
        charges_gainid_set,
        billings_gainid_set,
    )


def get_main_upsert(
    move_flag: bool,
    charges_update_flag: bool,
    billings_update_flag: bool,
    cases_update_flag: bool,
    test: bool,
    all_data: bool,
) -> None:
    '''
    Get upsert from ATI, post to Canonical in Redshift
    '''
    context.request_context.set({'route': 'ATI_To_Redshift_Upsert'})
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if (
        last_run_time == -1
    ):  # only problem is if all_data=False i.e. delta changes but last run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for ATI_To_Redshift missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    bucket, directory, sub_directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["ATI"]["S3"]["sub_directory"]}/upsert',
        f'results/{datetime.date.today()}',
    )
    (
        transactions_gainid_set,
        charges_gainid_set,
        billings_gainid_set,
    ) = get_data(
        timestamp,
        bucket,
        directory,
        sub_directory,
        move_flag,
        'upsert',
        all_data,
        charges_update_flag,
        billings_update_flag,
        cases_update_flag,
    )
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    updated_charge_keys = []
    # update charges with non-gain adjustments and non-gain payments
    if charges_update_flag:
        transactions_id_subset = [gainid for gainid in transactions_gainid_set]
        updated_charge_keys = (
            local_operations.update_charges_from_transactions(
                transactions_id_subset
            )
        )
    # update billings using charges aggregating non-gain adjustments and non-gain payments
    if billings_update_flag:
        charges_id_subset = set([gainid for gainid in charges_gainid_set])
        if updated_charge_keys:
            charges_id_subset.update(updated_charge_keys)
        charges_id_subset = list(charges_id_subset)
        updated_billing_keys = local_operations.update_billings_from_charges(
            charges_id_subset
        )
    # update cases using billings aggregating non-gain adjustments and non-gain payments
    if cases_update_flag:
        billings_id_subset = set([gainid for gainid in billings_gainid_set])
        if updated_billing_keys:
            billings_id_subset.update(updated_billing_keys)
        billings_id_subset = list(billings_id_subset)
        local_operations.update_cases_from_billings(billings_id_subset)


def get_main_delete(move_flag: bool, test: bool, all_data: bool) -> None:
    '''
    Get delete from ATI, post to Canonical in Redshift
    '''
    context.request_context.set({'route': 'ATI_To_Redshift_Delete'})
    timestamp = datetime.datetime.now()
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if (
        last_run_time == -1
    ):  # only problem is if all_data=False i.e. delta changes but last run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for ATI_To_Redshift missing from Redshift, cannot perform delta change ingestion. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data ingestion instead of delta ingestion'
        )
        return  # terminate process early
    bucket, directory, sub_directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["ATI"]["S3"]["sub_directory"]}/delete',
        f'results/{datetime.date.today()}',
    )
    get_data(
        timestamp,
        bucket,
        directory,
        sub_directory,
        move_flag,
        'delete',
        all_data,
    )
    aws_operations.upsert_integration_timestamp(timestamp, all_data)


def get_main_update_settled(
    move_flag: bool, test: bool, all_data: bool
) -> None:
    '''
    Get settle from ATI, post to Canonical in Redshift
    '''
    context.request_context.set({'route': 'ATI_To_Redshift_Update_Settled'})
    timestamp = datetime.datetime.now()
    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
    last_run_time = (
        aws_operations.get_last_run_time() if not all_data else None
    )  # timestamp for last run, -1 for last run not found, None for all_data
    if (
        last_run_time == -1
    ):  # only problem is if all_data=False i.e. delta changes but last run time is not found i.e. value of -1
        logger.error(
            'Last run timestamp for ATI_To_Redshift missing from Redshift, cannot perform delta change claim settlement. Either update last run timestamp in Redshift or pass "all_data" parameter as true to perform all data claim settlement instead of delta claim settlement'
        )
        return  # terminate process early
    bucket, directory, sub_directory = (
        settings['Common']['AWS']['S3']['bucket'],
        f'{settings["Integration"]["AWS"]["S3"]["directory"]}/{settings["Integration"]["ATI"]["S3"]["sub_directory"]}/update_settled',
        f'results/{datetime.date.today()}',
    )
    get_data(
        timestamp,
        bucket,
        directory,
        sub_directory,
        move_flag,
        'update_settled',
        all_data,
    )
    aws_operations.upsert_integration_timestamp(timestamp, all_data)
    return


def post_main_upsert(all_data: bool) -> None:
    '''
    Get Canonical upsert from Redshift, post to ATI
    '''
    pass


def post_main_delete(all_data: bool) -> None:
    '''
    Get Canonical delete from Redshift, post to ATI
    '''
    pass


def post_main_update_settled(all_data: bool) -> None:
    '''
    Get Canonical settle from Redshift, post to ATI
    '''
    pass
