from unittest.mock import MagicMock


class TestAtiUpdateSettledTask:
    def test_ati_update_settled_task_with_default_args(
        self,
        mock_get_main_update_settled: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI update settled task with default arguments."""
        from integration.integration_code.tasks import ati_update_settled_task

        args_dict = {}
        result = ati_update_settled_task(args_dict)
        mock_get_main_update_settled.assert_called_once_with(
            True, False, False
        )
        assert result == 'ATI Update Settled Complete'

    def test_ati_update_settled_task_with_custom_args(
        self,
        mock_get_main_update_settled: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI update settled task with custom arguments."""
        from integration.integration_code.tasks import ati_update_settled_task

        args_dict = {
            'move_flag': False,
            'test': True,
            'all_data': True,
        }
        result = ati_update_settled_task(args_dict)
        mock_get_main_update_settled.assert_called_once_with(<PERSON>alse, True, True)
        assert result == 'ATI Update Settled Complete'

    def test_ati_update_settled_task_with_partial_args(
        self,
        mock_get_main_update_settled: MagicMock,
        mock_advisory_lock_success: MagicMock,
    ):
        """Test ATI update settled task with partial arguments."""
        from integration.integration_code.tasks import ati_update_settled_task

        args_dict = {'test': True, 'all_data': True}
        result = ati_update_settled_task(args_dict)
        mock_get_main_update_settled.assert_called_once_with(True, True, True)
        assert result == 'ATI Update Settled Complete'

    def test_ati_update_settled_task_lock_not_acquired(
        self,
        mock_advisory_lock_failure: MagicMock,
    ):
        """
        Test that ati_update_settled_task returns the correct message when the advisory lock is not acquired.
        """
        from integration.integration_code.tasks import ati_update_settled_task

        args_dict = {}

        result = ati_update_settled_task(args_dict)
        assert result == "ATI Update Settled Failed: Lock not acquired"
