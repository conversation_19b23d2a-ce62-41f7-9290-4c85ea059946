--Dev TABLE
-- Create the law firm table
CREATE TABLE integration.dev.lawfirms (
    SourceId	character varying(100) ENCODE lzo,
    SourceName	character varying(100) ENCODE lzo,
    Name	character varying(250) ENCODE lzo,
    Phone	character varying(100) ENCODE lzo,
    Fax	character varying(100) ENCODE lzo,
    Email	character varying(100) ENCODE lzo,
    FollowUpEmail	character varying(100) ENCODE lzo,
    BillingAddressLine1	character varying(250) ENCODE lzo,
    BillingAddressLine2	character varying(250) ENCODE lzo,
    BillingAddressCity	character varying(100) ENCODE lzo,
    BillingAddressState	character varying(100) ENCODE lzo,
    BillingAddressZip	character varying(20) ENCODE lzo,
    PhysicalAddressLine1	character varying(250) ENCODE lzo,
    PhysicalAddressLine2	character varying(250) ENCODE lzo,
    PhysicalAddressCity	character varying(100) ENCODE lzo,
    PhysicalAddressState	character varying(100) ENCODE lzo,
    PhysicalAddressZip	character varying(20) ENCODE lzo,
    Website	character varying(1000) ENCODE lzo,
    TypeOfLaw	character varying(1000) ENCODE bytedict,
    Description	character varying(1000) ENCODE lzo,
    EmployeeCountRange	character varying(1000) ENCODE lzo,
    DoNotFund	boolean ENCODE raw,
    DoNotFundType	character varying(100) ENCODE bytedict,
    AutomaticCaseUpdateRequest	boolean ENCODE raw,
    NonResponsive	boolean ENCODE raw,
    NonResponsiveNote	character varying(1000) ENCODE lzo,
    PortalAccount	boolean ENCODE raw,
    PortalRewardsParticipant	boolean ENCODE raw,
    ParentId	character varying(100) ENCODE lzo,
    RelevantToGain	boolean ENCODE raw,
    SourceCreateDateTime	timestamp without time zone ENCODE az64,
    SourceModifiedDateTime	timestamp without time zone ENCODE az64,
    CreateDateTime	timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    ModifiedDateTime	timestamp without time zone ENCODE az64,
    ToDelete	boolean DEFAULT false ENCODE raw,
    ToDeleteSystem	character varying(100) ENCODE lzo,
    DeletePreventOverride	boolean DEFAULT false ENCODE raw,
    DeletePreventOverrideReason	character varying(1000) ENCODE lzo,
    UNIQUE (SourceId)
) DISTSTYLE AUTO;


-- Add LegalPersonnel TABLE
CREATE OR REPLACE PROCEDURE delete_foreign_keys_referencing_legalpersonnel()
AS $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec IN SELECT tc.constraint_name, tc.table_name  
  FROM information_schema.table_constraints tc 
  JOIN information_schema.constraint_column_usage cc 
  on tc.constraint_name = cc.constraint_name 
  where tc.table_schema = 'dev' 
  and tc.constraint_catalog = 'integration'
  and tc.table_catalog = 'integration'
  and tc.constraint_type = 'FOREIGN KEY' 
  and cc.table_schema = 'dev' 
  and cc.table_name = 'legalpersonnel'
  LOOP
    raise NOTICE 'Deleting Constraint: % from %',rec.constraint_name, rec.table_name;
    EXECUTE 'ALTER TABLE integration.dev.' || rec.table_name ||' DROP CONSTRAINT ' || rec.constraint_name;
  END LOOP;
END;
$$
LANGUAGE plpgsql SET stored_proc_log_min_messages = NOTICE;
CALL delete_foreign_keys_referencing_legalpersonnel();

DROP TABLE IF EXISTS integration.dev.legalpersonnel
;
CREATE TABLE integration.dev.legalpersonnel (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    name character varying(250) ENCODE lzo,
    title character varying(100) ENCODE lzo,
    lawfirmid character varying(100) ENCODE lzo,
    lawfirmname character varying(100) ENCODE lzo,
    homephone character varying(100) ENCODE lzo,
    cellphone character varying(100) ENCODE lzo,
    businessphone character varying(100) ENCODE lzo,
    businessphoneext character varying(20) ENCODE lzo,
    otherphone character varying(100) ENCODE lzo,
    fax character varying(100) ENCODE lzo,
    primaryemail character varying(100) ENCODE lzo,
    secondaryemail character varying(100) ENCODE lzo,
    primaryaddressline1 character varying(250) ENCODE lzo,
    primaryaddressline2 character varying(250) ENCODE lzo,
    primaryaddresscity character varying(100) ENCODE lzo,
    primaryaddressstate character varying(100) ENCODE lzo,
    primaryaddresszip character varying(20) ENCODE lzo,
    otheraddressline1 character varying(250) ENCODE lzo,
    otheraddressline2 character varying(250) ENCODE lzo,
    otheraddresscity character varying(100) ENCODE lzo,
    otheraddressstate character varying(100) ENCODE lzo,
    otheraddresszip character varying(20) ENCODE lzo,
    notes character varying(1000) ENCODE lzo,
    relevanttogain boolean ENCODE raw,
    sourcecreatedatetime timestamp without time zone ENCODE az64,
    sourcemodifieddatetime timestamp without time zone ENCODE az64,
    createdatetime timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    modifieddatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    UNIQUE (sourceid),
    FOREIGN KEY (lawfirmid) REFERENCES integration.dev.lawfirms(sourceid)
) DISTSTYLE AUTO;

-- Create Law Firm Insert Data map
CREATE TABLE integration.dev.lawfirms_insertdata_map (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    salesforceid character varying(20) ENCODE lzo,
    createdatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    salesforceexternalidcontainer character varying(20) ENCODE lzo,
    UNIQUE (sourceid),
    UNIQUE (salesforceid),
    FOREIGN KEY (sourceid) REFERENCES integration.dev.lawfirms(sourceid)
) DISTSTYLE AUTO
;


-- Add legalpersonnel information to the cases table
ALTER TABLE integration.dev.cases
ADD COLUMN attorneyid character varying(20) ENCODE lzo
;

ALTER TABLE integration.dev.cases
ADD COLUMN paralegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.dev.cases
ADD COLUMN casemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.dev.cases
ADD COLUMN cocounselid character varying(20) ENCODE lzo
;

ALTER TABLE integration.dev.cases
ADD COLUMN coparalegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.dev.cases
ADD COLUMN cocasemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.dev.cases
ADD FOREIGN KEY(attorneyid) REFERENCES integration.dev.legalpersonnel(sourceid)
;

ALTER TABLE integration.dev.cases
ADD FOREIGN KEY(paralegalid) REFERENCES integration.dev.legalpersonnel(sourceid)
;

ALTER TABLE integration.dev.cases
ADD FOREIGN KEY(casemanagerid) REFERENCES integration.dev.legalpersonnel(sourceid)
;

ALTER TABLE integration.dev.cases
ADD FOREIGN KEY(cocounselid) REFERENCES integration.dev.legalpersonnel(sourceid)
;

ALTER TABLE integration.dev.cases
ADD FOREIGN KEY(coparalegalid) REFERENCES integration.dev.legalpersonnel(sourceid)
;

ALTER TABLE integration.dev.cases
ADD FOREIGN KEY(cocasemanagerid) REFERENCES integration.dev.legalpersonnel(sourceid)
;

--staging TABLE
-- Create the law firm table
CREATE TABLE integration.staging.lawfirms (
    SourceId	character varying(100) ENCODE lzo,
    SourceName	character varying(100) ENCODE lzo,
    Name	character varying(250) ENCODE lzo,
    Phone	character varying(100) ENCODE lzo,
    Fax	character varying(100) ENCODE lzo,
    Email	character varying(100) ENCODE lzo,
    FollowUpEmail	character varying(100) ENCODE lzo,
    BillingAddressLine1	character varying(250) ENCODE lzo,
    BillingAddressLine2	character varying(250) ENCODE lzo,
    BillingAddressCity	character varying(100) ENCODE lzo,
    BillingAddressState	character varying(100) ENCODE lzo,
    BillingAddressZip	character varying(20) ENCODE lzo,
    PhysicalAddressLine1	character varying(250) ENCODE lzo,
    PhysicalAddressLine2	character varying(250) ENCODE lzo,
    PhysicalAddressCity	character varying(100) ENCODE lzo,
    PhysicalAddressState	character varying(100) ENCODE lzo,
    PhysicalAddressZip	character varying(20) ENCODE lzo,
    Website	character varying(1000) ENCODE lzo,
    TypeOfLaw	character varying(1000) ENCODE bytedict,
    Description	character varying(1000) ENCODE lzo,
    EmployeeCountRange	character varying(1000) ENCODE lzo,
    DoNotFund	boolean ENCODE raw,
    DoNotFundType	character varying(100) ENCODE bytedict,
    AutomaticCaseUpdateRequest	boolean ENCODE raw,
    NonResponsive	boolean ENCODE raw,
    NonResponsiveNote	character varying(1000) ENCODE lzo,
    PortalAccount	boolean ENCODE raw,
    PortalRewardsParticipant	boolean ENCODE raw,
    ParentId	character varying(100) ENCODE lzo,
    RelevantToGain	boolean ENCODE raw,
    SourceCreateDateTime	timestamp without time zone ENCODE az64,
    SourceModifiedDateTime	timestamp without time zone ENCODE az64,
    CreateDateTime	timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    ModifiedDateTime	timestamp without time zone ENCODE az64,
    ToDelete	boolean DEFAULT false ENCODE raw,
    ToDeleteSystem	character varying(100) ENCODE lzo,
    DeletePreventOverride	boolean DEFAULT false ENCODE raw,
    DeletePreventOverrideReason	character varying(1000) ENCODE lzo,
    UNIQUE (SourceId)
) DISTSTYLE AUTO;


-- Add LegalPersonnel TABLE
CREATE OR REPLACE PROCEDURE delete_foreign_keys_referencing_legalpersonnel()
AS $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec IN SELECT tc.constraint_name, tc.table_name  
  FROM information_schema.table_constraints tc 
  JOIN information_schema.constraint_column_usage cc 
  on tc.constraint_name = cc.constraint_name 
  where tc.table_schema = 'staging' 
  and tc.constraint_catalog = 'integration'
  and tc.table_catalog = 'integration'
  and tc.constraint_type = 'FOREIGN KEY' 
  and cc.table_schema = 'staging' 
  and cc.table_name = 'legalpersonnel'
  LOOP
    raise NOTICE 'Deleting Constraint: % from %',rec.constraint_name, rec.table_name;
    EXECUTE 'ALTER TABLE integration.staging.' || rec.table_name ||' DROP CONSTRAINT ' || rec.constraint_name;
  END LOOP;
END;
$$
LANGUAGE plpgsql SET stored_proc_log_min_messages = NOTICE;
CALL delete_foreign_keys_referencing_legalpersonnel();

DROP TABLE IF EXISTS integration.staging.legalpersonnel
;
CREATE TABLE integration.staging.legalpersonnel (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    name character varying(250) ENCODE lzo,
    title character varying(100) ENCODE lzo,
    lawfirmid character varying(100) ENCODE lzo,
    lawfirmname character varying(100) ENCODE lzo,
    homephone character varying(100) ENCODE lzo,
    cellphone character varying(100) ENCODE lzo,
    businessphone character varying(100) ENCODE lzo,
    businessphoneext character varying(20) ENCODE lzo,
    otherphone character varying(100) ENCODE lzo,
    fax character varying(100) ENCODE lzo,
    primaryemail character varying(100) ENCODE lzo,
    secondaryemail character varying(100) ENCODE lzo,
    primaryaddressline1 character varying(250) ENCODE lzo,
    primaryaddressline2 character varying(250) ENCODE lzo,
    primaryaddresscity character varying(100) ENCODE lzo,
    primaryaddressstate character varying(100) ENCODE lzo,
    primaryaddresszip character varying(20) ENCODE lzo,
    otheraddressline1 character varying(250) ENCODE lzo,
    otheraddressline2 character varying(250) ENCODE lzo,
    otheraddresscity character varying(100) ENCODE lzo,
    otheraddressstate character varying(100) ENCODE lzo,
    otheraddresszip character varying(20) ENCODE lzo,
    notes character varying(1000) ENCODE lzo,
    relevanttogain boolean ENCODE raw,
    sourcecreatedatetime timestamp without time zone ENCODE az64,
    sourcemodifieddatetime timestamp without time zone ENCODE az64,
    createdatetime timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    modifieddatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    UNIQUE (sourceid),
    FOREIGN KEY (lawfirmid) REFERENCES integration.staging.lawfirms(sourceid)
) DISTSTYLE AUTO;

-- Create Law Firm Insert Data map
CREATE TABLE integration.staging.lawfirms_insertdata_map (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    salesforceid character varying(20) ENCODE lzo,
    createdatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    salesforceexternalidcontainer character varying(20) ENCODE lzo,
    UNIQUE (sourceid),
    UNIQUE (salesforceid),
    FOREIGN KEY (sourceid) REFERENCES integration.staging.lawfirms(sourceid)
) DISTSTYLE AUTO
;


-- Add legalpersonnel information to the cases table
ALTER TABLE integration.staging.cases
ADD COLUMN attorneyid character varying(20) ENCODE lzo
;

ALTER TABLE integration.staging.cases
ADD COLUMN paralegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.staging.cases
ADD COLUMN casemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.staging.cases
ADD COLUMN cocounselid character varying(20) ENCODE lzo
;

ALTER TABLE integration.staging.cases
ADD COLUMN coparalegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.staging.cases
ADD COLUMN cocasemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.staging.cases
ADD FOREIGN KEY(attorneyid) REFERENCES integration.staging.legalpersonnel(sourceid)
;

ALTER TABLE integration.staging.cases
ADD FOREIGN KEY(paralegalid) REFERENCES integration.staging.legalpersonnel(sourceid)
;

ALTER TABLE integration.staging.cases
ADD FOREIGN KEY(casemanagerid) REFERENCES integration.staging.legalpersonnel(sourceid)
;

ALTER TABLE integration.staging.cases
ADD FOREIGN KEY(cocounselid) REFERENCES integration.staging.legalpersonnel(sourceid)
;

ALTER TABLE integration.staging.cases
ADD FOREIGN KEY(coparalegalid) REFERENCES integration.staging.legalpersonnel(sourceid)
;

ALTER TABLE integration.staging.cases
ADD FOREIGN KEY(cocasemanagerid) REFERENCES integration.staging.legalpersonnel(sourceid)
;

--prod TABLE
-- Create the law firm table
CREATE TABLE integration.prod.lawfirms (
    SourceId	character varying(100) ENCODE lzo,
    SourceName	character varying(100) ENCODE lzo,
    Name	character varying(250) ENCODE lzo,
    Phone	character varying(100) ENCODE lzo,
    Fax	character varying(100) ENCODE lzo,
    Email	character varying(100) ENCODE lzo,
    FollowUpEmail	character varying(100) ENCODE lzo,
    BillingAddressLine1	character varying(250) ENCODE lzo,
    BillingAddressLine2	character varying(250) ENCODE lzo,
    BillingAddressCity	character varying(100) ENCODE lzo,
    BillingAddressState	character varying(100) ENCODE lzo,
    BillingAddressZip	character varying(20) ENCODE lzo,
    PhysicalAddressLine1	character varying(250) ENCODE lzo,
    PhysicalAddressLine2	character varying(250) ENCODE lzo,
    PhysicalAddressCity	character varying(100) ENCODE lzo,
    PhysicalAddressState	character varying(100) ENCODE lzo,
    PhysicalAddressZip	character varying(20) ENCODE lzo,
    Website	character varying(1000) ENCODE lzo,
    TypeOfLaw	character varying(1000) ENCODE bytedict,
    Description	character varying(1000) ENCODE lzo,
    EmployeeCountRange	character varying(1000) ENCODE lzo,
    DoNotFund	boolean ENCODE raw,
    DoNotFundType	character varying(100) ENCODE bytedict,
    AutomaticCaseUpdateRequest	boolean ENCODE raw,
    NonResponsive	boolean ENCODE raw,
    NonResponsiveNote	character varying(1000) ENCODE lzo,
    PortalAccount	boolean ENCODE raw,
    PortalRewardsParticipant	boolean ENCODE raw,
    ParentId	character varying(100) ENCODE lzo,
    RelevantToGain	boolean ENCODE raw,
    SourceCreateDateTime	timestamp without time zone ENCODE az64,
    SourceModifiedDateTime	timestamp without time zone ENCODE az64,
    CreateDateTime	timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    ModifiedDateTime	timestamp without time zone ENCODE az64,
    ToDelete	boolean DEFAULT false ENCODE raw,
    ToDeleteSystem	character varying(100) ENCODE lzo,
    DeletePreventOverride	boolean DEFAULT false ENCODE raw,
    DeletePreventOverrideReason	character varying(1000) ENCODE lzo,
    UNIQUE (SourceId)
) DISTSTYLE AUTO;


-- Add LegalPersonnel TABLE
CREATE OR REPLACE PROCEDURE delete_foreign_keys_referencing_legalpersonnel()
AS $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec IN SELECT tc.constraint_name, tc.table_name  
  FROM information_schema.table_constraints tc 
  JOIN information_schema.constraint_column_usage cc 
  on tc.constraint_name = cc.constraint_name 
  where tc.table_schema = 'prod' 
  and tc.constraint_catalog = 'integration'
  and tc.table_catalog = 'integration'
  and tc.constraint_type = 'FOREIGN KEY' 
  and cc.table_schema = 'prod' 
  and cc.table_name = 'legalpersonnel'
  LOOP
    raise NOTICE 'Deleting Constraint: % from %',rec.constraint_name, rec.table_name;
    EXECUTE 'ALTER TABLE integration.prod.' || rec.table_name ||' DROP CONSTRAINT ' || rec.constraint_name;
  END LOOP;
END;
$$
LANGUAGE plpgsql SET stored_proc_log_min_messages = NOTICE;
CALL delete_foreign_keys_referencing_legalpersonnel();

DROP TABLE IF EXISTS integration.prod.legalpersonnel
;
CREATE TABLE integration.prod.legalpersonnel (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    name character varying(250) ENCODE lzo,
    title character varying(100) ENCODE lzo,
    lawfirmid character varying(100) ENCODE lzo,
    lawfirmname character varying(100) ENCODE lzo,
    homephone character varying(100) ENCODE lzo,
    cellphone character varying(100) ENCODE lzo,
    businessphone character varying(100) ENCODE lzo,
    businessphoneext character varying(20) ENCODE lzo,
    otherphone character varying(100) ENCODE lzo,
    fax character varying(100) ENCODE lzo,
    primaryemail character varying(100) ENCODE lzo,
    secondaryemail character varying(100) ENCODE lzo,
    primaryaddressline1 character varying(250) ENCODE lzo,
    primaryaddressline2 character varying(250) ENCODE lzo,
    primaryaddresscity character varying(100) ENCODE lzo,
    primaryaddressstate character varying(100) ENCODE lzo,
    primaryaddresszip character varying(20) ENCODE lzo,
    otheraddressline1 character varying(250) ENCODE lzo,
    otheraddressline2 character varying(250) ENCODE lzo,
    otheraddresscity character varying(100) ENCODE lzo,
    otheraddressstate character varying(100) ENCODE lzo,
    otheraddresszip character varying(20) ENCODE lzo,
    notes character varying(1000) ENCODE lzo,
    relevanttogain boolean ENCODE raw,
    sourcecreatedatetime timestamp without time zone ENCODE az64,
    sourcemodifieddatetime timestamp without time zone ENCODE az64,
    createdatetime timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    modifieddatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    UNIQUE (sourceid),
    FOREIGN KEY (lawfirmid) REFERENCES integration.prod.lawfirms(sourceid)
) DISTSTYLE AUTO;

-- Create Law Firm Insert Data map
CREATE TABLE integration.prod.lawfirms_insertdata_map (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    salesforceid character varying(20) ENCODE lzo,
    createdatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    salesforceexternalidcontainer character varying(20) ENCODE lzo,
    UNIQUE (sourceid),
    UNIQUE (salesforceid),
    FOREIGN KEY (sourceid) REFERENCES integration.prod.lawfirms(sourceid)
) DISTSTYLE AUTO
;


-- Add legalpersonnel information to the cases table
ALTER TABLE integration.prod.cases
ADD COLUMN attorneyid character varying(20) ENCODE lzo
;

ALTER TABLE integration.prod.cases
ADD COLUMN paralegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.prod.cases
ADD COLUMN casemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.prod.cases
ADD COLUMN cocounselid character varying(20) ENCODE lzo
;

ALTER TABLE integration.prod.cases
ADD COLUMN coparalegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.prod.cases
ADD COLUMN cocasemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.prod.cases
ADD FOREIGN KEY(attorneyid) REFERENCES integration.prod.legalpersonnel(sourceid)
;

ALTER TABLE integration.prod.cases
ADD FOREIGN KEY(paralegalid) REFERENCES integration.prod.legalpersonnel(sourceid)
;

ALTER TABLE integration.prod.cases
ADD FOREIGN KEY(casemanagerid) REFERENCES integration.prod.legalpersonnel(sourceid)
;

ALTER TABLE integration.prod.cases
ADD FOREIGN KEY(cocounselid) REFERENCES integration.prod.legalpersonnel(sourceid)
;

ALTER TABLE integration.prod.cases
ADD FOREIGN KEY(coparalegalid) REFERENCES integration.prod.legalpersonnel(sourceid)
;

ALTER TABLE integration.prod.cases
ADD FOREIGN KEY(cocasemanagerid) REFERENCES integration.prod.legalpersonnel(sourceid)
;

--main TABLE
-- Create the law firm table
CREATE TABLE integration.main.lawfirms (
    SourceId	character varying(100) ENCODE lzo,
    SourceName	character varying(100) ENCODE lzo,
    Name	character varying(250) ENCODE lzo,
    Phone	character varying(100) ENCODE lzo,
    Fax	character varying(100) ENCODE lzo,
    Email	character varying(100) ENCODE lzo,
    FollowUpEmail	character varying(100) ENCODE lzo,
    BillingAddressLine1	character varying(250) ENCODE lzo,
    BillingAddressLine2	character varying(250) ENCODE lzo,
    BillingAddressCity	character varying(100) ENCODE lzo,
    BillingAddressState	character varying(100) ENCODE lzo,
    BillingAddressZip	character varying(20) ENCODE lzo,
    PhysicalAddressLine1	character varying(250) ENCODE lzo,
    PhysicalAddressLine2	character varying(250) ENCODE lzo,
    PhysicalAddressCity	character varying(100) ENCODE lzo,
    PhysicalAddressState	character varying(100) ENCODE lzo,
    PhysicalAddressZip	character varying(20) ENCODE lzo,
    Website	character varying(1000) ENCODE lzo,
    TypeOfLaw	character varying(1000) ENCODE bytedict,
    Description	character varying(1000) ENCODE lzo,
    EmployeeCountRange	character varying(1000) ENCODE lzo,
    DoNotFund	boolean ENCODE raw,
    DoNotFundType	character varying(100) ENCODE bytedict,
    AutomaticCaseUpdateRequest	boolean ENCODE raw,
    NonResponsive	boolean ENCODE raw,
    NonResponsiveNote	character varying(1000) ENCODE lzo,
    PortalAccount	boolean ENCODE raw,
    PortalRewardsParticipant	boolean ENCODE raw,
    ParentId	character varying(100) ENCODE lzo,
    RelevantToGain	boolean ENCODE raw,
    SourceCreateDateTime	timestamp without time zone ENCODE az64,
    SourceModifiedDateTime	timestamp without time zone ENCODE az64,
    CreateDateTime	timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    ModifiedDateTime	timestamp without time zone ENCODE az64,
    ToDelete	boolean DEFAULT false ENCODE raw,
    ToDeleteSystem	character varying(100) ENCODE lzo,
    DeletePreventOverride	boolean DEFAULT false ENCODE raw,
    DeletePreventOverrideReason	character varying(1000) ENCODE lzo,
    UNIQUE (SourceId)
) DISTSTYLE AUTO;


-- Add LegalPersonnel TABLE
CREATE OR REPLACE PROCEDURE delete_foreign_keys_referencing_legalpersonnel()
AS $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec IN SELECT tc.constraint_name, tc.table_name  
  FROM information_schema.table_constraints tc 
  JOIN information_schema.constraint_column_usage cc 
  on tc.constraint_name = cc.constraint_name 
  where tc.table_schema = 'main' 
  and tc.constraint_catalog = 'integration'
  and tc.table_catalog = 'integration'
  and tc.constraint_type = 'FOREIGN KEY' 
  and cc.table_schema = 'main' 
  and cc.table_name = 'legalpersonnel'
  LOOP
    raise NOTICE 'Deleting Constraint: % from %',rec.constraint_name, rec.table_name;
    EXECUTE 'ALTER TABLE integration.main.' || rec.table_name ||' DROP CONSTRAINT ' || rec.constraint_name;
  END LOOP;
END;
$$
LANGUAGE plpgsql SET stored_proc_log_min_messages = NOTICE;
CALL delete_foreign_keys_referencing_legalpersonnel();

DROP TABLE IF EXISTS integration.main.legalpersonnel
;
CREATE TABLE integration.main.legalpersonnel (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    name character varying(250) ENCODE lzo,
    title character varying(100) ENCODE lzo,
    lawfirmid character varying(100) ENCODE lzo,
    lawfirmname character varying(100) ENCODE lzo,
    homephone character varying(100) ENCODE lzo,
    cellphone character varying(100) ENCODE lzo,
    businessphone character varying(100) ENCODE lzo,
    businessphoneext character varying(20) ENCODE lzo,
    otherphone character varying(100) ENCODE lzo,
    fax character varying(100) ENCODE lzo,
    primaryemail character varying(100) ENCODE lzo,
    secondaryemail character varying(100) ENCODE lzo,
    primaryaddressline1 character varying(250) ENCODE lzo,
    primaryaddressline2 character varying(250) ENCODE lzo,
    primaryaddresscity character varying(100) ENCODE lzo,
    primaryaddressstate character varying(100) ENCODE lzo,
    primaryaddresszip character varying(20) ENCODE lzo,
    otheraddressline1 character varying(250) ENCODE lzo,
    otheraddressline2 character varying(250) ENCODE lzo,
    otheraddresscity character varying(100) ENCODE lzo,
    otheraddressstate character varying(100) ENCODE lzo,
    otheraddresszip character varying(20) ENCODE lzo,
    notes character varying(1000) ENCODE lzo,
    relevanttogain boolean ENCODE raw,
    sourcecreatedatetime timestamp without time zone ENCODE az64,
    sourcemodifieddatetime timestamp without time zone ENCODE az64,
    createdatetime timestamp without time zone DEFAULT ('now':: text):: timestamp without time zone ENCODE az64,
    modifieddatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    UNIQUE (sourceid),
    FOREIGN KEY (lawfirmid) REFERENCES integration.main.lawfirms(sourceid)
) DISTSTYLE AUTO;

-- Create Law Firm Insert Data map
CREATE TABLE integration.main.lawfirms_insertdata_map (
    sourceid character varying(20) ENCODE lzo,
    sourcename character varying(100) ENCODE lzo,
    salesforceid character varying(20) ENCODE lzo,
    createdatetime timestamp without time zone ENCODE az64,
    todelete boolean DEFAULT false ENCODE raw,
    todeletesystem character varying(100) ENCODE lzo,
    deletepreventoverride boolean DEFAULT false ENCODE raw,
    deletepreventoverridereason character varying(1000) ENCODE lzo,
    salesforceexternalidcontainer character varying(20) ENCODE lzo,
    UNIQUE (sourceid),
    UNIQUE (salesforceid),
    FOREIGN KEY (sourceid) REFERENCES integration.main.lawfirms(sourceid)
) DISTSTYLE AUTO
;


-- Add legalpersonnel information to the cases table
ALTER TABLE integration.main.cases
ADD COLUMN attorneyid character varying(20) ENCODE lzo
;

ALTER TABLE integration.main.cases
ADD COLUMN paralegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.main.cases
ADD COLUMN casemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.main.cases
ADD COLUMN cocounselid character varying(20) ENCODE lzo
;

ALTER TABLE integration.main.cases
ADD COLUMN coparalegalid character varying(20) ENCODE lzo
;

ALTER TABLE integration.main.cases
ADD COLUMN cocasemanagerid character varying(20) ENCODE lzo
;

ALTER TABLE integration.main.cases
ADD FOREIGN KEY(attorneyid) REFERENCES integration.main.legalpersonnel(sourceid)
;

ALTER TABLE integration.main.cases
ADD FOREIGN KEY(paralegalid) REFERENCES integration.main.legalpersonnel(sourceid)
;

ALTER TABLE integration.main.cases
ADD FOREIGN KEY(casemanagerid) REFERENCES integration.main.legalpersonnel(sourceid)
;

ALTER TABLE integration.main.cases
ADD FOREIGN KEY(cocounselid) REFERENCES integration.main.legalpersonnel(sourceid)
;

ALTER TABLE integration.main.cases
ADD FOREIGN KEY(coparalegalid) REFERENCES integration.main.legalpersonnel(sourceid)
;

ALTER TABLE integration.main.cases
ADD FOREIGN KEY(cocasemanagerid) REFERENCES integration.main.legalpersonnel(sourceid)
;