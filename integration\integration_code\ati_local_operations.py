import collections
import datetime
import hashlib
import json
import typing

import numpy as np
import pandas as pd

from . import (
    aws_operations,
    id_record,
    local_operations,
    logger_config,
    shared,
)

logger = logger_config.get_logger(__name__)

with (
    open(
        'integration/integration_code/canonical_model.json', 'rb'
    ) as canonical_map_file,
    open(
        'integration/integration_code/canonical_data_model.json', 'rb'
    ) as canonical_data_map_file,
    open(
        'integration/integration_code/state_name_mapping.json',
        'rb',
    ) as state_name_mapping_file,
    open(
        'integration/integration_code/claim_settlement_configuration.json',
        'rb',
    ) as claim_settlement_configuration_file,
):
    canonical = json.load(canonical_map_file)
    canonical_objects = set(canonical.keys())
    canonical_data = json.load(canonical_data_map_file)
    state_name_mapping = json.load(state_name_mapping_file)
    state_code_to_name_map = state_name_mapping["state_code_to_name_map"]
    state_name_to_code_map = state_name_mapping["state_name_to_code_map"]
    claim_settlement_configuration = json.load(
        claim_settlement_configuration_file
    )


partner_account_id = '0b89f4beff68d371'


def ati_generate_plaintiffs_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input['name'] = (
        df_input['patientfirstname'].fillna('')
        + ' '
        + df_input['patientmiddlename']
        .fillna('')
        .apply(lambda x: x + ' ' if x != '' else '')
        + df_input['patientlastname'].fillna('')
    )
    df_input.rename(
        columns={
            'medicalrecordnumber': 'sourceid',
            'patientgender': 'gender',
            'patientaddress1': 'primaryaddressline1',
            'patientaddress2': 'primaryaddressline2',
            'patientcity': 'primaryaddresscity',
            'patientstate': 'primaryaddressstate',
            'patientzip': 'primaryaddresszip',
            'patienthomephone': 'homephone',
            'patientcellphone': 'cellphone',
            'patientdob': 'dateofbirth',
            'patientssn': 'ssn',
        },
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_input['dateofbirth'], df_output['ssn'] = pd.to_datetime(
        df_input['dateofbirth']
    ), df_output['ssn'].apply(str)
    # normal set operation will yield col in a random order during iteration and will cause a conflict with df
    # if createdatetitme/modifieddatetime come before other cols in df_input, it will try to add a scalar into an empty df, causing nan
    # Instead of directly converting, I utilized a set to keep track of the duplicate columns while preserving the column order.
    # This will ensure column insertion from the original df_input to come first, defining the height of the dataframe before inserting a scalar value.

    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]

    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output['dateofbirth'] = df_output['dateofbirth'].dt.date

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='plaintiffs'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def ati_generate_medicalfacilities_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
) -> tuple[pd.DataFrame | None, dict[str, str] | None]:
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    # Disable medical facility upsert to Salesforce until we recieve necessary information(name) from ATI
    df_input['relevanttogain'] = False
    df_input.rename(
        columns={
            'locationid': 'sourceid',
            'locationname': 'name',
            'locationaddress': 'physicaladdressline1',
            'locationcity': 'physicaladdresscity',
            'locationstate': 'physicaladdressstate',
            'locationzip': 'physicaladdresszip',
        },
        inplace=True,
    )
    df_input['sourceid'].replace(
        "", np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_input['sourceid'] = (
        df_input['sourceid'].str.lstrip('0').fillna(value='0')
    )  # remove leading zeros when reading this column
    # extract claimID and map it to medical facility ID for billing's medical facilityid
    claimid_to_medical_facility_id: dict[str, str] = df_input.set_index(
        'claimid'
    )['sourceid'].to_dict()
    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = False
    df_output['deletepreventoverride'] = False

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None
    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]
    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='medicalfacilities'
    )
    # Create a mapping of medical facility id to gainid
    medical_facility_id_to_gainid: dict[str, str] = dict(
        zip(df_output['sourceid'], gainids)
    )
    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids
    # Create the mapping of claimid to gainid
    claimid_to_gainid = {}
    for claimid, medical_facility_id in claimid_to_medical_facility_id.items():
        if medical_facility_id in medical_facility_id_to_gainid:
            claimid_to_gainid[claimid] = medical_facility_id_to_gainid[
                medical_facility_id
            ]
    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']
    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output, claimid_to_gainid


def ati_generate_lawfirms_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    bucket: str,
    directory: str,
    timestamp: str,
) -> pd.DataFrame:
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input['attorneystate'] = df_input['attorneystate'].apply(
        lambda x: str(x).upper() if pd.notnull(x) else x
    )
    df_input = df_input.replace(r'^\s*$', np.nan, regex=True)
    attorney_lawfirm_map = (
        aws_operations.get_legalpersonnel_lawfirm_sourceid_map('ATI')
    )
    manual_review_indices = df_input[
        (
            df_input['attorneyaddress1'].isna()
            | df_input['attorneycity'].isna()
            | df_input['attorneystate'].isna()
            | df_input['attorneyzip'].isna()
            | df_input['attorneyphone'].isna()
        )
    ].index.tolist()
    df_input_manual_review_missing_info = df_input.loc[manual_review_indices]

    if not df_input_manual_review_missing_info.empty:
        timestamp_string = timestamp.replace(' ', '_').replace(':', '-')
        date_string = datetime.datetime.now().strftime('%Y-%m-%d')
        filename = f'integration/integration_code/missing_info_manual_review_{timestamp_string}.csv'
        df_input_manual_review_missing_info.to_csv(filename, index=False)
        aws_operations.upload_s3(
            bucket,
            f'{directory}/lawfirms_manual_review/' + date_string,
            filename,
        )
        local_operations.clean_up_file(filename)
    df_input.drop(manual_review_indices, inplace=True)
    rs_lawfirm_data = aws_operations.get_rs_lawfirm_data_sourceid_map('ATI')
    df_input['sourceid'] = None
    for row in df_input.itertuples(index=True):
        index = row.Index  # Extract index from the named tuple

        # If we have a pre-existing mapping for the attorney in RS, use the lawfirm source id from the mapping.
        # This logic is in place because we can reliably use attorney id to determine the law firms. If an attorney
        # changes law firms, then a new attorney id will be assigned to them. Thus, if attorney id is the same and
        # law firm id changes, we assume that new law firm id is a duplicate. This is useful in cases where the law
        # address changes, but the attorney id remains the same. In such cases, we can avoid creating unnecessary
        # duplicates by identifying the lawfirm using the attorney id.
        if (
            (row.attorneythirdpartyattorneyid is not None)
            and (row.attorneythirdpartyattorneyid != '')
            and (
                row.attorneythirdpartyattorneyid
                in attorney_lawfirm_map['sourceid'].tolist()
            )
        ):
            df_input.at[index, 'sourceid'] = shared.get_first_value(
                attorney_lawfirm_map.loc[
                    (
                        (
                            attorney_lawfirm_map['sourceid']
                            == row.attorneythirdpartyattorneyid
                        )
                        & (attorney_lawfirm_map['sourcename'] == 'ATI')
                    ),
                    'lawfirmsourceid',
                ]
            )
        else:
            row_attorneyphone = (
                local_operations.remove_nonalphanumeric_characters(
                    str(row.attorneyphone)
                )
                if row.attorneyphone is not None
                else None
            )
            if (
                (row_attorneyphone is not None)
                and (row_attorneyphone != '')
                and (row_attorneyphone in rs_lawfirm_data['phone'].tolist())
            ):
                # Skip the law firm ingestion if the law firm with same phone number already exists in RS
                continue
            else:
                df_input.at[index, 'sourceid'] = hashlib.sha1(
                    local_operations.remove_nonalphanumeric_characters(
                        str(row.attorneyaddress1).split(' ')[0]
                        + str(row.attorneycity)
                        + state_name_to_code_map.get(
                            str(row.attorneystate).title(),
                            str(row.attorneystate),
                        )
                        + str(row.attorneyzip)
                        + str(row.attorneyphone)
                    )
                    .lower()
                    .encode('utf-8')
                ).hexdigest()[:20]
    # If the attorneyfirm is missing, we will use the attorneyfirstname and attorneylastname to create the lawfirm name.
    blank_attorney_firm_indices = df_input[
        df_input['attorneyfirm'].isna()
    ].index.tolist()
    df_input.loc[blank_attorney_firm_indices, 'attorneyfirm'] = (
        df_input.loc[blank_attorney_firm_indices, 'attorneyfirstname']
        + ' '
        + df_input.loc[blank_attorney_firm_indices, 'attorneylastname']
    )

    df_input.rename(
        columns={
            'attorneyfirm': 'name',
            'attorneyaddress1': 'billingaddressline1',
            'attorneyaddress2': 'billingaddressline2',
            'attorneycity': 'billingaddresscity',
            'attorneystate': 'billingaddressstate',
            'attorneyzip': 'billingaddresszip',
            'attorneyphone': 'phone',
            'attorneyfax': 'fax',
        },
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols
            and df_input[col].dtype == 'object'
            and col != 'sourceid'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    # Assigned False manually since default value is not assigned if the column exists in csv
    df_output['todelete'] = False
    df_output['deletepreventoverride'] = False

    # Code to Get createdatetime from redshift. This is to be removed and replaced with a more efficient solution.
    # lawfirms_ids = set([(x, y) for x, y in zip(df_output['sourceid'], df_output['sourcename'])])
    # rs_createdatetime = aws_operations.get_redshift_createdatetime(
    #     'LawFirms', lawfirms_ids
    # )
    # if not rs_createdatetime.empty:
    #     rs_createdatetime['tuple_key'] = rs_createdatetime.apply(lambda x: (x['sourceid'], x['sourcename']), axis=1)
    #     rs_createdatetime_dict = rs_createdatetime.dropna(subset=['createdatetime']).set_index('tuple_key')['createdatetime'].to_dict()
    # else:
    #     rs_createdatetime_dict = {}
    # df_output['createdatetime'] = df_output.apply(lambda x: rs_createdatetime_dict.get((x['sourceid'], x['sourcename']), x['createdatetime']), axis=1)
    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='lawfirms'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def ati_generate_legalpersonnel_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    bucket: str,
    directory: str,
    timestamp: str,
) -> pd.DataFrame:
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input['name'] = (
        df_input['attorneyfirstname'].fillna('').str.strip()
        + ' '
        + df_input['attorneymiddlename']
        .fillna('')
        .str.strip()
        .apply(lambda x: x + ' ' if x != '' else '')
        + df_input['attorneylastname'].fillna('').str.strip()
    )
    df_input['attorneystate'] = df_input['attorneystate'].apply(
        lambda x: str(x).upper() if pd.notnull(x) else x
    )
    df_input = df_input.replace(r'^\s*$', np.nan, regex=True)
    # Exlcude rows with missing information. We need to log cases where the attonreythirdpartyattorneyid
    # is missing but the guarfirstname or guarlastname are not missing (only in cases where guarantor relationship is attorney).
    manual_review_indices = df_input[
        (
            (
                df_input["attorneythirdpartyattorneyid"].isna()
                & ~(
                    df_input["guarfirstname"].isna()
                    & df_input["guarlastname"].isna()
                )
            )
            & (df_input["guarrelationship"] == "Attorney")
        )
        | df_input["attorneyaddress1"].isna()
        | df_input["attorneycity"].isna()
        | df_input["attorneystate"].isna()
        | df_input["attorneyzip"].isna()
        | df_input["attorneyphone"].isna()
    ].index.tolist()
    df_input_manual_review_missing_info = df_input.loc[manual_review_indices]

    if not df_input_manual_review_missing_info.empty:
        timestamp_string = timestamp.replace(' ', '_').replace(':', '-')
        date_string = datetime.datetime.now().strftime('%Y-%m-%d')
        filename = f'integration/integration_code/missing_info_manual_review_{timestamp_string}.csv'
        df_input_manual_review_missing_info.to_csv(filename, index=False)
        aws_operations.upload_s3(
            bucket,
            f'{directory}/legalpersonnel_manual_review/' + date_string,
            filename,
        )
        local_operations.clean_up_file(filename)
    df_input.drop(manual_review_indices, inplace=True)
    attorney_lawfirm_map = (
        aws_operations.get_legalpersonnel_lawfirm_sourceid_map()
    )
    rs_lawfirm_data = aws_operations.get_rs_lawfirm_data_sourceid_map()
    legalpersonnel_lawfirm_map = (
        aws_operations.get_legalpersonnel_lawfirm_sourceid_map()
    )
    case_map_legalpersonnel = (
        aws_operations.get_case_map_legalpersonnel_sourceid_map()
    )
    case_map_legalpersonnel.rename(
        columns={'sourceid': 'caseid'}, inplace=True
    )
    case_map_lawfirms = pd.merge(
        legalpersonnel_lawfirm_map,
        case_map_legalpersonnel,
        left_on=['sourceid', 'sourcename'],
        right_on=['attorneyid', 'sourcename'],
        how='inner',
    )
    case_map_lawfirms = case_map_lawfirms[
        ['caseid', 'attorneyid', 'sourcename', 'lawfirmsourceid']
    ]
    case_map_lawfirms.dropna(subset=['lawfirmsourceid'], inplace=True)
    rs_lawfirm_data.dropna(subset=['sourceid'], inplace=True)
    case_map_lawfirms_data = pd.merge(
        case_map_lawfirms,
        rs_lawfirm_data,
        left_on=['lawfirmsourceid', 'sourcename'],
        right_on=['sourceid', 'sourcename'],
        how='inner',
    )
    for row in df_input.itertuples(index=True):
        index = row.Index
        # If we have a pre-existing mapping for the attorney in RS, use the lawfirm source id from the mapping.
        # This logic is in place because we can reliably use attorney id to determine the law firms. If an attorney
        # changes law firms, then a new attorney id will be assigned to them. Thus, if attorney id is the same and
        # law firm id changes, we assume that new law firm id is a duplicate. This is useful in cases where the law
        # address changes, but the attorney id remains the same. In such cases, we can avoid creating unnecessary
        # duplicates by identifying the lawfirm using the attorney id.
        if (
            (row.attorneythirdpartyattorneyid is not None)
            and (row.attorneythirdpartyattorneyid != '')
            and (
                row.attorneythirdpartyattorneyid
                in attorney_lawfirm_map['sourceid'].tolist()
            )
        ):
            df_input.at[index, 'lawfirmid'] = shared.get_first_value(
                attorney_lawfirm_map.loc[
                    (
                        (
                            attorney_lawfirm_map['sourceid']
                            == row.attorneythirdpartyattorneyid
                        )
                        & (attorney_lawfirm_map['sourcename'] == 'ATI')
                    ),
                    'lawfirmsourceid',
                ]
            )
        else:
            '''
            If we don't have a pre-existing mapping for the attorney in RS, we will use the attorney phone number
            to map the law firm. If a pre-existing firm with the same phone number exists, we will do a further
            check to see if the city or state matches. If a match is found, we will use that law firm id. If city/state
            match is not found, then we will pick one of the law firms with the same phone number. If there are multiple
            law firms to pick from, we will pick the one which has been assigned the highest number of cases. If ambiguity
            still remains, then we will pick the first choice in an alphabetically sorted list in ascending order.
            name. If no law firm is found with the same phone number, we will create a new law firm id.
            This logic is in place because for ATI data, the law firm address changes frequently. When the address
            changes and a new attorney/contact needs to ingested, then the system will try to ingest a new law firm.
            This ingestion often fails because the law firm already exists in the system with different address and same
            phone number. To avoid this, we will use the phone number to map the law firm.
            '''
            mapped_lawfirm_id = None
            row_attorneyphone = (
                local_operations.remove_nonalphanumeric_characters(
                    str(row.attorneyphone)
                )
                if row.attorneyphone is not None
                else None
            )
            row_attorneystate = (
                state_code_to_name_map.get(
                    str(row.attorneystate).upper(),
                    str(row.attorneystate),
                )
                if row.attorneystate is not None
                else None
            )
            if (
                (row_attorneyphone is not None)
                and (row_attorneyphone != '')
                and (row_attorneyphone in rs_lawfirm_data['phone'].tolist())
            ):
                rs_matching_lawfirms = None
                rs_matching_phone_lawfirms = rs_lawfirm_data[
                    rs_lawfirm_data['phone'] == row_attorneyphone
                ]
                rs_matching_phone_state_lawfirms = rs_matching_phone_lawfirms[
                    rs_matching_phone_lawfirms['billingaddressstate']
                    == row_attorneystate
                ]
                if not rs_matching_phone_state_lawfirms.empty:
                    rs_matching_phone_city_state_lawfirms = (
                        rs_matching_phone_state_lawfirms[
                            rs_matching_phone_state_lawfirms[
                                'billingaddresscity'
                            ]
                            == row.attorneycity
                        ]
                    )
                    # First check if there is a law firm with the same phone number, city and state
                    if not rs_matching_phone_city_state_lawfirms.empty:
                        rs_matching_lawfirms = (
                            rs_matching_phone_city_state_lawfirms
                        )
                    else:
                        # If no law firm with the same phone number, city and state is found, then
                        # check if there is a law firm with the same phone number and state
                        rs_matching_lawfirms = rs_matching_phone_state_lawfirms
                else:
                    # If no law firm with the same phone number and state is found, then
                    # check if there is a law firm with the same phone number
                    rs_matching_lawfirms = rs_matching_phone_lawfirms
                # Find all cases associated with the picked firms
                case_map_matching_lawfirms_data = case_map_lawfirms_data[
                    case_map_lawfirms_data['lawfirmsourceid'].isin(
                        rs_matching_lawfirms['sourceid']
                    )
                ]
                if (
                    case_map_matching_lawfirms_data is not None
                    and not case_map_matching_lawfirms_data.empty
                ):
                    case_map_lawfirms_groups = (
                        case_map_matching_lawfirms_data.groupby(
                            'lawfirmsourceid'
                        )
                        .count()['caseid']
                        .to_dict()
                    )
                    case_map_matching_lawfirms_data['case_count'] = (
                        case_map_matching_lawfirms_data['lawfirmsourceid'].map(
                            case_map_lawfirms_groups
                        )
                    )
                    # If there are multiple law firms, then pick the one with the highest number of cases
                    case_map_matching_lawfirms_data = (
                        case_map_matching_lawfirms_data[
                            case_map_matching_lawfirms_data['case_count']
                            == case_map_matching_lawfirms_data[
                                'case_count'
                            ].max()
                        ]
                    )
                    case_map_matching_lawfirms_data.sort_values(
                        by=['name'], ascending=True, inplace=True
                    )
                    mapped_lawfirm_id = case_map_matching_lawfirms_data[
                        'lawfirmsourceid'
                    ].iloc[0]
                else:
                    # If no associated cases are found, then pick the first law firm
                    # in an alphabetically sorted list in ascending order
                    rs_matching_lawfirms.sort_values(
                        by=['name'], ascending=True, inplace=True
                    )
                    mapped_lawfirm_id = rs_matching_lawfirms['sourceid'].iloc[
                        0
                    ]
            if mapped_lawfirm_id is not None:
                df_input.at[index, 'lawfirmid'] = mapped_lawfirm_id
            else:
                # If no law firm is found with the same phone number, create a new law firm id
                df_input.at[index, 'lawfirmid'] = hashlib.sha1(
                    local_operations.remove_nonalphanumeric_characters(
                        str(row.attorneyaddress1).split(' ')[0]
                        + str(row.attorneycity)
                        + state_name_to_code_map.get(
                            str(row.attorneystate).title(),
                            str(row.attorneystate),
                        )
                        + str(row.attorneyzip)
                        + str(row.attorneyphone)
                    )
                    .lower()
                    .encode('utf-8')
                ).hexdigest()[:20]

    df_input.rename(
        columns={
            'attorneythirdpartyattorneyid': 'sourceid',
            'attorneyfirm': 'lawfirmname',
            'attorneyaddress1': 'primaryaddressline1',
            'attorneyaddress2': 'primaryaddressline2',
            'attorneycity': 'primaryaddresscity',
            'attorneystate': 'primaryaddressstate',
            'attorneyzip': 'primaryaddresszip',
            'attorneyphone': 'businessphone',
            'attorneyext': 'businessphoneext',
            'attorneycellphone': 'cellphone',
            'attorneyfax': 'fax',
            'attorneynotes': 'sourcenotes',
            'attorneyemail': 'primaryemail',
        },
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols
            and df_input[col].dtype == 'object'
            and col != 'lawfirmid'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    # Assigned False manually since default value is not assigned if the column exists in csv
    df_output['todelete'] = False
    df_output['deletepreventoverride'] = False
    # Code to Get createdatetime from redshift. This is to be removed and replaced with a more efficient solution.
    # legalpersonnel_ids = set([(x, y) for x, y in zip(df_output['sourceid'], df_output['sourcename'])])
    # rs_createdatetime = aws_operations.get_redshift_createdatetime(
    #     'LegalPersonnel', legalpersonnel_ids
    # )
    # if not rs_createdatetime.empty:
    #     rs_createdatetime['tuple_key'] = rs_createdatetime.apply(lambda x: (x['sourceid'], x['sourcename']), axis=1)
    #     rs_createdatetime_dict = rs_createdatetime.dropna(subset=['createdatetime']).set_index('tuple_key')['createdatetime'].to_dict()
    # else:
    #     rs_createdatetime_dict = {}
    # df_output['createdatetime'] = df_output.apply(lambda x: rs_createdatetime_dict.get((x['sourceid'], x['sourcename']), x['createdatetime']), axis=1)
    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='legalpersonnel'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'lawfirmid',
        'lawfirms',
        aws_operations.get_gainids_by_ati_ids,
    )

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def ati_generate_cases_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
) -> tuple[pd.DataFrame | None, dict[str, pd.DataFrame]]:
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input['plaintiffname'] = (
        df_input['patientfirstname'].fillna('')
        + ' '
        + df_input['patientmiddlename']
        .fillna('')
        .apply(lambda x: x + ' ' if x != '' else '')
        + df_input['patientlastname'].fillna('')
    )
    # df_input[['locationaddress1', 'locationaddress2']] = df_input['locationaddress'].str.split(',', expand=True) # don't use this; might expand to 3 or even more columns, causing pandas to break
    df_input.rename(
        columns={
            'caseid': 'sourceid',
            'medicalrecordnumber': 'plaintiffid',
            'patientdob': 'plaintiffdateofbirth',
            'dateofinjury': 'accidentdate',
            'accounttype': 'type',
            'locationid': 'medicalfacilityid',
            'locationaddress': 'medicalfacilityaddressline1',  # for now let facility address be in 1 column; TBD - clean address using Google API
            'locationcity': 'medicalfacilityaddresscity',
            'locationstate': 'medicalfacilityaddressstate',
            'locationzip': 'medicalfacilityaddresszip',
            'attorneythirdpartyattorneyid': 'attorneyid',
            'tailclaim': 'tailclaimcase',
            'patientstatus': 'patientstatus',
            'dischargedate': 'datetreatmentcompleted',
        },
        inplace=True,
    )
    # Use first date of service as fallback for accident date if accident date is missing
    df_input.loc[df_input['accidentdate'].isna(), 'accidentdate'] = (
        df_input.loc[df_input['accidentdate'].isna(), 'firstdateofservice']
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_input['medicalfacilityid'] = (
        df_input['medicalfacilityid'].str.lstrip('0').fillna(value='0')
    )  # remove leading zeros when reading this column
    (
        df_input['plaintiffdateofbirth'],
        df_input['accidentdate'],
    ) = pd.to_datetime(df_input['plaintiffdateofbirth']), pd.to_datetime(
        df_input['accidentdate']
    )
    df_input['type'] = df_input['type'].map(
        canonical_data['ATI']['ToCanonical']['CaseTypes']
    )

    df_input['datetreatmentcompleted'] = pd.to_datetime(
        df_input['datetreatmentcompleted']
    )

    df_input['treatmentcompleted'] = False
    df_input.loc[
        df_input['patientstatus'] == 'Discharged', 'treatmentcompleted'
    ] = True

    data_from_case_data_file = {}
    # Extract date of service data from case data file
    df_date_of_service = df_input[['claimid', 'dateofservice']]
    df_date_of_service.drop_duplicates(
        subset=['claimid'], keep='last', inplace=True
    )
    df_date_of_service.set_index('claimid', inplace=True)
    data_from_case_data_file['dateofservice'] = df_date_of_service

    # New; extract billing level location data from case data file
    # once duplicate rows are dropped, only unique billing source ids remain
    # ^ deduplication needed to allow one to one mapping from claim id to medical facility id => relevant when trying to match billing ids to medical facility ids (in `ati_generate_billings_data`)
    # normal set operation will yield col in a random order during iteration and will cause a conflict with df
    # if createdatetitme/modifieddatetime come before other cols in df_input, it will try to add a scalar into an empty df, causing nan
    # Instead of directly converting, I utilized a set to keep track of the duplicate columns while preserving the column order.
    # This will ensure column insertion from the original df_input to come first, defining the height of the dataframe before inserting a scalar value.

    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if (
                        'source' not in col
                        and 'datetime' in col
                        and 'servicingend' not in col
                    )
                    else np.nan
                )
            )
        )
    (
        df_output['plaintiffdateofbirth'],
        df_output['accidentdate'],
    ) = (
        df_output['plaintiffdateofbirth'].dt.date,
        df_output['accidentdate'].dt.date,
    )
    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output = df_output[
        (df_output['tailclaimcase'] == '1')
        | (df_output['tailclaimcase'] == '0')
    ]
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='cases'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'plaintiffid',
        'plaintiffs',
        aws_operations.get_gainids_by_ati_ids,
    )

    # Define the list of columns to update
    columns_to_update = [
        'attorneyid',
        'paralegalid',
        'casemanagerid',
        'cocounselid',
        'coparalegalid',
        'cocasemanagerid',
    ]

    # Update each column
    for column in columns_to_update:
        local_operations.assign_gainids_to_dataframe_column(
            df_output,
            column,
            'legalpersonnel',
            aws_operations.get_gainids_by_ati_ids,
        )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output, data_from_case_data_file


def ati_generate_billings_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    claimid_to_medicalfacilityid_map: dict[str, str] | None,
    data_from_case_data_file: dict[str, pd.DataFrame] | None,
    timestamp: str,
):
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    (
        df_input['medicalclaimnumber'],
        df_input['relevanttogain'],
        df_input['type'],
        df_input['gaintype'],
    ) = (
        df_input['claimid'],
        True,
        'Medical Funding',
        'Medical Funding - Serviced',
    )  # TBD? Make Gain Type customized based on contract type in Salesforce
    # For gain type => get salesforce client name based on canonical source => get it's salesforce id => get it's funding record type
    df_input.rename(
        columns={'claimid': 'sourceid'},
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    total_amount_per_billing = collections.defaultdict(int)
    for (
        row
    ) in (
        df_input.itertuples()
    ):  # needed to compute total amount per billing, which is an aggregate sum of charge amounts per claim id in the input data
        total_amount_per_billing[
            row.sourceid
        ] += (
            row.chargeamount
        )  # sum up charge amounts on a claim level, i.e. the current source id
    df_input['totalamount'] = df_input['sourceid'].map(
        total_amount_per_billing
    )  # now, can assign a billing total based on the source id => map total using created dictionary
    df_input['totalamount'] = df_input['totalamount'].round(
        4
    )  # necessary to match redshift table specifications
    # normal set operation will yield col in a random order during iteration and will cause a conflict with df
    # if createdatetitme/modifieddatetime come before other cols in df_input, it will try to add a scalar into an empty df, causing nan
    # Instead of directly converting, I utilized a set to keep track of the duplicate columns while preserving the column order.
    # This will ensure column insertion from the original df_input to come first, defining the height of the dataframe before inserting a scalar value.
    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if (
                        'source' not in col
                        and 'datetime' in col
                        and 'servicingend' not in col
                    )
                    else (pd.NaT if 'date' in col else np.nan)
                )
            )
        )
    df_output.drop_duplicates(
        subset=['sourceid'], keep='last', inplace=True
    )  # once duplicate rows are dropped, only unique billings/source ids remain
    # New; pull in medical facility location using opportunity data (pass in as dataframe 'df_billing_location')
    # need indexing for combining dataframe based on index value
    gainids = aws_operations.get_gainids_by_ati_ids(
        df_input['medicalclaimnumber'].unique().tolist(), 'billings'
    )
    rs_billing_medical_facility_id = (
        aws_operations.get_billing_claim_id_medical_facility_id(
            set(gainids), 'ATI'
        )
    )
    rs_billing_medical_facility_id_dict = (
        rs_billing_medical_facility_id.set_index('medicalclaimnumber')[
            'medicalfacilityid'
        ].to_dict()
    )
    if not rs_billing_medical_facility_id.empty:
        df_output['medicalfacilityid'] = df_output['sourceid'].map(
            rs_billing_medical_facility_id_dict
        )
    rs_billing_date_of_service = (
        aws_operations.get_billing_claim_id_date_of_service(
            set(gainids), 'ATI'
        )
    )
    if not rs_billing_date_of_service.empty:
        rs_billing_date_of_service['dateofservice'] = (
            rs_billing_date_of_service['dateofservice'].astype(str)
        )
    rs_billing_date_of_service_dict = rs_billing_date_of_service.set_index(
        'medicalclaimnumber'
    )['dateofservice'].to_dict()
    if not rs_billing_date_of_service.empty:
        df_output['dateofservice'] = df_output.apply(
            lambda x: rs_billing_date_of_service_dict.get(
                x['sourceid'],
                x['dateofservice'],
            ),
            axis=1,
        )
    if claimid_to_medicalfacilityid_map:
        df_output['medicalfacilityid'] = df_output.apply(
            lambda x: claimid_to_medicalfacilityid_map.get(x['sourceid'])
            or x['medicalfacilityid'],
            axis=1,
        )
    df_output.set_index('sourceid', inplace=True)  # "sourceid" for df_output

    if data_from_case_data_file:
        # combined date of service info from data_from_case_data_file into df_input
        df_output.update(data_from_case_data_file['dateofservice'])
    df_output.reset_index(
        inplace=True
    )  # need to un-index else sourceid will no longer be a column in result df
    df_output['dateofservice'] = pd.to_datetime(df_output['dateofservice'])
    df_output['dateofservice'] = df_output['dateofservice'].dt.date
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    # This is the root ATI partner account, refer to GainInternalWebApp\Database\Scripts\patch_1.1.5.sql for details
    df_output['partneraccountid'] = partner_account_id

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='billings'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output, 'caseid', 'cases', aws_operations.get_gainids_by_ati_ids
    )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )
    billings_servicing_datetime_map = (
        df_output[['gainid', 'servicingstartdatetime', 'servicingenddatetime']]
        .set_index('gainid')
        .to_dict('index')
    )
    return df_output, billings_servicing_datetime_map


def ati_generate_charges_upsert_data(
    df_input: pd.DataFrame, df_output: pd.DataFrame, timestamp: str
):
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input.rename(
        columns={
            'chargedetailid': 'sourceid',
            'postdate': 'dateofservice',
            'chargeamount': 'amount',
            'hcpc': 'cptcode',
            'hcpcdescription': 'cptdescription',
            'units': 'quantity',
            'claimid': 'billingid',
        },
        inplace=True,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    df_input['cptcode'] = df_input['cptcode'].str[
        :9
    ]  # TBD - better filtering approach
    df_input['dateofservice'] = pd.to_datetime(df_input['dateofservice'])
    # normal set operation will yield col in a random order during iteration and will cause a conflict with df
    # if createdatetitme/modifieddatetime come before other cols in df_input, it will try to add a scalar into an empty df, causing nan
    # Instead of directly converting, I utilized a set to keep track of the duplicate columns while preserving the column order.
    # This will ensure column insertion from the original df_input to come first, defining the height of the dataframe before inserting a scalar value.

    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output['dateofservice'] = df_output['dateofservice'].dt.date

    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='charges'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output,
        'billingid',
        'billings',
        aws_operations.get_gainids_by_ati_ids,
    )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def ati_generate_transactions_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    timestamp: str,
    billings_servicing_datetime_map: (
        dict[typing.Hashable, typing.Any] | None
    ) = None,
):
    global canonical_data
    if billings_servicing_datetime_map is None:
        billings_servicing_datetime_map = {}
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input.rename(
        columns={
            'transactionid': 'sourceid',
            'chargedetailid': 'chargeid',
            'transactiontype': 'type',
            'transactionscodedesc': 'description',
            'transactionamount': 'amount',
            'postdate': 'postdatetime',
            'paymentdate': 'paymentdatetime',
            'entrydate': 'entrydatetime',
        },
        inplace=True,
    )
    # Create a unique identifier for transaction with carccode values in ['1', '2', '3']
    df_input['carccode'] = df_input['carccode'].str.strip()
    df_input['carccode'] = df_input['carccode'].apply(
        lambda x: canonical_data['ATI']['ToCanonical']['CARCCodes'].get(
            x, None
        )
    )
    df_input['postdatetime'] = pd.to_datetime(df_input['postdatetime'])
    df_input['paymentdatetime'] = pd.to_datetime(df_input['paymentdatetime'])
    df_input['entrydatetime'] = pd.to_datetime(df_input['entrydatetime'])
    patient_responsibility_indices = df_input[
        df_input['carccode'].isin(['Deductible', 'Copayment', 'Coinsurance'])
        & df_input['sourceid'].isna()
    ].index
    df_input.loc[
        patient_responsibility_indices,
        'sourceid',
    ] = (
        df_input['chargeid'].astype(str)
        + df_input['carccode']
        + df_input['entrydatetime'].dt.strftime('%Y%m%d%H%M%S')
    )
    df_input.loc[patient_responsibility_indices, 'sourceid'] = df_input.apply(
        lambda x: local_operations.get_md5_hash(str(x['sourceid']))[:16],
        axis=1,
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows
    # Exclude transactions with descriptions that don't belong to Gain
    df_input['amount'] = df_input['amount'].astype('float')  # convert to float
    df_input['amount'] = (
        df_input['amount']
        .groupby(df_input['sourceid'])
        .transform(lambda x: x.sum())
    )  # aggregate transaction amount by source id
    df_input.drop_duplicates(
        subset=['sourceid'], keep='last', inplace=True
    )  # once duplicate rows are dropped, only unique transactions/source ids remain
    non_rollup_description_gainservicing_df = df_input[
        'description'
    ].str.startswith('Gain Servicing')
    non_rollup_description_settlement_litigation_df = df_input[
        'description'
    ].str.startswith('Settlement - Liti')
    non_rollup_description_litigation_discount_df = (
        df_input['description'] == 'Litigation Discount'
    )
    non_rollup_description_timely_to_bill_statement_df = df_input[
        'description'
    ].str.startswith('Timely To Bill Patient')

    # Litigation payments denote direct payment from attorneys to ATI
    # Before 2023, because they are not owed to Gain, those are considered to reduce outstanding balance
    # After 2023, because they are owed to us, those are not considered to reduce outstanding balance
    # UPDATE to this logic: if litigation payment datetime is in between servicing start and end datetime, then it is considered to reduce outstanding balance
    non_rollup_overall_description_df = (
        (non_rollup_description_gainservicing_df)
        | (non_rollup_description_settlement_litigation_df)
        | (non_rollup_description_litigation_discount_df)
        | (non_rollup_description_timely_to_bill_statement_df)
    )
    # Update 'relevanttogain' column based on the condition
    df_input.loc[non_rollup_overall_description_df, 'relevanttogain'] = False
    # After excluding litigation payment with postdatetime > 2023-01-01, we need to include litigation payment back with postdatetime in between servicing date time
    # Get hashed gainids for the ClaimIDs
    billing_gainids = aws_operations.get_gainids_by_ati_ids(
        df_input['claimid'].tolist(), 'billings'
    )
    # Create a mapping between ClaimID and hashed gainid
    claimid_to_gainid_map = dict(zip(df_input['claimid'], billing_gainids))
    # Get servicing datetime information for the hashed gainids
    billing_servicing_datetime_df = (
        aws_operations.get_postgres_servicingdatetime(
            'billings', set(billing_gainids)
        )
    )
    # Create a mapping between hashed gainid and servicing datetime information
    gainid_to_servicing_datetime_map = billing_servicing_datetime_df.set_index(
        'gainid'
    ).to_dict('index')
    # Function to check if a transaction is within servicing period
    # Apply the check and update 'relevanttogain'
    # Two checks required: one for the newly upserted billing data and one for the redshift data
    df_input.loc[
        df_input.apply(  # pyright: ignore[reportCallIssue]
            lambda row: ati_check_within_servicing_period(
                row, claimid_to_gainid_map, gainid_to_servicing_datetime_map
            ),
            axis=1,
        ),
        'relevanttogain',
    ] = False
    df_input.loc[
        df_input.apply(  # pyright: ignore[reportCallIssue]
            lambda row: ati_check_within_servicing_period(
                row, claimid_to_gainid_map, billings_servicing_datetime_map
            ),
            axis=1,
        ),
        'relevanttogain',
    ] = False
    #     # Define the condition
    # condition = (df_input['description'] == 'Litigation Payment')
    # # Update 'relevanttogain' column based on the condition
    # df_input.loc[condition, 'relevanttogain'] = True
    # normal set operation will yield col in a random order during iteration and will cause a conflict with df
    # if createdatetitme/modifieddatetime come before other cols in df_input, it will try to add a scalar into an empty df, causing nan
    # Instead of directly converting, I utilized a set to keep track of the duplicate columns while preserving the column order.
    # This will ensure column insertion from the original df_input to come first, defining the height of the dataframe before inserting a scalar value.

    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else np.nan
                )
            )
        )
    df_output.drop_duplicates(subset=['sourceid'], keep='last', inplace=True)
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='transactions'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output, 'chargeid', 'charges', aws_operations.get_gainids_by_ati_ids
    )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def ati_generate_files_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    df_cases: pd.DataFrame | None,
    bucket: str,
    timestamp: str,
) -> pd.DataFrame:
    global canonical
    # ATI file name: CasesSourceId_ATIDocumentType_CreatedDate.pdf
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['filename'] = df_input['path'].str.split('/').str[-1]
    df_output['sourceid'] = df_input['filename'].str.split('-').str[0]
    df_input['doctype'] = df_input['filename'].str.split('_').str[1]
    df_output['caseid'] = df_input['filename'].str.split('_').str[0]
    for i in range(len(df_input)):
        # path = df_input['filename'][i].str.split('_')
        path = df_input['filename'][i].split('_')
        df_output['sourceid'][i] = df_input['filename'][i].split('-')[0]
        if 'AccountLedger' in path[0]:
            df_input['doctype'][i] = 'AccountLedger'
            path[0] = path[0].replace("AccountLedger", "")
            df_output['caseid'][i] = path[0]
        else:
            df_input['doctype'][i] = path[1]
            df_output['caseid'][i] = path[0]

    case_to_plaintiff = {}

    local_operations.assign_gainids_to_dataframe_column(
        df_output, 'caseid', 'cases', aws_operations.get_gainids_by_ati_ids
    )

    redshift_case_ids = set(df_output['caseid'])
    if df_cases is not None and not df_cases.empty:
        cases_in_progress = (
            df_cases[['gainid', 'plaintiffid']]
            .set_index('gainid')
            .to_dict()['plaintiffid']
        )  # use the plaintiff id of cases in progress
        case_to_plaintiff.update(cases_in_progress)
        redshift_case_ids = redshift_case_ids - set(df_cases['gainid'])
    id_subset = [
        case_id for case_id in redshift_case_ids if case_id is not None
    ]  # only querying plaintiff ids of cases that are not in progress
    redshift_data = aws_operations.query_postgres(
        ['cases'], id_subset=id_subset
    )
    redshift_data_prevent_override_true = aws_operations.query_postgres(
        ['cases'],
        id_subset=id_subset,
        for_delete_prevent_override=True,
    )
    redshift_data_todelete_true = aws_operations.query_postgres(
        ['cases'],
        id_subset=id_subset,
        for_delete=True,
    )

    if (
        'cases' in redshift_data_prevent_override_true
        and len(redshift_data_prevent_override_true['cases']) > 0
    ):
        redshift_data['cases'] = pd.concat(
            [
                redshift_data['cases'],
                redshift_data_prevent_override_true['cases'],
            ],
            ignore_index=True,
        )

    if (
        'cases' in redshift_data_todelete_true
        and len(redshift_data_todelete_true['cases']) > 0
    ):
        redshift_data['cases'] = pd.concat(
            [redshift_data['cases'], redshift_data_todelete_true['cases']],
            ignore_index=True,
        )

    if redshift_data and 'cases' in redshift_data:
        for case in redshift_data['cases'].itertuples():
            case_id = case.GainId
            plaintiff_id = case.PlaintiffId
            case_to_plaintiff[case_id] = plaintiff_id
    df_output['plaintiffid'] = df_output['caseid'].map(case_to_plaintiff)
    df_output['url'] = df_input["path"].apply(
        lambda path: f's3://{bucket}/{path}'
    )
    df_output['type'] = df_input['doctype'].map(
        canonical_data['ATI']['ToCanonical']['DocumentTypes']
    )
    df_output.loc[
        (df_output['plaintiffid'].notnull()) & (df_output['type'].notnull()),
        'relevanttogain',
    ] = True
    df_output.loc[
        (df_output['plaintiffid'].isna()) | (df_output['type'].isna()),
        'relevanttogain',
    ] = False
    df_output['sourcecreatedatetime'] = (
        df_output['sourceid'].str.split('_').str[-1]
    )
    df_output['sourcecreatedatetime'] = pd.to_datetime(
        df_output['sourcecreatedatetime'], format='%Y-%m-%d'
    )
    df_output['modifieddatetime'] = pd.to_datetime(
        timestamp, format='%Y-%m-%d'
    )
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )

    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None

    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]

    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='files'
    )

    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)

    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']

    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )

    return df_output


def ati_generate_notes_upsert_data(
    df_input: pd.DataFrame,
    df_output: pd.DataFrame,
    timestamp: str,
) -> pd.DataFrame:
    global canonical_data
    df_output.columns = [s.lower() for s in (df_output.columns)]
    df_output.rename(columns={'gainid': 'sourceid'}, inplace=True)
    df_input['relevanttogain'] = True
    df_input.rename(
        columns={
            'lognotes': 'note',
            'username': 'notecreatorname',
            'createddate': 'sourcecreatedatetime',
        },
        inplace=True,
    )
    df_input['sourceid'] = df_input.apply(
        lambda x: local_operations.get_md5_hash(
            f'{x["caseid"]}_{x["notecreatorname"]}_{x["sourcecreatedatetime"]}'.lower()
        )[:16],
        axis=1,
    )
    df_input['notecreatorname'] = df_input['notecreatorname'].apply(
        lambda x: ' '.join(reversed(x.split(', ')))
    )
    df_input['sourceid'].replace(
        '', np.nan, inplace=True
    )  # replace blank string with NaN values
    df_input.dropna(
        subset=['sourceid'], inplace=True
    )  # remove NaN source id rows

    local_operations.assign_gainids_to_dataframe_column(
        df_output, 'caseid', 'cases', aws_operations.get_gainids_by_ati_ids
    )
    # normal set operation will yield col in a random order during iteration and will cause a conflict with df
    # if createdatetitme/modifieddatetime come before other cols in df_input, it will try to add a scalar into an empty df, causing nan
    # Instead of directly converting, I utilized a set to keep track of the duplicate columns while preserving the column order.
    # This will ensure column insertion from the original df_input to come first, defining the height of the dataframe before inserting a scalar value.
    # set operation to remove duplicate while preserving the order of cols
    df_in_cols_set, df_out_cols_set = set(), set()
    df_in_cols = [
        x
        for x in df_input.columns
        if not (x in df_in_cols_set or df_in_cols_set.add(x))
    ]
    df_out_cols = [
        x
        for x in df_output.columns
        if not (x in df_out_cols_set or df_out_cols_set.add(x))
    ]
    for col in df_in_cols:
        if 'state' in col:
            df_input[col] = df_input[col].map(state_code_to_name_map)
    for col in df_out_cols:
        df_output[col] = (
            df_input[col].str.title()
            if col in df_in_cols and df_input[col].dtype == 'object'
            else (
                df_input[col]
                if col in df_in_cols
                else (
                    timestamp
                    if ('source' not in col and 'datetime' in col)
                    else (pd.NaT if 'date' in col else np.nan)
                )
            )
        )
    df_output.drop_duplicates(
        subset=['sourceid'], keep='last', inplace=True
    )  # once duplicate rows are dropped, only unique notes/source ids remain
    df_output['todelete'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    df_output['deletepreventoverride'] = (
        False  # Assigned False manually since default value is not assigned if the column exists in csv
    )
    if 'sourcecreatedatetime' in df_input.columns:
        df_output['sourcecreatedatetime'] = df_input['sourcecreatedatetime']
    else:
        df_output['sourcecreatedatetime'] = None

    if 'sourcemodifieddatetime' in df_input.columns:
        df_output['sourcemodifieddatetime'] = df_input[
            'sourcemodifieddatetime'
        ]
    else:
        df_output['sourcemodifieddatetime'] = None
    source_ids = [
        id_record.IdRecord(
            source_id, sourcecreatedatetime, sourcemodifieddatetime
        )
        for source_id, sourcecreatedatetime, sourcemodifieddatetime in zip(
            df_output['sourceid'],
            df_output['sourcecreatedatetime'],
            df_output['sourcemodifieddatetime'],
        )
    ]
    gainids = aws_operations.upsert_gainid_records_by_ids(
        ati_records=source_ids, canonical_object='notes'
    )
    # Assign the returned gainids to the DataFrame
    df_output['sourceid'] = gainids

    local_operations.assign_gainids_to_dataframe_column(
        df_output, 'caseid', 'cases', aws_operations.get_gainids_by_ati_ids
    )

    df_output.rename(columns={'sourceid': 'gainid'}, inplace=True)
    columns_to_drop = ['sourcemodifieddatetime', 'sourcecreatedatetime']
    # Drop columns if they exist in df_output
    df_output.drop(
        columns=[col for col in columns_to_drop if col in df_output.columns],
        inplace=True,
    )
    return df_output


def ati_generate_s3_upsert_data(
    data: dict[str, pd.DataFrame | None],
    bucket: str,
    directory: str,
    timestamp: str,
) -> dict[str, pd.DataFrame | None]:
    global canonical

    df_plaintiffs = (
        ati_generate_plaintiffs_upsert_data(
            typing.cast(pd.DataFrame, data['plaintiffs']),
            pd.DataFrame(columns=canonical['Plaintiffs']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'plaintiffs')
        else None
    )

    df_medicalfacilities, claim_to_medicalfacility_map = (
        ati_generate_medicalfacilities_upsert_data(
            typing.cast(pd.DataFrame, data['medicalfacilities']),
            pd.DataFrame(columns=canonical['MedicalFacilities']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'medicalfacilities')
        else (None, None)
    )

    df_lawfirms = (
        ati_generate_lawfirms_upsert_data(
            typing.cast(pd.DataFrame, data['lawfirms']),
            pd.DataFrame(columns=canonical['LawFirms']),
            bucket,
            directory,
            timestamp,
        )
        if shared.verify_is_valid(data, 'lawfirms')
        else None
    )

    df_legalpersonnel = (
        ati_generate_legalpersonnel_upsert_data(
            typing.cast(pd.DataFrame, data['legalpersonnel']),
            pd.DataFrame(columns=canonical['LegalPersonnel']),
            bucket,
            directory,
            timestamp,
        )
        if shared.verify_is_valid(data, 'legalpersonnel')
        else None
    )
    df_cases, data_from_case_data_file = (
        ati_generate_cases_upsert_data(
            typing.cast(pd.DataFrame, data['cases']),
            pd.DataFrame(columns=canonical['Cases']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'cases')
        else (None, None)
    )
    df_billings, billings_servicing_datetime_map = (
        ati_generate_billings_upsert_data(
            typing.cast(pd.DataFrame, data['billings']),
            pd.DataFrame(columns=canonical['Billings']),
            claim_to_medicalfacility_map,
            data_from_case_data_file,
            timestamp,
        )
        if shared.verify_is_valid(data, 'billings')
        else (None, None)
    )
    df_charges = (
        ati_generate_charges_upsert_data(
            typing.cast(pd.DataFrame, data['charges']),
            pd.DataFrame(columns=canonical['Charges']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'charges')
        else None
    )
    df_transactions = (
        ati_generate_transactions_upsert_data(
            typing.cast(pd.DataFrame, data['transactions']),
            pd.DataFrame(columns=canonical['Transactions']),
            timestamp,
            billings_servicing_datetime_map,
        )
        if shared.verify_is_valid(data, 'transactions')
        else None
    )

    df_files = (
        ati_generate_files_upsert_data(
            typing.cast(pd.DataFrame, data['files']),
            pd.DataFrame(columns=canonical['Files']),
            df_cases,
            bucket,
            timestamp,
        )
        if shared.verify_is_valid(data, 'files')
        else None
    )

    df_notes = (
        ati_generate_notes_upsert_data(
            typing.cast(pd.DataFrame, data['notes']),
            pd.DataFrame(columns=canonical['Notes']),
            timestamp,
        )
        if shared.verify_is_valid(data, 'notes')
        else None
    )

    return {  # return Canonical-compliant data, to be written to CSV and moved to S3, then copied to Redshift; returns None if incoming data is empty or null
        'plaintiffs': df_plaintiffs,
        'medicalfacilities': df_medicalfacilities,
        'lawfirms': df_lawfirms,
        'legalpersonnel': df_legalpersonnel,
        'cases': df_cases,
        'billings': df_billings,
        'charges': df_charges,
        'transactions': df_transactions,
        'files': df_files,
        'notes': df_notes,
    }


def ati_generate_upsert_csv(
    all_s3_data: dict[str, pd.DataFrame | None], timestamp: str
):
    files = {}
    timestamp_string = '-'.join('_'.join(timestamp.split(' ')).split(':'))
    for data in all_s3_data:
        if (
            all_s3_data[data] is None or all_s3_data[data].empty
        ):  # if data is empty or missing for a canonical object
            continue
        all_s3_data[data].to_csv(
            f'integration/integration_code/ati_{data}_{timestamp_string}.csv',
            index=False,
            quoting=1,
        )
        files[data] = (
            f'integration/integration_code/ati_{data}_{timestamp_string}.csv'
        )
    return files


def exclude_files_when_cases_not_found(
    unprocessed_files: list[str],
    new_case_ids: typing.Optional[list[str]] = None,
) -> list[str]:
    df_files = pd.DataFrame(unprocessed_files, columns=['path'])
    df_files['case_id'] = (
        df_files['path'].str.split('/').str[-1].str.split('_').str[0]
    )
    for i in range(len(df_files['case_id'])):
        if 'AccountLedger' in df_files['case_id'][i]:
            df_files['case_id'][i] = df_files['case_id'][i].replace(
                'AccountLedger', ''
            )
    files_case_ids = set(df_files['case_id'])

    allowed_case_ids = set()
    if new_case_ids:
        allowed_case_ids.update(set(new_case_ids))
    id_subset = list(
        files_case_ids.difference(allowed_case_ids)
    )  # Only use the case id from new files to query those are not in new cases
    gainid_subset = aws_operations.get_gainids_by_ati_ids(id_subset, 'cases')

    redshift_data = aws_operations.query_postgres(
        ['cases'], id_subset=gainid_subset
    )
    redshift_data_prevent_override_true = aws_operations.query_postgres(
        ['cases'],
        id_subset=gainid_subset,
        for_delete_prevent_override=True,
    )
    redshift_data_todelete_true = aws_operations.query_postgres(
        ['cases'], id_subset=gainid_subset, for_delete=True
    )
    if (
        'cases' in redshift_data_prevent_override_true
        and len(redshift_data_prevent_override_true['cases']) > 0
    ):
        redshift_data['cases'] = pd.concat(
            [
                redshift_data['cases'],
                redshift_data_prevent_override_true['cases'],
            ],
            ignore_index=True,
        )

    if (
        'cases' in redshift_data_todelete_true
        and len(redshift_data_todelete_true['cases']) > 0
    ):
        redshift_data['cases'] = pd.concat(
            [redshift_data['cases'], redshift_data_todelete_true['cases']],
            ignore_index=True,
        )

    if redshift_data and 'cases' in redshift_data:
        gainids = [case.GainId for case in redshift_data['cases'].itertuples()]
        source_ids = aws_operations.get_sourceids_by_gainids(gainids)
        if source_ids:
            source_ids = [
                source_id for source_id in source_ids if source_id is not None
            ]
            allowed_case_ids.update(source_ids)

    df_files = df_files[
        df_files['case_id'].isin(allowed_case_ids)
    ]  # Only use the files if the case id is in Redshift or new cases
    return list(df_files['path'])


def ati_generate_plaintiffs_delete_data(
    case_gainids: list[str],
) -> tuple[list[str], dict[str, str]]:
    related_plaintiff_gainids = (
        aws_operations.get_postgres_plaintiffs_using_cases(
            id_subset=case_gainids
        )
    )
    plaintiff_gainids_list = (
        aws_operations.get_postgres_delete_plaintiffs_using_cases(
            id_subset=related_plaintiff_gainids
        )
    )
    plaintiff_gainids_list_map = {
        plaintiff_gainid: 'ATI' for plaintiff_gainid in plaintiff_gainids_list
    }
    return (plaintiff_gainids_list, plaintiff_gainids_list_map)


def ati_generate_cases_delete_data(
    billings_gainids: list[str],
) -> tuple[list[str], dict[str, str]]:
    related_case_gainids = aws_operations.get_postgres_cases_using_billings(
        id_subset=billings_gainids
    )
    case_gainids_list = (
        aws_operations.get_postgres_delete_cases_using_billings(
            id_subset=related_case_gainids
        )
    )
    case_gainids_list_map = {
        case_gainid: 'ATI' for case_gainid in case_gainids_list
    }
    return (case_gainids_list, case_gainids_list_map)


def ati_generate_cases_update_settled_data(
    billings_gainids: list[str],
) -> tuple[dict[str, str], dict[str, str], dict[str, str]]:
    related_case_gainids = aws_operations.get_postgres_cases_using_billings(
        id_subset=billings_gainids
    )
    case_gainids_status_list = (
        aws_operations.get_postgres_update_settled_cases_using_billings(
            id_subset=related_case_gainids
        )
    )
    if case_gainids_status_list is None or case_gainids_status_list.empty:
        logger.error('No data fetched for settled cases.')
        return ({}, {}, {})
    # Remove all the cases that have billings with no status
    unassigned_status_case_gainids_list = (
        case_gainids_status_list[
            (case_gainids_status_list['Status'] == '')
            | (case_gainids_status_list['Status'].isnull())
        ]['GainId']
        .unique()
        .tolist()
    )
    assigned_status_case_gainids = case_gainids_status_list[
        ~case_gainids_status_list['GainId'].isin(
            unassigned_status_case_gainids_list
        )
    ]

    # After removing billings with no status, we are left with thress statuses: Settled, WriteOff, PaidByHealthInsurance
    # If the status of at least one billing is Settled, we will mark the case as settled
    settled_case_gainids_list = (
        assigned_status_case_gainids[
            assigned_status_case_gainids['Status'] == 'Settled'
        ]['GainId']
        .unique()
        .tolist()
    )
    unsettled_case_gainids = assigned_status_case_gainids[
        ~assigned_status_case_gainids['GainId'].isin(settled_case_gainids_list)
    ]
    # If the case is not settled, and at least one billing is PaidByHealthInsurance
    # we will mark the case as PaidByHealthInsurance
    paidbyhealthinsurance_case_gainids_list = unsettled_case_gainids[
        unsettled_case_gainids['Status'] == 'PaidByHealthInsurance'
    ]['GainId'].tolist()
    # If all billings are WriteOff, we will mark the case as WriteOff
    writeoff_case_gainids_list = assigned_status_case_gainids[
        (assigned_status_case_gainids['Status'] == 'WriteOff')
        & (
            ~assigned_status_case_gainids['GainId'].isin(
                paidbyhealthinsurance_case_gainids_list
            )
        )
        & (
            ~assigned_status_case_gainids['GainId'].isin(
                settled_case_gainids_list
            )
        )
    ]['GainId'].tolist()
    settled_case_gainids_map = {
        settled_case_gainid: 'ATI'
        for settled_case_gainid in settled_case_gainids_list
    }
    written_off_case_gainids_map = {
        written_off_case_gainid: 'ATI'
        for written_off_case_gainid in writeoff_case_gainids_list
    }
    paidbyhealthinsurance_case_gainids_map = {
        paidbyhealthinsurance_case_gainid: 'ATI'
        for paidbyhealthinsurance_case_gainid in paidbyhealthinsurance_case_gainids_list
    }
    return (
        settled_case_gainids_map,
        written_off_case_gainids_map,
        paidbyhealthinsurance_case_gainids_map,
    )


def ati_generate_billings_delete_data(
    df_input: pd.DataFrame | None,
) -> tuple[list[str], dict[str, str]]:
    # For ATI, deletions are on a billing level,
    # i.e. the claim to withdraw is received
    # => claim id is translated to gainid in canonical,
    # list of claim ids from ATI is enough to identify
    # to mark billings which are to be marked for deletion
    if df_input is None or df_input.empty:
        logger.error('No data fetched for billings delete.')
        return ([], {})

    claim_ids = df_input['claimid'].tolist()

    billing_gainids = aws_operations.get_gainid_records_by_ids(
        ati_ids=claim_ids,
        canonical_object='billings',
    )

    billing_gainids = list(
        set([record['gain_record'].id for record in billing_gainids])
    )

    billing_gainids_map = {
        billing_gainid: 'ATI' for billing_gainid in billing_gainids
    }
    return (billing_gainids, billing_gainids_map)


def ati_generate_billings_update_settled_data(
    df_input: pd.DataFrame | None,
) -> tuple[list[str], dict[str, str]]:
    # For ATI, settlements are on a billing level,
    # i.e. the claim to close is received
    # => claim id is translated to gainid in canonical,
    # list of claim ids from ATI is enough to identify
    # to mark billings which are to be marked for settlement
    if df_input is None or df_input.empty:
        logger.error('No data fetched for settled billings.')
        return ([], {})

    claim_ids = df_input['claimid'].tolist()

    billing_gainids = aws_operations.get_gainid_records_by_ids(
        ati_ids=claim_ids,
        canonical_object='billings',
    )

    billing_gainids = list(
        set([record['gain_record'].id for record in billing_gainids])
    )

    billing_gainids_map = {
        billing_gainid: 'ATI' for billing_gainid in billing_gainids
    }
    return (billing_gainids, billing_gainids_map)


def ati_generate_charges_delete_data(
    billing_ids: list[str],
) -> tuple[list[str], dict[str, str]]:
    # Getting charges from Redshift corresponding to the list of to-delete billings
    charge_gainids_list = (
        aws_operations.get_postgres_delete_charges_using_billings(
            id_subset=billing_ids
        )
    )
    charge_gainids_list_map = {
        charge_gainid: 'ATI' for charge_gainid in charge_gainids_list
    }
    return (charge_gainids_list, charge_gainids_list_map)


def ati_generate_charges_update_settled_data(
    billing_ids: list[str],
) -> tuple[list[str], dict[str, str]]:
    # Getting charges from Redshift corresponding to the list of to-settle billings
    charge_gainids_list = (
        aws_operations.get_postgres_update_settled_charges_using_billings(
            id_subset=billing_ids
        )
    )
    charge_gainids_list_map = {
        charge_gainid: 'ATI' for charge_gainid in charge_gainids_list
    }
    return (charge_gainids_list, charge_gainids_list_map)


def ati_generate_transactions_delete_data(
    charge_gainids: list[str],
) -> tuple[list[str], dict[str, str]]:
    # Getting transactions from Redshift corresponding to the list of to-delete charges
    transaction_gainids_list = (
        aws_operations.get_postgres_delete_transactions_using_charges(
            id_subset=charge_gainids
        )
    )
    transaction_gainids_list_map = {
        transaction_gainid: 'ATI'
        for transaction_gainid in transaction_gainids_list
    }
    return (transaction_gainids_list, transaction_gainids_list_map)


def ati_generate_transactions_update_settled_data(
    charge_gainids: list[str],
) -> tuple[list[str], dict[str, str]]:
    # Getting transactions from Redshift corresponding to the list of to-settle charges
    transaction_gainids_list = (
        aws_operations.get_postgres_update_settled_transactions_using_charges(
            id_subset=charge_gainids
        )
    )
    transaction_gainids_list_map = {
        transaction_gainid: 'ATI'
        for transaction_gainid in transaction_gainids_list
    }
    return (transaction_gainids_list, transaction_gainids_list_map)


def ati_mark_redshift_delete_data(
    timestamp: str, data: dict[str, pd.DataFrame | None]
) -> None:
    # Billings
    (
        billings_gainids,
        billings_gainids_map,
    ) = ati_generate_billings_delete_data(data.get('billings'))
    aws_operations.update_postgres_delete_flag(
        'Billings',
        billings_gainids_map,
        timestamp,
        True,
    )
    # need to update billings immediately, because finding charges, cases depends on billings being marked
    # do NOT run update after generating delete data for other objects
    (
        charges_gainids,
        charges_gainids_map,
    ) = ati_generate_charges_delete_data(billings_gainids)
    aws_operations.update_postgres_delete_flag(
        'Charges',
        charges_gainids_map,
        timestamp,
    )
    # Transactions
    _, transactions_gainids_map = ati_generate_transactions_delete_data(
        charges_gainids
    )
    aws_operations.update_postgres_delete_flag(
        'Transactions',
        transactions_gainids_map,
        timestamp,
    )
    # Cases
    case_gainids, case_gainids_map = ati_generate_cases_delete_data(
        billings_gainids
    )
    aws_operations.update_postgres_delete_flag(
        'Cases', case_gainids_map, timestamp, True
    )
    # Plaintiffs
    _, plaintiff_gainids_map = ati_generate_plaintiffs_delete_data(
        case_gainids
    )
    aws_operations.update_postgres_delete_flag(
        'Plaintiffs',
        plaintiff_gainids_map,
        timestamp,
    )
    return


def ati_mark_redshift_update_settled_data(
    timestamp: str, data: dict[str, pd.DataFrame | None]
) -> None:
    # Billings
    (
        billings_gainids,
        _,
    ) = ati_generate_billings_update_settled_data(data.get('billings'))
    billings_transactions_data = aws_operations.get_transactions_for_billings(
        billings_gainids
    )
    billings_servicing_data = aws_operations.get_postgres_servicingdatetime(
        'billings', set(billings_gainids)
    )
    if (
        billings_servicing_data is not None
        and not billings_servicing_data.empty
        and billings_transactions_data is not None
        and not billings_transactions_data.empty
    ):
        billings_transactions_data = billings_transactions_data.merge(
            billings_servicing_data[
                [
                    'gainid',
                    'servicingstartdatetime',
                    'servicingenddatetime',
                ]
            ],
            left_on='billinggainid',
            right_on='gainid',
            how='left',
        )
        billings_transactions_data.drop('gainid', axis=1, inplace=True)
    settled_billings_gainids, closed_billings_gainids = (
        [],
        [],
    )  # initialize to empty lists
    settled_transaction_descriptions = claim_settlement_configuration['ATI'][
        'Transactions'
    ]['FromDescription']['Status']['Settled']
    settled_transaction_carrier_insurance_types = (
        claim_settlement_configuration['ATI']['Transactions'][
            'FromCarrierInsuranceType'
        ]['Status']['Settled']
    )
    writeoff_transaction_descriptions = claim_settlement_configuration['ATI'][
        'Transactions'
    ]['FromDescription']['Status']['WriteOff']
    pending_payment_from_hcp_transaction_descriptions = (
        claim_settlement_configuration['ATI']['Transactions'][
            'FromDescription'
        ]['Status']['PendingPaymentFromHCP']
    )
    pending_payment_from_hcp_transaction_carrier_insurance_types = (
        claim_settlement_configuration['ATI']['Transactions'][
            'FromCarrierInsuranceType'
        ]['Status']['PendingPaymentFromHCP']
    )
    paid_by_attorney_transaction_descriptions = claim_settlement_configuration[
        'ATI'
    ]['Transactions']['FromDescription']['PaidBy']['Attorney']
    if (
        billings_transactions_data is not None
        and not billings_transactions_data.empty
    ):
        '''
        Calculate transaction sum grouped by billinggainid, transaction description, and carrier insurance type
        We are aggregating the transaction amount because sometimes we receive pairs of transactions whose amounts
        cancel each other out. We need to sum the transaction amounts to filter out the zeroed transaction descriptions
        and carrier insurance types.
        '''
        billings_transactions_data = billings_transactions_data[
            billings_transactions_data['transactionamount'] != 0
        ]
        billings_transactions_data[
            'descriptionwisegroupedtransactionamount'
        ] = billings_transactions_data.groupby(
            ['billinggainid', 'transactiondescription']
        )[
            'transactionamount'
        ].transform(
            'sum'
        )
        billings_transactions_data[
            'carrierinsurancetypewisegroupedtransactionamount'
        ] = billings_transactions_data.groupby(
            ['billinggainid', 'transactioncarrierinsurancetype']
        )[
            'transactionamount'
        ].transform(
            'sum'
        )
        billings_transactions_data = billings_transactions_data[
            billings_transactions_data[
                'descriptionwisegroupedtransactionamount'
            ]
            != 0
        ]
        billings_transactions_data['totaltransactionamount'] = (
            billings_transactions_data.groupby('billinggainid')[
                'transactionamount'
            ].transform('sum')
        )
        '''
        We use the billingtotalamount field to determine if the billing is closed.
        If the billingtotalamount is zeroed by the total transaction amount, the billing is considered closed.
        '''
        billings_transactions_data_closed_billings = (
            billings_transactions_data[
                billings_transactions_data['billingtotalamount']
                == billings_transactions_data['totaltransactionamount']
            ]
        )
        closed_billings_gainids = (
            billings_transactions_data_closed_billings['billinggainid']
            .unique()
            .tolist()
        )
        '''
        We have three statuses for a billing: Settled, WriteOff, and Paid by Health Insurance
        Based on the entity that has been paid, Settled billings are divided into two categories:
        1. Paid to Gain
        2. Paid to HCP
        Based on the paying entity, we have two subcategories for Settled billings:
        1. Paid by Attorney
        2. Paid by Auto/Commercial Insurance
        '''

        # Collect all the write-off transactions: 'Settlement - Liti%', 'Timely To Bill Patient Statement'
        billings_transactions_data_closed_billings_write_offs = (
            billings_transactions_data_closed_billings[
                billings_transactions_data_closed_billings[
                    'transactiondescription'
                ].isin(writeoff_transaction_descriptions)
            ]
        )
        billings_transactions_data_closed_billings_write_offs[
            'totalwriteoffamount'
        ] = billings_transactions_data_closed_billings_write_offs.groupby(
            'billinggainid'
        )[
            'transactionamount'
        ].transform(
            'sum'
        )
        billings_transactions_data_closed_billings_complete_write_offs = (
            billings_transactions_data_closed_billings_write_offs[
                billings_transactions_data_closed_billings_write_offs[
                    'totalwriteoffamount'
                ]
                == billings_transactions_data_closed_billings_write_offs[
                    'billingtotalamount'
                ]
            ]
        )
        '''
        If a closed billing have transactions of descriptions: 'Litigation Payment', 'Litigation Discount'
        or 'Gain Servicing%' or if the carrier insurance type is 'Auto' or 'Commercial' then it is
        considered as settled.
        '''
        billings_transactions_data_closed_billings_settled = billings_transactions_data_closed_billings[
            (
                (
                    billings_transactions_data_closed_billings[
                        'transactiondescription'
                    ].isin(settled_transaction_descriptions)
                )
                | (
                    billings_transactions_data_closed_billings[
                        'transactioncarrierinsurancetype'
                    ].isin(settled_transaction_carrier_insurance_types)
                )
            )
            & (
                ~billings_transactions_data_closed_billings[
                    'billinggainid'
                ].isin(
                    billings_transactions_data_closed_billings_complete_write_offs[
                        'billinggainid'
                    ]
                )
            )
        ]
        '''
        We need to find out whether the invoiceable amount is paid to HCP or Gain
        If for a settled case, there are 1.) Non-zero Litigation Payments/Discounts
        with postdatetime > 2023-01-14 or 2.) Non-zero Auto/Commercial Insurance transactions with postdatetime > 2023-01-14
        then the billing is considered as paid to HCP
        '''
        billings_transactions_data_closed_billings_settled_paid_to_hcp_ids = (
            (
                billings_transactions_data_closed_billings_settled[
                    (
                        (
                            (
                                billings_transactions_data_closed_billings_settled[
                                    'transactiondescription'
                                ].isin(
                                    pending_payment_from_hcp_transaction_descriptions
                                )
                            )
                            | (
                                billings_transactions_data_closed_billings_settled[
                                    'transactioncarrierinsurancetype'
                                ].isin(
                                    pending_payment_from_hcp_transaction_carrier_insurance_types
                                )
                            )
                        )
                        & (
                            (
                                billings_transactions_data_closed_billings_settled[
                                    'transactionpaymentdatetime'
                                ]
                                > billings_transactions_data_closed_billings_settled[
                                    'servicingstartdatetime'
                                ]
                            )
                            & (
                                (
                                    billings_transactions_data_closed_billings_settled[
                                        'transactionpaymentdatetime'
                                    ]
                                    < billings_transactions_data_closed_billings_settled[
                                        'servicingenddatetime'
                                    ]
                                )
                                | (
                                    billings_transactions_data_closed_billings_settled[
                                        'servicingenddatetime'
                                    ].isnull()
                                )
                            )
                        )
                    )
                ]
            )['billinggainid']
            .unique()
            .tolist()
        )
        billings_transactions_data_closed_billings_settled_paid_by_attorney_ids = (
            (
                billings_transactions_data_closed_billings_settled[
                    (
                        billings_transactions_data_closed_billings_settled[
                            'transactiondescription'
                        ].isin(paid_by_attorney_transaction_descriptions)
                    )
                ]
            )['billinggainid']
            .unique()
            .tolist()
        )

        billings_transactions_data_closed_billings_settled_paid_by_auto_commercial_insurance_ids = (
            (
                billings_transactions_data_closed_billings_settled[
                    (
                        (
                            billings_transactions_data_closed_billings_settled[
                                'transactioncarrierinsurancetype'
                            ].isin(['Auto'])
                        )
                        & (
                            ~billings_transactions_data_closed_billings_settled[
                                'billinggainid'
                            ].isin(
                                billings_transactions_data_closed_billings_settled_paid_by_attorney_ids
                            )
                        )
                    )
                ]
            )['billinggainid']
            .unique()
            .tolist()
        )
        closed_billings_blank_carrier_insurance_type_ids = (
            (
                billings_transactions_data_closed_billings[
                    (
                        billings_transactions_data_closed_billings[
                            'transactioncarrierinsurancetype'
                        ].isnull()
                    )
                    | (
                        billings_transactions_data_closed_billings[
                            'transactioncarrierinsurancetype'
                        ]
                        == ''
                    )
                ]
            )['billinggainid']
            .unique()
            .tolist()
        )
        # If a closed billing is neither settled nor written off, it is considered as paid by health insurance
        billings_transactions_data_closed_billings_paid_by_health_insurance = billings_transactions_data_closed_billings[
            (
                ~billings_transactions_data_closed_billings[
                    'billinggainid'
                ].isin(
                    billings_transactions_data_closed_billings_complete_write_offs[
                        'billinggainid'
                    ]
                )
            )
            & (
                ~billings_transactions_data_closed_billings[
                    'billinggainid'
                ].isin(
                    billings_transactions_data_closed_billings_settled[
                        'billinggainid'
                    ]
                )
            )
            & (
                ~billings_transactions_data_closed_billings[
                    'billinggainid'
                ].isin(closed_billings_blank_carrier_insurance_type_ids)
            )
        ]
        billings_transactions_data_closed_billings_paid_by_health_insurance_ids = (
            billings_transactions_data_closed_billings_paid_by_health_insurance[
                'billinggainid'
            ]
            .unique()
            .tolist()
        )
        settled_billings_gainids = (
            billings_transactions_data_closed_billings_settled['billinggainid']
            .unique()
            .tolist()
        )
        written_off_billings = (
            billings_transactions_data_closed_billings_complete_write_offs[
                'billinggainid'
            ]
            .unique()
            .tolist()
        )
        paid_by_health_insurance_billings = (
            billings_transactions_data_closed_billings_paid_by_health_insurance[
                'billinggainid'
            ]
            .unique()
            .tolist()
        )
        settled_billings_map = {
            billing_gainid: 'ATI'
            for billing_gainid in settled_billings_gainids
        }
        written_off_billings_map = {
            billing_gainid: 'ATI' for billing_gainid in written_off_billings
        }
        paid_by_health_insurance_billings_map = {
            billing_gainid: 'ATI'
            for billing_gainid in paid_by_health_insurance_billings
        }
        aws_operations.update_postgres_paid_to_flag(
            'Billings',
            'HCP',
            billings_transactions_data_closed_billings_settled_paid_to_hcp_ids,
            timestamp,
        )
        aws_operations.update_postgres_paid_by_flag(
            'Billings',
            'Attorney',
            billings_transactions_data_closed_billings_settled_paid_by_attorney_ids,
            timestamp,
        )
        aws_operations.update_postgres_paid_by_flag(
            'Billings',
            'AutoCommercialInsurance',
            billings_transactions_data_closed_billings_settled_paid_by_auto_commercial_insurance_ids,
            timestamp,
        )
        aws_operations.update_postgres_paid_by_flag(
            'Billings',
            'HealthInsurance',
            billings_transactions_data_closed_billings_paid_by_health_insurance_ids,
            timestamp,
        )
        aws_operations.update_postgres_update_settled_flag(
            'Billings',
            'Settled',
            settled_billings_map,
            timestamp,
        )
        aws_operations.update_postgres_update_settled_flag(
            'Billings',
            'WriteOff',
            written_off_billings_map,
            timestamp,
        )
        aws_operations.update_postgres_update_settled_flag(
            'Billings',
            'PaidByHealthInsurance',
            paid_by_health_insurance_billings_map,
            timestamp,
        )
    # Charges
    (
        settled_charges_gainids,
        settled_charges_gainids_map,
    ) = ati_generate_charges_update_settled_data(settled_billings_gainids)
    aws_operations.update_postgres_update_settled_flag(
        'Charges',
        'Settled',
        settled_charges_gainids_map,
        timestamp,
    )
    # Transactions
    (
        _,
        transactions_gainids_map,
    ) = ati_generate_transactions_update_settled_data(settled_charges_gainids)
    aws_operations.update_postgres_update_settled_flag(
        'Transactions',
        'Settled',
        transactions_gainids_map,
        timestamp,
    )
    # Cases
    (
        settled_case_gainids_map,
        writeoff_case_gainids_map,
        paidbyhealthinsurance_case_gainids_map,
    ) = ati_generate_cases_update_settled_data(closed_billings_gainids)
    aws_operations.update_postgres_update_settled_flag(
        'Cases',
        'Settled',
        settled_case_gainids_map,
        timestamp,
    )
    aws_operations.update_postgres_update_settled_flag(
        'Cases',
        'WriteOff',
        writeoff_case_gainids_map,
        timestamp,
    )
    aws_operations.update_postgres_update_settled_flag(
        'Cases',
        'PaidByHealthInsurance',
        paidbyhealthinsurance_case_gainids_map,
        timestamp,
    )
    return


def ati_check_within_servicing_period(
    row: pd.Series,  # pyright: ignore[reportMissingTypeArgument]
    claim_to_gainid_map: dict[typing.Hashable, typing.Any],
    gainid_to_servicing_map: dict[typing.Hashable, typing.Any],
) -> bool:
    if gainid_to_servicing_map is None or not gainid_to_servicing_map:
        return False
    gainid = claim_to_gainid_map.get(row['claimid'])
    if not gainid or gainid not in gainid_to_servicing_map:
        return False
    servicing_info = gainid_to_servicing_map[gainid]
    servicing_start = servicing_info.get('servicingstartdatetime')
    servicing_end = servicing_info.get('servicingenddatetime')
    if row['description'] != 'Litigation Payment' or servicing_start is None:
        return False
    if (
        servicing_end is None
        or pd.isnull(servicing_end)
        or servicing_end == pd.Timestamp('')
    ):
        return row['paymentdatetime'] >= pd.Timestamp(servicing_start)
    return (
        pd.Timestamp(servicing_start)
        <= row['paymentdatetime']
        <= pd.Timestamp(servicing_end)
    )
