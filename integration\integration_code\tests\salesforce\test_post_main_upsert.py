import typing
from unittest.mock import MagicMock, patch

import psycopg
import pytest
from freezegun import freeze_time

import integration.models as models
from integration.integration_code.models import types


class TestPostMainUpsert:
    @pytest.fixture
    def mock_get_sf_accounts_data(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_accounts_data'
        ) as mock:
            mock.return_value = {
                'root_name_type_map': {},
                'root_name_id_map': {
                    'ATI Physical Therapy': '',
                    'Mike <PERSON> Firm': '',
                },
                'name_type_map': {
                    'ATI Physical Therapy': '',
                    'Mike Hostilo Law Firm': '',
                },
                'name_id_map': {},
                'id_parentid_map': {},
                'locationid_id_map': {},
            }

            yield mock

    @pytest.fixture
    def mock_get_sf_plaintiffaccount_data(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_plaintiffaccount_data'
        ) as mock:
            mock.return_value = {
                'plaintiff_accounts_nameDOB': {},
                'plaintiff_account_id_DOA': {},
                'plaintiff_accounts_check_last_modified_date_before_update': {},
            }
            yield mock

    @pytest.fixture
    def mock_get_sf_medicalfacilities_data(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_medicalfacilities_data'
        ) as mock:
            mock.return_value = {
                'medicalfacility_unique_key_group': {},
                'medicalfacility_phone_group': {},
                'medicalfacility_email_group': {},
            }
            yield mock

    @pytest.fixture
    def mock_get_sf_lawfirms_data(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_lawfirms_data'
        ) as mock:
            mock.return_value = {
                'lawfirm_unique_key_group': {},
                'lawfirm_phone_group': {},
                'lawfirm_email_group': {},
            }

            yield mock

    @pytest.fixture
    def mock_get_sf_opportunity_data(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_opportunity_data'
        ) as mock:
            mock.return_value = {
                'opportunities_DOB_DOA': {},
                'opportunities_DOB_DOA_email': {},
                'opportunities_DOB_DOA_phone': {},
                'opportunities_DOB_DOA_name': {},
                'opportunities_salesforce_id_modifiedtime': {},
                'opportunity_account_id': {'001Ec00000emK49IAE': ''},
                'opportunity_attorney_id': {'001Ec00000emK49IAE': ''},
                'opportunity_case_status': {'001Ec00000emK49IAE': ''},
                'opportunity_stage_name': {},
            }

            yield mock

    @pytest.fixture
    def mock_get_sf_medicalfacilities_map(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_medicalfacilities_map'
        ) as mock:
            mock.return_value = {
                'root_name_type_map': {},
                'root_name_id_map': {},
                'name_type_map': {},
                'name_id_map': {},
                'id_parentid_map': {},
                'locationid_id_map': {
                    '0b89f4beff68d371': '123',
                    '0b89f4beff68d372': '123',
                },
            }
            yield mock

    @pytest.fixture
    def mock_get_sf_funding_data(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_funding_data'
        ) as mock:
            mock.return_value = {
                'fundings_mapping_res': {},
                'fundings_potential_duplicates_check_before_insert': {},
                'fundings_check_last_modified_date_before_update': {},
                'fundings_check_stage_sub_stage_before_update': {},
            }
            yield mock

    @pytest.fixture
    def mock_get_sf_charge_data(self):
        with patch(
            'integration.integration_code.salesforce_operations.get_sf_charge_data'
        ) as mock:
            mock.return_value = {
                'charges_billing_mapping_res': {},
                'charges_externalsource_mapping_res': {},
                'charges_salesforce_id_modifiedtime': {},
            }
            yield mock

    def validate_all_type_fields_in_dict(
        self,
        typed_dict_class: typing.Type[typing.Any],
        data_dict: types.BaseRollbackData,
    ) -> None:

        type_hints = type_hints = getattr(
            typed_dict_class, '__annotations__', {}
        )

        for field_name in type_hints:
            if field_name not in data_dict:
                raise ValueError(
                    f'{typed_dict_class.__name__} is missing field {field_name}'
                )

    @freeze_time("2023-01-01 12:00:00")
    @pytest.mark.django_db
    def test_should_post_main_upsert(
        self,
        database: psycopg.Connection[typing.Any],
        mock_get_sf_action: MagicMock,
        mock_get_sf_bulk: MagicMock,
        mock_get_sf_accounts_data: MagicMock,
        mock_get_sf_plaintiffaccount_data: MagicMock,
        mock_get_sf_medicalfacilities_data: MagicMock,
        mock_get_sf_lawfirms_data: MagicMock,
        mock_get_sf_opportunity_data: MagicMock,
        mock_get_sf_medicalfacilities_map: MagicMock,
        mock_get_sf_funding_data: MagicMock,
        mock_get_sf_charge_data: MagicMock,
        ref_files: typing.Any,
    ):
        from integration.integration_code import salesforce

        def mock_get_sf_action_value(*x: typing.Any, **y: typing.Any):
            return [
                {
                    'success': True,
                    'created': True,
                    'id': '001Ec00000emK49IAE',
                    'errors': [],
                }
            ]

        mock_get_sf_action.return_value = mock_get_sf_action_value

        base_response = {'attributes': {'url': 'url', 'type': 'type'}}
        medical_facility_rollback: types.MedicalFacilityRollbackData = {
            'id': 'sf_id_medical_manual_review',
            'Name': 'Different name',
            'Phone': '',
            'Fax': '',
            'Email__c': '',
            'Follow_up_email_for_entire_account__c': '',
            'ShippingPostalCode': '',
            'BillingStreet': '',
            'BillingCity': '',
            'BillingState': '',
            'BillingPostalCode': '',
            'Website': '',
            'Gain_ID__c': '',
            'External_Location_ID__c': '',
        }
        self.validate_all_type_fields_in_dict(
            types.MedicalFacilityRollbackData, medical_facility_rollback
        )

        charges_rollback: types.ChargesRollbackData = {
            'id': 'sf_id_charges_manual_review',
            'Date_of_Service__c': '',
            'Amount__c': '',
            'CPT_Code__c': '',
            'CPT_Modifier__c': '',
            'Non_Gain_Adjustment__c': '',
            'Non_Gain_Amount_Paid_to_Provider__c': '',
            'Reimbursement_Rate__c': '',
            'Funding__c': '',
            'Charge_Id__c': '',
            'Deductible__c': '',
            'Coinsurance__c': '',
            'Copayment__c': '',
            'Gain_Adjustment__c': '',
            'Gain_Pre_Negotiation_Amount_Paid__c': '',
        }
        self.validate_all_type_fields_in_dict(
            types.ChargesRollbackData, charges_rollback
        )

        fundings_rollback: types.FundingsRollbackData = {
            'id': 'sf_id_billings_manual_review',
            'RecordType': {'Name': 'Billing'},
            'Medical_Facility__r': {'Name': ''},
            'Medical_Location__r': {'Name': ''},
            'Partner_Account__r': {'Name': ''},
            'Name': '',
            'Medical_Claim_Number__c': '',
            'Date_of_Service__c': '',
            'Partner_Account__c': '',
            'Plaintiff__c': '',
            'Medical_Case__c': '',
            'RecordTypeId': '',
            'Non_Rollup_Charge_Amounts_Total__c': '',
            'Non_Rollup_Balance_Total__c': '',
            'Non_Rollup_Non_Gain_Adjustments_Total__c': '',
            'Non_Rollup_Non_Gain_Payments_Total__c': '',
            'Non_Rollup_Amounts_to_Partner_Total__c': '',
            'Total_Coinsurance__c': '',
            'Total_Deductible__c': '',
            'Total_Copayment__c': '',
            'Medical_Location__c': '',
            'Medical_Facility__c': '',
            'Funding_Stage__c': '',
            'Funding_Sub_Stage__c': '',
        }
        self.validate_all_type_fields_in_dict(
            types.FundingsRollbackData, fundings_rollback
        )

        opportunities_rollback: types.OpportunityRollbackData = {
            'id': 'sf_id_cases_manual_review',
            'Name': '',
            'Date_of_Birth__c': '',
            'SSN__c': '',
            'Drivers_License__c': '',
            'Gender__c': '',
            'Company_Name__c': '',
            'Home_Phone__c': '',
            'Cell_Phone__c': '',
            'Other_Phone__c': '',
            'Plaintiff_Email__c': '',
            'Address__c': '',
            'Address_2__c': '',
            'City__c': '',
            'State__c': '',
            'Zip__c': '',
            'Case_Status__c': '',
            'Date_of_Accident__c': '',
            'Description_of_Accident_Incident__c': '',
            'What_type_of_case__c': '',
            'AccountId': '',
            'Law_Firm_Account_Name__c': '',
            'Attorney__c': '',
            'StageName': '',
            'CloseDate': '',
            'Plaintiff_Account__c': '',
            'Plaintiff_Account__r': {'Name': ''},
            'Insured_s_Name__c': '',
            'Insurance_Company__c': '',
            'Insurance_Limits__c': '',
            'Insurance_Agent__c': '',
            'Insurance_Co_Address__c': '',
            'Insurance_Co_Address_2__c': '',
            'Insurance_Co_City__c': '',
            'Insurance_Co_State__c': '',
            'Insurance_Co_Zipcode__c': '',
            'Insurance_Co_Phone__c': '',
            'Insurance_Co_Fax__c': '',
            'X2_Insured_s_Name__c': '',
            'Insurance_Company_2__c': '',
            'Insurance_Limits_2__c': '',
            'X2_Zipcode__c': '',
            'Surgery_Date__c': '',
            'Surgeon__c': '',
            'Type_of_Surgery__c': '',
            'Surgery_Comments__c': '',
            'Estimated_Surgical_Charges__c': '',
            'Medical_Facility_P__c': '',
            'Medical_Facility_P__r': {'Name': ''},
            'Partner_Account__c': '',
            'Partner_Account__r': {'Name': ''},
            'Paralegal_or_Case_Manager__c': '',
            'Grand_Total_Deductible__c': '',
            'Grand_Total_Coinsurance__c': '',
            'Grand_Total_Copayment__c': '',
            'RecordTypeId': '',
            'RecordType': {'Name': ''},
            'ATI_Tail_Claim__c': '',
            'Cocounsel__c': '',
        }
        self.validate_all_type_fields_in_dict(
            types.OpportunityRollbackData, opportunities_rollback
        )

        opportunities_response = base_response | opportunities_rollback
        medical_facility_response = base_response | medical_facility_rollback
        charges_response = base_response | charges_rollback
        fundings_response = base_response | fundings_rollback

        mock_get_sf_bulk.return_value.query.return_value = [
            {
                'id': '001Ec00000emK49IAE',
                'RecordType': {'Name': 'Plaintiff'},
                'Name': 'Different name',
                'Date_of_Birth__c': '10/10/2010',
                'RecordTypeId': '1',
                'attributes': [],
            },
            {
                'id': 'sf_id_plaintiff_manual_review',
                'RecordType': {'Name': 'Plaintiff'},
                'Name': 'Different name',
                'Date_of_Birth__c': '10/10/2010',
                'RecordTypeId': '1',
                'attributes': [],
            },
            medical_facility_response,
            {
                'id': 'sf_id_lawfirms_manual_review',
                'Name': '',
                'Phone': '',
                'Fax': '',
                'Email__c': '',
                'Follow_up_email_for_entire_account__c': '',
                'BillingStreet': '',
                'BillingCity': '',
                'BillingState': '',
                'BillingPostalCode': '',
                'ShippingStreet': '',
                'ShippingCity': '',
                'ShippingState': '',
                'ShippingPostalCode': '',
                'Website': '',
                'Type_of_Law__c': '',
                'Description': '',
                'Employee_Size_Range__c': '',
                'Automatic_Case_Update_Requests__c': '',
                'attributes': [],
            },
            {
                'id': 'sf_id_legalpersonnel_manual_review',
                'FirstName': '',
                'LastName': '',
                'Title': '',
                'HomePhone': '',
                'MobilePhone': '',
                'Phone': '',
                'OtherPhone': '',
                'Fax': '',
                'Email': '',
                'Personal_Email__c': '',
                'MailingStreet': '',
                'MailingCity': '',
                'MailingState': '',
                'MailingPostalCode': '',
                'MailingCountry': '',
                'OtherStreet': '',
                'OtherCity': '',
                'OtherState': '',
                'OtherPostalCode': '',
                'OtherCountry': '',
                'AccountId': '',
                'attributes': [],
            },
            opportunities_response,
            fundings_response,
            charges_response,
            {
                'id': 'sf_id_files_manual_review',
                'Title': '',
                'Document_Type__c': '',
                'PathOnClient': '',
            },
            {
                'id': 'sf_id_notes_manual_review',
                'Title': '',
            },
        ]

        salesforce.post_main_upsert(
            test=True,
            all_data=True,
            partial_config={
                'plaintiffs': True,
                'medicalfacilities': True,
                'lawfirms': True,
                'legalpersonnel': True,
                'cases': True,
                'billings': True,
                'charges': True,
                'files': True,
                'notes': True,
            },
        )

        assert mock_get_sf_action.call_count == 10
        assert models.update_manual_review_data.objects.count() == 9

        salesforce.post_main_upsert(
            test=True,
            all_data=True,
            partial_config={
                'plaintiffs': True,
                'medicalfacilities': True,
                'lawfirms': True,
                'legalpersonnel': True,
                'cases': True,
                'billings': True,
                'charges': True,
                'files': True,
                'notes': True,
            },
        )

        assert mock_get_sf_bulk.return_value.query.call_count == 22
