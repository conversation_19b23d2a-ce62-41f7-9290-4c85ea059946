/*
 * Define and populate gainid in primary tables
 * This code adds a 'gainid' column to each primary table within the schema
 * and populates it with a unique identifier based on a 16-character substring
 * of an MD5 hash of a random value.
 */
ALTER TABLE integration.dev.billings DROP CONSTRAINT fk_caseid_billings CASCADE;
ALTER TABLE integration.dev.charges DROP CONSTRAINT fk_billingid_charges CASCADE;
ALTER TABLE integration.dev.cases DROP CONSTRAINT fk_plaintiffid_cases CASCADE;
ALTER TABLE integration.dev.cases DROP CONSTRAINT fk_attorneyid_cases CASCADE;
ALTER TABLE integration.dev.cases DROP CONSTRAINT fk_paralegalid_cases CASCADE;
ALTER TABLE integration.dev.cases DROP CONSTRAINT fk_casemanagerid_cases CASCADE;
ALTER TABLE integration.dev.cases DROP CONSTRAINT fk_cocounselid_cases CASCADE;
ALTER TABLE integration.dev.cases DROP CONSTRAINT fk_coparalegalid_cases CASCADE;
ALTER TABLE integration.dev.cases DROP CONSTRAINT fk_cocasemanagerid_cases CASCADE;
ALTER TABLE integration.dev.transactions DROP CONSTRAINT fk_chargeid_transactions CASCADE;
ALTER TABLE integration.dev.files DROP CONSTRAINT fk_plaintiffid_files CASCADE;
ALTER TABLE integration.dev.files DROP CONSTRAINT fk_caseid_files CASCADE;
ALTER TABLE integration.dev.legalpersonnel DROP CONSTRAINT fk_lawfirmid_legalpersonnel CASCADE;
ALTER TABLE integration.dev.surgery DROP CONSTRAINT fk_caseid_surgery CASCADE;

ALTER TABLE integration.dev.billings DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.charges DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.cases DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.transactions DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.files DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.lawfirms DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.legalpersonnel DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.plaintiffs DROP COLUMN gainid CASCADE;
ALTER TABLE integration.dev.surgery DROP COLUMN gainid CASCADE;

ALTER TABLE integration.dev.billings ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.billings SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.charges ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.charges SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.cases ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.cases SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.transactions ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.transactions SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.files ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.files SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.lawfirms ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.lawfirms SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.legalpersonnel ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.legalpersonnel SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.plaintiffs ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.plaintiffs SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.dev.surgery ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.dev.surgery SET gainid = LEFT(MD5(random()::text), 16);

-- Adding UNIQUE constraints
ALTER TABLE integration.dev.billings ADD CONSTRAINT unique_gainid_billings PRIMARY KEY (gainid);
ALTER TABLE integration.dev.charges ADD CONSTRAINT unique_gainid_charges PRIMARY KEY (gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT unique_gainid_cases PRIMARY KEY (gainid);
ALTER TABLE integration.dev.transactions ADD CONSTRAINT unique_gainid_transactions PRIMARY KEY (gainid);
ALTER TABLE integration.dev.files ADD CONSTRAINT unique_gainid_files PRIMARY KEY (gainid);
ALTER TABLE integration.dev.lawfirms ADD CONSTRAINT unique_gainid_lawfirms PRIMARY KEY (gainid);
ALTER TABLE integration.dev.plaintiffs ADD CONSTRAINT unique_gainid_plaintiffs PRIMARY KEY (gainid);
ALTER TABLE integration.dev.legalpersonnel ADD CONSTRAINT unique_gainid_legalpersonnel PRIMARY KEY (gainid);
ALTER TABLE integration.dev.surgery ADD CONSTRAINT unique_gainid_surgery PRIMARY KEY (gainid);

-- Adding FOREIGN KEY constraints
ALTER TABLE integration.dev.billings ADD CONSTRAINT fk_caseid_billings FOREIGN KEY (caseid) REFERENCES integration.dev.cases(gainid);
ALTER TABLE integration.dev.charges ADD CONSTRAINT fk_billingid_charges FOREIGN KEY (billingid) REFERENCES integration.dev.billings(gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT fk_plaintiffid_cases FOREIGN KEY (plaintiffid) REFERENCES integration.dev.plaintiffs(gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT fk_attorneyid_cases FOREIGN KEY (attorneyid) REFERENCES integration.dev.legalpersonnel(gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT fk_paralegalid_cases FOREIGN KEY (paralegalid) REFERENCES integration.dev.legalpersonnel(gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT fk_casemanagerid_cases FOREIGN KEY (casemanagerid) REFERENCES integration.dev.legalpersonnel(gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT fk_cocounselid_cases FOREIGN KEY (cocounselid) REFERENCES integration.dev.legalpersonnel(gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT fk_coparalegalid_cases FOREIGN KEY (coparalegalid) REFERENCES integration.dev.legalpersonnel(gainid);
ALTER TABLE integration.dev.cases ADD CONSTRAINT fk_cocasemanagerid_cases FOREIGN KEY (cocasemanagerid) REFERENCES integration.dev.legalpersonnel(gainid);
ALTER TABLE integration.dev.transactions ADD CONSTRAINT fk_chargeid_transactions FOREIGN KEY (chargeid) REFERENCES integration.dev.charges(gainid);
ALTER TABLE integration.dev.files ADD CONSTRAINT fk_plaintiffid_files FOREIGN KEY (plaintiffid) REFERENCES integration.dev.plaintiffs(gainid);
ALTER TABLE integration.dev.files ADD CONSTRAINT fk_caseid_files FOREIGN KEY (caseid) REFERENCES integration.dev.cases(gainid);
ALTER TABLE integration.dev.legalpersonnel ADD CONSTRAINT fk_lawfirmid_legalpersonnel FOREIGN KEY (lawfirmid) REFERENCES integration.dev.lawfirms(gainid);
ALTER TABLE integration.dev.surgery ADD CONSTRAINT fk_caseid_surgery FOREIGN KEY (caseid) REFERENCES integration.dev.cases(gainid);




/*
 * Define and populate gainid in primary tables
 * This code adds a 'gainid' column to each primary table within the schema
 * and populates it with a unique identifier based on a 16-character substring
 * of an MD5 hash of a random value.
 */
ALTER TABLE integration.staging.billings DROP CONSTRAINT fk_caseid_billings CASCADE;
ALTER TABLE integration.staging.charges DROP CONSTRAINT fk_billingid_charges CASCADE;
ALTER TABLE integration.staging.cases DROP CONSTRAINT fk_plaintiffid_cases CASCADE;
ALTER TABLE integration.staging.cases DROP CONSTRAINT fk_attorneyid_cases CASCADE;
ALTER TABLE integration.staging.cases DROP CONSTRAINT fk_paralegalid_cases CASCADE;
ALTER TABLE integration.staging.cases DROP CONSTRAINT fk_casemanagerid_cases CASCADE;
ALTER TABLE integration.staging.cases DROP CONSTRAINT fk_cocounselid_cases CASCADE;
ALTER TABLE integration.staging.cases DROP CONSTRAINT fk_coparalegalid_cases CASCADE;
ALTER TABLE integration.staging.cases DROP CONSTRAINT fk_cocasemanagerid_cases CASCADE;
ALTER TABLE integration.staging.transactions DROP CONSTRAINT fk_chargeid_transactions CASCADE;
ALTER TABLE integration.staging.files DROP CONSTRAINT fk_plaintiffid_files CASCADE;
ALTER TABLE integration.staging.files DROP CONSTRAINT fk_caseid_files CASCADE;
ALTER TABLE integration.staging.legalpersonnel DROP CONSTRAINT fk_lawfirmid_legalpersonnel CASCADE;
ALTER TABLE integration.staging.surgery DROP CONSTRAINT fk_caseid_surgery CASCADE;

ALTER TABLE integration.staging.billings DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.charges DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.cases DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.transactions DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.files DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.lawfirms DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.legalpersonnel DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.plaintiffs DROP COLUMN gainid CASCADE;
ALTER TABLE integration.staging.surgery DROP COLUMN gainid CASCADE;

ALTER TABLE integration.staging.billings ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.billings SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.charges ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.charges SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.cases ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.cases SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.transactions ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.transactions SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.files ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.files SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.lawfirms ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.lawfirms SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.legalpersonnel ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.legalpersonnel SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.plaintiffs ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.plaintiffs SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.staging.surgery ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.staging.surgery SET gainid = LEFT(MD5(random()::text), 16);

-- Adding UNIQUE constraints
ALTER TABLE integration.staging.billings ADD CONSTRAINT unique_gainid_billings PRIMARY KEY (gainid);
ALTER TABLE integration.staging.charges ADD CONSTRAINT unique_gainid_charges PRIMARY KEY (gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT unique_gainid_cases PRIMARY KEY (gainid);
ALTER TABLE integration.staging.transactions ADD CONSTRAINT unique_gainid_transactions PRIMARY KEY (gainid);
ALTER TABLE integration.staging.files ADD CONSTRAINT unique_gainid_files PRIMARY KEY (gainid);
ALTER TABLE integration.staging.lawfirms ADD CONSTRAINT unique_gainid_lawfirms PRIMARY KEY (gainid);
ALTER TABLE integration.staging.plaintiffs ADD CONSTRAINT unique_gainid_plaintiffs PRIMARY KEY (gainid);
ALTER TABLE integration.staging.legalpersonnel ADD CONSTRAINT unique_gainid_legalpersonnel PRIMARY KEY (gainid);
ALTER TABLE integration.staging.surgery ADD CONSTRAINT unique_gainid_surgery PRIMARY KEY (gainid);

-- Adding FOREIGN KEY constraints
ALTER TABLE integration.staging.billings ADD CONSTRAINT fk_caseid_billings FOREIGN KEY (caseid) REFERENCES integration.staging.cases(gainid);
ALTER TABLE integration.staging.charges ADD CONSTRAINT fk_billingid_charges FOREIGN KEY (billingid) REFERENCES integration.staging.billings(gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT fk_plaintiffid_cases FOREIGN KEY (plaintiffid) REFERENCES integration.staging.plaintiffs(gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT fk_attorneyid_cases FOREIGN KEY (attorneyid) REFERENCES integration.staging.legalpersonnel(gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT fk_paralegalid_cases FOREIGN KEY (paralegalid) REFERENCES integration.staging.legalpersonnel(gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT fk_casemanagerid_cases FOREIGN KEY (casemanagerid) REFERENCES integration.staging.legalpersonnel(gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT fk_cocounselid_cases FOREIGN KEY (cocounselid) REFERENCES integration.staging.legalpersonnel(gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT fk_coparalegalid_cases FOREIGN KEY (coparalegalid) REFERENCES integration.staging.legalpersonnel(gainid);
ALTER TABLE integration.staging.cases ADD CONSTRAINT fk_cocasemanagerid_cases FOREIGN KEY (cocasemanagerid) REFERENCES integration.staging.legalpersonnel(gainid);
ALTER TABLE integration.staging.transactions ADD CONSTRAINT fk_chargeid_transactions FOREIGN KEY (chargeid) REFERENCES integration.staging.charges(gainid);
ALTER TABLE integration.staging.files ADD CONSTRAINT fk_plaintiffid_files FOREIGN KEY (plaintiffid) REFERENCES integration.staging.plaintiffs(gainid);
ALTER TABLE integration.staging.files ADD CONSTRAINT fk_caseid_files FOREIGN KEY (caseid) REFERENCES integration.staging.cases(gainid);
ALTER TABLE integration.staging.legalpersonnel ADD CONSTRAINT fk_lawfirmid_legalpersonnel FOREIGN KEY (lawfirmid) REFERENCES integration.staging.lawfirms(gainid);
ALTER TABLE integration.staging.surgery ADD CONSTRAINT fk_caseid_surgery FOREIGN KEY (caseid) REFERENCES integration.staging.cases(gainid);




/*
 * Define and populate gainid in primary tables
 * This code adds a 'gainid' column to each primary table within the schema
 * and populates it with a unique identifier based on a 16-character substring
 * of an MD5 hash of a random value.
 */
ALTER TABLE integration.prod.billings DROP CONSTRAINT fk_caseid_billings CASCADE;
ALTER TABLE integration.prod.charges DROP CONSTRAINT fk_billingid_charges CASCADE;
ALTER TABLE integration.prod.cases DROP CONSTRAINT fk_plaintiffid_cases CASCADE;
ALTER TABLE integration.prod.cases DROP CONSTRAINT fk_attorneyid_cases CASCADE;
ALTER TABLE integration.prod.cases DROP CONSTRAINT fk_paralegalid_cases CASCADE;
ALTER TABLE integration.prod.cases DROP CONSTRAINT fk_casemanagerid_cases CASCADE;
ALTER TABLE integration.prod.cases DROP CONSTRAINT fk_cocounselid_cases CASCADE;
ALTER TABLE integration.prod.cases DROP CONSTRAINT fk_coparalegalid_cases CASCADE;
ALTER TABLE integration.prod.cases DROP CONSTRAINT fk_cocasemanagerid_cases CASCADE;
ALTER TABLE integration.prod.transactions DROP CONSTRAINT fk_chargeid_transactions CASCADE;
ALTER TABLE integration.prod.files DROP CONSTRAINT fk_plaintiffid_files CASCADE;
ALTER TABLE integration.prod.files DROP CONSTRAINT fk_caseid_files CASCADE;
ALTER TABLE integration.prod.legalpersonnel DROP CONSTRAINT fk_lawfirmid_legalpersonnel CASCADE;
ALTER TABLE integration.prod.surgery DROP CONSTRAINT fk_caseid_surgery CASCADE;

ALTER TABLE integration.prod.billings DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.charges DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.cases DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.transactions DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.files DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.lawfirms DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.legalpersonnel DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.plaintiffs DROP COLUMN gainid CASCADE;
ALTER TABLE integration.prod.surgery DROP COLUMN gainid CASCADE;

ALTER TABLE integration.prod.billings ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.billings SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.charges ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.charges SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.cases ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.cases SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.transactions ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.transactions SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.files ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.files SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.lawfirms ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.lawfirms SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.legalpersonnel ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.legalpersonnel SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.plaintiffs ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.plaintiffs SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.prod.surgery ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.prod.surgery SET gainid = LEFT(MD5(random()::text), 16);

-- Adding UNIQUE constraints
ALTER TABLE integration.prod.billings ADD CONSTRAINT unique_gainid_billings PRIMARY KEY (gainid);
ALTER TABLE integration.prod.charges ADD CONSTRAINT unique_gainid_charges PRIMARY KEY (gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT unique_gainid_cases PRIMARY KEY (gainid);
ALTER TABLE integration.prod.transactions ADD CONSTRAINT unique_gainid_transactions PRIMARY KEY (gainid);
ALTER TABLE integration.prod.files ADD CONSTRAINT unique_gainid_files PRIMARY KEY (gainid);
ALTER TABLE integration.prod.lawfirms ADD CONSTRAINT unique_gainid_lawfirms PRIMARY KEY (gainid);
ALTER TABLE integration.prod.plaintiffs ADD CONSTRAINT unique_gainid_plaintiffs PRIMARY KEY (gainid);
ALTER TABLE integration.prod.legalpersonnel ADD CONSTRAINT unique_gainid_legalpersonnel PRIMARY KEY (gainid);
ALTER TABLE integration.prod.surgery ADD CONSTRAINT unique_gainid_surgery PRIMARY KEY (gainid);

-- Adding FOREIGN KEY constraints
ALTER TABLE integration.prod.billings ADD CONSTRAINT fk_caseid_billings FOREIGN KEY (caseid) REFERENCES integration.prod.cases(gainid);
ALTER TABLE integration.prod.charges ADD CONSTRAINT fk_billingid_charges FOREIGN KEY (billingid) REFERENCES integration.prod.billings(gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT fk_plaintiffid_cases FOREIGN KEY (plaintiffid) REFERENCES integration.prod.plaintiffs(gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT fk_attorneyid_cases FOREIGN KEY (attorneyid) REFERENCES integration.prod.legalpersonnel(gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT fk_paralegalid_cases FOREIGN KEY (paralegalid) REFERENCES integration.prod.legalpersonnel(gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT fk_casemanagerid_cases FOREIGN KEY (casemanagerid) REFERENCES integration.prod.legalpersonnel(gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT fk_cocounselid_cases FOREIGN KEY (cocounselid) REFERENCES integration.prod.legalpersonnel(gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT fk_coparalegalid_cases FOREIGN KEY (coparalegalid) REFERENCES integration.prod.legalpersonnel(gainid);
ALTER TABLE integration.prod.cases ADD CONSTRAINT fk_cocasemanagerid_cases FOREIGN KEY (cocasemanagerid) REFERENCES integration.prod.legalpersonnel(gainid);
ALTER TABLE integration.prod.transactions ADD CONSTRAINT fk_chargeid_transactions FOREIGN KEY (chargeid) REFERENCES integration.prod.charges(gainid);
ALTER TABLE integration.prod.files ADD CONSTRAINT fk_plaintiffid_files FOREIGN KEY (plaintiffid) REFERENCES integration.prod.plaintiffs(gainid);
ALTER TABLE integration.prod.files ADD CONSTRAINT fk_caseid_files FOREIGN KEY (caseid) REFERENCES integration.prod.cases(gainid);
ALTER TABLE integration.prod.legalpersonnel ADD CONSTRAINT fk_lawfirmid_legalpersonnel FOREIGN KEY (lawfirmid) REFERENCES integration.prod.lawfirms(gainid);
ALTER TABLE integration.prod.surgery ADD CONSTRAINT fk_caseid_surgery FOREIGN KEY (caseid) REFERENCES integration.prod.cases(gainid);




/*
 * Define and populate gainid in primary tables
 * This code adds a 'gainid' column to each primary table within the schema
 * and populates it with a unique identifier based on a 16-character substring
 * of an MD5 hash of a random value.
 */
ALTER TABLE integration.main.billings DROP CONSTRAINT fk_caseid_billings CASCADE;
ALTER TABLE integration.main.charges DROP CONSTRAINT fk_billingid_charges CASCADE;
ALTER TABLE integration.main.cases DROP CONSTRAINT fk_plaintiffid_cases CASCADE;
ALTER TABLE integration.main.cases DROP CONSTRAINT fk_attorneyid_cases CASCADE;
ALTER TABLE integration.main.cases DROP CONSTRAINT fk_paralegalid_cases CASCADE;
ALTER TABLE integration.main.cases DROP CONSTRAINT fk_casemanagerid_cases CASCADE;
ALTER TABLE integration.main.cases DROP CONSTRAINT fk_cocounselid_cases CASCADE;
ALTER TABLE integration.main.cases DROP CONSTRAINT fk_coparalegalid_cases CASCADE;
ALTER TABLE integration.main.cases DROP CONSTRAINT fk_cocasemanagerid_cases CASCADE;
ALTER TABLE integration.main.transactions DROP CONSTRAINT fk_chargeid_transactions CASCADE;
ALTER TABLE integration.main.files DROP CONSTRAINT fk_plaintiffid_files CASCADE;
ALTER TABLE integration.main.files DROP CONSTRAINT fk_caseid_files CASCADE;
ALTER TABLE integration.main.legalpersonnel DROP CONSTRAINT fk_lawfirmid_legalpersonnel CASCADE;
ALTER TABLE integration.main.surgery DROP CONSTRAINT fk_caseid_surgery CASCADE;

ALTER TABLE integration.main.billings DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.charges DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.cases DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.transactions DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.files DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.lawfirms DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.legalpersonnel DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.plaintiffs DROP COLUMN gainid CASCADE;
ALTER TABLE integration.main.surgery DROP COLUMN gainid CASCADE;

ALTER TABLE integration.main.billings ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.billings SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.charges ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.charges SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.cases ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.cases SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.transactions ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.transactions SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.files ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.files SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.lawfirms ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.lawfirms SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.legalpersonnel ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.legalpersonnel SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.plaintiffs ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.plaintiffs SET gainid = LEFT(MD5(random()::text), 16);

ALTER TABLE integration.main.surgery ADD COLUMN gainid VARCHAR(16) NOT NULL DEFAULT '';
UPDATE integration.main.surgery SET gainid = LEFT(MD5(random()::text), 16);

-- Adding UNIQUE constraints
ALTER TABLE integration.main.billings ADD CONSTRAINT unique_gainid_billings PRIMARY KEY (gainid);
ALTER TABLE integration.main.charges ADD CONSTRAINT unique_gainid_charges PRIMARY KEY (gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT unique_gainid_cases PRIMARY KEY (gainid);
ALTER TABLE integration.main.transactions ADD CONSTRAINT unique_gainid_transactions PRIMARY KEY (gainid);
ALTER TABLE integration.main.files ADD CONSTRAINT unique_gainid_files PRIMARY KEY (gainid);
ALTER TABLE integration.main.lawfirms ADD CONSTRAINT unique_gainid_lawfirms PRIMARY KEY (gainid);
ALTER TABLE integration.main.plaintiffs ADD CONSTRAINT unique_gainid_plaintiffs PRIMARY KEY (gainid);
ALTER TABLE integration.main.legalpersonnel ADD CONSTRAINT unique_gainid_legalpersonnel PRIMARY KEY (gainid);
ALTER TABLE integration.main.surgery ADD CONSTRAINT unique_gainid_surgery PRIMARY KEY (gainid);

-- Adding FOREIGN KEY constraints
ALTER TABLE integration.main.billings ADD CONSTRAINT fk_caseid_billings FOREIGN KEY (caseid) REFERENCES integration.main.cases(gainid);
ALTER TABLE integration.main.charges ADD CONSTRAINT fk_billingid_charges FOREIGN KEY (billingid) REFERENCES integration.main.billings(gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT fk_plaintiffid_cases FOREIGN KEY (plaintiffid) REFERENCES integration.main.plaintiffs(gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT fk_attorneyid_cases FOREIGN KEY (attorneyid) REFERENCES integration.main.legalpersonnel(gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT fk_paralegalid_cases FOREIGN KEY (paralegalid) REFERENCES integration.main.legalpersonnel(gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT fk_casemanagerid_cases FOREIGN KEY (casemanagerid) REFERENCES integration.main.legalpersonnel(gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT fk_cocounselid_cases FOREIGN KEY (cocounselid) REFERENCES integration.main.legalpersonnel(gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT fk_coparalegalid_cases FOREIGN KEY (coparalegalid) REFERENCES integration.main.legalpersonnel(gainid);
ALTER TABLE integration.main.cases ADD CONSTRAINT fk_cocasemanagerid_cases FOREIGN KEY (cocasemanagerid) REFERENCES integration.main.legalpersonnel(gainid);
ALTER TABLE integration.main.transactions ADD CONSTRAINT fk_chargeid_transactions FOREIGN KEY (chargeid) REFERENCES integration.main.charges(gainid);
ALTER TABLE integration.main.files ADD CONSTRAINT fk_plaintiffid_files FOREIGN KEY (plaintiffid) REFERENCES integration.main.plaintiffs(gainid);
ALTER TABLE integration.main.files ADD CONSTRAINT fk_caseid_files FOREIGN KEY (caseid) REFERENCES integration.main.cases(gainid);
ALTER TABLE integration.main.legalpersonnel ADD CONSTRAINT fk_lawfirmid_legalpersonnel FOREIGN KEY (lawfirmid) REFERENCES integration.main.lawfirms(gainid);
ALTER TABLE integration.main.surgery ADD CONSTRAINT fk_caseid_surgery FOREIGN KEY (caseid) REFERENCES integration.main.cases(gainid);