# pyright: reportAttributeAccessIssue=error,reportMissingTypeArgument=error,reportMissingParameterType=error
import collections

import django.conf
import rest_framework.decorators
import rest_framework.permissions
import rest_framework.request
import rest_framework.response
from django_q.tasks import async_task, fetch

import integration.exceptions
from integration.integration_code import aws_operations, context, logger_config
from integration.integration_code.models import views

logger = logger_config.get_logger()

app_config = django.conf.settings
app_env_name = app_config.APP_ENV_NAME  # get the app environment
test = (
    False if app_env_name == 'Prod' else True
)  # app environment determines whether to use test or prod

# Create your views here.

# Mapping of exception types to status codes
EXCEPTION_STATUS_CODES = {
    integration.exceptions.IntegrationExternalAuthenticationException: 500,
    integration.exceptions.IntegrationExternalConnectionException: 500,
    integration.exceptions.IntegrationExternalTimeoutException: 504,
    integration.exceptions.IntegrationExternalDataException: 500,
    integration.exceptions.IntegrationInternalBadRequestException: 400,
    integration.exceptions.IntegrationExternalBadRequestException: 500,
    integration.exceptions.IntegrationInternalException: 500,
}


def convert_boolean_val(s: str | bool):
    if isinstance(s, (bool)):  # if boolean value, return as is
        return s
    val = (
        True
        if s.lower() in ['true', '1', 't', 'y', 'yes']
        else False if s.lower() in ['false', '0', 'f', 'n', 'no'] else None
    )  # convert string to boolean
    if val is None:  # if bad data passed, raise exception
        raise ValueError(f'Cannot covert {s} to a bool')
    return val  # else return booolean


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def filevine_upsert(request: rest_framework.request.Request):
    from .integration_code import filevine, local_operations

    global test
    try:
        test = convert_boolean_val(
            request.query_params.get('test', test)
        )  # If test is not passed, use global test
        name: str | None = request.query_params.get('name', None)
        if not name:
            raise integration.exceptions.IntegrationInternalBadRequestException(
                logger_config.AuditLog(
                    route_section='Filevine_Upsert View',
                    additional_details=None,
                    is_error=True,
                    exception_message='No parameter `name` passed',
                )
            )
        context.request_context.set(
            {'route': f'{name.title()}_Filevine_to_Redshift_Upsert'}
        )
        all_data = convert_boolean_val(
            request.query_params.get('all_data', False)
        )
        skip_files = convert_boolean_val(
            request.query_params.get('skip_files', False)
        )
        cases_update_flag = convert_boolean_val(
            request.query_params.get('cases_update_flag', True)
        )
        if request.method == 'GET':
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            params: views.FileVineParams = {'limit': limit, 'offset': offset}
            filevine.get_main_upsert(
                test,
                name,
                all_data,
                skip_files,
                cases_update_flag,
                params,
            )
        return rest_framework.response.Response(
            'Arm completed successfully', 200
        )
    except tuple(EXCEPTION_STATUS_CODES) as e:
        # Get the status code from the mapping, defaulting to 500 if not found
        status_code = EXCEPTION_STATUS_CODES.get(type(e), 500)
        return rest_framework.response.Response(str(e), status=status_code)
    finally:
        # Clean temp_downloads directory after each request
        # to avoid unnecessary storage usage
        local_operations.clean_up_temp_downloads()


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def athenahealth_upsert(request: rest_framework.request.Request):
    from .integration_code import athenahealth

    global test
    name = request.query_params['name']
    all_data = convert_boolean_val(request.query_params.get('all_data', False))
    if request.method == 'GET':
        athenahealth.get_main_upsert(name, test, all_data)
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def jopari_upsert(request: rest_framework.request.Request):
    from .integration_code import jopari

    all_data = convert_boolean_val(request.query_params.get('all_data', False))
    move_flag = convert_boolean_val(
        request.query_params.get('move_flag', True)
    )
    charges_update_flag = convert_boolean_val(
        request.query_params.get('charges_update_flag', True)
    )
    billings_update_flag = convert_boolean_val(
        request.query_params.get('billings_update_flag', True)
    )
    cases_update_flag = convert_boolean_val(
        request.query_params.get('cases_update_flag', True)
    )
    if request.method == 'GET':
        jopari.get_main_upsert(
            move_flag,
            charges_update_flag,
            billings_update_flag,
            cases_update_flag,
            all_data,
        )
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['GET'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def get_route_last_run_time(request: rest_framework.request.Request):
    route_name = str(request.query_params.get('route_name'))

    if not route_name:
        return rest_framework.response.Response(
            {"error": "Missing required parameter: route_name"},
            status=400,
        )

    # Use current UTC time
    current_time = aws_operations.get_last_run_time(route_name)

    return rest_framework.response.Response(
        {route_name: current_time}, status=200
    )


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def salesforce_upsert(request: rest_framework.request.Request):
    from .integration_code import salesforce

    global test
    context.request_context.set({'route': 'salesforce_upsert'})

    try:
        partial_config: dict[str, bool] = (
            request.data
            if request.data
            else {
                'plaintiffs': True,
                'lawfirms': True,
                'legalpersonnel': True,
                'cases': True,
                'billings': True,
                'charges': True,
                'files': True,
                'notes': True,
            }
        )
        partial_config = collections.defaultdict(lambda: True, partial_config)
        all_data = convert_boolean_val(
            request.query_params.get('all_data', False)
        )

        if request.method == 'POST':
            salesforce.post_main_upsert(test, partial_config, all_data)
            salesforce.post_upsert_update(test, all_data)

        logger.info("Salesforce Upsert Complete")
        return rest_framework.response.Response('1', 200)

    except Exception as e:
        if str(e) == "LOCK_NOT_ACQUIRED":
            logger.error(
                str(e),
                extra={
                    'additional_details': 'Salesforce Upsert Conflict',
                },
            )

            return rest_framework.response.Response(
                "Another upsert is already running.", status=429
            )

        logger.error(
            str(e),
            extra={
                'additional_details': 'Salesforce Upsert Failed',
            },
        )
        return rest_framework.response.Response('0', status=500)


@rest_framework.decorators.api_view(['PATCH'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def duplicates_from_salesforce(request: rest_framework.request.Request):
    from .integration_code import salesforce_local_operations

    message = "Records Not Found."
    status_code = 404
    if request.method == 'PATCH':
        data: dict[str, list[dict[str, str]]] = request.data
        if data:
            try:
                salesforce_local_operations.duplicates_from_salesforce(data)
            except ValueError as ex:
                message = f"Failed to update following data: {str(ex)}"
                status_code = 404
            except KeyError as ex:
                message = f"Some records do not contain key: {str(ex)}."
                status_code = 404
            else:
                message = "Successfully updated records."
                status_code = 200
    response_body = {"message": message, "code": status_code}
    return rest_framework.response.Response(response_body, status=status_code)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def remove_salesforce_records(
    request: rest_framework.request.Request,
):
    from .integration_code import salesforce_local_operations

    message = "Records Not Found."
    status_code = 404

    if not isinstance(request.data, dict) or not request.data:
        return rest_framework.response.Response(
            {"eror": "Request body is empty or invalid"}, status=400
        )

    sf_ids: list[str] = request.data['sf_ids']

    if not isinstance(sf_ids, list) or not sf_ids:
        return rest_framework.response.Response(
            {"message": "Invalid or missing 'sf_ids' list.", "code": 400},
            status=400,
        )

    try:
        salesforce_local_operations.remove_sf_records(sf_ids)
    except ValueError as ex:
        message = f"Failed to remove records: {str(ex)}"
        status_code = 400
    except KeyError as ex:
        message = f"Missing expected key in data: {str(ex)}"
        status_code = 400
    else:
        message = "Successfully removed records."
        status_code = 200

    response_body = {"message": message, "code": status_code}
    return rest_framework.response.Response(response_body, status=status_code)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def salesforce_delete(request: rest_framework.request.Request):
    from .integration_code import salesforce

    global test
    context.request_context.set({'route': 'salesforce_delete'})
    partial_config: dict[str, bool] = (
        request.data
        if request.data
        else {
            'plaintiffs': True,
            'legalpersonnel': True,
            'cases': True,
            'billings': True,
            'charges': True,
            'files': True,
        }
    )

    partial_config = collections.defaultdict(
        lambda: True, partial_config
    )  # default/fallback value is True
    all_data = convert_boolean_val(request.query_params.get('all_data', False))
    if request.method == 'POST':
        salesforce.post_main_delete(test, partial_config, all_data)
        logger.info("Salesforce Delete Complete")
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def salesforce_update_settled(request: rest_framework.request.Request):
    from .integration_code import salesforce

    global test
    partial_config: dict[str, bool] = (
        request.data
        if request.data
        else {
            'billings': True,
        }
    )
    partial_config = collections.defaultdict(
        lambda: True, partial_config
    )  # default/fallback value is True
    all_data = convert_boolean_val(request.query_params.get('all_data', False))

    salesforce.post_main_update_settled(test, partial_config, all_data)
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def salesforce_post_upsert_update(request: rest_framework.request.Request):
    from .integration_code import salesforce

    global test
    all_data = convert_boolean_val(request.query_params.get('all_data', False))
    if request.method == 'POST':
        salesforce.post_upsert_update(test, all_data)
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def ati_upsert(request: rest_framework.request.Request):
    from .integration_code import ati

    global test
    try:
        all_data = convert_boolean_val(
            request.query_params.get('all_data', False)
        )
        move_flag = convert_boolean_val(
            request.query_params.get('move_flag', True)
        )
        charges_update_flag = convert_boolean_val(
            request.query_params.get('charges_update_flag', True)
        )
        billings_update_flag = convert_boolean_val(
            request.query_params.get('billings_update_flag', True)
        )
        cases_update_flag = convert_boolean_val(
            request.query_params.get('cases_update_flag', True)
        )
        if request.method == 'GET':
            ati.get_main_upsert(
                move_flag,
                charges_update_flag,
                billings_update_flag,
                cases_update_flag,
                test,
                all_data,
            )
        if request.method == 'POST':
            ati.post_main_upsert(all_data)
        logger.info("ATI Upsert Complete")
        return rest_framework.response.Response('1', 200)
    except Exception as e:
        logger.error(
            e,
            extra={
                'additional_details': 'ATI Upsert Failed',
            },
        )
        return rest_framework.response.Response('0', status=500)


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def ati_delete(request: rest_framework.request.Request):
    from .integration_code import ati

    global test
    all_data = convert_boolean_val(request.query_params.get('all_data', False))
    move_flag = convert_boolean_val(
        request.query_params.get('move_flag', True)
    )
    if request.method == 'GET':
        ati.get_main_delete(move_flag, test, all_data)
    if request.method == 'POST':
        ati.post_main_delete(all_data)
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def ati_update_settled(request: rest_framework.request.Request):
    from .integration_code import ati

    global test
    all_data = convert_boolean_val(request.query_params.get('all_data', False))
    move_flag = convert_boolean_val(
        request.query_params.get('move_flag', True)
    )
    if request.method == 'GET':
        ati.get_main_update_settled(move_flag, test, all_data)
    if request.method == 'POST':
        ati.post_main_update_settled(all_data)
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def update_charges_from_transactions(request: rest_framework.request.Request):
    from .integration_code import local_operations

    global test

    if request.method == 'POST':
        if not isinstance(request.data, dict) or not request.data:
            return rest_framework.response.Response(
                {"error": "Request body is empty or invalid"}, status=400
            )
        request_data: dict[str, list[dict[str, str]]] = request.data

        transaction_data = request_data['Transactions']
        transaction_id_subset = [item['gainid'] for item in transaction_data]
        local_operations.update_charges_from_transactions(
            transaction_id_subset
        )
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def update_billings_from_charges(request: rest_framework.request.Request):
    from .integration_code import local_operations

    if request.method == 'POST':
        if not isinstance(request.data, dict) or not request.data:
            return rest_framework.response.Response(
                {"error": "Request body is empty or invalid"}, status=400
            )
        request_data: dict[str, list[dict[str, str]]] = request.data

        charge_data = request_data['Charges']
        charge_id_subset = [item['gainid'] for item in charge_data]
        local_operations.update_billings_from_charges(charge_id_subset)
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def update_cases_from_billings(request: rest_framework.request.Request):
    from .integration_code import local_operations

    if request.method == 'POST':
        if not isinstance(request.data, dict) or not request.data:
            return rest_framework.response.Response(
                {"error": "Request body is empty or invalid"}, status=400
            )
        request_data: dict[str, list[dict[str, str]]] = request.data

        billing_data = request_data['Billings']
        billing_id_subset = [item['gainid'] for item in billing_data]
        local_operations.update_cases_from_billings(billing_id_subset)
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def update_cases_from_insurances(request: rest_framework.request.Request):
    from .integration_code import local_operations

    if request.method == 'POST':
        local_operations.update_cases_from_insurances()
    return rest_framework.response.Response('1', 200)


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def run_select_query(request: rest_framework.request.Request):
    from .integration_code import local_operations

    # Only handle POST requests
    if request.method == 'POST':
        # Get the query string from the request body
        query = str(request.query_params.get('query', ''))

        if (query.startswith('"') and query.endswith('"')) or (
            query.startswith("'") and query.endswith("'")
        ):
            query = query[1:-1].strip()

        # Remove trailing semicolon if it exists
        if query.endswith(';'):
            query = query[:-1].strip()

        # Ensure the query starts with SELECT and contains no other statements
        if not query.lower().startswith('select') or ';' in query:
            return rest_framework.response.Response(
                {'error': 'Only a single SELECT statement is allowed.'},
                status=400,
            )

        try:
            result = local_operations.run_query(query)

            # Return the result as a JSON response
            return rest_framework.response.Response(result, status=200)
        except Exception as e:
            # Return any database or execution error
            return rest_framework.response.Response(
                {'error': str(e)}, status=500
            )

    # Return method not allowed for non-POST requests
    return rest_framework.response.Response(
        {'error': 'Invalid request method.'}, status=405
    )


@rest_framework.decorators.api_view(['GET'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def reviewed_counts(request: rest_framework.request.Request):
    from .integration_code import data_review_logger

    canonical_objects = [
        'Cases',
        'Billings',
        'Plaintiffs',
        'LegalPersonnel',
        'LawFirms',
        'Charges',
        'Files',
        'MedicalFacilities',
    ]

    review_counts = {}

    for object in canonical_objects:
        review_counts[object] = (
            data_review_logger.get_unreviewed_manual_review_count(object)
        )

    return rest_framework.response.Response(
        {'message': 'Reviews counts', 'data': review_counts}, 200
    )


@rest_framework.decorators.api_view(['GET'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def get_object_fields(request: rest_framework.request.Request):
    from django.core.cache import cache

    from .integration_code import salesforce

    global test

    cache_key = 'sf_object_fields'
    fields = cache.get(cache_key)
    if not fields:
        fields = {
            'Opportunity': salesforce.get_object_fields(test, 'Opportunity'),
            'Funding__c': salesforce.get_object_fields(test, 'Funding__c'),
            'Account': salesforce.get_object_fields(test, 'Account'),
            'Contact': salesforce.get_object_fields(test, 'Contact'),
            'Charge__c': salesforce.get_object_fields(test, 'Charge__c'),
            'ContentVersion': salesforce.get_object_fields(
                test, 'ContentVersion'
            ),
        }
        cache.set(cache_key, fields, 60 * 60)

    return rest_framework.response.Response(
        {'message': 'Object fields', 'data': fields}, 200
    )


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def reviewed_data_update(request: rest_framework.request.Request):
    from .integration_code import salesforce

    global test
    context.request_context.set({'route': 'Redshift_To_Salesforce_Delta'})

    def exception_log(exception_message: str):
        logger.error(
            exception_message,
            extra={
                'additional_details': str(request),
            },
        )

    if request.method == 'GET':
        try:
            canonical_object = request.query_params.get(
                'canonical_object', None
            )
            page_index = request.query_params.get('page_index', None)
            page_size = request.query_params.get('page_size', None)
            manual_review_data = salesforce.get_salesforce_delta_review_update(
                canonical_object=canonical_object,
                page_index=int(page_index) if page_index else None,
                page_size=int(page_size) if page_size else None,
            )
            return rest_framework.response.Response(manual_review_data, 200)
        except Exception as e:
            exception_log(str(e))
            return rest_framework.response.Response('Server error', 500)
    if request.method == 'POST':
        if not isinstance(request.data, dict) or not request.data:
            return rest_framework.response.Response(
                {"error": "Request body is empty or invalid"}, status=400
            )

        try:
            (
                user,
                manual_review_ids,
                canonical_object,
                redshift_reviewed_records,
                redshift_gainids,
            ) = (
                request.data['user'],
                request.data['manual_review_ids'],
                request.data['canonical_object'],
                request.data['redshift_reviewed_records'],
                request.data['redshift_gainids'],
            )

            if len(manual_review_ids) != len(redshift_reviewed_records):
                exception_log(
                    'The number of records does not match the number of manual review ids'
                )
                return rest_framework.response.Response(
                    'Invalid data passed', 400
                )
            salesforce.post_salesforce_delta_upsert(
                test,
                user,
                manual_review_ids,  # pyright: ignore[reportArgumentType]
                canonical_object,  # pyright: ignore[reportArgumentType]
                redshift_reviewed_records,  # pyright: ignore[reportArgumentType]
                redshift_gainids,  # pyright: ignore[reportArgumentType]
            )
            return rest_framework.response.Response('1', 200)
        except tuple(EXCEPTION_STATUS_CODES) as e:
            exception_log(str(e))
            status_code = EXCEPTION_STATUS_CODES.get(type(e), 500)
            return rest_framework.response.Response(str(e), status=status_code)
        except KeyError as e:
            print(e)
            exception_log(
                'Object missing a key-value pair necessary for the delta update'
            )
            return rest_framework.response.Response(
                'Incorrect object passed', 400
            )
        except Exception as e:
            exception_log(str(e))
            return rest_framework.response.Response('Server error', 500)


@rest_framework.decorators.api_view(['POST'])
# @rest_framework.decorators.permission_classes(
#     [rest_framework.permissions.IsAuthenticated]
# )
def remove_salesforce_notfound(request: rest_framework.request.Request):
    from .integration_code import salesforce

    global test

    sf_canonical_map = {
        'Cases': 'Opportunity',
        'Plaintiffs': 'Account',
        'LawFirms': 'Account',
        'Billings': 'Funding__c',
        'LegalPersonnel': 'Contact',
        'Charges': 'Charge__c',
        'Files': 'ContentVersion',
    }
    for canonical in sf_canonical_map:
        sf_object = sf_canonical_map[canonical]

        manual_review_data = salesforce.get_salesforce_delta_review_update(
            canonical_object=canonical
        )

        ids_map = {}
        sf_ids = []
        manual_ids = []
        for item in manual_review_data:
            if item['salesforce_id'] not in ids_map:
                ids_map[item['salesforce_id']] = [
                    item['manual_review_data_id']
                ]
                sf_ids.append(item['salesforce_id'])
            else:
                ids_map[item['salesforce_id']].append(
                    item['manual_review_data_id']
                )
            manual_ids.append(item['manual_review_data_id'])

        salesforce.remove_salesforce_notfounds(
            test, sf_object, ids_map, manual_ids, sf_ids
        )
        print(f'Canonical {canonical} completed')

    return rest_framework.response.Response(
        {'message': 'not founds removed'}, 200
    )


@rest_framework.decorators.api_view(['POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def queue_async_tasks(
    request: rest_framework.request.Request,
):
    def await_task_status(
        task_id: str, task_name: str, wait_time: int
    ) -> tuple[bool, str]:
        """Check if a task completed successfully within wait_time"""
        try:
            # Try to get the result within wait_time
            task_result = fetch(task_id, wait=wait_time)
            if task_result is None:
                # Task didn't complete within wait_time
                return (
                    False,
                    f"Task {task_name} did not complete within {wait_time} ms",
                )
            if task_result.success:
                # Task completed successfully
                return True, task_result.result
            else:
                # Task failed or is in another state
                return (
                    False,
                    f"Task {task_name} failed",
                )
        except Exception as e:
            return False, f"Error checking task {task_name}: {str(e)}"

    try:
        # Get pipeline_tasks from request if provided, else use default
        pipeline_tasks = (
            request.data.get('pipeline_tasks')
            if isinstance(request.data, dict)
            else None
        )
        if not pipeline_tasks:
            return rest_framework.response.Response(
                {
                    'error': 'No pipeline tasks provided. Please provide a list of tasks to execute.'
                },
                status=400,
            )

        # Execute tasks sequentially
        for task_config in pipeline_tasks:
            if not isinstance(task_config, dict):
                raise ValueError(
                    f"Each task_config must be a dict, got {type(task_config)}: {task_config}"
                )
            # Execute the task with a single dictionary argument
            task_id = async_task(
                task_config['task_path'], task_config['args_dict']
            )
            async_flag = task_config.get('async', False)
            if async_flag:
                # If the task is asynchronous, we can skip waiting for its completion
                continue

            # Wait for the task to complete and check its status
            wait_time = task_config.get('wait_time', 2 * 60 * 60 * 1000)
            success, result_msg = await_task_status(
                task_id, task_config['name'], wait_time
            )
            if not success:
                return rest_framework.response.Response(
                    {
                        'error': f'Pipeline halted: {task_config["name"]} task failed or timed out',
                        'details': result_msg,
                    },
                    status=408,
                )

        return rest_framework.response.Response(
            'Pipeline Task Completed Successfully', 200
        )

    except Exception as e:
        return rest_framework.response.Response(
            {'error': 'Pipeline halted: Unexpected error', 'details': str(e)},
            status=500,
        )
