"""GainInternalWebApp URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.urls import path

from . import views

app_name = 'providermapdata'

urlpatterns = [
    path('', views.providerMapData_view, name='home'),
    path('data/', views.providerMapData_update, name='data_refreshing'),
    path('logs/', views.providerMapData_update, name='logs'),
    path('process/', views.providerMapData_process, name='data_processing'),
]
