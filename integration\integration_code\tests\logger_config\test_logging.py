from unittest.mock import MagicMock

import pytest


class TestLogging:
    @pytest.mark.django_db
    def test_logging(self, mock_context: MagicMock):
        import integration.integration_code.logger_config as logger_config

        logger = logger_config.get_logger()

        logger.info("Test info message", extra={'additional_details': 'Test'})
        logger.warning("Test warn message")
        logger.error("Test error message")
        logger.warning(
            f"Multiple matching medical facilities found",
            extra={
                'record': {'test': 'test'},
                's3_filename': 'test_filename',
                's3_subdirectory': 'medicalfacilities_manual_review',
            },
        )
        logger.warning('flush', extra={'flush': True})
