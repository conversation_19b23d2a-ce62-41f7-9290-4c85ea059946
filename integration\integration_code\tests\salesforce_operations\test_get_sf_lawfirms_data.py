from unittest.mock import MagicMock

import integration.integration_code.salesforce_operations as salesforce_operations
from tests import test_helper


class TestGetSfLawfirmsData:
    """Test cases for get_sf_lawfirms_data function."""

    def test_should_return_grouped_lawfirm_data_successfully(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test successful retrieval and grouping of lawfirm data."""
        # Arrange
        mock_salesforce_data = [
            {
                'Id': '001Ec00000nYh1cIAC',
                'Name': 'Smith & Associates Law Firm',
                'Phone': '(*************',
                'Email__c': '<EMAIL>',
                'BillingStreet': '123 Main Street',
                'BillingCity': 'New York',
                'BillingState': 'New York',
                'BillingPostalCode': '10001',
                'attributes': {
                    'type': 'Account',
                    'url': '/services/data/v58.0/sobjects/Account/001Ec00000nYh1cIAC',
                },
            },
            {
                'Id': '001Ec00000nYh2dIAC',
                'Name': 'Johnson Legal Group',
                'Phone': '************',
                'Email__c': '<EMAIL>',
                'BillingStreet': '456 Oak Avenue',
                'BillingCity': 'Los Angeles',
                'BillingState': 'California',
                'BillingPostalCode': '90210',
                'attributes': {
                    'type': 'Account',
                    'url': '/services/data/v58.0/sobjects/Account/001Ec00000nYh2dIAC',
                },
            },
        ]

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        # Act
        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert 'lawfirm_unique_key_group' in result
        assert 'lawfirm_phone_group' in result
        assert 'lawfirm_email_group' in result

        # Verify that the function was called with the correct query
        mock_get_sf_bulk.assert_called_once_with('Account')

        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)

    def test_should_handle_empty_salesforce_response(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test handling of empty response from Salesforce."""
        # Arrange
        mock_get_sf_bulk.return_value.query.return_value = []

        # Act
        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        # When DataFrame is empty, pandas operations will fail and function returns empty dict
        assert result == {}

        # Verify that an error was logged due to empty DataFrame operations
        assert verify_errors.call_count > 0

    def test_should_handle_none_values_in_data_fields(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test handling of None values in data fields."""
        # Arrange
        mock_salesforce_data = [
            {
                'Id': '001Ec00000nYh1cIAC',
                'Name': 'Test Law Firm',
                'Phone': None,
                'Email__c': None,
                'BillingStreet': None,
                'BillingCity': 'Test City',
                'BillingState': 'Texas',
                'BillingPostalCode': '12345',
            }
        ]

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        # Act
        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert result is not None
        assert 'lawfirm_unique_key_group' in result
        assert 'lawfirm_phone_group' in result
        assert 'lawfirm_email_group' in result

        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)

    def test_should_group_lawfirms_by_unique_key_correctly(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test that lawfirms are correctly grouped by unique key."""
        # Arrange
        mock_salesforce_data = [
            {
                'Id': '001Ec00000nYh1cIAC',
                'Name': 'Test Law Firm',
                'Phone': '5551234567',
                'Email__c': '<EMAIL>',
                'BillingStreet': '123 Main St',
                'BillingCity': 'Austin',
                'BillingState': 'Texas',
                'BillingPostalCode': '78701',
            },
            {
                'Id': '001Ec00000nYh2dIAC',
                'Name': 'Another Law Firm',
                'Phone': '5551234567',  # Same phone
                'Email__c': '<EMAIL>',
                'BillingStreet': '456 Oak Ave',
                'BillingCity': 'Dallas',
                'BillingState': 'Texas',
                'BillingPostalCode': '75201',
            },
        ]

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert result is not None

        # Check that phone grouping contains both IDs for the same phone number
        phone_groups = result['lawfirm_phone_group']
        assert len(phone_groups) > 0

        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)

    def test_should_handle_state_name_to_code_mapping(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test that state names are correctly mapped to state codes."""
        # Arrange
        mock_salesforce_data = [
            {
                'Id': '001Ec00000nYh1cIAC',
                'Name': 'Test Law Firm',
                'Phone': '5551234567',
                'Email__c': '<EMAIL>',
                'BillingStreet': '123 Main St',
                'BillingCity': 'Austin',
                'BillingState': 'Texas',
                'BillingPostalCode': '78701',
            }
        ]

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert result is not None
        # The state mapping should be used in the UniqueKey generation
        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)

    def test_should_handle_duplicate_lawfirms_with_same_unique_key(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test handling of duplicate lawfirms with the same unique key."""
        # Arrange
        mock_salesforce_data = [
            {
                'Id': '001Ec00000nYh1cIAC',
                'Name': 'Test Law Firm',
                'Phone': '5551234567',
                'Email__c': '<EMAIL>',
                'BillingStreet': '123 Main St',
                'BillingCity': 'Austin',
                'BillingState': 'Texas',
                'BillingPostalCode': '78701',
            },
            {
                'Id': '001Ec00000nYh2dIAC',
                'Name': 'Test Law Firm',  # Same name
                'Phone': '5551234567',  # Same phone
                'Email__c': '<EMAIL>',  # Same email
                'BillingStreet': '123 Main St',  # Same address
                'BillingCity': 'Austin',
                'BillingState': 'Texas',
                'BillingPostalCode': '78701',
            },
        ]

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert result is not None

        # Check that both IDs are grouped together for the same unique key
        unique_key_groups = result['lawfirm_unique_key_group']
        phone_groups = result['lawfirm_phone_group']
        email_groups = result['lawfirm_email_group']

        # Should have one group for each type with both IDs
        assert len(unique_key_groups) == 1
        assert len(phone_groups) == 1
        assert len(email_groups) == 1

        # Each group should contain both IDs as a set
        for group in unique_key_groups.values():
            assert isinstance(group, set)
            assert len(group) == 2
            assert '001Ec00000nYh1cIAC' in group
            assert '001Ec00000nYh2dIAC' in group

        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)

    def test_should_handle_missing_billing_state_gracefully(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test handling of missing billing state in state mapping."""
        # Arrange
        mock_salesforce_data = [
            {
                'Id': '001Ec00000nYh1cIAC',
                'Name': 'Test Law Firm',
                'Phone': '5551234567',
                'Email__c': '<EMAIL>',
                'BillingStreet': '123 Main St',
                'BillingCity': 'Austin',
                'BillingState': 'Unknown State',  # Not in mapping
                'BillingPostalCode': '78701',
            }
        ]

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert result is not None
        assert 'lawfirm_unique_key_group' in result
        assert 'lawfirm_phone_group' in result
        assert 'lawfirm_email_group' in result

        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)

    def test_should_handle_empty_string_values(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test handling of empty string values in data fields."""
        # Arrange
        mock_salesforce_data = [
            {
                'Id': '001Ec00000nYh1cIAC',
                'Name': '',  # Empty string
                'Phone': '',
                'Email__c': '',
                'BillingStreet': '',
                'BillingCity': 'Test City',
                'BillingState': 'Texas',
                'BillingPostalCode': '',
            }
        ]

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        # Act
        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert result is not None
        assert 'lawfirm_unique_key_group' in result
        assert 'lawfirm_phone_group' in result
        assert 'lawfirm_email_group' in result

        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)

    def test_should_handle_large_dataset(
        self, mock_get_sf_bulk: MagicMock, verify_errors: MagicMock
    ):
        """Test handling of large dataset from Salesforce."""
        # Arrange
        mock_salesforce_data = []
        for i in range(1000):  # Create 1000 records
            mock_salesforce_data.append(
                {
                    'Id': f'001Ec00000nYh{i:03d}IAC',
                    'Name': f'Law Firm {i}',
                    'Phone': f'555123{i:04d}',
                    'Email__c': f'contact{i}@lawfirm.com',
                    'BillingStreet': f'{i} Main Street',
                    'BillingCity': 'Test City',
                    'BillingState': 'Texas',
                    'BillingPostalCode': '78701',
                }
            )

        mock_get_sf_bulk.return_value.query.return_value = mock_salesforce_data

        # Act
        result = salesforce_operations.get_sf_lawfirms_data()

        # Assert
        assert result is not None
        assert 'lawfirm_unique_key_group' in result
        assert 'lawfirm_phone_group' in result
        assert 'lawfirm_email_group' in result

        # Should have processed all 1000 records
        total_groups = (
            len(result['lawfirm_unique_key_group'])
            + len(result['lawfirm_phone_group'])
            + len(result['lawfirm_email_group'])
        )
        assert total_groups > 0

        # Verify no errors were logged
        test_helper.verify_audit_errors(verify_errors)
