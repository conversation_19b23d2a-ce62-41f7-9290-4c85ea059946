INSERT INTO billings (
    totalamount,
    totalamountsent,
    medicalclaimnumber,
    dateofservice,
    medicalfacilityid,
    medicalfacilityaddressline1,
    medicalfacilityaddressline2,
    medicalfacilityaddresscity,
    medicalfacilityaddressstate,
    medicalfacilityaddresszip,
    gaintype,
    type,
    caseid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    totalnongainadjustment,
    totalnongainamountpaidtoprovider,
    totalbalance,
    status,
    createdatetime,
    patientresponsibility,
    notes,
    gainid,
    paidto,
    partneraccountid,
    paidby,
    servicingstartdatetime,
    servicingenddatetime,
    totalgainprenegotiationadjustment,
    totalgainprenegotiationamountpaidtoprovider
) VALUES (
    560.4,
    NULL,  -- totalamountsent
    'T58310752',
    '2024-09-10',
    '0b89f4beff68d371',
    '',
    '',
    '',
    '',
    '',
    'Medical Funding - Serviced',
    'Medical Funding',
    '04b1ace113fe41b1',
    true,
    NULL,  -- sourcecreatedatetime
    NULL,  -- sourcemodifieddatetime
    '2024-10-29 03:39:29',
    false,
    '',
    false,
    '',
    NULL,  -- totalnongainadjustment
    NULL,  -- totalnongainamountpaidtoprovider
    560.4,
    '',
    NULL,  -- createdatetime
    'None',
    '',
    '9050d035837c491e',
    NULL,  -- paidto
    '0b89f4beff68d371',
    NULL,  -- paidby
    '2023-01-01 00:00:00',
    '2024-01-01 00:00:00',
    NULL,  -- totalgainprenegotiationadjustment
    NULL   -- totalgainprenegotiationamountpaidtoprovider
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    canonical_object
) VALUES (
    '9050d035837c491e',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'billings'
);

INSERT INTO billings (
    medicalclaimnumber,
    todelete,
    relevanttogain,
    gainid
) VALUES (
    'Billing to be deleted',
    true,
    true,
    '9050d035837c491f'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '9050d035837c491f',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'billings'
);

INSERT INTO billings (
    medicalclaimnumber,
    todelete,
    relevanttogain,
    gainid,
    status,
    paidto,
    paidby
) VALUES (
    'Billing with Settled status',
    false,
    true,
    '9050d035837c491g',
    'Settled',
    'HCP',
    'Attorney'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '9050d035837c491g',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'billings'
);

INSERT INTO billings (
    medicalclaimnumber,
    todelete,
    relevanttogain,
    gainid,
    status,
    paidby
) VALUES (
    'Billing with WriteOff status',
    false,
    true,
    '9050d035837c491h',
    'WriteOff',
    NULL
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '9050d035837c491h',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'billings'
);

INSERT INTO billings (
    medicalclaimnumber,
    todelete,
    relevanttogain,
    gainid,
    status,
    paidto,
    paidby
) VALUES (
    'Billing Auto Insurance Payment',
    false,
    true,
    '9050d035837c491k',
    'Settled',
    'HCP',
    'AutoCommercialInsurance'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '9050d035837c491k',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id auto',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'billings'
);

INSERT INTO billings (
    medicalclaimnumber,
    todelete,
    relevanttogain,
    gainid,
    status,
    paidto,
    paidby
) VALUES (
    'Billing Other Payment Type',
    false,
    true,
    '9050d035837c491l',  -- new gainid
    'Settled',
    'HCP',
    'OtherPaymentType'  -- This should NOT match any of our filters
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '9050d035837c491l',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id other',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'billings'
);

INSERT INTO billings (
    medicalclaimnumber,
    todelete,
    relevanttogain,
    gainid,
    status
) VALUES (
    'Bill w/ PaidByHealthI sta',
    false,
    true,
    '9050d035837c491i',
    'PaidByHealthInsurance'
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '9050d035837c491i',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'salesforce id',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'billings'
);

INSERT INTO billings (
    totalamount,
    totalamountsent,
    medicalclaimnumber,
    dateofservice,
    medicalfacilityid,
    medicalfacilityaddressline1,
    medicalfacilityaddressline2,
    medicalfacilityaddresscity,
    medicalfacilityaddressstate,
    medicalfacilityaddresszip,
    gaintype,
    type,
    caseid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    totalnongainadjustment,
    totalnongainamountpaidtoprovider,
    totalbalance,
    status,
    createdatetime,
    patientresponsibility,
    notes,
    gainid,
    paidto,
    partneraccountid,
    paidby,
    servicingstartdatetime,
    servicingenddatetime,
    totalgainprenegotiationadjustment,
    totalgainprenegotiationamountpaidtoprovider
) VALUES (
    560.4,
    NULL,  -- totalamountsent
    'Billings manual review',
    '2024-09-10',
    '0b89f4beff68d372',
    '',
    '',
    '',
    '',
    '',
    'Medical Funding - Serviced',
    'Medical Funding',
    '04b1ace113fe41b1',
    true,
    NULL,  -- sourcecreatedatetime
    NULL,  -- sourcemodifieddatetime
    '2024-10-29 03:39:29',
    false,
    '',
    false,
    '',
    NULL,  -- totalnongainadjustment
    NULL,  -- totalnongainamountpaidtoprovider
    560.4,
    '',
    NULL,  -- createdatetime
    'None',
    '',
    '9050d035837c491j',
    NULL,  -- paidto
    '0b89f4beff68d372',
    NULL,  -- paidby
    '2023-01-01 00:00:00',
    '2024-01-01 00:00:00',
    NULL,  -- totalgainprenegotiationadjustment
    NULL   -- totalgainprenegotiationamountpaidtoprovider
);

INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    filevine_id,
    filevine_createddatetime,
    filevine_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    canonical_object
) VALUES (
    '9050d035837c491j',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    '9050d035837c491j',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'sf_id_billings_manual_review',
    '2024-09-10 15:04:27.68565',
    '2024-09-10 15:04:27.68565',
    'billings'
);


--billings to test UpdateCharges and UpdateCasesFromBillings
INSERT INTO billings (
    gainid,
    totalamount,
    totalamountsent,
    medicalclaimnumber,
    dateofservice,
    medicalfacilityid,
    medicalfacilityaddressline1,
    medicalfacilityaddressline2,
    medicalfacilityaddresscity,
    medicalfacilityaddressstate,
    medicalfacilityaddresszip,
    gaintype,
    type,
    caseid,
    relevanttogain,
    sourcecreatedatetime,
    sourcemodifieddatetime,
    modifieddatetime,
    todelete,
    todeletesystem,
    deletepreventoverride,
    deletepreventoverridereason,
    totalnongainadjustment,
    totalnongainamountpaidtoprovider,
    totalbalance,
    status,
    createdatetime,
    notes,
    paidto,
    partneraccountid,
    paidby,
    servicingstartdatetime,
    servicingenddatetime,
    totalgainprenegotiationadjustment,
    totalgainprenegotiationamountpaidtoprovider
)
VALUES
(
    'b1c2d3e4f5g6h7i8', --gainid
    5000.00, --totalamount
    4000.00, --totalamountsent
    'MCN12345', --medicalclaimnumber
    '2025-01-15', --dateofservice
    '0b82f4geff68d292', --medicalfacilityid - using existing medical facility ID
    '123 Medical St.', --medicalfacilityaddressline1
    NULL, --medicalfacilityaddressline2
    'New York', --medicalfacilityaddresscity
    'NY', --medicalfacilityaddressstate
    '10001', --medicalfacilityaddresszip
    'Health', --gaintype
    'Outpatient', --type
    '04b1ace113fe41b1', --caseid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    1000.00, --totalnongainadjustment
    3500.00, --totalnongainamountpaidtoprovider
    500.00, --totalbalance
    'Paid', --status
    NOW(), --createdatetime
    'Routine medical check', --notes
    'Provider X', --paidto
    '0b82f4geff68d292', --partneraccountid
    'Insurance A', --paidby
    NOW(), --servicingstartdatetime
    NOW(), --servicingenddatetime
    600.00, --totalgainprenegotiationadjustment
    2900.00 --totalgainprenegotiationamountpaidtoprovider
),

--Generates invoice
(
    '1a2b3c4d5e6f7890', --gainid
    5000.00, --totalamount
    4000.00, --totalamountsent
    'MCN12345', --medicalclaimnumber
    '2025-01-15', --dateofservice
    '0f9e8d7c6b5a4321', --medicalfacilityid
    '123 Medical St.', --medicalfacilityaddressline1
    NULL, --medicalfacilityaddressline2
    'New York', --medicalfacilityaddresscity
    'NY', --medicalfacilityaddressstate
    '10001', --medicalfacilityaddresszip
    'Health', --gaintype
    'Outpatient', --type
    '04b1ace113fe41b1', --caseid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    1000.00, --totalnongainadjustment
    3500.00, --totalnongainamountpaidtoprovider
    500.00, --totalbalance
    'Paid', --status
    NOW(), --createdatetime
    'Routine medical check', --notes
    'Provider X', --paidto
    '0f9e8d7c6b5a4321', --partneraccountid
    'Insurance A', --paidby
    NOW(), --servicingstartdatetime
    NOW() + INTERVAL '5 days', --servicingenddatetime
    600.00, --totalgainprenegotiationadjustment
    2900.00 --totalgainprenegotiationamountpaidtoprovider
),
-- multiple
(
    '7a8b9c0d1e2f3456', --gainid
    5000.00, --totalamount
    4000.00, --totalamountsent
    'MCN12345', --medicalclaimnumber
    '2025-01-15', --dateofservice
    '6f7a8b9c0d1e2345', --medicalfacilityid - using existing medical facility ID
    '123 Medical St.', --medicalfacilityaddressline1
    NULL, --medicalfacilityaddressline2
    'New York', --medicalfacilityaddresscity
    'NY', --medicalfacilityaddressstate
    '10001', --medicalfacilityaddresszip
    'Health', --gaintype
    'Outpatient', --type
    '04b1ace113fe41b1', --caseid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    1000.00, --totalnongainadjustment
    3500.00, --totalnongainamountpaidtoprovider
    500.00, --totalbalance
    'Paid', --status
    NOW(), --createdatetime
    'Routine medical check', --notes
    'Provider X', --paidto
    '6f7a8b9c0d1e2345', --partneraccountid - using existing medical facility ID
    'Insurance A', --paidby
    NOW(), --servicingstartdatetime
    NOW() + INTERVAL '5 days', --servicingenddatetime
    600.00, --totalgainprenegotiationadjustment
    2900.00 --totalgainprenegotiationamountpaidtoprovider
),
-- For testing update_cases_from_billings
(
    'testbill4case001', --gainid (exactly 16 chars)
    8000.00, --totalamount
    7000.00, --totalamountsent
    'MCN-CASE-TEST-1', --medicalclaimnumber
    '2025-01-15', --dateofservice
    'testmedfac00001', --medicalfacilityid 
    '123 Test Blvd', --medicalfacilityaddressline1
    NULL, --medicalfacilityaddressline2
    'Testville', --medicalfacilityaddresscity
    'CA', --medicalfacilityaddressstate
    '90210', --medicalfacilityaddresszip
    'Health', --gaintype
    'Outpatient', --type
    '04b1ace113fe41b2', --caseid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    1500.00, --totalnongainadjustment
    4000.00, --totalnongainamountpaidtoprovider
    1500.00, --totalbalance
    'Paid', --status
    NOW(), --createdatetime
    'Test billing for case update', --notes
    'Provider Y', --paidto
    'testmedfac00001', --partneraccountid 
    'Insurance B', --paidby
    NOW(), --servicingstartdatetime
    NOW(), --servicingenddatetime
    800.00, --totalgainprenegotiationadjustment
    3200.00 --totalgainprenegotiationamountpaidtoprovider
),
(
    'testbill4case002', --gainid (exactly 16 chars)
    6000.00, --totalamount
    5000.00, --totalamountsent
    'MCN-CASE-TEST-2', --medicalclaimnumber
    '2025-01-15', --dateofservice
    'testmedfac00002', --medicalfacilityid 
    '456 Test Ave', --medicalfacilityaddressline1
    NULL, --medicalfacilityaddressline2
    'Testopolis', --medicalfacilityaddresscity
    'NY', --medicalfacilityaddressstate
    '10001', --medicalfacilityaddresszip
    'Health', --gaintype
    'Outpatient', --type
    '04b1ace113fe41b3', --caseid
    TRUE, --relevanttogain
    NOW(), --sourcecreatedatetime
    NOW(), --sourcemodifieddatetime
    NOW(), --modifieddatetime
    FALSE, --todelete
    NULL, --todeletesystem
    FALSE, --deletepreventoverride
    NULL, --deletepreventoverridereason
    1200.00, --totalnongainadjustment
    3000.00, --totalnongainamountpaidtoprovider
    1000.00, --totalbalance
    'Paid', --status
    NOW(), --createdatetime
    'Test billing for case update', --notes
    'Provider Z', --paidto
    'testmedfac00002', --partneraccountid 
    'Insurance C', --paidby
    NOW(), --servicingstartdatetime
    NOW(), --servicingenddatetime
    500.00, --totalgainprenegotiationadjustment
    2300.00 --totalgainprenegotiationamountpaidtoprovider
);


INSERT INTO gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    ati_id,
    canonical_object
) VALUES (
    'b1c2d3e4f5g6h7i8',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'billings'
),
(
    '7a8b9c0d1e2f3456',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'billings'
),
(
    '1a2b3c4d5e6f7890',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'billings'
),
(
    'testbill4case001',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'billings'
),
(
    'testbill4case002',
    '2024-10-25 15:18:27.084127',
    '2024-10-29 16:01:37.61754',
    'T49767911',
    'billings'
);