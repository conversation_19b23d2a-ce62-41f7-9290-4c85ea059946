import typing
from unittest.mock import MagicMock

import psycopg
import psycopg.sql
import pytest
from psycopg.rows import dict_row

from integration.integration_code import local_operations
from tests import test_helper


class TestUpdateBillingsFromCharges:
    # These IDs match the existing test data in the seeds
    BILLING_ID: str = 'b1c2d3e4f5g6h7i8'
    BILLING_ID_2: str = '7a8b9c0d1e2f3456'
    BILLING_ID_3: str = '1a2b3c4d5e6f7890'

    CHARGE_ID: str = '6b7a8f913f2e4d5c'
    CHARGE_ID_2: str = '9f8e7d6c5b4a3210'
    CHARGE_ID_3: str = 'a1b2c3d4e5f67890'

    @pytest.mark.django_db
    def test_no_charges(
        self,
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
    ):
        """Should return None when no charges exist"""

        result = local_operations.update_billings_from_charges(["dummy"])

        assert result is None
        assert test_helper.verify_if_log_exists(
            verify_errors, "No data fetched for billing update"
        )

    @pytest.mark.django_db
    def test_valid_charges(self, database: psycopg.Connection[typing.Any]):
        """Should process valid charges and return billing IDs"""

        cursor = database.cursor(row_factory=dict_row)

        result = local_operations.update_billings_from_charges(
            [self.CHARGE_ID]
        )

        assert result is not None
        assert self.BILLING_ID_2 in result

        cursor.execute(
            psycopg.sql.SQL("SELECT * FROM billings WHERE gainid = {}").format(
                self.BILLING_ID_2
            )
        )
        updated_billing = cursor.fetchone()
        assert updated_billing is not None

        cursor.execute(
            "SELECT * FROM integrationtimestamps WHERE integrationroute = 'Redshift_Update'"
        )
        timestamp = cursor.fetchone()
        assert timestamp is not None

    @pytest.mark.django_db
    def test_multiple_charges(self, database: psycopg.Connection[typing.Any]):
        """Should process multiple charges and return all billing IDs"""

        cursor = database.cursor(row_factory=dict_row)
        cursor.execute(
            psycopg.sql.SQL(
                "SELECT * FROM billings WHERE gainid IN ({}, {}, {})"
            ).format(self.BILLING_ID, self.BILLING_ID_2, self.BILLING_ID_3)
        )
        initial_billings = cursor.fetchall()

        result = local_operations.update_billings_from_charges(
            [self.CHARGE_ID, self.CHARGE_ID_2, self.CHARGE_ID_3]
        )

        assert result is not None
        assert set(result) == {
            self.BILLING_ID,
            self.BILLING_ID_2,
            self.BILLING_ID_3,
        }

        cursor.execute(
            psycopg.sql.SQL(
                "SELECT * FROM billings WHERE gainid IN ({}, {}, {})"
            ).format(self.BILLING_ID, self.BILLING_ID_2, self.BILLING_ID_3)
        )
        updated_billings = cursor.fetchall()
        assert len(updated_billings) == len(initial_billings)
