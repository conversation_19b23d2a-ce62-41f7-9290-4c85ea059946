import datetime
import typing
from collections import OrderedDict
from unittest.mock import MagicMock

import psycopg
import pytest
from requests.exceptions import HTTPError
from requests.models import Response

import integration.integration_code.salesforce_operations as salesforce_operations


class TestInsertSfMedicalFacilities:

    @pytest.mark.django_db
    def test_should_insert_sf_medicalfacilities(
        self,
        database: psycopg.Connection[typing.Any],
        mock_get_sf_action: MagicMock,
    ):
        """Test that medical facilities can be inserted in bulk."""
        mock_get_sf_action.return_value = lambda *x, **y: [
            {
                'success': True,
                'created': True,
                'id': '001Ec00000e97MkIAI',
                'errors': [],
            }
        ]

        salesforce_operations.insert_sf_medicalfacilities(
            'gain-servicing',
            'integration/ati/2024-10-23',
            [
                {
                    'Name': 'test',
                }
            ],
            ['12336'],
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

    @pytest.mark.django_db
    def test_should_insert_sf_medicalfacilities_individual(
        self,
        database: psycopg.Connection[typing.Any],
        mock_get_sf_action: MagicMock,
    ):
        """Test that medical facilities can be inserted individually when bulk fails."""
        mock_get_sf_action.side_effect = [
            Exception('error'),
            Exception('error'),
            lambda *x, **y: OrderedDict(
                [
                    ('id', '001Ec00000e9NPQIA2'),
                    ('success', True),
                    ('errors', []),
                ]
            ),
        ]

        salesforce_operations.insert_sf_medicalfacilities(
            'gain-servicing',
            'integration/ati/2024-10-23',
            [
                {
                    'Name': 'test',
                }
            ],
            ['12336'],
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )

    @pytest.mark.django_db
    def test_should_insert_sf_medicalfacilities_individual_failed(
        self, mock_get_sf_action: MagicMock
    ):
        """Test that errors are properly handled when inserting medical facilities."""
        mocked_response = Response()
        mocked_response.status_code = 400
        mocked_response._content = b'[ {\n  "message" : "No such column \'NonExistentField\' on sobject of type Account",\n  "errorCode" : "INVALID_FIELD"\n} ]'

        mock_get_sf_action.side_effect = [
            Exception('error'),
            Exception('error'),
            HTTPError(response=mocked_response),
        ]

        salesforce_operations.insert_sf_medicalfacilities(
            'gain-servicing',
            'integration/ati/2024-10-23',
            [
                {
                    'NonExistentField': 'test',
                }
            ],
            ['12336'],
            datetime.datetime(2024, 10, 23, 15, 44, 27, 96154),
        )
