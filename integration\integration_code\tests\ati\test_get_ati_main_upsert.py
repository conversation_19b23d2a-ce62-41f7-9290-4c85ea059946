import json
import typing
from unittest.mock import MagicMock

import psycopg
import pytest
from freezegun import freeze_time
from mypy_boto3_s3 import S3Client

from tests import test_helper


class TestGetMainUpsert:

    @freeze_time("2023-01-01 12:00:00")
    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "processed_data", [{"source": "ati"}], indirect=True
    )
    def test_should_get_main_upsert(
        self,
        processed_data: dict[str, typing.Any],
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
        mock_s3_to_postgres: MagicMock,
        s3: S3Client,
        snapshot: typing.Any,
    ):
        """Test that the main upsert function correctly processes ATI data."""
        from integration.integration_code import ati

        # Define tables to count for this test
        tables_to_count = [
            'plaintiffs',
            'medicalfacilities',
            'lawfirms',
            'legalpersonnel',
            'cases',
            'billings',
            'charges',
            'transactions',
            'files',
            'notes',
        ]
        old_count_tables = test_helper.get_tables_count(
            database, tables_to_count
        )

        ati.get_main_upsert(
            move_flag=True,
            charges_update_flag=True,
            billings_update_flag=True,
            cases_update_flag=True,
            test=True,
            all_data=True,
        )

        test_helper.verify_audit_errors(verify_errors)

        new_count_tables = test_helper.get_tables_count(
            database, tables_to_count
        )

        json_files = test_helper.get_s3_to_postgres_data(
            s3, mock_s3_to_postgres
        )

        snapshot.assert_match(
            json.dumps(json_files, indent=4), 'test_should_get_main_upsert.txt'
        )

        for table in new_count_tables:
            assert (
                new_count_tables[table] - old_count_tables[table] == 1
            ), f'{table} should have been inserted, old: {old_count_tables[table]} new: {new_count_tables[table]}'

    @freeze_time("2023-01-01 12:00:00")
    @pytest.mark.django_db
    def test_should_get_main_upsert_with_unproceeded_data(
        self,
        unproceeded_data_ati: dict[str, typing.Any],
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
        mock_s3_to_postgres: MagicMock,
        s3: S3Client,
        snapshot: typing.Any,
    ):
        """Test that the main upsert function correctly processes ATI data."""
        from integration.integration_code import ati

        ati.get_main_upsert(
            move_flag=True,
            charges_update_flag=True,
            billings_update_flag=True,
            cases_update_flag=True,
            test=True,
            all_data=True,
        )

        test_helper.verify_audit_errors(verify_errors)

        # json_files = test_helper.get_s3_to_postgres_data(
        #     s3, mock_s3_to_postgres
        # )

        # snapshot.assert_match(
        #     json.dumps(json_files, indent=4),
        #     'test_should_get_main_upsert_with_unproceeded_data.txt',
        # )
