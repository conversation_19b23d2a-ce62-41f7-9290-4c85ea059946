repos:
  - repo: https://github.com/psf/black
    rev: 22.10.0
    hooks:
      - id: black
        name: "Black Code Formatter"
        entry: "python run_pdm.py run black" # Ensures black uses the PDM environment
        language: system
        files: "\\.py$"
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: "Isort Formatter"
        entry: "python run_pdm.py run isort" # Ensures black uses the PDM environment
        language: system
        files: "\\.py$"
  - repo: local
    hooks:
      - id: pyright-check
        name: "Pyright Check"
        entry: "python run_pdm.py run pyright --level error ."
        language: system
        files: "\\.py$"
