from unittest.mock import patch

import pytest


@pytest.fixture
def mock_get_main_delete():
    with patch('integration.integration_code.ati.get_main_delete') as mock:
        yield mock


@pytest.fixture
def mock_get_main_update_settled():
    with patch(
        'integration.integration_code.ati.get_main_update_settled'
    ) as mock:
        yield mock


@pytest.fixture
def mock_get_main_upsert():
    with patch('integration.integration_code.ati.get_main_upsert') as mock:
        yield mock
