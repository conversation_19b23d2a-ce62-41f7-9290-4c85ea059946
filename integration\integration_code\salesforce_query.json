{"Account": {"Columns": ["Id", "Name", "Date_of_Birth__c", "Lead_Source__c", "Lead_Source_Name__c", "Keywords__c", "RecordTypeId", "RecordType.Name", "Gain_ID__c", "ParentId", "LastModifiedDate", "(SELECT Date_of_Accident__c FROM Plaintiff_Details__r)"], "ConditionList": {"IdFilter": {"Condition": "Id IN {sf_id_list}", "Params": ["sf_id_list"]}, "GetData": {"Condition": "Name IN {sf_name_list} OR Root_Parent_Name__c IN {sf_name_list}", "Params": ["sf_name_list"]}}}, "Contact": {"Columns": ["Id", "FirstName", "LastName", "Title", "HomePhone", "MobilePhone", "Phone", "OtherPhone", "Email", "Personal_Email__c", "MailingStreet", "MailingCity", "MailingState", "MailingPostalCode", "MailingCountry", "OtherStreet", "OtherCity", "OtherState", "OtherPostalCode", "OtherCountry", "AccountId", "Name"], "ConditionList": {"IdFilter": {"Condition": "Id IN {sf_id_list}", "Params": ["sf_id_list"]}, "GetData": {"Condition": "(AccountId IN {sf_id_list} OR Account.ParentId IN {sf_id_list}) AND (NOT Account.Name LIKE '%NO LONGER%') AND (NOT (Name LIKE '%NO LONGER%' OR Name LIKE '%Duplicate%')) AND (NOT (Title LIKE '%NO LONGER%' OR Title LIKE '%Duplicate%'))", "Params": ["sf_id_list"]}}}, "Opportunity": {"Columns": ["Id", "Name", "Date_of_Birth__c", "SSN__c", "Drivers_License__c", "Gender__c", "Company_Name__c", "Home_Phone__c", "Cell_Phone__c", "Other_Phone__c", "Plaintiff_Email__c", "Address__c", "Address_2__c", "City__c", "State__c", "Zip__c", "Case_Status__c", "Date_of_Accident__c", "Description_of_Accident_Incident__c", "What_type_of_case__c", "AccountId", "Law_Firm_Account_Name__c", "StageName", "CloseDate", "Plaintiff_Account__c", "Plaintiff_Account__r.name", "Insured_s_Name__c", "Insurance_Company__c", "Insurance_Limits__c", "Insurance_Agent__c", "Insurance_Co_Address__c", "Insurance_Co_Address_2__c", "Insurance_Co_City__c", "Insurance_Co_State__c", "Insurance_Co_Zipcode__c", "Insurance_Co_Phone__c", "Insurance_Co_Fax__c", "X2_Insured_s_Name__c", "Insurance_Company_2__c", "Insurance_Limits_2__c", "X2_Zipcode__c", "Surgery_Date__c", "Surgeon__c", "Type_of_Surgery__c", "Surgery_Comments__c", "Estimated_Surgical_Charges__c", "Medical_Facility_P__c", "Medical_Facility_P__r.Name", "Partner_Account__c", "Partner_Account__r.Name", "RecordTypeId", "RecordType.Name", "LastModifiedDate"], "ConditionList": {"IdFilter": {"Condition": "Id IN {sf_id_list}", "Params": ["sf_id_list"]}, "GetData": {"Condition": "(AccountId IN {sf_id_list} OR Account.ParentId IN {sf_id_list} OR Medical_Facility_P__c IN {sf_id_list} OR Partner_Account__c IN {sf_id_list}) AND (NOT Account.Name LIKE '%NO LONGER%') AND (NOT (Name LIKE '%NO LONGER%' OR Name LIKE '%Duplicate%'))", "Params": ["sf_id_list"]}, "ExcludeOpportunityStage": {"Condition": "StageName NOT IN {stage_names_to_exclude}", "Params": ["stage_names_to_exclude"]}, "ExcludeOpportunitySubStageInGivenStage": {"Condition": "StageName NOT IN {stage_names_to_exclude_substage} OR Sub_Stage__c NOT IN {substage_names_to_exclude}", "Params": ["stage_names_to_exclude_substage", "substage_names_to_exclude"]}}}, "Funding__c": {"Columns": ["Id", "Name", "Date_of_Service__c", "Medical_Claim_Number__c", "Medical_Case__c", "Non_Rollup_Charge_Amounts_Total__c", "Non_Rollup_Non_Gain_Adjustments_Total__c", "Non_Rollup_Non_Gain_Payments_Total__c", "Non_Rollup_Balance_Total__c", "Non_Rollup_Amounts_to_Partner_Total__c", "Medical_Facility__c", "Medical_Facility__r.Name", "Medical_Location__c", "Medical_Location__r.Name", "Partner_Account__c", "Partner_Account__r.Name", "Plaintiff__c", "RecordTypeId", "RecordType.Name", "Plaintiff__r.Name", "Plaintiff__r.Date_of_Birth__c", "Plaintiff__r.Date_of_Accident__c", "Plaintiff__r.LastModifiedDate", "Plaintiff__r.Home_Phone__c", "Plaintiff__r.Cell_Phone__c", "Plaintiff__r.Other_Phone__c", "Plaintiff__r.<PERSON>_<PERSON>ail__c", "Invoice_Amount__c", "LastModifiedDate"], "ConditionList": {"IdFilter": {"Condition": "Id IN {sf_id_list}", "Params": ["sf_id_list"]}, "GetData": {"Condition": "Plaintiff__r.AccountId IN {sf_id_list} OR Medical_Facility__c IN {sf_id_list} OR Partner_Account__c IN  {sf_id_list}", "Params": ["sf_id_list"]}, "ExcludeOpportunityStage": {"Condition": "Plaintiff__r.<PERSON> NOT IN {stage_names_to_exclude}", "Params": ["stage_names_to_exclude"]}, "ExcludeOpportunitySubStageInGivenStage": {"Condition": "Plaintiff__r.StageName NOT IN {stage_names_to_exclude_substage} OR Plaintiff__r.Sub_Stage__c NOT IN {substage_names_to_exclude}", "Params": ["stage_names_to_exclude_substage", "substage_names_to_exclude"]}}}, "Charge__c": {"Columns": ["Id", "Date_of_Service__c", "Amount__c", "CPT_Code__c", "CPT_Modifier__c", "Non_Gain_Adjustment__c", "Non_Gain_Amount_Paid_to_Provider__c", "Reimbursement_Rate__c", "Funding__c", "Charge_Id__c", "LastModifiedDate"], "ConditionList": {"IdFilter": {"Condition": "Id IN {sf_id_list}", "Params": ["sf_id_list"]}, "GetData": {"Condition": "Funding__r.Medical_Facility__c IN {sf_id_list} OR Funding__r.Partner_Account__c IN {sf_id_list}", "Params": ["sf_id_list"]}, "ExcludeOpportunityStage": {"Condition": "Funding__r.<PERSON>__r.<PERSON><PERSON>ame NOT IN {stage_names_to_exclude}", "Params": ["stage_names_to_exclude"]}, "ExcludeOpportunitySubStageInGivenStage": {"Condition": "Funding__r.<PERSON>tiff__r.<PERSON><PERSON>ame NOT IN {stage_names_to_exclude_substage} OR Funding__r.<PERSON>tiff__r.Sub_Stage__c NOT IN {substage_names_to_exclude}", "Params": ["stage_names_to_exclude_substage", "substage_names_to_exclude"]}}}, "ContentVersion": {"Columns": ["Id", "Title", "Document_Type__c", "PathOnClient", "FirstPublishLocationId", "Law_Firm__c", "Medical_Facility__c", "Opportunity__c"], "ConditionList": {"IdFilter": {"Condition": "Id IN {sf_id_list}", "Params": ["sf_id_list"]}, "GetData": {"Condition": "Id IN {sf_id_list}", "Params": ["sf_id_list"]}}}}