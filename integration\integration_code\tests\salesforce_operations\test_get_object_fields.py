from unittest.mock import MagicMock

import integration.integration_code.salesforce_operations as salesforce_operations


class TestGetObjectFields:

    def test_should_get_object_metadata(self, mock_get_sf_action: MagicMock):
        """Test that object metadata is correctly retrieved and formatted."""
        mock_get_sf_action.return_value = lambda *x, **y: {
            'fields': [
                {'name': 'Id', 'label': 'Record ID'},
                {'name': 'Name', 'label': 'Account Name'},
                {'name': 'BillingCity', 'label': 'Billing City'},
            ]
        }

        expected_result = {
            'Id': 'Record ID',
            'Name': 'Account Name',
            'BillingCity': 'Billing City',
        }

        result = salesforce_operations.get_object_fields(
            object_name='Opportunity'
        )
        assert result == expected_result
