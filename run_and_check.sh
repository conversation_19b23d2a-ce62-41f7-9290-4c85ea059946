#!/bin/bash

export IS_TESTING=true
# Start Django server in the background
echo "Starting Django development server..."
pdm run python manage.py runserver --settings GainInternalWebApp.$1 &

# Set the healthcheck endpoint
HEALTHCHECK_URL="http://127.0.0.1:8000/healthcheck"

# Check health status every 10 seconds for up to 60 seconds
MAX_RETRIES=6
RETRY_INTERVAL=10
retries=0

sleep $RETRY_INTERVAL

echo "Checking healthcheck endpoint..."

# Loop to check the endpoint
while [ $retries -lt $MAX_RETRIES ]; do
    HTTP_RESPONSE=$(curl --write-out "%{http_code}" --silent --output /dev/null "$HEALTHCHECK_URL")

    if [ "$HTTP_RESPONSE" -eq 200 ]; then
        echo "Healthcheck successful: Server is running!"
        exit 0
    else
        echo "Healthcheck attempt $((retries + 1)) failed. Retrying in $RETRY_INTERVAL seconds..."
        retries=$((retries + 1))
        sleep $RETRY_INTERVAL
    fi
done

# If the loop completes without success
echo "Healthcheck failed: Server did not start correctly after multiple attempts."
kill %1
exit 1