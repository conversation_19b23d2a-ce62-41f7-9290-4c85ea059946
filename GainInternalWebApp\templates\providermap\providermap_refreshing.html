{% load static %}
<script src="{% static 'js/app.js' %}"></script>  
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>


<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
<link rel="stylesheet" type="text/css" href="{% static '/css/styles.css' %}">

<div class="row">
    <div class="col-md-6">
        <div class="card mb-2">
            <img class="card-img-top" src="{% static 'images/providers.png' %}">
            <div class="card-body">
                <h5 class="card-title">The providers map</h5>
                <p class="card-text">This project is about refreshing the providers map data for the Gain Portal.</p>
                <a href="{% url 'providermapdata:data_processing' %}"
                   class="btn btn-primary"  onclick="showStep1RefreshDiv()" >
                    Refreshing the providers map data
                </a>
            </div>
        </div>
    </div>


    <div class="col-md-6" id="welcomeDiv" >
        <div class="row">
            <div class="card mb-2">
                <div class="card-body">
                    <h5 class="card-title">Loading...</h5>
                    <h5 class="card-title">Last refreshing time: {{DataShape}}</h5>
                    <h5 class="card-title">Last refreshing time: {{OverallShape}}</h5>
                    <h5 class="card-title">The data format is: {{OverallShape}}</h5>
                    <p class="card-text">Last refreshing time: {{RefreshingTime}}</p>
                    <button type="button" class="btn btn-primary">Click Me!</button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="card mb-2">
                <div class="card-body">
                    <h5 class="card-title">Loading...</h5>
                    <h5 class="card-title">Last refreshing time: {{DataShape}}</h5>
                    <h5 class="card-title">Last refreshing time: {{OverallShape}}</h5>
                    <h5 class="card-title">The data format is: {{OverallShape}}</h5>
                    <p class="card-text">Last refreshing time: {{RefreshingTime}}</p>
                    <button type="button" class="btn btn-primary">Click Me!</button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<div class="container">
    {% block page_content %}{% endblock %}
</div>

