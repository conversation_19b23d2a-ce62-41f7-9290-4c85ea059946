# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning
import base64
import json
import pathlib

import rest_framework.decorators
import rest_framework.permissions
import rest_framework.response

# Create your views here.


@rest_framework.decorators.api_view(['GET', 'POST'])
@rest_framework.decorators.permission_classes(
    [rest_framework.permissions.IsAuthenticated]
)
def hcfa_parse(request):
    from .parsing_code import hcfa

    if request.method == 'POST':
        name = request.data['name']
        data = request.data['data']
        try:
            with open(f'parsing/parsing_code/{name}.pdf', 'wb') as f:
                f.write(base64.b64decode(data))
        except Exception as e:
            print(str(e))
        print(f'File {name} written successfully! Will start to process.')
        res = hcfa.main(f'parsing/parsing_code/{name}.pdf')
        print(f'Processing {name} done')
        if res == 0:
            return rest_framework.response.Response(
                f'Parsed {name} sucessfully', 200
            )
        elif res == 1:
            return rest_framework.response.Response(
                f'Failed to parse {name}', 200
            )
        else:
            return rest_framework.response.Response(
                f'Error occured trying to parse {name}', 200
            )
    if request.method == 'GET':
        name = request.query_params['name']
        print(name)
        try:
            with open(f'parsing/parsing_code/{name}.json', 'rb') as f:
                details = json.load(f)
            pathlib.Path(f'parsing/parsing_code/{name}.json').unlink(
                missing_ok=True
            )
            return rest_framework.response.Response(details)
        except:
            return rest_framework.response.Response(
                f'Error occured. Could not find results for {name} on the server. Please make sure the file name is correct.'
            )
