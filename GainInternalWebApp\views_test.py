import re

import django.urls
import pytest
from django.test import Client


class TestGainInternalWebAppViews:
    def test_healthcheck(self, client: Client):
        url = django.urls.reverse('healthcheck')
        response = client.get(url)

        assert response.status_code == 200
        assert response.json() == 'Successful'

    def test_test_ip(self, client: Client):
        url = django.urls.reverse('test_ip')
        response = client.get(url)

        assert response.status_code == 200
        expected_pattern = (
            r'External IP for this device is: \d+\.\d+\.\d+\.\d+ \(IPv4\)'
        )
        assert re.match(expected_pattern, response.json()) is not None

    @pytest.mark.django_db
    def test_test_logging(self, client: Client):
        url = django.urls.reverse('test_logging')
        response = client.get(url)

        assert response.status_code == 200
        assert response.json() == 'Successful'

    def test_check_app_config(self, authorized_client: Client):
        url = django.urls.reverse('check_app_config')
        response = authorized_client.get(url, {'config_param': 'SETTINGS'})

        assert response.status_code == 200
        assert response.json() == 'App Config SETTINGS printed'
