CREATE TABLE charges (
    dateofservice date,
    amount numeric(10, 4),
    cptcode character varying(10),
    cptmodifier character varying(20),
    cptdescription character varying(1000),
    nongainadjustment numeric(10, 4),
    nongainamountpaidtoprovider numeric(10, 4),
    reimbursementrate numeric(7, 4),
    amountsent numeric(10, 4),
    quantity double precision,
    billingid character varying(20),
    relevanttogain boolean,
    sourcecreatedatetime timestamp without time zone,
    sourcemodifieddatetime timestamp without time zone,
    modifieddatetime timestamp without time zone,
    todelete boolean DEFAULT false,
    todeletesystem character varying(100),
    deletepreventoverride boolean DEFAULT false,
    deletepreventoverridereason character varying(1000),
    status character varying(100) DEFAULT '':: character varying,
    balance numeric(10, 4),
    createdatetime timestamp without time zone,
    gainid character varying(16) NOT NULL DEFAULT '':: character varying,
    gainprenegotiationamountpaidtoprovider numeric(10, 4),
    gainprenegotiationadjustment numeric(10, 4),
    PRIMARY KEY (gainid),
    FOREIGN KEY (billingid) REFERENCES billings(gainid)
);