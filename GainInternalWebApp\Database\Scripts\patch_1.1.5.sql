create temp table temp_facility_map (
    medicalfacilityid varchar(255),
    sf_id varchar(255)
);

copy temp_facility_map from 's3://gain-servicing-dev/temp/medicalfacilitymapping/mapping.csv'
iam_role 'arn:aws:iam::976467972712:role/RedshiftS3FullAccess'
format as csv;

create temp table temp_gainid_map as
select
    medicalfacilityid,
    sf_id,
    left(md5(random()::text), 16) as gainid
from
    (
    select distinct medicalfacilityid, sf_id
    from temp_facility_map
) as distinct_facility;

insert into integration.dev.gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
)
    select
        gainid,
        getdate(),
        getdate(),
        sf_id,
        getdate(),
        getdate(),
        medicalfacilityid,
        getdate(),
        getdate(),
        'medicalfacilities'
    from
        temp_gainid_map;

update integration.dev.billings b
set
    medicalfacilityid = g.gainid from temp_gainid_map g
where
    b.medicalfacilityid = g.medicalfacilityid
    and b.medicalfacilityid is not null
    and b.medicalfacilityid <> '';

insert into integration.dev.medicalfacilities (
    gainid, name, relevanttogain, todelete, deletepreventoverride, modifieddatetime
)
select gainid, '', false, false, false, getdate() from temp_gainid_map;

drop table temp_gainid_map;
drop table temp_facility_map;

create temp table temp_facility_map (
    medicalfacilityid varchar(255),
    sf_id varchar(255)
);

copy temp_facility_map from 's3://gain-servicing-dev/temp/medicalfacilitymapping/mapping.csv'
iam_role 'arn:aws:iam::976467972712:role/RedshiftS3FullAccess'
format as csv;

create temp table temp_gainid_map as
select
    medicalfacilityid,
    sf_id,
    left(md5(random()::text), 16) as gainid
from
    (
    select distinct medicalfacilityid, sf_id
    from temp_facility_map
) as distinct_facility;

insert into integration.staging.gain_id_map (
    gainid,
    gain_createddatetime,
    gain_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    canonical_object
)
    select
        gainid,
        getdate(),
        getdate(),
        sf_id,
        getdate(),
        getdate(),
        medicalfacilityid,
        getdate(),
        getdate(),
        'medicalfacilities'
    from
        temp_gainid_map;

update integration.staging.billings b
set
    medicalfacilityid = g.gainid from temp_gainid_map g
where
    b.medicalfacilityid = g.medicalfacilityid
    and b.medicalfacilityid is not null
    and b.medicalfacilityid <> '';

insert into integration.staging.medicalfacilities (
    gainid, name, relevanttogain, todelete, deletepreventoverride, modifieddatetime
)

select gainid, '', false, false, false, getdate() from temp_gainid_map;

drop table temp_gainid_map;
drop table temp_facility_map;

insert into integration.dev.gain_id_map (
    gainid,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    gain_createddatetime,
    gain_modifieddatetime,
    canonical_object
) values (
    '0b89f4beff68d371', -- gainid
    '0b89f4beff68d371', -- ati_id
    current_timestamp, -- ati_createddatetime
    current_timestamp, -- ati_modifieddatetime
    '0018Y00002sakVUQAY', -- prod sf id for ati
    current_timestamp, -- salesforce_createddatetime
    current_timestamp, -- salesforce_modifieddatetime
    current_timestamp, -- gain_createddatetime
    current_timestamp, -- gain_modifieddatetime
    'medicalfacilities' -- canonical_object
);

update integration.dev.medicalfacilities
set
    parentid = '0b89f4beff68d371'
where
    gainid != '0b89f4beff68d371';

insert into integration.staging.gain_id_map (
    gainid,
    ati_id,
    ati_createddatetime,
    ati_modifieddatetime,
    salesforce_id,
    salesforce_createddatetime,
    salesforce_modifieddatetime,
    gain_createddatetime,
    gain_modifieddatetime,
    canonical_object
) values (
    '0b89f4beff68d371', -- new gainid
    '0b89f4beff68d371', -- ati_id
    current_timestamp, -- ati_createddatetime
    current_timestamp, -- ati_modifieddatetime
    '0018Y00002sakVUQAY', -- prod sf id for ati
    current_timestamp, -- salesforce_createddatetime
    current_timestamp, -- salesforce_modifieddatetime
    current_timestamp, -- gain_createddatetime
    current_timestamp, -- gain_modifieddatetime
    'medicalfacilities' -- canonical_object
);

-- update the staging medicalfacilities table
update integration.staging.medicalfacilities
set
    parentid = '0b89f4beff68d371'
where
    gainid != '0b89f4beff68d371';
