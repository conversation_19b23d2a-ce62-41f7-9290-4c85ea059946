import pytest

import integration.integration_code.data_review_logger as data_review_logger
import integration.models as models


class TestUpdateManualReviewData:
    @pytest.mark.django_db
    def test_should_only_log_real_differences(self):
        """Test that only real differences are logged for manual review."""
        data_review_logger.update_manual_review_data(
            'Cases',
            '1234',
            {
                'Id': '1234',
                'CaseNumber': '1234',
                'ModifiedDateTime': '2020',
                'PartnerAccountName': 'name',
            },
            {
                'Id': '1234',
                'CaseNumber': '4321',
                'ModifiedDateTime': '2020',
                'PartnerAccountName': 'name',
            },
            '1234',
            review_status=1,
        )

        assert models.update_manual_review_data.objects.count() == 1

    @pytest.mark.django_db
    def test_should_not_be_case_sensitive_for_differences(self):
        """Test that case differences are not logged for manual review."""
        data_review_logger.update_manual_review_data(
            'Cases',
            '1234',
            {'Id': '1234', 'Gender': 'm', 'ModifiedDateTime': '2020'},
            {'Id': '1234', 'Gender': 'M', 'ModifiedDateTime': '2020'},
            '1234',
            review_status=1,
        )

        assert models.update_manual_review_data.objects.count() == 0

    @pytest.mark.django_db
    def test_should_not_be_logging_if_is_the_same_number(self):
        """Test that number format differences are not logged for manual review."""
        data_review_logger.update_manual_review_data(
            'Cases',
            '1234',
            {'Id': '1234', 'Invoice_Amount__c': '311.7000'},
            {'Id': '1234', 'Invoice_Amount__c': '311.7'},
            '1234',
            review_status=1,
        )

        assert models.update_manual_review_data.objects.count() == 0
