import json

from simple_salesforce import Salesforce


class SfCredentials:
    def __init__(self):
        pass

    def read_sf_credentials(
        self, filepath: str, environment: str = "production"
    ):
        with open(filepath, 'rb') as f:
            credentials = json.load(f)

        # For production
        if environment == "production":
            # Connect to Salesforce using simple-salesforce
            username = credentials['salesforce-production']['username']
            password = credentials['salesforce-production']['password']
            security_token = credentials['salesforce-production'][
                'security_token'
            ]
            sf = Salesforce(
                username=username,
                password=password,
                security_token=security_token,
            )

        # For sandbox
        if environment == "sandbox":
            # Connect to Salesforce using simple-salesforce
            username = credentials['salesforce-sandbox']['username']
            password = credentials['salesforce-sandbox']['password']
            security_token = credentials['salesforce-sandbox'][
                'security_token'
            ]
            sf = Salesforce(
                username=username,
                password=password,
                security_token=security_token,
                domain='test',
            )

        return sf
