# pyright: reportMissingTypeArgument=warning,reportMissingParameterType=warning
import re

import numpy as np


def standardize_data(
    input_data: np.ndarray,
    mean: np.ndarray,
    std: np.ndarray,
) -> np.ndarray:
    ''' '''
    return (input_data - mean) / std


def pull_number(
    value: str, regex_pattern: str, post_processing_func=None
) -> float:
    # Check for valid value
    if (
        value is not None
        and str(value) != 'nan'
        and str(value) != '0.0'
        and not isinstance(value, float)
    ):
        result = []
        # Extract number
        for s in str(value).split():
            if s.isdigit():
                result = [s]
            else:
                result = re.findall(regex_pattern, value)

        # If a number was found, process and return it
        if len(result) != 0:
            processed_value = (
                str(result[0])
                .replace(',', '')
                .replace('$', '')
                .replace('k', '000')
                .replace('K', '000')
                .replace(' ', '')
                .replace('m', '000000')
                .replace('M', '000000')
            )
            if post_processing_func:
                return post_processing_func(processed_value)
            return float(processed_value)  # Default processing

    return np.nan
