from unittest.mock import MagicMock, patch

import pytest


@pytest.fixture
def mock_sf():
    with patch(
        'integration.integration_code.salesforce_operations.sf'
    ) as mock:
        yield mock


@pytest.fixture
def mock_get_sf_action(mock_sf: MagicMock):
    with patch(
        'integration.integration_code.salesforce_operations.get_sf_action'
    ) as mock:
        yield mock


@pytest.fixture
def mock_sf_action_method(mock_get_sf_action: MagicMock):
    update_response = [
        {
            'success': True,
            'created': True,
            'id': '001Ec00000emK49IAE',
            'errors': [],
        },
    ]

    mock_update = MagicMock()
    mock_update.return_value = update_response

    mock_get_sf_action.side_effect = [
        mock_update,
    ]

    yield mock_update


@pytest.fixture
def mock_get_sf_bulk(mock_sf: MagicMock):
    with patch(
        'integration.integration_code.salesforce_operations.get_sf_bulk'
    ) as mock:
        yield mock


@pytest.fixture
def mock_sf_action_with_error_method(mock_get_sf_action: MagicMock):
    update_response = [
        {
            'success': True,
            'created': True,
            'id': '001Ec00000emK49IAE',
            'errors': [],
        },
        {
            'success': False,
            'created': False,
            'id': None,
            'errors': [
                {
                    'statusCode': 'REQUIRED_FIELD_MISSING',
                    'message': 'Required fields are missing: [VersionData]',
                    'fields': ['VersionData'],
                },
                {
                    'statusCode': 'REQUIRED_FIELD_MISSING',
                    'message': 'Required fields are missing: [VersionData]',
                    'fields': ['VersionData'],
                },
            ],
        },
    ]

    mock_update = MagicMock()
    mock_update.return_value = update_response

    mock_get_sf_action.side_effect = [
        mock_update,
    ]

    yield mock_update
