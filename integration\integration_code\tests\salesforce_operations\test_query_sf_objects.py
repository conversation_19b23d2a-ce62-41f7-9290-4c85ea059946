from unittest.mock import MagicMock, patch

import pytest

import integration.integration_code.salesforce_operations as salesforce_operations


class TestQuerySfObjects:
    """Test cases for query_sf_objects function."""

    @pytest.fixture
    def mock_sf_bulk_query(self):
        """Fixture to mock Salesforce bulk query functionality."""
        with patch.object(
            salesforce_operations, 'get_sf_bulk'
        ) as mock_get_sf_bulk:
            mock_bulk_handler = MagicMock()
            mock_get_sf_bulk.return_value = mock_bulk_handler
            yield mock_bulk_handler

    @pytest.mark.django_db
    def test_query_sf_objects_opportunity_with_batch_size_greater_than_800(
        self,
        mock_sf_bulk_query: MagicMock,
    ):
        """Test that query_sf_objects correctly handles sf_id_list with size > 800 by batching for Opportunity."""
        # Arrange
        sf_object_name = "Opportunity"
        # Create a list of 1200 IDs (greater than the 800 batch size)
        sf_id_list = [f"006Ec00000D8RUzIAN{i:03d}" for i in range(1200)]

        # Mock the salesforce_query global variable for Opportunity
        mock_salesforce_query = {
            "Opportunity": {
                "Columns": [
                    "Id",
                    "Name",
                ],
                "ConditionList": {
                    "IdFilter": {
                        "Condition": "Id IN {sf_id_list}",
                        "Params": ["sf_id_list"],
                    }
                },
            }
        }

        # Mock the get_sf_bulk function to return a mock bulk handler
        mock_bulk_handler = MagicMock()
        mock_bulk_handler.query.return_value = [
            {"Id": "006Ec00000D8RUzIAN001", "Name": "Test Opportunity 1"},
            {"Id": "006Ec00000D8RUzIAN002", "Name": "Test Opportunity 2"},
        ]

        # Expected: 1200 IDs should be split into 2 batches: 800 + 400
        expected_batch_calls = 2

        with (
            patch.object(
                salesforce_operations,
                'salesforce_query',
                mock_salesforce_query,
            ),
            patch.object(
                salesforce_operations,
                'get_sf_bulk',
                return_value=mock_bulk_handler,
            ),
            patch.object(salesforce_operations, 'logger') as mock_logger,
        ):

            # Act
            result = salesforce_operations.query_sf_objects(
                sf_object_name=sf_object_name,
                sf_id_list=sf_id_list,
                condition_names=['IdFilter'],
            )

            # Assert
            # Verify that get_sf_bulk was called twice (once for each batch)
            assert (
                salesforce_operations.get_sf_bulk.call_count
                == expected_batch_calls
            )

            # Verify that query was called on the bulk handler for each batch
            assert mock_bulk_handler.query.call_count == expected_batch_calls

            # Verify the query calls were made with the correct batch sizes
            query_calls = mock_bulk_handler.query.call_args_list
            assert len(query_calls) == expected_batch_calls

            # First batch should have 800 IDs
            first_batch_query = query_calls[0][0][
                0
            ]  # First call, first argument
            assert (
                "006Ec00000D8RUzIAN799" in first_batch_query
            )  # Last ID of first batch
            assert (
                "006Ec00000D8RUzIAN800" not in first_batch_query
            )  # First ID of second batch

            # Second batch should have 400 IDs
            second_batch_query = query_calls[1][0][
                0
            ]  # Second call, first argument
            assert (
                "006Ec00000D8RUzIAN800" in second_batch_query
            )  # First ID of second batch
            assert (
                "006Ec00000D8RUzIAN1199" in second_batch_query
            )  # Last ID of second batch

            # Verify that the result contains data from both batches
            assert len(result) == 4  # 2 results per batch * 2 batches

            # Verify no errors were logged
            mock_logger.error.assert_not_called()

    @pytest.mark.django_db
    def test_query_sf_objects_funding_with_exact_batch_size_800(
        self,
        mock_sf_bulk_query: MagicMock,
    ):
        """Test that query_sf_objects correctly handles sf_id_list with exactly 800 IDs for Funding__c."""
        # Arrange
        sf_object_name = "Funding__c"
        # Create a list of exactly 800 IDs
        sf_id_list = [f"a0XEc00000D8RUzIAN{i:03d}" for i in range(800)]

        mock_salesforce_query = {
            "Funding__c": {
                "Columns": [
                    "Id",
                    "Name",
                ],
                "ConditionList": {
                    "IdFilter": {
                        "Condition": "Id IN {sf_id_list}",
                        "Params": ["sf_id_list"],
                    }
                },
            }
        }

        mock_bulk_handler = MagicMock()
        mock_bulk_handler.query.return_value = [
            {"Id": "a0XEc00000D8RUzIAN001", "Name": "Test Funding 1"}
        ]

        with (
            patch.object(
                salesforce_operations,
                'salesforce_query',
                mock_salesforce_query,
            ),
            patch.object(
                salesforce_operations,
                'get_sf_bulk',
                return_value=mock_bulk_handler,
            ),
            patch.object(salesforce_operations, 'logger') as mock_logger,
        ):

            # Act
            result = salesforce_operations.query_sf_objects(
                sf_object_name=sf_object_name,
                sf_id_list=sf_id_list,
                condition_names=['IdFilter'],
            )

            # Assert
            # Should only be one batch since it's exactly 800 IDs
            assert salesforce_operations.get_sf_bulk.call_count == 1
            assert mock_bulk_handler.query.call_count == 1

            # Verify the query contains all 800 IDs
            query_call = mock_bulk_handler.query.call_args[0][0]
            assert "a0XEc00000D8RUzIAN000" in query_call
            assert "a0XEc00000D8RUzIAN799" in query_call

            # Verify result
            assert len(result) == 1
            assert result[0]["Id"] == "a0XEc00000D8RUzIAN001"

            # Verify no errors were logged
            mock_logger.error.assert_not_called()

    @pytest.mark.django_db
    def test_query_sf_objects_funding_with_batch_size_greater_than_800(
        self,
        mock_sf_bulk_query: MagicMock,
    ):
        """Test that query_sf_objects correctly handles sf_id_list with size > 800 by batching for Funding__c."""
        # Arrange
        sf_object_name = "Funding__c"
        # Create a list of 1200 IDs (greater than the 800 batch size)
        sf_id_list = [f"a0XEc00000D8RUzIAN{i:03d}" for i in range(1200)]

        # Mock the salesforce_query global variable for Funding__c
        mock_salesforce_query = {
            "Funding__c": {
                "Columns": [
                    "Id",
                    "Name",
                ],
                "ConditionList": {
                    "IdFilter": {
                        "Condition": "Id IN {sf_id_list}",
                        "Params": ["sf_id_list"],
                    }
                },
            }
        }

        # Mock the get_sf_bulk function to return a mock bulk handler
        mock_bulk_handler = MagicMock()
        mock_bulk_handler.query.return_value = [
            {"Id": "a0XEc00000D8RUzIAN001", "Name": "Test Funding 1"},
            {"Id": "a0XEc00000D8RUzIAN002", "Name": "Test Funding 2"},
        ]

        # Expected: 1200 IDs should be split into 2 batches: 800 + 400
        expected_batch_calls = 2

        with (
            patch.object(
                salesforce_operations,
                'salesforce_query',
                mock_salesforce_query,
            ),
            patch.object(
                salesforce_operations,
                'get_sf_bulk',
                return_value=mock_bulk_handler,
            ),
            patch.object(salesforce_operations, 'logger') as mock_logger,
        ):

            # Act
            result = salesforce_operations.query_sf_objects(
                sf_object_name=sf_object_name,
                sf_id_list=sf_id_list,
                condition_names=['IdFilter'],
            )

            # Assert
            # Verify that get_sf_bulk was called twice (once for each batch)
            assert (
                salesforce_operations.get_sf_bulk.call_count
                == expected_batch_calls
            )

            # Verify that query was called on the bulk handler for each batch
            assert mock_bulk_handler.query.call_count == expected_batch_calls

            # Verify the query calls were made with the correct batch sizes
            query_calls = mock_bulk_handler.query.call_args_list
            assert len(query_calls) == expected_batch_calls

            # First batch should have 800 IDs
            first_batch_query = query_calls[0][0][
                0
            ]  # First call, first argument
            assert (
                "a0XEc00000D8RUzIAN799" in first_batch_query
            )  # Last ID of first batch
            assert (
                "a0XEc00000D8RUzIAN800" not in first_batch_query
            )  # First ID of second batch

            # Second batch should have 400 IDs
            second_batch_query = query_calls[1][0][
                0
            ]  # Second call, first argument
            assert (
                "a0XEc00000D8RUzIAN800" in second_batch_query
            )  # First ID of second batch
            assert (
                "a0XEc00000D8RUzIAN1199" in second_batch_query
            )  # Last ID of second batch

            # Verify that the result contains data from both batches
            assert len(result) == 4  # 2 results per batch * 2 batches

            # Verify no errors were logged
            mock_logger.error.assert_not_called()

    @pytest.mark.django_db
    def test_query_sf_objects_opportunity_with_multiple_batches_and_error_handling(
        self,
        mock_sf_bulk_query: MagicMock,
    ):
        """Test that query_sf_objects handles errors in individual batches correctly for Opportunity."""
        # Arrange
        sf_object_name = "Opportunity"
        # Create a list of 2000 IDs to test multiple batches
        sf_id_list = [f"006Ec00000D8RUzIAN{i:04d}" for i in range(2000)]

        mock_salesforce_query = {
            "Opportunity": {
                "Columns": [
                    "Id",
                    "Name",
                    "Date_of_Birth__c",
                ],
                "ConditionList": {
                    "IdFilter": {
                        "Condition": "Id IN {sf_id_list}",
                        "Params": ["sf_id_list"],
                    }
                },
            }
        }

        mock_bulk_handler = MagicMock()
        # First batch succeeds, second batch fails, third batch succeeds
        mock_bulk_handler.query.side_effect = [
            [
                {
                    "Id": "006Ec00000D8RUzIAN0001",
                    "Name": "Test Opportunity 1",
                    "Date_of_Birth__c": "1990-01-01",
                }
            ],  # First batch
            Exception("Salesforce API Error"),  # Second batch fails
            [
                {
                    "Id": "006Ec00000D8RUzIAN1601",
                    "Name": "Test Opportunity 1601",
                    "Date_of_Birth__c": "1985-05-15",
                }
            ],  # Third batch
        ]

        with (
            patch.object(
                salesforce_operations,
                'salesforce_query',
                mock_salesforce_query,
            ),
            patch.object(
                salesforce_operations,
                'get_sf_bulk',
                return_value=mock_bulk_handler,
            ),
            patch.object(salesforce_operations, 'logger') as mock_logger,
        ):

            # Act
            result = salesforce_operations.query_sf_objects(
                sf_object_name=sf_object_name,
                sf_id_list=sf_id_list,
                condition_names=['IdFilter'],
            )

            # Assert
            # Should be 3 batches: 800 + 800 + 400
            assert salesforce_operations.get_sf_bulk.call_count == 3
            assert mock_bulk_handler.query.call_count == 3

            # Should have results from successful batches only
            assert len(result) == 2  # 1 from first batch + 1 from third batch

            # Verify error was logged for the failed batch
            mock_logger.error.assert_called_once()
            error_call = mock_logger.error.call_args
            assert (
                "Bulk query batch 2 of 800"
                in error_call[1]['extra']['additional_details']
            )

    @pytest.mark.django_db
    def test_query_sf_objects_funding_with_empty_sf_id_list(
        self,
        mock_sf_bulk_query: MagicMock,
    ):
        """Test that query_sf_objects handles empty sf_id_list correctly for Funding__c."""
        # Arrange
        sf_object_name = "Funding__c"
        sf_id_list = []

        mock_salesforce_query = {
            "Funding__c": {
                "Columns": [
                    "Id",
                    "Name",
                    "Date_of_Service__c",
                ],
                "ConditionList": {
                    "IdFilter": {
                        "Condition": "Id IN {sf_id_list}",
                        "Params": ["sf_id_list"],
                    }
                },
            }
        }

        with (
            patch.object(
                salesforce_operations,
                'salesforce_query',
                mock_salesforce_query,
            ),
            patch.object(salesforce_operations, 'logger') as mock_logger,
        ):

            # Act
            result = salesforce_operations.query_sf_objects(
                sf_object_name=sf_object_name,
                sf_id_list=sf_id_list,
                condition_names=['IdFilter'],
            )

            # Assert
            assert result == []
            mock_logger.error.assert_called_once()
            assert "Invalid sf_id_list" in mock_logger.error.call_args[0][0]

    @pytest.mark.django_db
    def test_query_sf_objects_with_invalid_object_name(
        self,
        mock_sf_bulk_query: MagicMock,
    ):
        """Test that query_sf_objects handles invalid object names correctly."""
        # Arrange
        sf_object_name = "InvalidObject"
        sf_id_list = ["006Ec00000D8RUzIAN001"]

        with patch.object(salesforce_operations, 'logger') as mock_logger:

            # Act
            result = salesforce_operations.query_sf_objects(
                sf_object_name=sf_object_name,
                sf_id_list=sf_id_list,
                condition_names=['IdFilter'],
            )

            # Assert
            assert result == []
            mock_logger.error.assert_called_once()
            assert (
                "Invalid Salesforce object InvalidObject."
                in mock_logger.error.call_args[0]
            )
