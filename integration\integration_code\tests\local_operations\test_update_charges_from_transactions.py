import typing
from contextvars import <PERSON><PERSON>V<PERSON>
from unittest.mock import MagicMock, patch

import psycopg
import pytest

from integration.integration_code import aws_operations, local_operations
from tests import test_helper


class TestUpdateChargesFromTransactions:
    # Constants for test data
    CHARGE_WITH_MULTIPLE_TRANSACTION_IDS = [
        '3f2e4d5c6b7a8f91',
        '4d5e6f7a8b9c0123',
        '5e6f7a8b9c0d1234',
    ]
    CHARGE_WITH_MULTIPLE_TRANSACTION_CHARGE_ID = '6b7a8f913f2e4d5c'

    REVERSAL_TRANSACTION_ID = '4e3d5c6b7a8f92'
    REVERSAL_TRANSACTION_CHARGE_ID = '9f8e7d6c5b4a3210'

    INVOICABLE_TRANSACTION_ID = '5d4c6b7a8f93'
    INVOICEABLE_TRANSACTION_CHARGE_ID = 'a1b2c3d4e5f67890'

    @pytest.fixture
    def update_charges_from_reversal_reversal(self):
        with patch(
            "integration.integration_code.local_operations.aws_operations.update_postgres_charges_from_complete_reversal_transactions",
            side_effect=aws_operations.update_postgres_charges_from_complete_reversal_transactions,
        ) as mock_function:
            yield mock_function

    @pytest.fixture
    def mock_update_charges(self):
        with patch(
            "integration.integration_code.local_operations.aws_operations.update_postgres_charges_from_non_reversal_transactions",
            side_effect=aws_operations.update_postgres_charges_from_non_reversal_transactions,
        ) as mock_function:
            yield mock_function

    @pytest.mark.django_db
    def test_no_transactions(
        self,
        database: psycopg.Connection[typing.Any],
        verify_errors: MagicMock,
    ):
        """Should return None when no transactions exist"""

        result = local_operations.update_charges_from_transactions(["dummy"])

        assert result is None
        assert test_helper.verify_if_log_exists(
            verify_errors, "No data fetched for transaction update."
        )

    @pytest.mark.django_db
    def test_valid_transactions(
        self,
        database: psycopg.Connection[typing.Any],
        mock_context: ContextVar[typing.Any],
    ):
        """Should process valid transactions and return charge IDs"""

        result = local_operations.update_charges_from_transactions(
            [self.CHARGE_WITH_MULTIPLE_TRANSACTION_IDS[0]]
        )
        assert result == [self.CHARGE_WITH_MULTIPLE_TRANSACTION_CHARGE_ID]

    @pytest.mark.django_db
    def test_transactions_with_reversals(
        self,
        database: psycopg.Connection[typing.Any],
        update_charges_from_reversal_reversal: MagicMock,
        mock_update_charges: MagicMock,
        mock_context: ContextVar[typing.Any],
    ):
        """Should process reversal transactions separately"""
        result = local_operations.update_charges_from_transactions(
            [self.REVERSAL_TRANSACTION_ID]
        )

        assert result == [self.REVERSAL_TRANSACTION_CHARGE_ID]

        mock_update_charges.assert_called()
        updated_data = mock_update_charges.call_args[0][0].iloc[0]
        assert updated_data['nongainamountpaidtoprovider'] == 0.0
        assert updated_data['nongainadjustment'] == 0.0
        assert updated_data['gainprenegotiationamountpaidtoprovider'] == 0
        assert updated_data['gainprenegotiationadjustment'] == 0.0
        assert updated_data['balance'] == 2100.0

        update_charges_from_reversal_reversal.assert_called()

    @pytest.mark.django_db
    def test_invoiceable_transactions(
        self,
        database: psycopg.Connection[typing.Any],
        mock_update_charges: MagicMock,
        mock_context: ContextVar[typing.Any],
    ):
        """Should correctly process invoiceable transactions"""

        result = local_operations.update_charges_from_transactions(
            [self.INVOICABLE_TRANSACTION_ID]
        )
        assert result == [self.INVOICEABLE_TRANSACTION_CHARGE_ID]

        # In this case, `gainprenegotiationamountpaidtoprovider` should be assigned
        # the seeded transaction amount (1500).
        # The `balance` should then be calculated as follows:
        # charge_amount (7200) - gainprenegotiationamountpaidtoprovider (1500) = 5700.
        mock_update_charges.assert_called()
        updated_data = mock_update_charges.call_args[0][0].iloc[0]
        assert updated_data['gainprenegotiationamountpaidtoprovider'] == 1500.0
        assert updated_data['balance'] == 5700.0

    @pytest.mark.django_db
    def test_charge_update_with_multiple_transaction(
        self,
        database: psycopg.Connection[typing.Any],
        mock_update_charges: MagicMock,
        mock_context: ContextVar[typing.Any],
    ):
        """Should process multiple valid transactions and return charge IDs and check value calculations"""

        result = local_operations.update_charges_from_transactions(
            self.CHARGE_WITH_MULTIPLE_TRANSACTION_IDS
        )
        assert result == [self.CHARGE_WITH_MULTIPLE_TRANSACTION_CHARGE_ID]

        # In this case, `nongainamountpaidtoprovider` should be assigned
        # the seeded transaction amount (1500).
        # The `balance` should then be calculated as follows:
        # charge_amount (2000) - nongainamountpaidtoprovider (1000) - gainprenegotiationamountpaidtoprovider (500)= 500.
        mock_update_charges.assert_called()
        updated_data = mock_update_charges.call_args[0][0].iloc[0]
        assert updated_data['nongainamountpaidtoprovider'] == 1000.0
        assert updated_data['gainprenegotiationamountpaidtoprovider'] == 500.0
        assert updated_data['balance'] == 500.0
