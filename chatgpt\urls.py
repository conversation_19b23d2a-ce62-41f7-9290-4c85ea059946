'''
GainInternalWebApp URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
'''

import django.urls

from . import views

app_name = 'chatgpt'
urlpatterns = [
    django.urls.path('openai/', views.openai, name='openai'),
    django.urls.path(
        'portal_reduction/', views.portal_reduction, name='portal_reduction'
    ),
]
